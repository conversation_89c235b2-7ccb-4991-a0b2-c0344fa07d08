<?xml version="1.0"?>
<configuration>
	<configSections>
		<section name="castle" type="Castle.Windsor.Configuration.AppDomain.CastleSectionHandler, Castle.Windsor" />
		<section name="hibernate-configuration" type="NHibernate.Cfg.Configuration<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NHibernate" allowLocation="true" allowDefinition="Everywhere" allowExeDefinition="MachineToApplication" overrideModeDefault="Allow" restartOnExternalChanges="true" requirePermission="true" />
		<section name="quartz" type="System.Configuration.NameValueSectionHandler, System, Version=*******,Culture=neutral, PublicKeyToken=b77a5c561934e089" />
		<section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler,log4net" />
	</configSections>
	<appSettings configProtectionProvider="DataProtectionConfigurationProvider">
		<EncryptedData>
			<CipherData>
				<CipherValue>AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAqZV0k7bEUkOtfyRATrlh+QQAAAACAAAAAAADZgAAwAAAABAAAADi78JWlCgA9eU9LNs0Q3StAAAAAASAAACgAAAAEAAAAKcNXaXxbdXszRpiM4uZe1oYAQAAAgRU8Nd6z9kjEoWsX/7vYdQqmlu3NsBQb1ff+szjybi9/eEiGXbxjt1Jilw0HAmqziVVoGju+Zmu+MOksfvUzP7nx09eoLSMF64IMtKzXngqU4PpD9NWPEoX9Fi63XxAA6RCQ4ljTVy6cs/fSKFu8E6H472HqP3KuYo2KKdOpiUTqqG3XjxhbpPlkMmfNODtvCLqUbiXFc3i+AGYgTpHgBj5t78p1lCTYFfrWhERgoOGUNxBR1sqLW2yTRVI7qCrNmqAoDeg8iKmFUUshblXZYYTFRTCVmS72QvZrfhdut4qTEXorMheA1yS2xRvauu559FaD9/DxC2MnoQtVqoMq/70W7pcFes3Ywgik7iHueMCdOcJ7ktDEhQAAABwEfzDNFEqnpluT0THZiSvrU0+EA==</CipherValue>
			</CipherData>
		</EncryptedData>
	</appSettings>
	<castle>
		<components>
			<component id="repositories" lifestyle="transient" service="Upp.Irms.Core.IRepository`1, Upp.Irms.Core" type="Upp.Irms.Core.Repository`1, Upp.Irms.Core" />
		</components>
	</castle>
	<hibernate-configuration configProtectionProvider="DataProtectionConfigurationProvider">
		<EncryptedData>
			<CipherData>
				<CipherValue>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</CipherValue>
			</CipherData>
		</EncryptedData>
	</hibernate-configuration>
	<quartz>
		<add key="quartz.scheduler.instanceName" value="QuartzScheduler" />
		<!-- Configure Thread Pool -->
		<add key="quartz.threadPool.type" value="Quartz.Simpl.SimpleThreadPool, Quartz" />
		<add key="quartz.threadPool.threadCount" value="10" />
		<add key="quartz.threadPool.threadPriority" value="Normal" />
		<!-- Configure Job Store -->
		<add key="quartz.jobStore.type" value="Quartz.Simpl.RAMJobStore, Quartz" />
		<add key="quartz.plugin.xml.type" value="Quartz.Plugin.Xml.JobInitializationPlugin, Quartz" />
		<add key="quartz.plugin.xml.fileNames" value="~/quartz_jobs.xml" />
		<add key="quartz.plugin.xml.overwriteExistingJobs" value="true" />
		<!-- ScanInterval in seconds -->
		<add key="quartz.plugin.xml.scanInterval" value="60" />
	</quartz>
	<log4net>
		<appender name="RollingFileAppender" type="log4net.Appender.RollingFileAppender">
			<file value="Logs\\AlertGenerator360.txt"/>
			<appendToFile value="true"/>
			<rollingStyle value="Composite"/>
			<maxSizeRollBackups value="14"/>
			<maximumFileSize value="5000KB"/>
			<datePattern value="yyyyMMdd"/>
			<staticLogFileName value="true"/>
			<layout type="log4net.Layout.PatternLayout">
				<conversionPattern value="[%-5p] %d  %m%n"/>
			</layout>
		</appender>
		<!-- Set levels DEBUG | INFO | WARN | ERROR   -->
		<root>
			<priority value="DEBUG"/>
			<appender-ref ref="RollingFileAppender"/>
		</root>

		<logger name="NHibernate" additivity="true">
			<level value="ERROR"/>
			<appender-ref ref="RollingFileAppender"/>
		</logger>
		<!-- To log SQL queries set level to DEBUG , otherwise ERROR -->
		<logger name="NHibernate.SQL" additivity="true">
			<level value="DEBUG"/>
			<appender-ref ref="RollingFileAppender"/>
		</logger>
		<logger name="Upp.Irms.Core" additivity="true">
			<level value="ERROR"/>
			<appender-ref ref="RollingFileAppender"/>
		</logger>
		<logger name="Upp.Irms.Domain" additivity="true">
			<level value="ERROR"/>
			<appender-ref ref="RollingFileAppender"/>
		</logger>

	</log4net>
</configuration>