using log4net;
using Quartz;

namespace Upp.Irms.AlertGenerator.Host
{
    /// <summary>
    /// A helpful abstract base class for implementors of <see cref="IJobListener" />.
    /// </summary>
    /// <remarks>
    /// <p>
    /// The methods in this class are empty so you only need to override the  
    /// subset for the <see cref="IJobListener" /> events you care about.
    /// </p>
    /// 
    /// <p>
    /// You are required to implement <see cref="IJobListener.Name" /> 
    /// to return the unique name of your <see cref="IJobListener" />.  
    /// </p>
    /// </remarks>
    /// <seealso cref="IJobListener" />
    public class JobListenerSupport : IJobListener
    {
        ILog logger = LogManager.GetLogger(typeof(JobListenerSupport));

        /// <summary>
        /// Initializes a new instance of the <see cref="JobListenerSupport"/> class.
        /// </summary>
        public JobListenerSupport()
        {

        }

        /// <summary>
        /// Get the name of the <see cref="IJobListener"/>.
        /// </summary>
        /// <value></value>
        public string Name
        {
            get { return "JobExecutionHistoryRecorder"; }
        }

        /// <summary>
        /// Called by the <see cref="IScheduler"/> when a <see cref="JobDetail"/>
        /// is about to be executed (an associated <see cref="Trigger"/>
        /// has occured).
        /// <p>
        /// This method will not be invoked if the execution of the Job was vetoed
        /// by a <see cref="ITriggerListener"/>.
        /// </p>
        /// </summary>
        /// <param name="context"></param>
        /// <seealso cref="JobExecutionVetoed(JobExecutionContext)"/>
        public void JobToBeExecuted(JobExecutionContext context)
        {
            if (context.JobDetail.Name == "JobInitializationPlugin_xml_quartz_jobs_xml")
                return;
            if (logger.IsInfoEnabled) logger.InfoFormat("Job Execution Started .. - {0}", context.JobDetail.Name);
            if (logger.IsDebugEnabled) logger.Debug("=====================================================================");
        }

        /// <summary>
        /// Called by the <see cref="IScheduler"/> when a <see cref="JobDetail"/>
        /// was about to be executed (an associated <see cref="Trigger"/>
        /// has occured), but a <see cref="ITriggerListener"/> vetoed it's
        /// execution.
        /// </summary>
        /// <param name="context"></param>
        /// <seealso cref="JobToBeExecuted(JobExecutionContext)"/>
        public void JobExecutionVetoed(JobExecutionContext context)
        {
            if (context.JobDetail.Name == "JobInitializationPlugin_xml_quartz_jobs_xml")
                return;
            if (logger.IsInfoEnabled) logger.InfoFormat("Job Execution Started .. - {0}", context.JobDetail.Name);
        }

        /// <summary>
        /// Called by the <see cref="IScheduler"/> after a <see cref="JobDetail"/>
        /// has been executed, and be for the associated <see cref="Trigger"/>'s
        /// <see cref="Trigger.Triggered"/> method has been called.
        /// </summary>
        /// <param name="context"></param>
        /// <param name="jobException"></param>
        public void JobWasExecuted(JobExecutionContext context, JobExecutionException jobException)
        {
            if (jobException == null)
            {
                if (context.JobDetail.Name == "JobInitializationPlugin_xml_quartz_jobs_xml")
                    return;
                if (logger.IsDebugEnabled) logger.Debug("=====================================================================");
                if (logger.IsInfoEnabled) logger.InfoFormat("Job Execution Completed .. - {0}", context.JobDetail.Name);
            }
            else
            {
                if (logger.IsErrorEnabled) logger.ErrorFormat("Job Execution Failed.. - {0}", context.JobDetail.Name);
            }
        }
    }
}