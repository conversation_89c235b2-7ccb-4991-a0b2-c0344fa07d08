﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Quartz;
using Upp.Shared.Utilities;
using log4net;
using Upp.Irms.Core;
using NHibernate.Criterion;
using NHibernate.Transform;
using Upp.Irms.Domain;
using NHibernate.SqlCommand;
using Upp.Irms.Constants;

namespace Upp.Irms.AlertGenerator.Host
{
    public class MustShipTodayJob : IJob
    {
        #region Fields

        ILog _logger = LogManager.GetLogger(typeof(MustShipTodayJob));

        const string JParam_Warehouses = "WAREHOUSES";
        string _warehouses = "";
        const string JParam_NumberOfDays = "NUMBEROFDAYS";
        int _numberOfDays = 0;
        const string JParam_OrderHeaderBillToCustomerCode = "BILLTOCUSTOMERCODE";
        string _orderHeaderBillToCustomerCode = "";
        const string JParam_CustomerOrderHeaderStatus = "ORDERSTATUS";
        string _customerOrderHeaderStatus = "";
        const string JParam_EmailSubject = "EMAILSUBJECT";
        string _emailSubject = "";

        #endregion

        #region Constructor

        public MustShipTodayJob()
        {
        }

        #endregion

        #region Methods.Public

        public virtual void Execute(JobExecutionContext context)
        {
            if (JobParametersAreValid(context.MergedJobDataMap) == false)
            {
                JobExecutionException jex = new JobExecutionException("Missing job parameter");
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }

            ParseJobParameters(context.MergedJobDataMap);

            DoWork();
        }

        #endregion

        #region Methods.Private

        private bool JobParametersAreValid(JobDataMap jobParams)
        {
            bool validity = true;

            if (!jobParams.Contains(JParam_Warehouses))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_Warehouses);
                validity = false;
            }
            if (!jobParams.Contains(JParam_NumberOfDays))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_NumberOfDays);
                validity = false;
            }
            if (!jobParams.Contains(JParam_OrderHeaderBillToCustomerCode))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_OrderHeaderBillToCustomerCode);
                validity = false;
            }
            if (!jobParams.Contains(JParam_CustomerOrderHeaderStatus))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_CustomerOrderHeaderStatus);
                validity = false;
            }
            if (!jobParams.Contains(JParam_EmailSubject))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_EmailSubject);
                validity = false;
            }
            return validity;
        }

        private void ParseJobParameters(JobDataMap jobParams)
        {
            try
            {
                //map the parameters to jobParams
                this._warehouses = jobParams.GetString(JParam_Warehouses);
                this._numberOfDays = jobParams.GetInt(JParam_NumberOfDays);
                this._orderHeaderBillToCustomerCode = jobParams.GetString(JParam_OrderHeaderBillToCustomerCode);
                this._customerOrderHeaderStatus = jobParams.GetString(JParam_CustomerOrderHeaderStatus);
                this._emailSubject = jobParams.GetString(JParam_EmailSubject);
            }
            catch (Exception ex)
            {
                JobExecutionException jex = new JobExecutionException("Invalid data type in job parameters", ex);
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }
        }

        private void DoWork()
        {
            try
            {
                if (String.IsNullOrWhiteSpace(_warehouses))
                {
                    if (_logger.IsErrorEnabled) _logger.Error("No Warehouses to Process");
                    return;
                }
                if (_numberOfDays <= 0)
                {
                    if (_logger.IsErrorEnabled) _logger.Error("Number of days must be greater than zero");
                    return;
                }
                if (String.IsNullOrWhiteSpace(_orderHeaderBillToCustomerCode))
                {
                    if (_logger.IsErrorEnabled) _logger.Error("No BillTo Customer Codes to Process");
                    return;
                }
                if (String.IsNullOrWhiteSpace(_customerOrderHeaderStatus))
                {
                    if (_logger.IsErrorEnabled) _logger.Error("No Orderheader status to Process");
                    return;
                }
                TransactionType transType = null;
                StatusCode taskOpenStatus = null;

                StatusCode openAlertStatuscode = null;
                AlertDefinition alertDefinition = null;
                List<int> billToLocationTypes = new List<int>();
                using (UnitWrapper wrapper = new UnitWrapper())
                {
                    wrapper.Execute(() =>
                    {
                        transType = GetReferenceObject<TransactionType>(AlertTransactions.AlertRequest, FunctionalAreas.Alert);
                        taskOpenStatus = GetReferenceObject<StatusCode>(TaskStatuses.Open);
                        openAlertStatuscode = GetReferenceObject<StatusCode>(AlertStatuses.Open, FunctionalAreas.Alert);
                        alertDefinition = GetReferenceObject<AlertDefinition>("MSTO");

                        DetachedCriteria criteriaLocationType = DetachedCriteria.For<LocationType>()
                                                               .CreateAlias("FunctionalAreaCode", "FunctionalAreaCode")
                                                               .SetProjection(Projections.ProjectionList()
                                                               .Add(Projections.Property("Id"), "Id"))
                                                                .Add(Restrictions.Eq("LocationTypeCode", "B"))
                                                                .Add(Restrictions.Eq("FunctionalAreaCode.Code", "ORG"))
                                                               .SetResultTransformer(Transformers.AliasToBean<LocationType>());
                        IList<LocationType> locationTypes = Repositories.Get<LocationType>().List(criteriaLocationType);
                        billToLocationTypes = locationTypes.Select(loc => loc.Id.Value).Distinct().ToList();
                    });
                }
                if (transType == null)
                {
                    if (_logger.IsErrorEnabled) _logger.Error("Supporting objects not available : TransactionType");
                    return;
                }
                if (taskOpenStatus == null)
                {
                    if (_logger.IsErrorEnabled) _logger.Error("Supporting objects not available : Task StatusCode");
                    return;
                }
                if (openAlertStatuscode == null)
                {
                    if (_logger.IsErrorEnabled) _logger.Error("Supporting objects not available : Alert StatusCode");
                    return;
                }
                if (alertDefinition == null)
                {
                    if (_logger.IsErrorEnabled) _logger.Error("Supporting objects not available : alert Definition with code 'MSTO'");
                    return;
                }

                _warehouses = _warehouses.TrimEnd(';');
                string[] rowFormats = _warehouses.Split(';');
                foreach (string rowFormat in rowFormats)
                {
                    string[] fieldFormatDescriptions = rowFormat.Split(':');

                    string companyCode = fieldFormatDescriptions[0];
                    string[] warehouses = fieldFormatDescriptions[1].Split(',');
                    if (_logger.IsDebugEnabled) _logger.DebugFormat("Comapny Code :{0}, CompanyLocations Count : {0}", companyCode, warehouses.Length.ToString());
                    string[] billToCustomerCodes = _orderHeaderBillToCustomerCode.Split(',');

                    IList<Customer> customers = null;
                    IList<OrderHeader> orderHeaders = null;
                    IList<CompanyLocationType> companyLocationTypes = null;
                    List<int> orderHeaderStatusIds = null;
                    _logger.InfoFormat("Get Customers and Order headers.");
                    using (UnitWrapper wrapper = new UnitWrapper())
                    {
                        wrapper.Execute(() =>
                        {
                            customers = GetListofCustomers(warehouses, billToCustomerCodes);

                            orderHeaderStatusIds = GetHeaderStatusIds();

                            if (customers != null && customers.Count > 0)
                            {
                                companyLocationTypes = GetCompanyLocationTypes(warehouses, companyCode);
                                orderHeaders = GetCustomerOrders(warehouses, customers, orderHeaderStatusIds, companyLocationTypes, billToLocationTypes);
                            }
                        });
                    }

                    if (customers == null || customers.Count == 0) return;

                    //Step 3.  Process the Job by Agencywise 
                    if (_logger.IsDebugEnabled) _logger.Debug("Step 3.  Process the Job by Customer wise");

                    CompanyLocationType companyLocationType = null;

                    foreach (Customer customer in customers)
                    {
                        using (UnitWrapper wrapper = new UnitWrapper())
                        {
                            wrapper.Execute(() =>
                            {
                                companyLocationType = companyLocationTypes.Where(c => c.Id == customer.Id).FirstOrDefault();
                                IList<OrderHeader> orders = orderHeaders.Where(c => c.CustomerCode == customer.CustomerCode && c.Id == companyLocationType.Id).ToList<OrderHeader>();
                                //create alert
                                if (orders.Count > 0)
                                {
                                    Alert alert = CreateAlert(alertDefinition, orders, openAlertStatuscode, customer.AlternateCustomerCode);
                                    Repositories.Get<Alert>().Add(alert);

                                    Task task = CreateTask(transType, taskOpenStatus, alert, companyLocationType);
                                    Repositories.Get<Task>().Add(task);

                                    if (_logger.IsDebugEnabled) _logger.DebugFormat("Created Task/Alert for User Email :{0}", alert.ToList);
                                }
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
            }
        }

        private IList<OrderHeader> GetCustomerOrders(string[] warehouses, IList<Customer> customers, List<int> orderHeaderStatusIds, IList<CompanyLocationType> companyLocationTypes, List<int> billToLocationTypes)
        {
            List<string> customerCodes = customers.Select(c => c.CustomerCode).ToList();
            List<int> locationIds = companyLocationTypes.Select(com => com.Id.Value).ToList();
            IList<OrderHeader> orders = null;
            try
            {
                DetachedCriteria criteriaOrderHeaders = DetachedCriteria.For<OrderHeader>("_root")
                                                        .CreateAlias("OrderLocations", "OrderLocations")
                                                        .CreateAlias("OrderStatuses", "OrderStatuses")
                                                        .CreateAlias("OrderStatuses.StatusCode", "StatusCode")
                                                        .SetProjection(Projections.ProjectionList()
                                                                        .Add(Projections.Property("CompanyLocationType.Id"), "Id")
                                                                        .Add(Projections.Property("OrderCode"), "OrderCode")
                                                                        .Add(Projections.Property("Ordered"), "Ordered")
                                                                        .Add(Projections.Property("OrderLocations.CustomerCode"), "CustomerCode"))
                                                        .Add(Restrictions.In("CompanyLocationType.Id", locationIds))
                                                        .Add(Restrictions.IsNotNull("Ordered"))
                                                        .Add(Restrictions.In("OrderLocations.CustomerCode", customerCodes))
                                                        .Add(Restrictions.In("StatusCode.Id", orderHeaderStatusIds))
                                                        .Add(Restrictions.In("OrderLocations.LocationType.Id", billToLocationTypes))
                                                        .SetResultTransformer(Transformers.AliasToBean<OrderHeader>());

                DetachedCriteria detachedCurrStatus = DetachedCriteria.For<OrderStatus>("os")
                         .CreateAlias("os.StatusCode", "StatusCodes")
                         .SetProjection(Projections.Property("os.Id"))
                         .Add(Expression.EqProperty("_root.Id", "os.OrderHeader.Id"))
                         .AddOrder(new Order("os.Occurred", false))
                         .AddOrder(new Order("StatusCodes.SortOrder", false))
                         .SetMaxResults(1);
                criteriaOrderHeaders.Add(Subqueries.PropertyEq("OrderStatuses.Id", detachedCurrStatus));

                orders = Repositories.Get<OrderHeader>().List(criteriaOrderHeaders);
                if (orders.Count > 0)
                    orders = orders.Where(order => DateTime.Now.Date.Subtract(order.Ordered.Value.Date).TotalDays >= _numberOfDays).ToList();

                if (orders == null || orders.Count == 0)
                {
                    if (_logger.IsErrorEnabled) _logger.ErrorFormat("No Order Headers found to send Must Ship Today alert");
                    throw new Exception(string.Format("No Order Headers found to send Must Ship Today alert"));
                }
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Exception while Retrieving Order Headers : {0}", ex.Message);
                throw ex;
            }
            return orders;
        }

        private IList<CompanyLocationType> GetCompanyLocationTypes(string[] warehouses, string companyCode)
        {
            IList<CompanyLocationType> companyLocationTypes = null;
            try
            {
                DetachedCriteria criteria = DetachedCriteria.For<CompanyLocationType>()
                                                            .CreateAlias("CompanyLocation", "CompanyLocation")
                                                            .CreateAlias("CompanyLocation.Company", "Company")
                                                           .Add(Restrictions.In("CompanyLocationCode", warehouses))
                                                           .Add(Restrictions.Eq("Company.CompanyCode", companyCode));
                companyLocationTypes = Repositories.Get<CompanyLocationType>().List(criteria);
                if (companyLocationTypes == null || companyLocationTypes.Count == 0)
                {
                    if (_logger.IsErrorEnabled) _logger.ErrorFormat("No Warehouses found.");
                    throw new Exception(string.Format("No Warehouses found."));
                }
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Exception while retrieving Warehouses :{0}", ex.Message);
                throw ex;
            }
            return companyLocationTypes;
        }

        private List<int> GetHeaderStatusIds()
        {
            List<int> headerStatuses = null;
            try
            {
                DetachedCriteria criteriastatusCodes = DetachedCriteria.For<StatusCode>()
                                                        .CreateAlias("FunctionalAreaCode", "FunctionalAreaCode")
                                                        .SetProjection(Projections.ProjectionList()
                                                             .Add(Projections.Property("Id"), "Id"))
                                                        .Add(Restrictions.In("Code", _customerOrderHeaderStatus.Split(',')))
                                                        .Add(Restrictions.Eq("FunctionalAreaCode.Code", "ORD"))
                                                        .SetResultTransformer(Transformers.AliasToBean<StatusCode>());

                IList<StatusCode> orderHeaderStatuses = Repositories.Get<StatusCode>().List(criteriastatusCodes);
                if (orderHeaderStatuses == null || orderHeaderStatuses.Count == 0)
                {
                    if (_logger.IsErrorEnabled) _logger.ErrorFormat("No Order statuses found.");
                    throw new Exception(string.Format("No Order statuses found."));
                }
                else
                {
                    headerStatuses = orderHeaderStatuses.Select(order => order.Id.Value).ToList();
                }

            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Error while fetching Order statuses : {0} ", ex.Message);
                throw ex;
            }
            return headerStatuses;
        }

        private IList<Customer> GetListofCustomers(string[] warehouses, string[] billToCustomerCodes)
        {
            IList<Customer> customers = null;
            try
            {
                DetachedCriteria criteria = DetachedCriteria.For<Customer>()
                                                            .CreateAlias("CustomerCommunications", "CustomerCommunications")
                                                            .CreateAlias("CustomerCommunications.CommunicationRole", "CommunicationRole")
                                                            .CreateAlias("CompanyLocationType", "CompanyLocationType")
                                                            .Add(Restrictions.Eq("CustomerCommunications.PrimaryCommunication", "Y"))
                                                            .Add(Restrictions.Eq("CustomerCommunications.Active", "A"))
                                                            .Add(Restrictions.Eq("CommunicationRole.Active", "A"))
                                                            .Add(Restrictions.Eq("CommunicationRole.CommunicationRoleCode", CodeValue.GetCode(CommunicationRoles.Email)))
                                                            .Add(Restrictions.In("CompanyLocationType.CompanyLocationCode", warehouses))
                                                            .Add(Restrictions.In("CustomerCode", billToCustomerCodes))
                                                            .SetProjection(Projections.ProjectionList()
                                                                .Add(Projections.Property("CustomerCode"), "CustomerCode")
                                                                .Add(Projections.Property("CompanyLocationType.Id"), "Id")
                                                                .Add(Projections.Property("CustomerCommunications.CommunicationValue"), "AlternateCustomerCode"))
                                                            .SetResultTransformer(Transformers.AliasToBean<Customer>());

                customers = Repositories.Get<Customer>().List(criteria);
                if (customers == null || customers.Count == 0)
                {
                    if (_logger.IsErrorEnabled) _logger.ErrorFormat("No customers found to send warehouse email alert");
                }
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("No customers found to send warehouse email alert" + ex.Message);
                throw ex;
            }
            return customers;
        }

        private Alert CreateAlert(AlertDefinition alertDefanition, IList<OrderHeader> orderHeaders, StatusCode openStatus, string toEmailAddresses)
        {
            Alert alertDetails = Entity.Activate<Alert>("alert_manager");
            try
            {
                alertDetails.StatusCode = openStatus;
                alertDetails.AlertDefinition = alertDefanition;
                if (string.IsNullOrEmpty(alertDetails.AlertDefinition.SubjectLine))
                    alertDetails.AlertDefinition.SubjectLine = _emailSubject;
                alertDetails.AlertEscalation = null;
                alertDetails.AlertContent = PrepareAlertContent(orderHeaders);

                if (!String.IsNullOrWhiteSpace(toEmailAddresses))
                {
                    if (toEmailAddresses.Length > 0)
                    {
                        if (toEmailAddresses.Substring(toEmailAddresses.Length - 1, 1).Equals(";")) toEmailAddresses = toEmailAddresses.Remove(toEmailAddresses.Length - 1, 1);

                        char[] delimit = { ';' };
                        string[] recepientList = toEmailAddresses.Split(delimit);

                        for (int i = 0; i < recepientList.Length; i++)
                        {
                            if (i == 0) alertDetails.ToList = recepientList[0];
                            else if (i == recepientList.Length - 1) alertDetails.CcList = alertDetails.CcList + recepientList[i];
                            else alertDetails.CcList = alertDetails.CcList + recepientList[i] + ";";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
            }

            return alertDetails;
        }

        private byte[] PrepareAlertContent(IList<OrderHeader> orderHeaders)
        {
            Byte[] byteData = null;
            try
            {
                StringBuilder bodyMessage = new StringBuilder();
                bodyMessage.Append("<html><body>");
                bodyMessage.Append("Customer Order Numbers : ");
                bodyMessage.Append("</br>");
                bodyMessage.Append(string.Join(",", orderHeaders.Select(order => order.OrderCode).ToList()));
                bodyMessage.Append("</body></html>");
                byteData = new System.Text.UTF8Encoding().GetBytes(bodyMessage.ToString());

            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
            }
            return byteData;
        }

        private Task CreateTask(TransactionType transactionType, StatusCode openStatus, Alert alert, CompanyLocationType companyLocationType)
        {
            Task entityDetails = null;
            try
            {
                entityDetails = new Task();
                entityDetails.Alert = alert;
                entityDetails.CompanyLocationType = companyLocationType;
                entityDetails.TransactionType = transactionType;
                entityDetails.StatusCode = openStatus;
                entityDetails.UserCreated = "alert_manager";
                entityDetails.DateCreated = DateTime.Now;
                entityDetails.TaskCode = Convert.ToString(GetCurrentValueForTask(EntityCodes.Task, companyLocationType));

            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
            }
            return entityDetails;
        }

        private int GetCurrentValueForTask(EntityCodes code, CompanyLocationType warehouse)
        {
            DetachedCriteria criteria = DetachedCriteria.For<EntityCode>()
                              .Add(new SimpleExpression("Code", CodeValue.GetCode(code), "="))
                              .SetMaxResults(1);

            if (warehouse != null) criteria = criteria.Add(new SimpleExpression("CompanyLocationType.Id", warehouse.Id, "="));

            IList<EntityCode> list = Repositories.Get<EntityCode>().List(criteria);

            if (list == null && list.Count == 0)
            {
                if (_logger.IsErrorEnabled)
                    _logger.ErrorFormat("Unable to find LookupPrimaryKey for '{0}'.", code);
                throw new Exception(String.Format("Unable to find LookupPrimaryKey for '{0}'.", code));
            }
            ++list[0].CurrentValue;
            return list[0].CurrentValue;
        }

        #endregion

        private static TEntity GetReferenceObject<TEntity>(Enum value, FunctionalAreas functionalArea) where TEntity : Entity
        {
            string code = CodeValue.GetCode(value);
            string area = CodeValue.GetCode(functionalArea);
            String property = typeof(TEntity).Name;
            if (property.EndsWith("Code")) property = "Code";
            /* Additional 'else if (...) property = ...;' go here. */
            else property = String.Format("{0}Code", property);
            //
            DetachedCriteria criteria = DetachedCriteria.For<TEntity>()
                .Add("Active", "A")
                .Add(property, code);
            if (!String.IsNullOrEmpty(area)) criteria = criteria.Add("FunctionalAreaCode.Code", area);
            //
            TEntity entity = Repositories.Get<TEntity>().Retrieve(criteria);
            if (entity == null)
            {
                if (String.IsNullOrEmpty(area)) throw new Exception(String.Format("Unable to find {0} for '{1}'.", property, code));
                else throw new Exception(String.Format("Unable to find {0} for '{1}' in {2}.", property, code, area));
            }
            //
            return entity;
        }

        private TEntity GetReferenceObject<TEntity>(string code) where TEntity : Entity
        {
            String property = typeof(TEntity).Name;
            if (property.EndsWith("Code")) property = "Code";
            /* Additional 'else if (...) property = ...;' go here. */
            else property = String.Format("{0}Code", property);
            //
            DetachedCriteria criteria = DetachedCriteria.For<TEntity>()
                .Add("Active", "A")
                .Add(property, code);
            //
            TEntity entity = Repositories.Get<TEntity>().Retrieve(criteria);
            if (entity == null)
            {
                throw new Exception(String.Format("Unable to find {0} for '{1}' .", property, code));
            }
            //
            return entity;
        }

        private TEntity GetReferenceObject<TEntity>(Enum value) where TEntity : Entity
        {
            TEntity entity = null;
            try
            {
                entity = Entity.Retrieve<TEntity>(value);

                if (entity == null)
                {
                    if (_logger.IsErrorEnabled) _logger.ErrorFormat("No {0} exists with Code : {1}", typeof(TEntity).Name, value);
                    throw new Exception(string.Format("No {0} exists with Code : {1}", typeof(TEntity).Name, value));
                }
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("No {0} exists with Code : {1}", typeof(TEntity).Name, value);
                throw ex;
            }
            return entity;
        }
    }
}
