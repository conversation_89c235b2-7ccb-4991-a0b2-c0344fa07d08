﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Reflection;
using System.Text.RegularExpressions;

using log4net;

using NHibernate;
using NHibernate.Criterion;
using NHibernate.Dialect.Function;
using NHibernate.SqlCommand;
using NHibernate.Transform;

using Quartz;

using Upp.Irms.Constants;
using Upp.Irms.Core;
using Upp.Irms.Domain;
using Upp.Shared.Application;
using Upp.Shared.Utilities;

namespace Upp.Irms.AlertGenerator.Host
{
    public class PasswordExpirationJob : IJob
    {
        ILog _logger = LogManager.GetLogger(typeof(PasswordExpirationJob));
        const string _constPasswordExpiration = "PE";
        string _jobName = string.Empty;
        const string JParam_Agencies = "AGENCIES";
        string agencies = "";       
        string jobname = "Password Expiration Job";
        const string messageText = @"<html><body>The following is an automated notification from IRMS 360.<br></body></html>";

        #region Properties

        public string JobName
        {
            get { return _jobName; }
            set { _jobName = value; }
        }

      
        #endregion

        #region Constructor

        public PasswordExpirationJob()
        {

        }

        #endregion

        #region Methods.Public
        public void Execute(JobExecutionContext context)
        {
            if (JobParametersAreValid(context.MergedJobDataMap) == false)
            {
                JobExecutionException jex = new JobExecutionException("Missing job parameter");
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }

            ParseJobParameters(context.MergedJobDataMap);

            GetAlerts(JobName);           
        }
        #endregion

        #region Methods.Private

        private bool JobParametersAreValid(JobDataMap jobParams)
        {
            bool validity = true;

            if (!jobParams.Contains(JParam_Agencies))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_Agencies);
                validity = false;
            }            
            return validity;
        }
        // helper method to parse job parameters
        private void ParseJobParameters(JobDataMap jobParams)
        {
            //Ensure that we catch all other types of exceptions
            // and throw only a JobExecutionException
            try
            {
                //map the parameters to jobParams               
                this.agencies = jobParams.GetString(JParam_Agencies);                
            }
            catch (Exception ex)
            {
                JobExecutionException jex = new JobExecutionException("Invalid data type in job parameters", ex);
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }
        }
        
        private void GetAlerts(string _jobName)
        {
            try
            {
                // Get Supporting objects 
                if (_logger.IsDebugEnabled)
                {
                    _logger.Debug("Step 2.  Get Supporting Objects :");
                    _logger.Debug("         »  TransactionType");
                    _logger.Debug("         »  StatusCode");
                    _logger.Debug("         »  AlertDefinition");
                    _logger.Debug("         »  AlertRecepient");
                }

                StatusCode statuscode = null;
                AlertDefinition alertDefinition = null;
                StatusCode openAlertStatuscode = null;
                TransactionType transType = null;                
               
                string advancedSecurity = string.Empty;
                string passwordExpiryDays = string.Empty;
                string passwordpriorNotify = string.Empty;
                string passwordNotify = string.Empty;

                using (UnitWrapper wrapper = new UnitWrapper())
                {
                    wrapper.Execute(() =>
                    {
                        alertDefinition = GetReferenceObject<AlertDefinition>(_constPasswordExpiration);
                        transType = GetReferenceObject<TransactionType>(AlertTransactions.AlertRequest, FunctionalAreas.Alert);
                        statuscode = GetReferenceObject<StatusCode>(TaskStatuses.Open);
                        openAlertStatuscode = GetReferenceObject<StatusCode>(AlertStatuses.Open, FunctionalAreas.Alert);
                        advancedSecurity = GetParameterValue("11000", "S");
                        passwordExpiryDays = GetParameterValue("11002", "S");
                        passwordNotify = GetParameterValue("11003", "S");
                        passwordpriorNotify = GetParameterValue("11007", "S");
                    });
                }

                if (transType == null)
                {
                    if (_logger.IsErrorEnabled) _logger.Error("         »  Supporting objects not available : TransactionType");
                    return;
                }
                if (statuscode == null)
                {
                    if (_logger.IsErrorEnabled) _logger.Error("         »  Supporting objects not available : Task StatusCode");
                    return;
                }
                if (openAlertStatuscode == null)
                {
                    if (_logger.IsErrorEnabled) _logger.Error("         »  Supporting objects not available : Alert StatusCode");
                    return;
                }
                if (alertDefinition == null)
                {
                    if (_logger.IsErrorEnabled) _logger.Error("         »  Supporting objects not available : AlertDefinition");
                    return;
                }

                int expiryDays = 0;
                int priorNotify = 0;

                if (!string.IsNullOrEmpty(advancedSecurity) && advancedSecurity.ToUpper().StartsWith("Y"))
                {
                    if (!string.IsNullOrEmpty(passwordExpiryDays))
                    {
                        expiryDays = Convert.ToInt32(passwordExpiryDays);
                    }

                    if (!string.IsNullOrEmpty(passwordpriorNotify))
                    {
                        priorNotify = Convert.ToInt32(passwordpriorNotify);
                    }
                }
                else return;

                if (expiryDays <= 0) return;


                if (!string.IsNullOrWhiteSpace(agencies))
                {
                    //Step 1.  Read Agencies from config 
                    if (_logger.IsDebugEnabled) _logger.Debug("Step 1.  Read Agencies from config ");

                    if (String.IsNullOrWhiteSpace(agencies))
                    {
                        if (_logger.IsWarnEnabled) _logger.Warn("         »  No Agencies to Process");
                        return;
                    }
                    string[] agencyCodes = agencies.Split(new char[] { ',' });
                    if (_logger.IsDebugEnabled) _logger.DebugFormat("         »  Agencies Count : {0}", agencyCodes.Length.ToString());

                    foreach (string agencyCode in agencyCodes)
                    {
                        Agency agency = null;
                        using (UnitWrapper wrapper = new UnitWrapper())
                        {
                            wrapper.Execute(() =>
                            {
                                agency = GetReferenceObject<Agency>(agencyCode);
                            });
                        }

                        if (agency == null)
                            return;



                        if (!string.IsNullOrEmpty(passwordNotify) && passwordNotify.ToUpper().StartsWith("Y"))
                        {
                            List<int> priorNotifyUserIds = new List<int>();
                            IList<UserAccount> partcipantEmailList = new List<UserAccount>();

                            using (UnitWrapper wrapper = new UnitWrapper())
                            {
                                wrapper.Execute(() =>
                                {
                                    priorNotifyUserIds = GetPriorNotifyAccounts(expiryDays, priorNotify, agency);
                                });
                            }

                            if (priorNotifyUserIds != null && priorNotifyUserIds.Count > 0)
                            {
                                using (UnitWrapper wrapper = new UnitWrapper())
                                {
                                    wrapper.Execute(() =>
                                    {
                                        partcipantEmailList = GetPartcipantEmail(priorNotifyUserIds);
                                    });
                                }
                                if (partcipantEmailList != null && partcipantEmailList.Count > 0)
                                    ProcessAlerts(partcipantEmailList, transType, statuscode, openAlertStatuscode, agency, alertDefinition, expiryDays, priorNotify);
                            }
                        }

                        List<int> expiryUserIds = new List<int>();

                        using (UnitWrapper wrapper = new UnitWrapper())
                        {
                            wrapper.Execute(() =>
                            {
                                expiryUserIds = GetExpiredAccounts(expiryDays, agency);
                            });
                        }

                        foreach (int userAccountId in expiryUserIds)
                        {
                            using (UnitWrapper wrapper = new UnitWrapper())
                            {
                                wrapper.Execute(() =>
                                {
                                    try
                                    {
                                        UserAccount userAccount = Repositories.Get<UserAccount>().Retrieve(userAccountId);
                                        if (userAccount != null)
                                        {
                                            userAccount.DateModified = DateTime.Now;
                                            userAccount.UserModified = "alert_manager";
                                            userAccount.Expired = DateTime.Now;

                                            Repositories.Get<UserAccount>().Update(userAccount);
                                            if (_logger.IsDebugEnabled) _logger.DebugFormat("         » User Account is Expired : {0}", Convert.ToString(userAccount.UserName));
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        if (_logger.IsErrorEnabled) _logger.ErrorFormat("Transaction failed for the User Account : {0} , Error occured : {1}.", userAccountId, Errors.GetError(ex));
                                    }
                                });
                            }
                        }
                    }                   
                }
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
            }
        }

        private void ProcessAlerts(IList<UserAccount> userEmailList, TransactionType transType, StatusCode statuscode, StatusCode openAlertStatuscode, Agency agency, AlertDefinition alertDefinition, int expiredDays, int priorNotifyDays)
        {
            try
            {
                if (alertDefinition == null)
                    return;

                List<int> userIdList = new List<int>();

                if (userEmailList == null || userEmailList.Count == 0)
                    return;

                using (UnitWrapper wrapper = new UnitWrapper())
                {
                    wrapper.Execute(() =>
                    {
                        foreach (UserAccount user in userEmailList)
                        {
                            if (!string.IsNullOrWhiteSpace(user.Association))
                            {
                                DateTime fDate = Convert.ToDateTime(DateTime.Now);
                                DateTime toDate = Convert.ToDateTime(DateTime.Now);

                                if (user.ChangePassword != null)
                                {
                                    toDate = Convert.ToDateTime(user.ChangePassword);
                                }
                                else
                                {
                                    toDate = Convert.ToDateTime(user.DateCreated);
                                }

                                int days = fDate.Subtract(toDate).Days;
                                if (days == 0)
                                {
                                    user.UserModified = "1";
                                }
                                else
                                {
                                    user.UserModified = Converter.ToString(days);
                                }

                                Alert alert = CreateAlert(alertDefinition, user.Association, user, openAlertStatuscode);
                                Repositories.Get<Alert>().Add(alert);

                                Task task = CreateTask(transType, statuscode, alert, agency);
                                Repositories.Get<Task>().Add(task);

                                if (_logger.IsDebugEnabled) _logger.DebugFormat("Created Task/Alert for User Email :{0}", user.Association);
                            }
                        }

                    });
                }
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
            }
        }
        private String GetAlertDestinations(AlertDefinition alertDefinition)
        {
            String alertDestinations = String.Empty;
            try
            {
                DetachedCriteria criteria = DetachedCriteria.For<AlertRecipient>()
                                                       .Add("AlertDefinition", alertDefinition)
                                                       .SetProjection(Projections.Property("AlertDestination"));

                IList<String> recipients = Repositories.Get<String>().List(criteria);
                if (recipients == null)
                {
                    if (_logger.IsErrorEnabled) _logger.ErrorFormat("No Recipients exists for AlertDefinition Code : {0} ", alertDefinition.AlertDefinitionCode);
                    throw new Exception(string.Format("No Recipients exists for AlertDefinition Code : {0} ", alertDefinition.AlertDefinitionCode));
                }
                else
                {
                    foreach (String recipient in recipients)
                        alertDestinations = alertDestinations + recipient + ";";
                }

            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("No Recipients exists for AlertDefinition Code : {0} ", alertDefinition.AlertDefinitionCode);
                throw ex;
            }
            return alertDestinations;
        }
        private TEntity GetReferenceObject<TEntity>(Enum value) where TEntity : Entity
        {
            TEntity entity = null;
            try
            {
                entity = Entity.Retrieve<TEntity>(value);

                if (entity == null)
                {
                    if (_logger.IsErrorEnabled) _logger.ErrorFormat("No {0} exists with Code : {1}", typeof(TEntity).Name, value);
                    throw new Exception(string.Format("No {0} exists with Code : {1}", typeof(TEntity).Name, value));
                }
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("No {0} exists with Code : {1}", typeof(TEntity).Name, value);
                throw ex;
            }
            return entity;
        }
        private static TEntity GetReferenceObject<TEntity>(Enum value, FunctionalAreas functionalArea)
           where TEntity : Entity
        {
            string code = CodeValue.GetCode(value);
            string area = CodeValue.GetCode(functionalArea);
            String property = typeof(TEntity).Name;
            if (property.EndsWith("Code")) property = "Code";
            /* Additional 'else if (...) property = ...;' go here. */
            else property = String.Format("{0}Code", property);
            //
            DetachedCriteria criteria = DetachedCriteria.For<TEntity>()
                .Add("Active", "A")
                .Add(property, code);
            if (!String.IsNullOrEmpty(area)) criteria = criteria.Add("FunctionalAreaCode.Code", area);
            //
            TEntity entity = Repositories.Get<TEntity>().Retrieve(criteria);
            if (entity == null)
            {
                if (String.IsNullOrEmpty(area)) throw new Exception(String.Format("Unable to find {0} for '{1}'.", property, code));
                else throw new Exception(String.Format("Unable to find {0} for '{1}' in {2}.", property, code, area));
            }
            //
            return entity;
        }
        private TEntity GetReferenceObject<TEntity>(string code)
         where TEntity : Entity
        {
            String property = typeof(TEntity).Name;
            if (property.EndsWith("Code")) property = "Code";
            /* Additional 'else if (...) property = ...;' go here. */
            else property = String.Format("{0}Code", property);
            //
            DetachedCriteria criteria = DetachedCriteria.For<TEntity>()
                .Add("Active", "A")
                .Add(property, code);
            //
            TEntity entity = Repositories.Get<TEntity>().Retrieve(criteria);
            if (entity == null)
            {
                throw new Exception(String.Format("Unable to find {0} for '{1}' .", property, code));
            }
            //
            return entity;
        }
        private Alert CreateAlert(AlertDefinition alertDef, string toList, UserAccount userAccount, StatusCode statuscode)
        {

            Alert entity = Entity.Activate<Alert>("alert_manager");
            try
            {
                entity.AlertDefinition = alertDef;
                entity.StatusCode = statuscode;
                entity.AlertContent = ProcessAlertTemplate(alertDef.AlertTemplate, userAccount);
                entity.FileName = alertDef.FileName;
                entity.AlertEscalation = null;
                if (!String.IsNullOrWhiteSpace(toList))
                {
                    if (toList.Length > 0)
                    {
                        if (toList.Substring(toList.Length - 1, 1).Equals(";"))
                        {
                            toList = toList.Remove(toList.Length - 1, 1);
                        }

                        char[] delimit = { ';' };
                        string[] recepientList = toList.Split(delimit);
                        for (int i = 0; i < recepientList.Length; i++)
                        {
                            if (i == 0) entity.ToList = recepientList[0];
                            else if (i == recepientList.Length - 1) entity.CcList = entity.CcList + recepientList[i];
                            else entity.CcList = entity.CcList + recepientList[i] + ";";
                        }
                    }
                }

            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
            }
            return entity;
        }

        private Task CreateTask(TransactionType type, StatusCode statuscode, Alert alert, Agency agency)
        {
            Task entity = new Task();

            try
            {
                entity.UserCreated = "alert_manager";
                entity.DateCreated = DateTime.Now;
                entity.DateModified = DateTime.Now;
                entity.Agency = agency;
                entity.StatusCode = statuscode;
                entity.TransactionType = type;
                entity.TaskCode = Convert.ToString(GetCurrentValueForTask(EntityCodes.Task, agency));
                entity.Alert = alert;
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
            }
            return entity;
        }

        private int GetCurrentValueForTask(EntityCodes code, Agency agency)
        {
            DetachedCriteria criteria = DetachedCriteria.For<EntityCode>()
                                                        .Add(new SimpleExpression("Code", CodeValue.GetCode(code), "="));
            if (agency != null) criteria = criteria.Add(new SimpleExpression("Agency.Id", agency.Id, "="));
            criteria = criteria.Add(new SimpleExpression("Code", CodeValue.GetCode(code), "="))
                              .SetMaxResults(1);

            IList<EntityCode> list = Repositories.Get<EntityCode>().List(criteria);

            if (list == null && list.Count == 0) throw new Exception(String.Format("Unable to find LookupPrimaryKey for '{0}'.", code));

            ++list[0].CurrentValue;

            return list[0].CurrentValue;
        }

        private Byte[] ProcessAlertTemplate(Byte[] alertTemplate, Entity entity)
        {

            if (alertTemplate == null)
            {
                return null;
            }
            try
            {
                string data = System.Text.Encoding.UTF8.GetString(alertTemplate);
                ReplaceFields(ref data, entity);
                Byte[] byteData = new System.Text.UTF8Encoding().GetBytes(data);
                return byteData;
            }
            catch
            {
                return null;
            }
        }

        private Byte[] ProcessAlertTemplate(Byte[] alertTemplate, IList<UserAccount> accounts)
        {
            if (alertTemplate == null)
            {
                return null;
            }
            try
            {
                string dataContent = messageText;
                string data = System.Text.Encoding.UTF8.GetString(alertTemplate);
                foreach (UserAccount user in accounts)
                    dataContent = dataContent + ReplaceData(data, user);
                Byte[] byteData = new System.Text.UTF8Encoding().GetBytes(dataContent);
                return byteData;
            }
            catch
            {
                return null;
            }
        }

        private string ReplaceData(string data, UserAccount source)
        {
            string dataContent = data;

            List<string> fields = FindMatches(dataContent);

            foreach (string field in fields)
            {
                dataContent = dataContent.Replace(field, GetValue(field, source));
            }

            return dataContent;
        }

        private void ReplaceFields(ref string data, Entity source)
        {
            List<string> fields = FindMatches(data);

            foreach (string field in fields)
            {
                data = data.Replace(field, GetValue(field, source));
            }
        }

        private List<string> FindMatches(string data)
        {
            List<string> fields = new List<string>();
            Regex reg = new Regex(@"<<[^>]*>>");

            MatchCollection match = reg.Matches(data);

            for (int i = 0; i < match.Count; i++)
            {
                fields.Add(match[i].Value);
            }
            return fields;
        }

        private string GetValue(string property, Entity entity)
        {
            string result = "___________________";
            try
            {
                property = property.Remove(0, 2);
                property = property.Remove(property.Length - 2, 2);
                result = Convert.ToString(GetPropValue(property, entity));
                return result;
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
                return result;
            }
        }

        public Object GetPropValue(String name, Object obj)
        {
            foreach (String part in name.Split('.'))
            {
                if (obj == null)
                { return null; }
                Type type = obj.GetType();
                PropertyInfo info = type.GetProperty(part);
                if (info == null)
                { return null; }
                obj = info.GetValue(obj, null);
            }
            return obj;
        }

        private string GetParameterValue(string businessRuleCode, string functionalAreaCode)
        {
            string parameterValue = null;

            DetachedCriteria query = DetachedCriteria.For<BusinessRuleParameterValue>()


                .CreateAlias("BusinessRuleParameter", "BusinessRuleParameter")
                .CreateAlias("BusinessRuleParameter.BusinessRule", "BusinessRule")
                .CreateAlias("Parameter", "Parameter")
                .CreateAlias("Parameter.FunctionalAreaCode", "FunctionalAreaCode")
                .SetProjection(Projections.ProjectionList()
                .Add(Projections.Property("ParameterValue"), "ParameterValue"))
                .Add(new SimpleExpression("BusinessRule.BusinessRuleCode", businessRuleCode, "="))
                .Add(new SimpleExpression("BusinessRule.Active", "A", "="))
                .Add(new SimpleExpression("Active", "A", "="))
                .Add(new SimpleExpression("Selected", "Y", "="))
                .SetResultTransformer(Transformers.AliasToBean<BusinessRuleParameterValue>());
            if (!string.IsNullOrEmpty(functionalAreaCode)) query = query.Add(new SimpleExpression("FunctionalAreaCode.Code", functionalAreaCode, "="));
            //
            IList<BusinessRuleParameterValue> businessRuleParameterValues = Repositories.Get<BusinessRuleParameterValue>().List(query);
            if (businessRuleParameterValues.Count > 0)
            {
                parameterValue = businessRuleParameterValues[0].ParameterValue;
            }
            return parameterValue;
        }      

        private IList<UserAccount> GetPartcipantEmail(List<int> userIds)
        {
            IList<UserAccount> userslist = new List<UserAccount>();

            DetachedCriteria detached = DetachedCriteria.For<ParticipantCommunication>("PC")
               .CreateAlias("PC.CommunicationRole", "CommunicationRole")
               .Add(Expression.EqProperty("Participants.Id", "PC.Participant.Id"))
               .Add(Restrictions.Eq("PC.PrimaryCommunication", "Y"))
                //.Add(Restrictions.Eq("CommunicationRole.CommunicationRoleCode", Utilities.Utility.GetStringValue(CommunicationRoleCodes.Email)))
               .Add(Restrictions.Eq("CommunicationRole.CommunicationRoleCode", "E"))
               .SetProjection(Projections.ProjectionList().Add(Projections.Property("CommunicationValue"), "CommunicationValue"))
               .SetMaxResults(1);

            DetachedCriteria participantEmail = DetachedCriteria.For<UserAccount>()
                                                     .CreateAlias("OrganizationParticipant", "OrganizationParticipant")
                                                     .CreateAlias("OrganizationParticipant.ParticipantRole", "ParticipantRole")
                                                     .CreateAlias("ParticipantRole.Participant", "Participants")
                                                     .SetProjection(Projections.ProjectionList()
                                                     .Add(Projections.Property("Id"), "Id")
                                                     .Add(Projections.Property("UserName"), "UserName")
                                                     .Add(Projections.Property("ChangePassword"), "ChangePassword")
                                                     .Add(Projections.Property("DateCreated"), "DateCreated")                                                     
                                                     .Add(Projections.SubQuery(detached), "Association"))
                                                     .Add(Restrictions.In("Id", userIds))
                                                     .SetResultTransformer(Transformers.AliasToBean<UserAccount>());
           
            userslist = Repositories.Get<UserAccount>().List(participantEmail);

            userslist = userslist.Where(c => String.IsNullOrWhiteSpace(c.Association) == false).ToList<UserAccount>();
            return userslist;
        }
        
        private List<int> GetExpiredAccounts(int expiryDays, Agency agency)
        {
            List<int> expiredUserIds = new List<int>();

            List<UserAccount> userslist = new List<UserAccount>();

            if (expiryDays > 0)
            {
                DateTime expiryDate = DateTime.Now.AddDays(expiryDays * -1);

                DetachedCriteria userAccounts_ChangePassword = DetachedCriteria.For<UserAccount>()
                                                    .CreateAlias("OrganizationParticipant", "OrganizationParticipant")                            
                                                    .SetProjection(Projections.ProjectionList()
                                                    .Add(Projections.Property("Id"), "Id"))                                                                                      
                                                    .Add(Restrictions.Le("ChangePassword",expiryDate))
                                                    .Add(Restrictions.IsNull("Expired"))
                                                    .Add(Restrictions.IsNull("Locked"))
                                                    .Add(Restrictions.IsNotNull("ChangePassword"))
                                                    .Add(Restrictions.Eq("OrganizationParticipant.Agency.Id",agency.Id))
                                                    .SetResultTransformer(Transformers.AliasToBean<UserAccount>());
               
               IList<UserAccount> userslist_ChangePassword = Repositories.Get<UserAccount>().List(userAccounts_ChangePassword);

               if (userslist_ChangePassword != null && userslist_ChangePassword.Count > 0)
                   userslist.AddRange(userslist_ChangePassword);

               DetachedCriteria userAccounts_DateCreated = DetachedCriteria.For<UserAccount>()
                                                   .CreateAlias("OrganizationParticipant", "OrganizationParticipant")   
                                                   .SetProjection(Projections.ProjectionList()
                                                   .Add(Projections.Property("Id"), "Id"))
                                                   .Add(Restrictions.Le("DateCreated", expiryDate))
                                                   .Add(Restrictions.IsNull("Expired"))
                                                   .Add(Restrictions.IsNull("Locked"))
                                                   .Add(Restrictions.IsNull("ChangePassword"))
                                                   .Add(Restrictions.Eq("OrganizationParticipant.Agency.Id", agency.Id))
                                                   .SetResultTransformer(Transformers.AliasToBean<UserAccount>());
               
               IList <UserAccount> userslist_DateCreated = Repositories.Get<UserAccount>().List(userAccounts_DateCreated);

               if (userslist_DateCreated != null && userslist_DateCreated.Count > 0)
                   userslist.AddRange(userslist_DateCreated);

               expiredUserIds = userslist.Select(c => c.Id.Value).Distinct().ToList<int>();
            }

            return expiredUserIds;
        }

        private List<int> GetPriorNotifyAccounts(int expiryDays,int priorNotifyDays, Agency agency)
        {
            List<int> expiredUserIds = new List<int>();

            List<UserAccount> userslist = new List<UserAccount>();

            if (expiryDays > 0 && priorNotifyDays > 0 && priorNotifyDays < expiryDays)
            {
                DateTime expiryDate = DateTime.Now.AddDays(expiryDays * -1);
                DateTime priorNotifyDate = expiryDate.AddDays(priorNotifyDays);

                DetachedCriteria userAccounts_ChangePassword = DetachedCriteria.For<UserAccount>()
                                                    .CreateAlias("OrganizationParticipant", "OrganizationParticipant")  
                                                    .SetProjection(Projections.ProjectionList()
                                                    .Add(Projections.Property("Id"), "Id"))
                                                    .Add(Restrictions.Between("ChangePassword", expiryDate, priorNotifyDate))
                                                    .Add(Restrictions.IsNull("Expired"))
                                                    .Add(Restrictions.IsNull("Locked"))
                                                    .Add(Restrictions.IsNotNull("ChangePassword"))
                                                    .Add(Restrictions.Eq("OrganizationParticipant.Agency.Id", agency.Id))
                                                    .SetResultTransformer(Transformers.AliasToBean<UserAccount>());

                IList<UserAccount> userslist_ChangePassword = Repositories.Get<UserAccount>().List(userAccounts_ChangePassword);

                if (userslist_ChangePassword != null && userslist_ChangePassword.Count > 0)
                    userslist.AddRange(userslist_ChangePassword);

                DetachedCriteria userAccounts_DateCreated = DetachedCriteria.For<UserAccount>()
                                                    .SetProjection(Projections.ProjectionList()
                                                    .Add(Projections.Property("Id"), "Id"))                                                   
                                                    .Add(Restrictions.Between("DateCreated", expiryDate, priorNotifyDate))
                                                    .Add(Restrictions.IsNull("Expired"))
                                                    .Add(Restrictions.IsNull("Locked"))
                                                    .Add(Restrictions.IsNull("ChangePassword"))
                                                    .SetResultTransformer(Transformers.AliasToBean<UserAccount>());

                IList<UserAccount> userslist_DateCreated = Repositories.Get<UserAccount>().List(userAccounts_DateCreated);

                if (userslist_DateCreated != null && userslist_DateCreated.Count > 0)
                    userslist.AddRange(userslist_DateCreated);

                expiredUserIds = userslist.Select(c => c.Id.Value).Distinct().ToList<int>();
            }

            return expiredUserIds;
        }

        #endregion
    }
}
