﻿using System;
using System.Configuration;
using System.Diagnostics;
using System.IO;
using System.ServiceProcess;
using log4net;
using Upp.Irms.Core;

//#if NHPROF
//using HibernatingRhinos.Profiler.Appender.NHibernate;
//#endif

//[assembly: log4net.Config.XmlConfigurator(Watch = true)]

namespace Upp.Irms.AlertGenerator.Host
{
    static class Program
    {
        #region Fields

        private static String _folder = String.Empty;
        private static String _location = String.Empty;
        private static String _name = String.Empty;
        private static String _utility = String.Empty;

        #endregion

        #region Properties

        public static String Folder
        {
            get
            {
                if (String.IsNullOrEmpty(_folder)) _folder = Path.GetDirectoryName(Program.Location);
                return _folder;
            }
        }

        public static String Location
        {
            get
            {
                if (String.IsNullOrEmpty(_location)) _location = typeof(ServiceEngine).Assembly.Location;
                return _location;
            }
        }

        public static String Name
        {
            get
            {
                if (String.IsNullOrEmpty(_name)) _name = typeof(ServiceEngine).Assembly.GetName().Name;
                return _name;
            }
        }

        public static String Utility
        {
            get
            {
                if (String.IsNullOrEmpty(_utility))
                {
                    String os = Environment.Is64BitOperatingSystem ? "64" : String.Empty;
                    _utility = String.Format(@"{0}\Microsoft.NET\Framework{1}\v4.0.30319\InstallUtil.exe",
                        Environment.GetEnvironmentVariable("Windir"), os);
                }
                //
                return _utility;
            }
        }

        #endregion

        #region Methods
       
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        static void Main(string[] args)
        {

//#if NHPROF
//            NHibernateProfiler.Initialize();
//#endif

            log4net.Config.XmlConfigurator.Configure();

            ILog _logger = LogManager.GetLogger(typeof(Program));
           
            SessionProvider.Initialize();
                 
#if DEBUG
			args = new String[] { "d" };
#endif
            if (args.Length == 0)
            {
                ServiceBase[] services = new ServiceBase[] { new HostService() };
                ServiceBase.Run(services);
            }
            else if (args.Length == 1)
            {
                String argument = args[0];
                if (argument.Length > 1) argument = argument.Substring(1);
                //
                if (argument == "d")
                {
                    ServiceEngine engine = new ServiceEngine();
                    engine.Start();
                }
                else if (argument.StartsWith("i"))
                {
                    if (!argument.Contains("=")) Process.Start(Program.Utility, Program.Location);
                    else
                    {
                        String instance = argument.Split('=')[1];
                        Process.Start(Program.Utility, String.Format(" /instance={0} {1}", instance, Program.Location));
                    }
                }
                else if (argument.StartsWith("u"))
                {
                    if (!argument.Contains("=")) Process.Start(Program.Utility, String.Format(" /u {0}", Program.Location));
                    else
                    {
                        String instance = argument.Split('=')[1];
                        Process.Start(Program.Utility, String.Format(" /instance={0} /u {1}", instance, Program.Location));
                    }
                }
                else Console.WriteLine("Unrecorgnized command line option.");
            }
            else Console.WriteLine("Invalid number of command line options.");
        }
        
        #endregion
    }
}
