﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.Net;
using System.Net.Mail;
using System.Timers;

using NHibernate.Criterion;
using NHibernate.SqlCommand;
using Quartz;
using Quartz.Impl;
using Quartz.Listener;

using Upp.Irms.Core;
using Upp.Irms.Domain;
using Upp.Shared.Utilities;
using log4net;

namespace Upp.Irms.AlertGenerator.Host
{
    public sealed class Service
    {
        #region Fields

        private String _sessionId = String.Empty;
        private ISchedulerFactory _schedulerFactory = null;
        private IScheduler _scheduler = null;
        private static ILog _logger = LogManager.GetLogger(typeof(AlertJob));

        #endregion

        #region Fields.Static

        private static readonly Lazy<Service> _lazy = new Lazy<Service>(() => new Service());

        #endregion

        #region Properties

        public String SessionId
        {
            get { return _sessionId; }
        }

        #endregion

        #region Properties.Static

        public static Service Instance
        {
            get { return _lazy.Value; }
        }

        public static void EncryptConfigSection(string sectionName)
        {
            try
            {
                // Encrypt the Section in the app.config using DataProtectionConfigurationProvider
                Configuration Config = null;

#if DEBUG
            Config = ConfigurationManager.OpenExeConfiguration("Upp.Irms.AlertGenerator.Host.exe");
#else
                Config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
#endif

                ConfigurationSection Section = Config.GetSection(sectionName);

                //if (!(Section.SectionInformation.IsProtected)
                //&& !Section.SectionInformation.IsLocked
                //&& !Section.IsReadOnly())
                //{
                    if (Section != null)
                    {
                        Section.SectionInformation.ProtectSection("DataProtectionConfigurationProvider");
                        Section.SectionInformation.ForceSave = true;
                        Config.Save(ConfigurationSaveMode.Full);
                    }
                //}
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
            }
        }

        public static void DecryptConfigSection(string sectionName)
        {
            try
            {
                // Decrypt the Section in the app.config 
                Configuration Config = null;

#if DEBUG
                Config = ConfigurationManager.OpenExeConfiguration("Upp.Irms.AlertGenerator.Host.exe");
#else
                Config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
#endif

                ConfigurationSection Section = Config.GetSection(sectionName);

                //if (Section.SectionInformation.IsProtected)
                //{
                    Section.SectionInformation.UnprotectSection();
                    Section.SectionInformation.ForceSave = true;
                    Config.Save(ConfigurationSaveMode.Full);
                //}
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
            }
        }

        #endregion

        #region Constructor

        private Service()
        {
            try
            { 
                
                _schedulerFactory = new StdSchedulerFactory();
                _scheduler = _schedulerFactory.GetScheduler();
                if (_logger.IsDebugEnabled) _logger.Debug("  » Created JobScheduler");
                
                if (_logger.IsDebugEnabled) _logger.Debug("  » Added Jobs to JobScheduler");
                
                _scheduler.AddGlobalJobListener(new JobListenerSupport());
                if (_logger.IsDebugEnabled) _logger.Debug("  » Added JobListener to JobScheduler");
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
            }
        }

        #endregion

        #region Methods.Public

        public void Start()
        {
            _scheduler.Start();
            if (_logger.IsDebugEnabled) _logger.Debug("  » Started JobScheduler");
        }

        public void Stop()
        {
            _scheduler.Shutdown();
            if (_logger.IsDebugEnabled) _logger.Debug("  » Stopped JobScheduler");
        }

        #endregion
    }
}
