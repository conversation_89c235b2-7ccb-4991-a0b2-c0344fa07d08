﻿using System;
using System.Collections.Generic;
using System.Collections;
using System.Configuration;
using System.Linq;
using System.Text;
using System.Threading;

using log4net;
using NHibernate;
using NHibernate.Transform;
using NHibernate.Criterion;

using Upp.Irms.Constants;
using Upp.Irms.Core;
using Upp.Irms.Domain;
using Upp.Irms.Domain.Utilities;
using Upp.Shared.Utilities;

namespace Upp.Irms.AlertGenerator.Host
{
	public class ServiceEngine
	{        
		#region Fields

        ILog _logger = LogManager.GetLogger(typeof(ServiceEngine));
		private Boolean _running = false;

		#endregion

		#region Methods

		public void Start()
		{
            if (_logger.IsInfoEnabled) _logger.InfoFormat("Starting {0}...", Program.Name);
			//
            try
            {
                Service.DecryptConfigSection("hibernate-configuration");

                String sessionId = String.Empty;
                using (IUnitOfWork unit = UnitOfWork.Start())
                {
                    try
                    {
                        if (_logger.IsDebugEnabled) _logger.Debug("Authentication Started...");
                        Service.DecryptConfigSection("appSettings");
                        Byte[] password = Encryption.Encrypt(ConfigurationManager.AppSettings["Password"].ToString(), "s33d");
                        String user = ConfigurationManager.AppSettings["User"].ToString();
                        Service.EncryptConfigSection("appSettings");
                        //
                        Authentication.Login(user, password);
                        sessionId = Authentication.CreateSessionId(user);
                        //
                        unit.Commit();
                        if (_logger.IsDebugEnabled) _logger.Debug("Authentication Completed...");
                    }
                    catch (AuthenticationException aex)
                    {
                        unit.Rollback();
                        if (_logger.IsErrorEnabled) _logger.ErrorFormat("Authentication failure: {0}.", Errors.GetError(aex));
                        throw new Exception(String.Format("Authentication failure: {0}.", Errors.GetError(aex)));
                    }
                    catch (Exception ex)
                    {
                        unit.Rollback();
                        if (_logger.IsErrorEnabled) _logger.ErrorFormat("Authentication failure: {0}.", Errors.GetError(ex));
                        throw ex;
                    }
                }
                #if DEBUG

           //     AlertJob aj = new AlertJob("OverdueAssetsJob", "APD");               

                #endif

                //
                Service.Instance.Start();
                _running = true;
#if DEBUG
                while (true) Thread.Sleep(100);
#endif
                if (_logger.IsInfoEnabled) _logger.InfoFormat("started {0} .", Program.Name);

            }
            catch (Exception ex)
            {
                if (_running) Service.Instance.Stop();
                if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
            }
            finally
            {
                Service.EncryptConfigSection("hibernate-configuration");                
            }
		}
        public void Stop()
        {
            if (_running) Service.Instance.Stop();
            {
                if (_logger.IsInfoEnabled) _logger.InfoFormat("Stopping {0}...", Program.Name);
            }
        }   


		#endregion
	}
}
