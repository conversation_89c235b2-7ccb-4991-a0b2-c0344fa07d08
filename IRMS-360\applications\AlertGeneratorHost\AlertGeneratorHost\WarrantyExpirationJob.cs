
using Quartz;
using System;
using Upp.Irms.Domain;
using System.Collections.Generic;
using Upp.Irms.Core;
using NHibernate.Criterion;
using NHibernate.SqlCommand;
using Upp.Shared.Application;
using Upp.Shared.Utilities;
using Upp.Irms.Constants;
using NHibernate.Transform;
using System.Configuration;
using System.Collections;
using log4net;

namespace Upp.Irms.AlertGenerator.Host
{
    /// <summary>
    /// This is just a simple job that checks OverdueAssets.
    /// </summary>

    public class WarrantyExpirationJob : IJob
    {
        #region Fields

        ILog _logger = LogManager.GetLogger(typeof(WarrantyExpirationJob));
        const string _jParamAgencies = "AGENCIES";
        string _agencies = ""; 

        #endregion

        #region Constructor

        /// <summary> 
        /// Empty constructor for job initilization
        /// <p>
        /// Quartz requires a public empty constructor so that the
        /// scheduler can instantiate the class whenever it needs.
        /// </p>
        /// </summary>
        public WarrantyExpirationJob()
        {
        }

        #endregion

        #region Methods.Public

        /// <summary> 
        /// Called by the <see cref="IScheduler" /> when a
        /// <see cref="Trigger" /> fires that is associated with
        /// the <see cref="IJob" />.
        /// </summary>
        public virtual void Execute(JobExecutionContext context)
        {
            if (JobParametersAreValid(context.MergedJobDataMap) == false)
            {
                JobExecutionException jex = new JobExecutionException("Missing job parameter");
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }

            ParseJobParameters(context.MergedJobDataMap);

            DoWork(context.JobDetail.Name ,this._agencies);
        }


        #endregion

        #region Methods.Private

        // Private, helper method to validate job parameters
        // Since the parameters are passed in a hashedmap
        // as strings, there is no compile-time verification.
        // There is a high likelihood, therefore, of mistakes
        // here, specially, typing mistakes in the key name.
        private bool JobParametersAreValid(JobDataMap jobParams)
        {
            bool validity = true;

            if (!jobParams.Contains(_jParamAgencies))
            {
                if (_logger.IsWarnEnabled) _logger.WarnFormat("Missing Job parameter '{0}'", _jParamAgencies);                
                validity = false;
            }

            return validity;
        }
        // helper method to parse job parameters
        private void ParseJobParameters(JobDataMap jobParams)
        {
            //Ensure that we catch all other types of exceptions
            // and throw only a JobExecutionException
            try
            {
                //map the parameters to jobParams
                this._agencies = jobParams.GetString(_jParamAgencies);
            }
            catch (Exception ex)
            {
                JobExecutionException jex = new JobExecutionException("Invalid data type in job parameters", ex);
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }
        }
        
        private void DoWork(string jobname,string agencycodes)
        {
            try
            {
                AlertJob aj = new AlertJob(jobname, agencycodes);

            }
            catch (Exception ex)
            {
             if(_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
            }
        }

       

        #endregion
    }
}