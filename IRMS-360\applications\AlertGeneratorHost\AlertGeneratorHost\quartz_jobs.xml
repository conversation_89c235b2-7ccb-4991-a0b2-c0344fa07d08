<?xml version="1.0" encoding="UTF-8"?>
<quartz xmlns="http://quartznet.sourceforge.net/JobSchedulingData" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
 				version="1.0"	overwrite-existing-jobs="true">

  <job>
    <job-detail>
      <name>NonHomeLocationAssetsJob</name>
      <group>Manual</group>
      <description>Job to check for Assets not in their home location</description>
      <job-type>Upp.Irms.AlertGenerator.Host.NonHomeLocationAssetsJob,Upp.Irms.AlertGenerator.Host</job-type>
      <volatile>false</volatile>
      <durable>true</durable>
      <recover>false</recover>
      <job-data-map>
        <entry>
          <key>AGENCIES</key>
          <value>AG001,DHS</value>
        </entry>
      </job-data-map>
    </job-detail>
    <trigger>
      <cron>
        <name>NonHomeLocationAssetsJobTrigger</name>
        <group>Manual</group>
        <description>To run NonHomeLocationAssets Job on manual trigger</description>
        <misfire-instruction>SmartPolicy</misfire-instruction>
        <volatile>false</volatile>
        <job-name>NonHomeLocationAssetsJob</job-name>
        <job-group>Manual</job-group>
        <cron-expression>0 0 23 * * ? *</cron-expression>
      </cron>
    </trigger>
  </job>

  <job>
    <job-detail>
      <name>OverdueAssetsJob</name>
      <group>Manual</group>
      <description>Job to check for overdue assets</description>
      <job-type>Upp.Irms.AlertGenerator.Host.OverdueAssetsJob,Upp.Irms.AlertGenerator.Host</job-type>
      <volatile>false</volatile>
      <durable>true</durable>
      <recover>false</recover>
      <job-data-map>
        <entry>
          <key>AGENCIES</key>
          <value>AG001,DHS</value>
        </entry>
      </job-data-map>
    </job-detail>
    <trigger>
      <cron>
        <name>OverdueAssetsJobTrigger</name>
        <group>Manual</group>
        <description>To run OverdueAssets job on manual trigger</description>
        <misfire-instruction>SmartPolicy</misfire-instruction>
        <volatile>false</volatile>
        <job-name>OverdueAssetsJob</job-name>
        <job-group>Manual</job-group>
        <cron-expression>0 0 20 * * ? *</cron-expression>
      </cron>
    </trigger>
  </job>

  <job>
    <job-detail>
      <name>WarrantyExpirationJob</name>
      <group>Manual</group>
      <description>Job to list pending Warranty Expiration</description>
      <job-type>Upp.Irms.AlertGenerator.Host.WarrantyExpirationJob,Upp.Irms.AlertGenerator.Host</job-type>
      <volatile>false</volatile>
      <durable>true</durable>
      <recover>false</recover>
      <job-data-map>
        <entry>
          <key>AGENCIES</key>
          <value>AG001,DHS</value>
        </entry>
      </job-data-map>
    </job-detail>
    <trigger>
      <cron>
        <name>WarrantyExpirationJobTrigger</name>
        <group>Manual</group>
        <description>To run WarrantyExpiration job on manual trigger</description>
        <misfire-instruction>SmartPolicy</misfire-instruction>
        <volatile>false</volatile>
        <job-name>WarrantyExpirationJob</job-name>
        <job-group>Manual</job-group>
        <cron-expression>0 0/2 0 * * ?</cron-expression>
      </cron>
    </trigger>
  </job>

  <job>
    <job-detail>
      <name>ShelfExpirationJob</name>
      <group>Manual</group>
      <description>Job to list pending Shelf Expiration</description>
      <job-type>Upp.Irms.AlertGenerator.Host.ShelfExpirationJob,Upp.Irms.AlertGenerator.Host</job-type>
      <volatile>false</volatile>
      <durable>true</durable>
      <recover>false</recover>
      <job-data-map>
        <entry>
          <key>AGENCIES</key>
          <value>AG001,DHS</value>
        </entry>
      </job-data-map>
    </job-detail>
    <trigger>
      <cron>
        <name>ShelfExpirationJobTrigger</name>
        <group>Manual</group>
        <description>To run ShelfExpiration job on manual trigger</description>
        <misfire-instruction>SmartPolicy</misfire-instruction>
        <volatile>false</volatile>
        <job-name>ShelfExpirationJob</job-name>
        <job-group>Manual</job-group>
        <cron-expression>0 0 20 * * ? *</cron-expression>
      </cron>
    </trigger>
  </job>

  <job>
    <job-detail>
      <name>StandardIssueJob</name>
      <group>Manual</group>
      <description>Job to list Standard Issue Exceptions</description>
      <job-type>Upp.Irms.AlertGenerator.Host.StandardIssueJob,Upp.Irms.AlertGenerator.Host</job-type>
      <volatile>false</volatile>
      <durable>true</durable>
      <recover>false</recover>
      <job-data-map>
        <entry>
          <key>AGENCIES</key>
          <value>AG001,DHS</value>
        </entry>
      </job-data-map>
    </job-detail>
    <trigger>
      <cron>
        <name>StandardIssueJobTrigger</name>
        <group>Manual</group>
        <description>To run Standard Issue Exceptions Job on manual trigger</description>
        <misfire-instruction>SmartPolicy</misfire-instruction>
        <volatile>false</volatile>
        <job-name>StandardIssueJob</job-name>
        <job-group>Manual</job-group>
        <cron-expression>0 0 20 * * ? *</cron-expression>
      </cron>
    </trigger>
  </job>
  <job>
    <job-detail>
      <name>TemporaryRolesJob</name>
      <group>Manual</group>
      <description>Job to list Temporary Roles</description>
      <job-type>Upp.Irms.AlertGenerator.Host.TemporaryRolesJob,Upp.Irms.AlertGenerator.Host</job-type>
      <volatile>false</volatile>
      <durable>true</durable>
      <recover>false</recover>
      <job-data-map>
        <entry>
          <key>AGENCIES</key>
          <value>AG001,DHS</value>
        </entry>
      </job-data-map>
    </job-detail>
    <trigger>
      <cron>
        <name>TemporaryRolesJobTrigger</name>
        <group>Manual</group>
        <description>To run Temporary Roles Job on manual trigger</description>
        <misfire-instruction>SmartPolicy</misfire-instruction>
        <volatile>false</volatile>
        <job-name>TemporaryRolesJob</job-name>
        <job-group>Manual</job-group>
        <cron-expression>0 0 20 * * ? *</cron-expression>
      </cron>
    </trigger>
  </job>

  <job>
    <job-detail>
      <name>AlternateRolesExpirationJob</name>
      <group>Manual</group>
      <description>Job to list Alternate Roles Expiration</description>
      <job-type>Upp.Irms.AlertGenerator.Host.AlternateRolesExpirationJob,Upp.Irms.AlertGenerator.Host</job-type>
      <volatile>false</volatile>
      <durable>true</durable>
      <recover>false</recover>
      <job-data-map>
        <entry>
          <key>AGENCIES</key>
          <value>TAG1</value>
        </entry>
      </job-data-map>
    </job-detail>
    <trigger>
      <cron>
        <name>AlternateRolesExpirationJobTrigger</name>
        <group>Manual</group>
        <description>To run Alternate Roles Expiration Job on manual trigger</description>
        <misfire-instruction>SmartPolicy</misfire-instruction>
        <volatile>false</volatile>
        <job-name>AlternateRolesExpirationJob</job-name>
        <job-group>Manual</job-group>
        <cron-expression>0 19 17 * * ?</cron-expression>
      </cron>
    </trigger>
  </job>
  <job>
    <job-detail>
      <name>MustShipTodayJob</name>
      <group>Manual</group>
      <description>Job to send mail for must ship today orders.</description>
      <job-type>Upp.Irms.AlertGenerator.Host.MustShipTodayJob,Upp.Irms.AlertGenerator.Host</job-type>
      <volatile>false</volatile>
      <durable>true</durable>
      <recover>false</recover>
      <job-data-map>
        <entry>
          <key>WAREHOUSES</key>
          <value>EGI:EGDC,PTLD,MAIN;</value>
        </entry>
        <entry>
          <key>BILLTOCUSTOMERCODE</key>
          <value>6402,454,5618</value>
        </entry>
        <entry>
          <key>ORDERSTATUS</key>
          <value>PG,H</value>
        </entry>
        <entry>
          <key>NUMBEROFDAYS</key>
          <value>20</value>
        </entry>
        <entry>
          <key>EMAILSUBJECT</key>
          <value>Must Ship Today</value>
        </entry>
      </job-data-map>
    </job-detail>
    <trigger>
      <cron>
        <name>MustShipTodayJobTrigger</name>
        <group>Manual</group>
        <description>To run Must Ship Today Job on manual trigger</description>
        <misfire-instruction>SmartPolicy</misfire-instruction>
        <volatile>false</volatile>
        <job-name>MustShipTodayJob</job-name>
        <job-group>Manual</job-group>
        <cron-expression>0 50 00 * * ?</cron-expression>
      </cron>
    </trigger>
  </job>
  
  <job>
    <job-detail>
      <name>AppointmentReminderAlertJob</name>
      <group>Manual</group>
      <description>Job to send email reminders to patients for upcoming scheduled appointments </description>
      <job-type>Upp.Irms.AlertGenerator.Host.AppointmentReminderAlertJob,Upp.Irms.AlertGenerator.Host</job-type>
      <volatile>false</volatile>
      <durable>true</durable>
      <recover>false</recover>
      <job-data-map>
        <entry>
          <key>PROVIDERS</key>
          <value>BCBS,BHP</value>
        </entry>
        <entry>
          <key>EMAILSUBJECT</key>
          <value>Appointment Reminder</value>
        </entry>
      </job-data-map>
    </job-detail>
    <trigger>
      <cron>
        <name>AppointmentReminderAlertJobTrigger</name>
        <group>Manual</group>
        <description>To run Appointment Reminder Job on manual trigger</description>
        <misfire-instruction>SmartPolicy</misfire-instruction>
        <volatile>false</volatile>
        <job-name>AppointmentReminderAlertJob</job-name>
        <job-group>Manual</job-group>
        <cron-expression>0 50 23 * * ?</cron-expression>
      </cron>
    </trigger>
  </job>

  <job>
    <job-detail>
      <name>PasswordExpirationJob</name>
      <group>Manual</group>
      <description>Job to send email reminders to users for prior to X days of expiration of password </description>
      <job-type>Upp.Irms.AlertGenerator.Host.PasswordExpirationJob,Upp.Irms.AlertGenerator.Host</job-type>
      <volatile>false</volatile>
      <durable>true</durable>
      <recover>false</recover>
      <job-data-map>
        <entry>
          <key>AGENCIES</key>
          <value>AG001,DHS</value>
        </entry>        
      </job-data-map>
    </job-detail>
    <trigger>
      <cron>
        <name>PasswordExpirationJobTrigger</name>
        <group>Manual</group>
        <description>To run Password Expiration Notification Reminder Job on manual trigger</description>
        <misfire-instruction>SmartPolicy</misfire-instruction>
        <volatile>false</volatile>
        <job-name>PasswordExpirationJob</job-name>
        <job-group>Manual</job-group>
        <cron-expression>0 55 23 * * ?</cron-expression>
      </cron>
    </trigger>
  </job>
  
</quartz>

