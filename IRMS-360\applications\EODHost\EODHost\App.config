<?xml version="1.0"?>
<configuration>
	<configSections>
		<section name="castle" type="Castle.Windsor.Configuration.AppDomain.CastleSectionHandler, Castle.Windsor" />
		<section name="hibernate-configuration" type="NHibernate.Cfg.Configuration<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NHibernate" allowLocation="true" allowDefinition="Everywhere" allowExeDefinition="MachineToApplication" overrideModeDefault="Allow" restartOnExternalChanges="true" requirePermission="true" />
		<section name="quartz" type="System.Configuration.NameValueSectionHandler, System, Version=1.0.3.3,Culture=neutral, PublicKeyToken=b77a5c561934e089" />
		<section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler,log4net" />
	</configSections>
	<appSettings file="">
		<clear />
		<add key="User" value="irmswm" />
		<add key="Password" value="password" />
		<add key="Warehouse" value="02" />
		<add key="Company" value="PSSC" />
		<add key="ServicePeriod" value="23" />
		<add key="JobsConfiguredInDatabase" value="false" />
		<add key="ScanInterval" value="5" />
		<add key="FileDestination" value="D:\EODfiles\" />
    <add key="FtpHost" value="ftp://************/" />
    <add key="FtpUser" value="enerco_it" />
    <add key="FtpPassword" value="w3admin5" />
    <add key="FtpFilePath" value="/website/ORDER_UPDATE/" />
		<add key="FromPTLPath" value="C:\Host\FromPTL\" />
    <add key="ExactaApiURL" value="http://localhost/exactaApi/import/product " />
		<add key="IntegrationURL" value="http://localhost/irms360/rest/integration"/>
    <add key="Reports" value="C:\IRMS360\Source\irms-360-1.0.0\IRMS-360-Web\IRMS-360\applications\WebClient\Core\Reports\"/>
    <add key="ExactaServerName" value="localhost" />
    <!-- Smtp -->
    <add key="SmtpFrom" value="<EMAIL>" />
    <add key="SmtpHost" value="smtp.prod.lclad.com" />
    <add key="SmtpPassword" value="" />
    <add key="SmtpPort" value="25" />
    <add key="SmtpSsl" value="" />
    <add key="SmtpUser" value="" />
	</appSettings>
	<castle>
		<components>
			<component id="repositories" lifestyle="transient" service="Upp.Irms.Core.IRepository`1, Upp.Irms.Core" type="Upp.Irms.Core.Repository`1, Upp.Irms.Core" />
		</components>
	</castle>
	<hibernate-configuration xmlns="urn:nhibernate-configuration-2.2">
		<session-factory name="Services">
			<property name="connection.driver_class">NHibernate.Driver.SqlClientDriver</property>
      <property name="connection.connection_string">Data Source=localhost;Initial Catalog=irms360_Demo_29;Integrated Security=False;User ID=sa;Password=*******;</property>
      <property name="dialect">NHibernate.Dialect.MsSql2008Dialect</property>
			<property name="current_session_context_class">NHibernate.Context.ThreadStaticSessionContext</property>
      <property name="show_sql">true</property>
		</session-factory>
	</hibernate-configuration>
	<quartz>
		<add key="quartz.scheduler.instanceName" value="QuartzScheduler" />
		<!-- Configure Thread Pool -->
		<add key="quartz.threadPool.type" value="Quartz.Simpl.SimpleThreadPool, Quartz" />
		<add key="quartz.threadPool.threadCount" value="10" />
		<add key="quartz.threadPool.threadPriority" value="Normal" />
		<!-- Configure Job Store -->
		<add key="quartz.jobStore.type" value="Quartz.Simpl.RAMJobStore, Quartz" />
		<add key="quartz.plugin.xml.type" value="Quartz.Plugin.Xml.JobInitializationPlugin, Quartz" />
		<add key="quartz.plugin.xml.fileNames" value="~/quartz_jobs.xml" />
		<add key="quartz.plugin.xml.overwriteExistingJobs" value="true" />
		<!-- ScanInterval in seconds -->
		<add key="quartz.plugin.xml.scanInterval" value="60" />
	</quartz>
	<log4net>
		<appender name="RollingFileAppender" type="log4net.Appender.RollingFileAppender, log4net">
      <file value="Logs\\EOD360.txt"/>
			<appendToFile value="true"/>
			<rollingStyle value="Composite"/>
			<maxSizeRollBackups value="14"/>
			<maximumFileSize value="5000KB"/>
			<datePattern value="yyyyMMdd"/>
			<staticLogFileName value="true"/>
			<layout type="log4net.Layout.PatternLayout, log4net">
				<conversionPattern value="[%-5p] %d  %-50c  %m%n"/>
			</layout>
		</appender>
    <appender name="SmtpAppender" type="log4net.Appender.SmtpAppender, log4net">
      <to value="" />
      <from value="" />
      <username value="" />
      <password value="" />
      <authentication value="Basic" />
      <port value="25"/>
      <EnableSsl value="false" />
      <subject value="EOD Error" />
      <smtpHost value="" />
      <bufferSize value="10" />
      <lossy value="true" />
      <threshold value="WARN"/>
      <evaluator type="log4net.Core.LevelEvaluator">
        <threshold value="WARN"/>
      </evaluator>
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="[%-5p] %d  %-50c  %m%n" />
      </layout>
    </appender>
    
		<!-- Set levels DEBUG | INFO | WARN | ERROR   -->
		<root>
			<priority value="ERROR"/>
			<appender-ref ref="RollingFileAppender"/>
      <appender-ref ref="SmtpAppender" />
		</root>

		<logger name="NHibernate" additivity="true">
			<level value="ERROR"/>
			<appender-ref ref="RollingFileAppender"/>
      <appender-ref ref="SmtpAppender" />
		</logger>
		<!-- To log SQL queries set level to DEBUG , otherwise ERROR -->
		<logger name="NHibernate.SQL" additivity="true">
			<level value="ERROR"/>
			<appender-ref ref="RollingFileAppender"/>
      <appender-ref ref="SmtpAppender" />
		</logger>
		<logger name="Upp.Irms.Core" additivity="true">
			<level value="ERROR"/>
			<appender-ref ref="RollingFileAppender"/>
      <appender-ref ref="SmtpAppender" />
		</logger>
		<logger name="Upp.Irms.Domain" additivity="true">
			<level value="ERROR"/>
			<appender-ref ref="RollingFileAppender"/>
      <appender-ref ref="SmtpAppender" />
		</logger>

	</log4net>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2"/>
  </startup>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-11.0.0.0" newVersion="11.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="NHibernate" publicKeyToken="aa95f207798dfdb4" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.1.0.0" newVersion="5.1.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>