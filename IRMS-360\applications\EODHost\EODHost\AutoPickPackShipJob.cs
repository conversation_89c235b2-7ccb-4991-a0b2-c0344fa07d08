using log4net;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NHibernate.Criterion;
using NHibernate.SqlCommand;
using NHibernate.Transform;
using Quartz;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Runtime.Serialization.Json;
using System.Text;
using System.Threading.Tasks;
using Upp.Irms.Constants;
using Upp.Irms.Core;
using Upp.Irms.Domain;

namespace Upp.Irms.EOD.Host
{
    class AutoPickPackShipJob : IJob
    {
        #region Fields

        ILog _logger = LogManager.GetLogger(typeof(AutoPickPackShipJob));

        const string JParam_PPSHostUserID = "PPSHOSTUSERID";
        const string JParam_PPSDownloadFileFormat = "PPSDOWNLOADFILEFORMAT";
        const string JParam_PPSToIRMSDownloadFolder = "PPSTOIRMSDOWNLOADFOLDER";
        const string JParam_PPSLogFileFolder = "PPSLOGFILEFOLDER";
        const string JParam_PPSMaxFilesToConsider = "PPSMAXFILESCOUNT";
        const string JParam_nextJob = "NEXTJOB";

        string ppsHostUserID = "";
        string ppsDownloadFileFormat = "";
        string ppsToIRMSDownloadFolders = "";
        string ppsToIRMSDownloadFolder = "";
        int ppsMaxFilesToConsider = 0;
        string ppsLogFileFolder = "";
        string nextJob = "";

        string jobname = "Auto Pick Pack Ship";
        string logDateTimeFormat = "dd/MM/yy HH:mm:ss.fff";

        #endregion

        #region Constructor

        public AutoPickPackShipJob()
        {
        }

        #endregion

        #region Methods.Public

        public virtual void Execute(JobExecutionContext context)
        {
            if (JobParametersAreValid(context.MergedJobDataMap) == false)
            {
                JobExecutionException jex = new JobExecutionException("Missing job parameter");
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }

            ParseJobParameters(context.MergedJobDataMap);
            //Loop through each directory if the input files are placed in multiple folders
            string[] IRMSDownloadFolders = ppsToIRMSDownloadFolders.Split(',');
            foreach (string directoryPath in IRMSDownloadFolders)
            {
                this.ppsToIRMSDownloadFolder = directoryPath;
                PerformPickingPackingShipping();
            }
            NextJobScheduling(jobname);
        }

        #endregion

        #region Methods.Private

        private bool JobParametersAreValid(JobDataMap jobParams)
        {
            bool validity = true;

            if (!jobParams.Contains(JParam_PPSHostUserID))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_PPSHostUserID);
                validity = false;
            }
            if (!jobParams.Contains(JParam_PPSDownloadFileFormat))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_PPSDownloadFileFormat);
                validity = false;
            }
            if (!jobParams.Contains(JParam_PPSToIRMSDownloadFolder))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_PPSToIRMSDownloadFolder);
                validity = false;
            }
            if (!jobParams.Contains(JParam_PPSLogFileFolder))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_PPSLogFileFolder);
                validity = false;
            }
            if (!jobParams.Contains(JParam_PPSMaxFilesToConsider))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_PPSMaxFilesToConsider);
                validity = false;
            }
            if (!jobParams.Contains(JParam_nextJob))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_nextJob);
                validity = false;
            }

            return validity;
        }

        private void ParseJobParameters(JobDataMap jobParams)
        {
            try
            {
                //map the parameters to jobParams              
                this.ppsHostUserID = jobParams.GetString(JParam_PPSHostUserID);
                this.ppsDownloadFileFormat = jobParams.GetString(JParam_PPSDownloadFileFormat);
                this.ppsToIRMSDownloadFolders = jobParams.GetString(JParam_PPSToIRMSDownloadFolder);
                this.ppsLogFileFolder = jobParams.GetString(JParam_PPSLogFileFolder);
                if (!string.IsNullOrEmpty(jobParams.GetString(JParam_PPSMaxFilesToConsider)))
                    this.ppsMaxFilesToConsider = Int32.Parse(jobParams.GetString(JParam_PPSMaxFilesToConsider));
                this.nextJob = jobParams.GetString(JParam_nextJob);
            }
            catch (Exception ex)
            {
                JobExecutionException jex = new JobExecutionException("Invalid data type in job parameters", ex);
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }
        }

        private void NextJobScheduling(string jobname)
        {
            if (!String.IsNullOrWhiteSpace(nextJob))
            {
                if (_logger.IsDebugEnabled) _logger.DebugFormat("NextJob:  {0}", nextJob);
                try
                {
                    string[] jobKeys = Service.Instance.Scheduler.GetJobNames("Manual");
                    foreach (string jobKey in jobKeys)
                    {
                        if (jobKey.Equals(nextJob))
                        {
                            SimpleTrigger trigger = new SimpleTrigger();
                            trigger.Name = nextJob + "Trigger";
                            trigger.JobName = nextJob;
                            trigger.JobGroup = "Manual";
                            trigger.Group = "Manual";
                            trigger.StartTimeUtc = DateTime.UtcNow.AddSeconds(30);
                            trigger.RepeatCount = 0;
                            trigger.RepeatInterval = TimeSpan.Zero;
                            Service.Instance.Scheduler.RescheduleJob(nextJob + "Trigger", "Manual", trigger);
                            if (_logger.IsDebugEnabled) _logger.DebugFormat("{1} - Next Job {0} is scheduled: ", nextJob, jobname);
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (_logger.IsErrorEnabled) _logger.ErrorFormat("{1} - Next Job error: {0}", ex.Message, jobname);
                }
            }
        }

        private void PerformPickingPackingShipping()
        {
            try
            {
                if (_logger.IsDebugEnabled) _logger.DebugFormat("START - EOD Job :  {0}", jobname);

                //Check the grsiToIRMSDownloadFolder exist or not
                if (!Directory.Exists(ppsToIRMSDownloadFolder))
                {
                    _logger.ErrorFormat("Directory not Found/Access Denied to folder :" + ppsToIRMSDownloadFolder);
                    return;
                }

                //Check the grsiLogFileFolder exist or not
                if (!Directory.Exists(ppsLogFileFolder))
                {
                    _logger.ErrorFormat("Directory not Found/Access Denied to folder :" + ppsLogFileFolder);
                    return;
                }

                //Create backUp folder to move the input files after processing 
                string ppsToIRMSDownloadFolderBk = string.Empty;
                string ppsToIRMSDownloadFolderErrorFiles = string.Empty;
                string ppsToIRMSDownloadFolderEchoFiles = string.Empty;
                string ppsToIRMSDownloadFolderInProcessFiles = string.Empty;

                StringBuilder backupPathSb = new StringBuilder();
                if (ppsToIRMSDownloadFolder.EndsWith("\\"))
                    backupPathSb.Append(ppsToIRMSDownloadFolder);
                else
                    backupPathSb.Append(ppsToIRMSDownloadFolder + "\\");
                backupPathSb.Append("bk\\");
                ppsToIRMSDownloadFolderBk = backupPathSb.ToString();

                if (!Directory.Exists(ppsToIRMSDownloadFolderBk))
                {
                    Directory.CreateDirectory(ppsToIRMSDownloadFolderBk);
                }

                StringBuilder errorPathSb = new StringBuilder();
                if (ppsToIRMSDownloadFolder.EndsWith("\\"))
                    errorPathSb.Append(ppsToIRMSDownloadFolder);
                else
                    errorPathSb.Append(ppsToIRMSDownloadFolder + "\\");
                errorPathSb.Append("FailedOrders\\");
                ppsToIRMSDownloadFolderErrorFiles = errorPathSb.ToString();

                if (!Directory.Exists(ppsToIRMSDownloadFolderErrorFiles))
                {
                    Directory.CreateDirectory(ppsToIRMSDownloadFolderErrorFiles);
                }

                StringBuilder echoPathSb = new StringBuilder();
                if (ppsToIRMSDownloadFolder.EndsWith("\\"))
                    echoPathSb.Append(ppsToIRMSDownloadFolder);
                else
                    echoPathSb.Append(ppsToIRMSDownloadFolder + "\\");
                echoPathSb.Append("EchoFiles\\");
                ppsToIRMSDownloadFolderEchoFiles = echoPathSb.ToString();

                if (!Directory.Exists(ppsToIRMSDownloadFolderEchoFiles))
                {
                    Directory.CreateDirectory(ppsToIRMSDownloadFolderEchoFiles);
                }

                StringBuilder inProcessPathSb = new StringBuilder();
                if (ppsToIRMSDownloadFolder.EndsWith("\\"))
                    inProcessPathSb.Append(ppsToIRMSDownloadFolder);
                else
                    inProcessPathSb.Append(ppsToIRMSDownloadFolder + "\\");
                inProcessPathSb.Append("InProcess\\");
                ppsToIRMSDownloadFolderInProcessFiles = inProcessPathSb.ToString();

                if (!Directory.Exists(ppsToIRMSDownloadFolderInProcessFiles))
                {
                    Directory.CreateDirectory(ppsToIRMSDownloadFolderInProcessFiles);
                }
                //Get the today's LogFilePath
                string fullLogFilePath = getLogFolderFilePath(ppsLogFileFolder, "ppsfrom");

                //Fetch all the Order file full paths and its content from PPSTOIRMSDOWNLOADFOLDER
                ppsDownloadFileFormat = ppsDownloadFileFormat.Replace(".", "*.");
                string[] DownloadFileFormats = ppsDownloadFileFormat.Split(',');
                Dictionary<string, string> allOrdersFilePathsContents = readAllOrdersAndMoveToBackFolder(ppsToIRMSDownloadFolder, fullLogFilePath, ppsToIRMSDownloadFolderInProcessFiles, ppsToIRMSDownloadFolderErrorFiles, ppsToIRMSDownloadFolderBk, DownloadFileFormats, "dppsc", "dppscnnnnnnnnn");

                //Read GRSI Download File Fields name and length from DB
                Dictionary<string, int> fieldsLength = getFieldsLengthFromDB("PickingPackingShipping");

                if(fieldsLength.Count == 0)
                {
                    _logger.ErrorFormat("No Interface Fields Found");
                    return;
                }

                //Get IntegrationApiUrl
                string integrationApiUrl = string.Empty;
                string integrationShipApiUrl = string.Empty;
                string integrationUrl = string.Empty;
                string UndoPickApiUrl = string.Empty;
                if (System.Configuration.ConfigurationManager.AppSettings["IntegrationURL"] != null)
                    integrationUrl = System.Configuration.ConfigurationManager.AppSettings["IntegrationURL"].ToString();
                integrationApiUrl = String.Format("{0}{1}", integrationUrl, "/processes/updatepickingpacking/update");
                integrationShipApiUrl = String.Format("{0}{1}", integrationUrl, "/download/shiptransactions");
                //loop each files, read its Contents, prepare input json format, call to Integration (grsiPickingPacking endpoint)
                foreach (KeyValuePair<string, string> orderFilePathContent in allOrdersFilePathsContents)
                {

                    try
                    {
                        //Log the FileName to PPSLOGFOLDER
                        File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": PPS Order File Found" });

                        if(string.IsNullOrEmpty(orderFilePathContent.Value))
                        {
                            File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": PPS Order File is Empty" });
                            continue;
                        }

                        string company = string.Empty;
                        string warehouse = string.Empty;
                        string orderCode = string.Empty;
                        string orderSuffix = string.Empty;
                        string PPSFlag = "N";
                        string carrier = string.Empty;
                        string service = string.Empty;
                        string trackingNumber = string.Empty;

                        string dataString = prepareInputForPickPackApi(fieldsLength, orderFilePathContent.Value, ppsHostUserID, ref company, ref warehouse, ref orderCode, ref orderSuffix, ref PPSFlag, ref carrier, ref service, ref trackingNumber);
                        if (!PPSFlag.Equals("Y"))
                        {
                            File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + "PPS flag was not set to Y for cartonId/order " + orderCode + " " + orderSuffix });
                            MoveFileToBackOrErrorFolder("error", orderFilePathContent.Key, ppsToIRMSDownloadFolderInProcessFiles, ppsToIRMSDownloadFolderErrorFiles);
                            MoveFileToBackOrErrorFolder("echo", orderFilePathContent.Key, ppsToIRMSDownloadFolderInProcessFiles, ppsToIRMSDownloadFolderEchoFiles, "9999", "PPS flag was not set to Y for cartonId/order " + orderCode + " " + orderSuffix);
                            continue;
                        }

                        JObject result = PostData(dataString, integrationApiUrl, true);
                        if (result["result_code"].ToString() == "0000")
                        {
                            File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": " + result["result_msg"].ToString() });
                            File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": PPS order '"+ orderCode + "' picking packing Processed Successfully" });
                            //Ship Carton. Carton code is same as OrderCode
                            dataString = prepareInputForShipConatiner(orderCode, company, warehouse);
                            result = PostData(dataString, integrationShipApiUrl, false);
                            if (result["result_code"].ToString() == "0000")
                            {
                                File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": " + result["result_msg"].ToString() });
                                File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": PPS order '" + orderCode + "' shipping Processed Successfully" });
                                MoveFileToBackOrErrorFolder("bk", orderFilePathContent.Key, ppsToIRMSDownloadFolderInProcessFiles, ppsToIRMSDownloadFolderBk);
                            }
                            else
                            {
                                File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": " + result["result_msg"].ToString() });
                                File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": PPS order '" + orderCode + "' Failed to ship" });
                                MoveFileToBackOrErrorFolder("bk", orderFilePathContent.Key, ppsToIRMSDownloadFolderInProcessFiles, ppsToIRMSDownloadFolderBk);
                                MoveFileToBackOrErrorFolder("error", orderFilePathContent.Key, ppsToIRMSDownloadFolderInProcessFiles, ppsToIRMSDownloadFolderErrorFiles);

                                UndoPickApiUrl = String.Format("{0}{1}{2}{3}{4}", integrationUrl, "/processes/undopickbyorder/", orderCode + "|" + orderSuffix + "/", company + "/", warehouse);
                                WebClient webClientProxy = new WebClient();
                                webClientProxy.Headers["Content-type"] = "application/json";
                                webClientProxy.DownloadData(UndoPickApiUrl);
                            }
                        }
                        else if (result["result_code"].ToString() == "9642" && result["result_msg"].ToString() == "Order not found")
                        {
                            File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": PPS order '" + orderCode + "' not available" });
                            MoveFileToBackOrErrorFolder("bk", orderFilePathContent.Key, ppsToIRMSDownloadFolderInProcessFiles, ppsToIRMSDownloadFolderBk);
                            MoveFileToBackOrErrorFolder("error", orderFilePathContent.Key, ppsToIRMSDownloadFolderInProcessFiles, ppsToIRMSDownloadFolderErrorFiles);
                        }
                        else if (result["result_msg"].ToString() == "Order not in distributed status")
                        {
                            File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": PPS order '" + orderCode + "' Invalid Order Status" });
                            MoveFileToBackOrErrorFolder("bk", orderFilePathContent.Key, ppsToIRMSDownloadFolderInProcessFiles, ppsToIRMSDownloadFolderBk);
                            MoveFileToBackOrErrorFolder("error", orderFilePathContent.Key, ppsToIRMSDownloadFolderInProcessFiles, ppsToIRMSDownloadFolderErrorFiles);
                        }
                        else if (result["result_msg"].ToString() == "Invalid Order Class")
                        {
                            File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": PPS order '" + orderCode + "' Invalid Order Class" });
                            MoveFileToBackOrErrorFolder("bk", orderFilePathContent.Key, ppsToIRMSDownloadFolderInProcessFiles, ppsToIRMSDownloadFolderBk);
                            MoveFileToBackOrErrorFolder("error", orderFilePathContent.Key, ppsToIRMSDownloadFolderInProcessFiles, ppsToIRMSDownloadFolderErrorFiles);
                        }
                        else
                        {
                            File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": " + result["result_msg"].ToString() });
                            MoveFileToBackOrErrorFolder("bk", orderFilePathContent.Key, ppsToIRMSDownloadFolderInProcessFiles, ppsToIRMSDownloadFolderBk);
                            MoveFileToBackOrErrorFolder("error", orderFilePathContent.Key, ppsToIRMSDownloadFolderInProcessFiles, ppsToIRMSDownloadFolderErrorFiles);
                        }
                        MoveFileToBackOrErrorFolder("echo", orderFilePathContent.Key, ppsToIRMSDownloadFolderInProcessFiles, ppsToIRMSDownloadFolderEchoFiles, result["result_code"].ToString(), result["result_msg"].ToString());
                        File.AppendAllText(fullLogFilePath, "\n");
                    }
                    catch(Exception ex)
                    {
                        File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": " + ex?.InnerException });
                        MoveFileToBackOrErrorFolder("bk", orderFilePathContent.Key, ppsToIRMSDownloadFolderInProcessFiles, ppsToIRMSDownloadFolderBk);
                        MoveFileToBackOrErrorFolder("error", orderFilePathContent.Key, ppsToIRMSDownloadFolderInProcessFiles, ppsToIRMSDownloadFolderErrorFiles);
                    }

                }
                if (_logger.IsDebugEnabled) _logger.DebugFormat("END - EOD Job :  {0}", jobname);
            }
            catch (Exception ex)
            {
                _logger.ErrorFormat("Error at PickPackShip::PerformPickingPacking() " + ex.Message);
            }
        }

        public string prepareInputForPickPackApi(Dictionary<string, int> fieldsLength, string orderFilePathContent, string grsiHostUserID, ref string company, ref string warehouse, ref string orderCode, ref string orderSuffix, ref string PPSFlag, ref string carrier, ref string service, ref string trackingNumber)
        {
            string pickPackApiInput = string.Empty;
            try
            {
                StringBuilder dataStrBuilder = new StringBuilder();
                dataStrBuilder.Append("[{");
                int startPosition = 0;
                foreach (KeyValuePair<string, int> fieldLength in fieldsLength)
                {
                    if ((startPosition + fieldLength.Value) <= orderFilePathContent.Length)
                    {
                        if (!"carton_id".Equals(fieldLength.Key)) dataStrBuilder.Append("\"" + fieldLength.Key + "\"" + ":" + "\"" + orderFilePathContent.Substring(startPosition, fieldLength.Value).Trim() + "\"");

                        string value = orderFilePathContent.Substring(startPosition, fieldLength.Value).Trim();
                        if ("company".Equals(fieldLength.Key))
                            company = value;
                        else if ("warehouse".Equals(fieldLength.Key))
                            warehouse = value;
                        else if ("pps_flag".Equals(fieldLength.Key))
                            PPSFlag = value;
                        else if ("carton_id".Equals(fieldLength.Key))
                        {
                            orderCode = value;
                            if (orderCode.Contains("#"))
                            {
                                string[] order = orderCode.Split('#');
                                orderCode = order[0];
                                orderSuffix = order[1];
                                dataStrBuilder.Append("\"" + "order_code" + "\"" + ":" + "\"" + orderCode + "\"");
                                dataStrBuilder.Append("\"" + "order_suffix" + "\"" + ":" + "\"" + orderSuffix + "\"");
                            }
                            else
                                dataStrBuilder.Append("\"" + "order_code" + "\"" + ":" + "\"" + orderCode + "\"");
                        }
                        else if ("carrier".Equals(fieldLength.Key))
                            carrier = value;
                        else if ("service".Equals(fieldLength.Key))
                            service = value;
                        else if ("tracking_number".Equals(fieldLength.Key))
                            trackingNumber = value;

                        startPosition += fieldLength.Value;
                        dataStrBuilder.Append(",");
                    }
                    else if ("order_code".Equals(fieldLength.Key))
                    {
                        dataStrBuilder.Append("\"" + "order_code" + "\"" + ":" + "\"" + orderFilePathContent.Substring(startPosition).Trim() + "\"");
                        orderCode = orderFilePathContent.Substring(startPosition).Trim();
                        startPosition += fieldLength.Value;
                        dataStrBuilder.Append(",");
                    }
                    else
                    {
                        dataStrBuilder.Append("\"" + fieldLength.Key + "\"" + ":" + "\"" + "\"");
                        dataStrBuilder.Append(",");
                        break;
                    }
                }
                dataStrBuilder.Append("\"user_id\"" + ":" + "\"" + grsiHostUserID+ "\"," + "\"result_code\"" + ":" + "\"" + "\"," + "\"result_msg\"" + ":" + "\"" + "\"");
                dataStrBuilder.Append("}]");
                pickPackApiInput = dataStrBuilder.ToString();
            }
            catch (Exception ex)
            {
                _logger.ErrorFormat("Error at PickPackShip::getDataString() " + ex.Message);                
            }
            return pickPackApiInput;
        }

        public string prepareInputForShipConatiner(string carton, string compny, string compLocType)
        {
            dynamic request = new
            {
                carton_id = carton,
                company = compny,
                declared_value = String.Empty,
                function = "THIRDPARTY",
                method_type = "carton",
                printer_name = "",
                printer_code = "",
                suppress_first_label = "Y",
                trans_type = "",
                user_name = ppsHostUserID,
                warehouse = compLocType,
                electronic_commercial_invoice = "N",
            };

            return JsonConvert.SerializeObject(request);
        }

        public Dictionary<string, int> getFieldsLengthFromDB(string intHeaderCode)
        {
            Dictionary<string, int> fieldsLength = new Dictionary<string, int>();
            using (UnitWrapper wrapper = new UnitWrapper())
            {
                wrapper.Execute(() =>
                {
                    try
                    {
                        DetachedCriteria nhdcfieldsLength = DetachedCriteria.For<InterfaceHeader>()
                                                          .CreateAlias("InterfaceDetails", "InterfaceDetail")
                                                          .SetProjection(Projections.ProjectionList()
                                                          .Add(Projections.Property("InterfaceDetail.FieldName"), "FieldName")
                                                          .Add(Projections.Property("InterfaceDetail.DataFormat"), "DataFormat"))
                                                          .Add(Restrictions.Eq("InterfaceHeaderCode", intHeaderCode))
                                                          .Add(Restrictions.Eq("Active", "A"))
                                                          .Add(Restrictions.Eq("InterfaceDetail.Active", "A"))
                                                          .AddOrder(Order.Asc("InterfaceDetail.SortOrder"))
                                                          .SetResultTransformer(Transformers.AliasToBean<InterfaceDetail>());
                        IList<InterfaceDetail> nhfieldsLength = Repositories.Get<InterfaceDetail>().List(nhdcfieldsLength);

                        foreach (InterfaceDetail fieldLength in nhfieldsLength)
                        {
                            if (!string.IsNullOrEmpty(fieldLength.FieldName) && !string.IsNullOrEmpty(fieldLength.DataFormat))
                                fieldsLength.Add(fieldLength.FieldName, Convert.ToInt32(fieldLength.DataFormat.Substring(1)));
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.ErrorFormat("Error at PickPackShip::getFieldsLengthFromDB() " + ex.Message);
                    }
                });
            }

            return fieldsLength;
        }

        public string getLogFolderFilePath(string grsiLogFileFolder, string logPrefix)
        {
            string fullLogFilePath = string.Empty;
            try
            {
                string logFileName = logPrefix + DateTime.Now.ToString("yyyyMMdd") + ".log";

                StringBuilder logpathStrBuilder = new StringBuilder();
                if (grsiLogFileFolder.EndsWith("\\"))
                    logpathStrBuilder.Append(grsiLogFileFolder);
                else
                    logpathStrBuilder.Append(grsiLogFileFolder + "\\");
                logpathStrBuilder.Append(logFileName);

                fullLogFilePath = logpathStrBuilder.ToString();
            }
            catch (Exception ex)
            {
                _logger.ErrorFormat("Error at PickPackShip::getPPSLogFolderFilePath() " + ex.Message);
            }
            return fullLogFilePath;
        }

        public Dictionary<string, string> readAllOrdersAndMoveToBackFolder(string ppsToIRMSDownloadFolder, string fullLogFilePath, string ppsToIRMSDownloadFolderEcho, string ppsToIRMSDownloadFolderFailedOrders, string ppsToIRMSDownloadFolderBkFolder, string[] ppsDownloadFileFormats, string filePrefix, string fileFormat)
        {
            Dictionary<string, string> filesPathContent = new Dictionary<string, string>();
            try
            {
                DirectoryInfo dinfo = new DirectoryInfo(ppsToIRMSDownloadFolder);
                FileInfo[] Files = null;

                foreach (string fileformat in ppsDownloadFileFormats)
                {
                    if (this.ppsMaxFilesToConsider > 0)
                    {
                        if (ppsDownloadFileFormats.Count() > 0 && ppsDownloadFileFormats[0] != "" && ppsDownloadFileFormats[0] != "*")
                            Files = dinfo.GetFiles(fileformat).OrderBy(c => c.CreationTime).Take(this.ppsMaxFilesToConsider).ToArray();
                        else
                            Files = dinfo.GetFiles().OrderBy(c => c.CreationTime).Take(this.ppsMaxFilesToConsider).ToArray();
                    }
                    else
                    {
                        if (ppsDownloadFileFormats.Count() > 0 && ppsDownloadFileFormats[0] != "" && ppsDownloadFileFormats[0] != "*")
                            Files = dinfo.GetFiles(fileformat).OrderBy(c => c.CreationTime).ToArray();
                        else
                            Files = dinfo.GetFiles().OrderBy(c => c.CreationTime).ToArray();
                    }
                    //
                    foreach (FileInfo file in Files)
                    {
                        if(!file.Name.StartsWith(filePrefix, StringComparison.InvariantCultureIgnoreCase))
                        {
                            File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + file.FullName + ": File name is not in format " + fileFormat });
                            file.CopyTo(Path.Combine(ppsToIRMSDownloadFolderBkFolder, file.Name), true);
                            file.CopyTo(Path.Combine(ppsToIRMSDownloadFolderFailedOrders, file.Name), true);
                        }
                        else
                        {
                            filesPathContent.Add(file.Name, File.ReadAllText(file.FullName));
                            _logger.Debug(file.FullName + " Moved file to Processing");
                            file.CopyTo(Path.Combine(ppsToIRMSDownloadFolderEcho, file.Name), true);
                        }
                        file.Delete();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.ErrorFormat("Error at PickPackShip::getAllOrdersFilePath() " + ex.Message);
            }
            return filesPathContent;
        }
        public void MoveFileToBackOrErrorFolder(string type, string FileName, string fromFolder, string toFolder, string resultCode = null, string resultMsg = null)
        {
            DirectoryInfo dinfo = new DirectoryInfo(fromFolder);
            FileInfo[] Files = dinfo.GetFiles(FileName);
            foreach (FileInfo file in Files)
            {
                if (type.Equals("echo"))
                {
                    file.CopyTo(Path.Combine(toFolder, file.Name.Replace("DPPS", "EPPS").Replace("dpps", "epps")), true);
                    File.AppendAllText(Path.Combine(toFolder, file.Name.Replace("DPPS", "EPPS").Replace("dpps", "epps")), resultCode + " " + resultMsg);
                    file.Delete();
                }
                else if (type.Equals("bk"))
                    file.CopyTo(Path.Combine(toFolder, file.Name), true);
                else
                {
                    file.CopyTo(Path.Combine(toFolder, file.Name), true);
                    file.Delete();
                }
            }          
            
        }

        #region Methods.Private.APICall
        public JObject PostData(string dataString, string url, bool isArrayResult)
        {
            JObject outputData = new JObject();
            try
            {
                WebClient webClientProxy = new WebClient();

                webClientProxy.Headers["Content-type"] = "application/json";

                MemoryStream mStream = new MemoryStream();

                DataContractJsonSerializer serializerToUplaod = new DataContractJsonSerializer(typeof(string));

                _logger.Debug("PPS Job URL : " + url);
                _logger.Debug("PPS Job Request : " + dataString);

                serializerToUplaod.WriteObject(mStream, dataString);

                byte[] data;
                data = webClientProxy.UploadData(url, "POST", mStream.ToArray());

                MemoryStream stream = new MemoryStream(data);
                DataContractJsonSerializer obj = new DataContractJsonSerializer(typeof(string));

                string result = obj.ReadObject(stream) as string;

                _logger.Debug("PPS Job Response : " + result);

                if (isArrayResult)
                {
                    JArray outputDataArray = JArray.Parse(result);
                    foreach (JObject jObj in outputDataArray)
                    {
                        outputData = jObj;
                        break;
                    }
                }
                else
                {
                    outputData = JObject.Parse(result);
                }
            }
            catch (Exception ex)
            {
                _logger.ErrorFormat("Error at PickPackShip::PostData() " + ex.Message);
                outputData.Add("result_msg", ex.Message);
            }
            return outputData;
        }

        #endregion

        #endregion
    }
}
