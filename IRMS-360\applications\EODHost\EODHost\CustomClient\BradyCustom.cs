﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using log4net;
using Newtonsoft.Json.Linq;
using System.Net;

namespace Upp.Irms.EOD.Host.CustomClient
{
    class BradyCustom
    {
        ILog _logger = LogManager.GetLogger(typeof(BradyCustom));
        public string CallExactaApi(string dataString, string url, bool isArrayResult)
        {
            string errorMessage = string.Empty;
            JObject result = PutData(dataString, url, isArrayResult);
            // JObject result = MockResponseFromExacta();
            JToken errorContent = "";
            JToken error = "";
            #region result
            if (result != null && result.ContainsKey("Code"))
            {
                if (result["Code"].ToString() == "200")
                {
                    _logger.Debug("Success");
                    errorMessage = "success";
                }
                else if (result["Code"].ToString() != "404" && result.ContainsKey("Content"))
                {
                    errorContent = result["Content"];
                    error = errorContent["error"];
                }
                else if (result.ContainsKey("error"))
                    error = result["error"];

                if (result["Code"].ToString() == "400")
                {
                    _logger.Debug("Result Code:" + result["Code"].ToString());
                    if (error != null)
                    {
                        _logger.Debug("Result Message:" + error.ToString());
                        errorMessage = error.ToString();
                    }
                    else
                        errorMessage = "BAD REQUEST";
                }
                else if (result["Code"].ToString() == "401")
                {
                    _logger.Debug("Result Code:" + result["Code"].ToString());
                    if (error != null)
                    {
                        _logger.Debug("Result Message:" + error.ToString());
                        errorMessage = error.ToString();
                    }
                    else
                        errorMessage = "UNAUTHORIZED";
                }
                else if (result["Code"].ToString() == "500")
                {
                    _logger.Debug("Result Code:" + result["Code"].ToString());
                    if (error != null)
                    {
                        _logger.Debug("Result Message:" + error.ToString());
                        errorMessage = error.ToString();
                    }
                    else
                        errorMessage = "INTERNAL SERVER ERROR";
                }
                else if (result["Code"].ToString() == "404")
                {
                    _logger.Debug("Result Code:" + result["Code"].ToString());
                    if (result["result_msg"] != null)
                    {
                        _logger.Debug("Result Message:" + error.ToString());
                        errorMessage = result["result_msg"].ToString();

                    }
                    else
                    {
                        errorMessage = "Endpoint not found";
                        _logger.Debug("exactaApiOutput is null");
                    }
                }
            }
            else
            {
                errorMessage = "exactaApiOutput is null";
                _logger.Debug("exactaApiOutput is null");
            }
            #endregion
            return errorMessage;
        }

        public JObject PutData(string dataString, string url, bool isArrayResult)
        {
            _logger.Debug("PutData - START");

            JObject outputData = new JObject();
            try
            {
                _logger.Debug("JSON Request =" + dataString);
                WebClient webClientProxy = new WebClient();

                webClientProxy.Headers["Content-type"] = "application/json";

                _logger.Debug("URL " + url);
                _logger.Debug("Before UploadData call");
                    string result = webClientProxy.UploadString(url, "PUT", dataString);
                _logger.Debug("After UploadData call");

            }
            catch (Exception ex)
            {
                _logger.ErrorFormat("Error at Exacta Put interface " + ex.Message);
                if (!string.IsNullOrEmpty(ex.StackTrace)) _logger.Error("Stacktrace :" + ex.StackTrace);
                outputData.Add("result_msg", ex.Message);
                outputData.Add("Code", "404");
            }
            _logger.Debug("PutData - FINISH");
            return outputData;
        }
    }
}
