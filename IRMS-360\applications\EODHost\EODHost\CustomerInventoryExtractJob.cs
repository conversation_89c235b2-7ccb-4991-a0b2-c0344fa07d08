﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;

using log4net;

using Quartz;

using NHibernate;
using NHibernate.Criterion;
using NHibernate.SqlCommand;
using NHibernate.Transform;

using Upp.Irms.Constants;
using Upp.Irms.Core;
using Upp.Irms.Domain;
using Upp.Shared.Application;
using Upp.Shared.Utilities;

namespace Upp.Irms.EOD.Host
{
    public class CustomerInventoryExtractJob : IJob
    {
        #region Fields

        ILog _logger = LogManager.GetLogger(typeof(CustomerInventoryExtractJob));

        const string JParam_Title = "TITLE";
        string title = "";
        const string JParam_Format = "FORMAT";
        string format = "";
        const string JParam_Customers = "CUSTOMERS";
        string customers = "";
        const string JParam_ShowHeader = "SHOWHEADER";
        bool showHeader = false;
        const string JParam_ShowFooter = "SHOWFOOTER";
        bool showFooter = false;
        const string JParam_Warehouses = "WAREHOUSES";
        string warehouses = "";
        const string JParam_nextJob = "NEXTJOB";
        string nextJob = "";
        const string JParam_FieldDelimiter = "FIELDDELIMITER";
        string fieldDelimiter = "";
        const string JParam_FlatFileDestination = "FLATFILEDESTINATION";
        string flatFileDestination = "";
        string jobname = "Customer Inventory Extract Job";

        #endregion

        #region Constructor

        /// <summary> 
        /// Empty constructor for job initilization
        /// <p>
        /// Quartz requires a public empty constructor so that the
        /// scheduler can instantiate the class whenever it needs.
        /// </p>
        /// </summary>
        public CustomerInventoryExtractJob()
        {
        }

        #endregion

        #region Methods.Public

        /// <summary> 
        /// Called by the <see cref="IScheduler" /> when a
        /// <see cref="Trigger" /> fires that is associated with
        /// the <see cref="IJob" />.
        /// </summary>
        public virtual void Execute(JobExecutionContext context)
        {
            if (JobParametersAreValid(context.MergedJobDataMap) == false)
            {
                JobExecutionException jex = new JobExecutionException("Missing job parameter");
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }

            ParseJobParameters(context.MergedJobDataMap);

            ExtractCustomerInventory();
            NextJobScheduling(jobname);
        }

        #endregion

        #region Methods.Private

        private bool JobParametersAreValid(JobDataMap jobParams)
        {
            bool validity = true;

            //if (!jobParams.Contains(JParam_Title))
            //{
            //    if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_Title);
            //    validity = false;
            //}
            if (!jobParams.Contains(JParam_Format))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_Format);
                validity = false;
            }
            //if (!jobParams.Contains(JParam_ShowHeader))
            //{
            //    if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_ShowHeader);
            //    validity = false;
            //}
            //if (!jobParams.Contains(JParam_ShowFooter))
            //{
            //    if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_ShowFooter);
            //    validity = false;
            //}            

            if (!jobParams.Contains(JParam_Warehouses))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_Warehouses);
                validity = false;
            }

            if (!jobParams.Contains(JParam_Customers))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_Customers);
                validity = false;
            }

            //if (!jobParams.Contains(JParam_nextJob))
            //{
            //    if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_nextJob);
            //    validity = false;
            //}
            return validity;
        }

        private void ParseJobParameters(JobDataMap jobParams)
        {
            //Ensure that we catch all other types of exceptions
            // and throw only a JobExecutionException
            try
            {
                //map the parameters to jobParams
                if (jobParams.Contains(JParam_Title))
                    this.title = jobParams.GetString(JParam_Title);

                this.format = jobParams.GetString(JParam_Format);

                if (jobParams.Contains(JParam_Customers))
                    this.customers = jobParams.GetString(JParam_Customers);

                if (jobParams.Contains(JParam_ShowHeader))
                    this.showHeader = jobParams.GetBoolean(JParam_ShowHeader);

                if (jobParams.Contains(JParam_ShowFooter))
                    this.showFooter = jobParams.GetBoolean(JParam_ShowFooter);

                this.warehouses = jobParams.GetString(JParam_Warehouses);

                if (jobParams.Contains(JParam_nextJob))
                    this.nextJob = jobParams.GetString(JParam_nextJob);

                if (jobParams.Contains(JParam_FieldDelimiter))
                    this.fieldDelimiter = jobParams.GetString(JParam_FieldDelimiter);

                if (jobParams.Contains(JParam_FlatFileDestination))
                    this.flatFileDestination = jobParams.GetString(JParam_FlatFileDestination);
            }
            catch (Exception ex)
            {
                JobExecutionException jex = new JobExecutionException("Invalid data type in job parameters", ex);
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }
        }

        private void NextJobScheduling(string jobname)
        {

            if (!String.IsNullOrWhiteSpace(nextJob))
            {
                if (_logger.IsDebugEnabled) _logger.DebugFormat("         »  NextJob:  {0}", nextJob);
                try
                {
                    string[] jobKeys = Service.Instance.Scheduler.GetJobNames("Manual");
                    foreach (string jobKey in jobKeys)
                    {
                        if (jobKey.Equals(nextJob))
                        {
                            SimpleTrigger trigger = new SimpleTrigger();
                            trigger.Name = nextJob + "Trigger";
                            trigger.JobName = nextJob;
                            trigger.JobGroup = "Manual";
                            trigger.Group = "Manual";
                            trigger.StartTimeUtc = DateTime.UtcNow.AddSeconds(30);
                            trigger.RepeatCount = 0;
                            trigger.RepeatInterval = TimeSpan.Zero;
                            Service.Instance.Scheduler.RescheduleJob(nextJob + "Trigger", "Manual", trigger);
                            if (_logger.IsDebugEnabled) _logger.DebugFormat("{1} - Next Job {0} is scheduled: ", nextJob, jobname);
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (_logger.IsErrorEnabled) _logger.ErrorFormat("{1} - Next Job error: {0}", ex.Message, jobname);
                }
            }
        }

        private void ExtractCustomerInventory()
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(warehouses))
                {
                    char[] fieldFormatSeparator = { ';' };
                    char[] formatSeparator = { ':' };

                    warehouses = warehouses.TrimEnd(fieldFormatSeparator);
                    string[] rowFormats = warehouses.Split(fieldFormatSeparator);

                    if (string.IsNullOrWhiteSpace(this.customers))
                    {
                        if (_logger.IsErrorEnabled) _logger.ErrorFormat("         »  Customers are not configured ");
                        return;
                    }

                    foreach (string rowFormat in rowFormats)
                    {
                        string[] fieldFormatDescriptions = rowFormat.Split(formatSeparator);

                        string companyCode = fieldFormatDescriptions[0];
                        string[] companyLocationCodes = fieldFormatDescriptions[1].Split(new char[] { ',' });

                        string filePath = System.Configuration.ConfigurationManager.AppSettings["FileDestination"].ToString();
                        if (!string.IsNullOrWhiteSpace(this.flatFileDestination))
                        {
                            filePath = this.flatFileDestination;
                        }

                        char[] delimit = { ',' };
                        String[] customersList = this.customers.Split(delimit);

                        if (_logger.IsDebugEnabled) _logger.DebugFormat("         »  Warehouses Count is {0} for the Company : {1}", companyLocationCodes.Length.ToString(), companyCode);

                        foreach (string companylocCode in companyLocationCodes)
                        {
                            foreach (string customer in customersList)
                            {
                                using (UnitWrapper wrapper = new UnitWrapper())
                                {
                                    wrapper.Execute(() =>
                                    {
                                        String customerName = "";

                                        DetachedCriteria customerQuery = DetachedCriteria.For<Customer>()
                                            .CreateAlias("CompanyLocationType", "lt", JoinType.LeftOuterJoin)
                                               .SetProjection(Projections.ProjectionList()
                                               .Add(Projections.Property("Name"), "Name"))
                                               .Add(Restrictions.Eq("lt.CompanyLocationCode", companylocCode))
                                               .Add(Restrictions.Eq("CustomerCode", customer))
                                               .SetResultTransformer(Transformers.AliasToBean<Customer>());

                                        IList<Customer> customers = Repositories.Get<Customer>().List(customerQuery);

                                        if (customers != null && customers.Count > 0)
                                        {
                                            customerName = customers[0].Name;

                                            DetachedCriteria queryTransactionDate = DetachedCriteria.For<ItemTransaction>()
                                                                .CreateAlias("InventoryItem", "InventoryItem")
                                                                .SetProjection(Projections.Max("Occurred"))
                                                                .Add(Restrictions.EqProperty("InventoryItem.Item.Id", "ParentItem.Id"));

                                            DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>("_root")
                                                        .CreateAlias("Item", "ParentItem")
                                                        .CreateAlias("Customer", "Customer")
                                                        .CreateAlias("StatusCode", "StatusCode", JoinType.LeftOuterJoin)
                                                        .CreateAlias("CompanyLocationType", "lt", JoinType.LeftOuterJoin)
                                                        .CreateAlias("lt.CompanyLocation", "cl", JoinType.LeftOuterJoin)
                                                        .CreateAlias("cl.Company", "c", JoinType.LeftOuterJoin)
                                                        .SetProjection(Projections.ProjectionList()
                                                        .Add(Projections.GroupProperty("ParentItem.Id"), "Id")
                                                        .Add(Projections.GroupProperty("ParentItem.ItemCode"), "ItemCode")
                                                        .Add(Projections.GroupProperty("ParentItem.Description"), "ItemDescription")
                                                        .Add(Projections.GroupProperty("StatusCode.Description"), "StatusCodeDescription")
                                                        .Add(Projections.SubQuery(queryTransactionDate), "DateModified")
                                                        .Add(Projections.GroupProperty("ParentItem.DivisionCode"), "LocationCode")
                                                        .Add(Projections.Sum(Projections.Conditional(Restrictions.Eq("StatusCode.Code", "A"), Projections.Property("Quantity"), Projections.Cast(NHibernateUtil.Decimal, Projections.Constant(0)))), "AvailableQuantity")
                                                        .Add(Projections.GroupProperty("c.CompanyCode"), "CompanyCode")
                                                        .Add(Projections.GroupProperty("lt.CompanyLocationCode"), "CompanyLocationCode"))
                                                        .Add(Restrictions.Eq("lt.CompanyLocationCode", companylocCode))
                                                        .Add(Restrictions.Eq("c.CompanyCode", companyCode))
                                                        .Add(Restrictions.Eq("Customer.CustomerCode", customer))
                                                        .SetResultTransformer(Transformers.AliasToBean<InventoryItem>());

                                            IList<InventoryItem> inventoryItems = Repositories.Get<InventoryItem>().List(criteria);

                                            List<int> itemIds = inventoryItems.Select(c => c.Id.Value).ToList<int>();

                                            DetachedCriteria detachedOrdStatus = DetachedCriteria.For<OrderStatus>("os")
                                                      .CreateAlias("os.StatusCode", "StatusCodes")
                                                      .SetProjection(Projections.Property("os.Id"))
                                                      .Add(Expression.EqProperty("ordr.Id", "os.OrderHeader.Id"))
                                                      .AddOrder(new Order("os.Occurred", false))
                                                      .AddOrder(new Order("StatusCodes.SortOrder", false))
                                                      .SetMaxResults(1);
                                            List<OrderDetail> reservedQuantityList = new List<OrderDetail>();
                                            List<OrderDetail> demandQuantityList = new List<OrderDetail>();

                                            int ItemsThresholdValue = 1500;
                                            if (itemIds != null)
                                            {
                                                int startIndex = 0, length = ItemsThresholdValue;
                                                do
                                                {

                                                    if (itemIds.Count < startIndex + length)
                                                    {
                                                        length = itemIds.Count - startIndex;
                                                    }


                                                    DetachedCriteria queryReserved = DetachedCriteria.For<OrderDetail>()
                                                        .CreateAlias("OrderHeader", "ordr", JoinType.LeftOuterJoin)
                                                        .CreateAlias("ordr.OrderStatuses", "OrderStatuses", JoinType.LeftOuterJoin)
                                                        .CreateAlias("OrderStatuses.StatusCode", "StatusCode")
                                                        .SetProjection(Projections.ProjectionList()
                                                        .Add(Projections.GroupProperty("Item.Id"), "Id")
                                                        .Add(Projections.Sum("Quantity"), "Quantity"))
                                                        .Add(Expression.Eq("StatusCode.Code", "DIST"))
                                                        .Add(Subqueries.PropertyEq("OrderStatuses.Id", detachedOrdStatus))
                                                        .Add(Restrictions.In("Item.Id", itemIds.GetRange(startIndex, length)))
                                                        .SetResultTransformer(Transformers.AliasToBean<OrderDetail>());

                                                    IList<OrderDetail> reservedQuantity = Repositories.Get<OrderDetail>().List(queryReserved);
                                                    if (reservedQuantity != null && reservedQuantity.Count > 0)
                                                        reservedQuantityList.AddRange(reservedQuantity);


                                                    DetachedCriteria queryDemand = DetachedCriteria.For<OrderDetail>()
                                                         .CreateAlias("OrderHeader", "ordr", JoinType.LeftOuterJoin)
                                                         .CreateAlias("ordr.OrderStatuses", "OrderStatuses", JoinType.LeftOuterJoin)
                                                         .CreateAlias("OrderStatuses.StatusCode", "StatusCode")
                                                         .SetProjection(Projections.ProjectionList()
                                                         .Add(Projections.GroupProperty("Item.Id"), "Id")
                                                         .Add(Projections.Sum("Quantity"), "Quantity"))
                                                         .Add(Expression.Eq("StatusCode.Code", "O"))
                                                         .Add(Subqueries.PropertyEq("OrderStatuses.Id", detachedOrdStatus))
                                                         .Add(Restrictions.In("Item.Id", itemIds.GetRange(startIndex, length)))
                                                         .SetResultTransformer(Transformers.AliasToBean<OrderDetail>());

                                                    IList<OrderDetail> demandQuantity = Repositories.Get<OrderDetail>().List(queryDemand);


                                                    if (demandQuantity != null && demandQuantity.Count > 0)
                                                        demandQuantityList.AddRange(demandQuantity);

                                                    startIndex += length;
                                                } while (startIndex < itemIds.Count);

                                                foreach (OrderDetail ordDetail in reservedQuantityList)
                                                {
                                                    InventoryItem item = inventoryItems.Where(c => c.Id == ordDetail.Id).FirstOrDefault();
                                                    item.ReservedQuantity = ordDetail.Quantity;
                                                }

                                                foreach (OrderDetail ordDetail in demandQuantityList)
                                                {
                                                    InventoryItem item = inventoryItems.Where(c => c.Id == ordDetail.Id).FirstOrDefault();
                                                    item.DemandQuantity = ordDetail.Quantity;
                                                }

                                                foreach (InventoryItem item in inventoryItems)
                                                {
                                                    item.OnHandQuantity = item.AvailableQuantity - item.ReservedQuantity;
                                                }
                                            }



                                            string fileName = string.Format("{0}-Inventory-{1:yyMMdd}-{1:HHmmss}.{2}", customerName, DateTime.Now, "csv");

                                            FlatFileGenerator fg = new FlatFileGenerator();
                                            fg.Prepare(filePath, this.format, this.showHeader, this.showFooter, this.title, this.fieldDelimiter, (List<InventoryItem>)inventoryItems, fileName);
                                        }

                                    });
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
            }
        }

        #endregion
    }
}
