﻿using Quartz;
using System;
using Upp.Irms.Domain;
using System.Collections.Generic;
using Upp.Irms.Core;
using System.Linq;
using NHibernate.Criterion;
using NHibernate.SqlCommand;
using Upp.Shared.Application;
using Upp.Shared.Utilities;
using Upp.Irms.Constants;
using NHibernate.Transform;
using System.Configuration;
using System.Collections;
using log4net;
using System.Text;
using Upp.Irms.Services.Integration.DataContracts;
using Newtonsoft.Json;
using Upp.Irms.EOD.Host.CustomClient;
using Newtonsoft.Json.Linq;

namespace Upp.Irms.EOD.Host
{
    public class CycleCountSetupJob : IJob
    {
        #region Fields

        const string JParam_Warehouses = "WAREHOUSES";
        string warehouses = "";
        const string JParam_nextJob = "NEXTJOB";
        string nextJob = "";
        string jobname = "CycleCount SetUp Job";
        ILog _logger = LogManager.GetLogger(typeof(CycleCountSetupJob));
        List<InventoryTask> inventoryTasks = new List<InventoryTask>();

        #endregion

        #region Constructor

        public CycleCountSetupJob()
        {
        }
        #endregion

        #region Methods.Public

        public virtual void Execute(JobExecutionContext context)
        {
            if (JobParametersAreValid(context.MergedJobDataMap) == false)
            {
                JobExecutionException jex = new JobExecutionException("Missing job parameter");
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }

            ParseJobParameters(context.MergedJobDataMap);

            DoCycleCountSetUp();
            NextJobScheduling(jobname);
        }

        #endregion

        #region Methods.Private

        private void NextJobScheduling(string jobname)
        {

            if (!String.IsNullOrWhiteSpace(nextJob))
            {
                if (_logger.IsDebugEnabled) _logger.DebugFormat("         »  NextJob:  {0}", nextJob);
                try
                {
                    string[] jobKeys = Service.Instance.Scheduler.GetJobNames("Manual");
                    foreach (string jobKey in jobKeys)
                    {
                        if (jobKey.Equals(nextJob))
                        {
                            SimpleTrigger trigger = new SimpleTrigger();
                            trigger.Name = nextJob + "Trigger";
                            trigger.JobName = nextJob;
                            trigger.JobGroup = "Manual";
                            trigger.Group = "Manual";
                            trigger.StartTimeUtc = DateTime.UtcNow.AddSeconds(30);
                            trigger.RepeatCount = 0;
                            trigger.RepeatInterval = TimeSpan.Zero;
                            Service.Instance.Scheduler.RescheduleJob(nextJob + "Trigger", "Manual", trigger);
                            if (_logger.IsDebugEnabled) _logger.DebugFormat("{1} - Next Job {0} is scheduled: ", nextJob, jobname);
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (_logger.IsErrorEnabled) _logger.ErrorFormat("{1} - Next Job error: {0}", ex.Message, jobname);
                }
            }
        }
        private bool JobParametersAreValid(JobDataMap jobParams)
        {
            bool validity = true;

            if (!jobParams.Contains(JParam_Warehouses))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_Warehouses);
                validity = false;
            }

            if (!jobParams.Contains(JParam_nextJob))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_nextJob);
                validity = false;
            }
            return validity;
        }
        // helper method to parse job parameters
        private void ParseJobParameters(JobDataMap jobParams)
        {
            //Ensure that we catch all other types of exceptions
            // and throw only a JobExecutionException
            try
            {
                //map the parameters to jobParams               
                this.warehouses = jobParams.GetString(JParam_Warehouses);
                this.nextJob = jobParams.GetString(JParam_nextJob);
            }
            catch (Exception ex)
            {
                JobExecutionException jex = new JobExecutionException("Invalid data type in job parameters", ex);
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }
        }


        #region CycleCountSetUp Methods


        private void DoCycleCountSetUp()
        {
            try
            {
                IList<CompanyStratification> items = new List<CompanyStratification>();

                if (!string.IsNullOrWhiteSpace(warehouses))
                {
                    char[] fieldFormatSeparator = { ';' };
                    char[] formatSeparator = { ':' };

                    warehouses = warehouses.TrimEnd(fieldFormatSeparator);
                    string[] rowFormats = warehouses.Split(fieldFormatSeparator);

                    foreach (string rowFormat in rowFormats)
                    {
                        string[] fieldFormatDescriptions = rowFormat.Split(formatSeparator);

                        string companyCode = fieldFormatDescriptions[0];
                        string[] companyLocationCodes = fieldFormatDescriptions[1].Split(new char[] { ',' });

                        if (_logger.IsDebugEnabled) _logger.DebugFormat("         »  Warehouses Count is {0} for the Company : {1}", companyLocationCodes.Length.ToString(), companyCode);

                        foreach (string companylocCode in companyLocationCodes)
                        {
                            Task task = null;
                            CompanyLocationType company = null;
                            string username = "eod_manager";
                            TransactionType cycle = null;
                            StatusCode availableStatus = null;
                            IList<InventoryCountProfile> countProfileSettings = null;

                            using (UnitWrapper wrapper = new UnitWrapper())
                            {
                                wrapper.Execute(() =>
                                {
                                    items = GetItemstoCycleCount(companyCode, companylocCode);
                                    cycle = Entity.Retrieve<TransactionType>(InventoryTransactions.CycleCountItem);
                                    availableStatus = Entity.Retrieve<StatusCode>(InventoryStatuses.Available);
                                    company = GetCompanyLocationType(companylocCode);
                                    username = "eod_manager";

                                    //get inventory count profile settings.
                                    if (countProfileSettings == null) countProfileSettings = GetInventoryCountProfiles(companyCode, companylocCode);
                                });
                            }
                            List<CompanyStratification> itemsWithRequiredCounts = new List<CompanyStratification>();
                            if (countProfileSettings != null && countProfileSettings.Count > 0 && !string.IsNullOrEmpty(countProfileSettings[0].DailyCycleCountType))
                            {
                                if ("M".Equals(countProfileSettings[0].DailyCycleCountType))
                                {
                                    if (_logger.IsDebugEnabled) _logger.Debug("Inventory Count profile is set to Max counts");
                                    if (_logger.IsDebugEnabled) _logger.Debug("Max A Count is set to " + countProfileSettings[0].MaxACounts.ToString());
                                    if (_logger.IsDebugEnabled) _logger.Debug("Max B Count is set to " + countProfileSettings[0].MaxBCounts.ToString());
                                    if (_logger.IsDebugEnabled) _logger.Debug("Max C Count is set to " + countProfileSettings[0].MaxCCounts.ToString());
                                    if (_logger.IsDebugEnabled) _logger.Debug("Max D Count is set to " + countProfileSettings[0].MaxDCounts.ToString());
                                    if (countProfileSettings[0].MaxACounts > 0) itemsWithRequiredCounts.AddRange(items.Where(c => c.StratificationCode == "A").Take(countProfileSettings[0].MaxACounts).ToList());
                                    if (countProfileSettings[0].MaxBCounts > 0) itemsWithRequiredCounts.AddRange(items.Where(c => c.StratificationCode == "B").Take(countProfileSettings[0].MaxBCounts).ToList());
                                    if (countProfileSettings[0].MaxCCounts > 0) itemsWithRequiredCounts.AddRange(items.Where(c => c.StratificationCode == "C").Take(countProfileSettings[0].MaxCCounts));
                                    if (countProfileSettings[0].MaxDCounts > 0) itemsWithRequiredCounts.AddRange(items.Where(c => c.StratificationCode == "D").Take(countProfileSettings[0].MaxDCounts).ToList());
                                }
                                else if ("R".Equals(countProfileSettings[0].DailyCycleCountType))
                                {
                                    if (_logger.IsDebugEnabled) _logger.Debug("Inventory Count profile is set to ABC Rotation");
                                    int TotalItems;
                                    int StratificationItemCount;
                                    if (countProfileSettings[0].AbcADays > 0)
                                    {
                                        TotalItems = items.Count(c => c.StratificationCode.Equals("A"));
                                        StratificationItemCount = Converter.ToInt32(Math.Ceiling(Converter.ToDecimal(TotalItems) / Converter.ToDecimal(countProfileSettings[0].AbcADays)));
                                        itemsWithRequiredCounts.AddRange(items.Where(c => c.StratificationCode == "A").Take(StratificationItemCount).ToList());
                                        if (_logger.IsDebugEnabled) _logger.Debug("Total A items : " + TotalItems.ToString());
                                        if (_logger.IsDebugEnabled) _logger.Debug("A days is set to : " + countProfileSettings[0].AbcADays.ToString());
                                        if (_logger.IsDebugEnabled) _logger.Debug("Max count for A items is based on the above counts is Total A items/A days is set to i,e " + StratificationItemCount.ToString());
                                    }
                                    if (countProfileSettings[0].AbcBDays > 0)
                                    {
                                        TotalItems = items.Count(c => c.StratificationCode.Equals("B"));
                                        StratificationItemCount = Converter.ToInt32(Math.Ceiling(Converter.ToDecimal(TotalItems) / Converter.ToDecimal(countProfileSettings[0].AbcBDays)));
                                        itemsWithRequiredCounts.AddRange(items.Where(c => c.StratificationCode == "B").Take(StratificationItemCount).ToList());
                                        if (_logger.IsDebugEnabled) _logger.Debug("Total B items : " + TotalItems.ToString());
                                        if (_logger.IsDebugEnabled) _logger.Debug("B days is set to : " + countProfileSettings[0].AbcBDays.ToString());
                                        if (_logger.IsDebugEnabled) _logger.Debug("Max count for B items is based on the above counts is Total B items/B days is set to i,e " + StratificationItemCount.ToString());
                                    }
                                    if (countProfileSettings[0].AbcCDays > 0)
                                    {
                                        TotalItems = items.Count(c => c.StratificationCode.Equals("C"));
                                        StratificationItemCount = Converter.ToInt32(Math.Ceiling(Converter.ToDecimal(TotalItems) / Converter.ToDecimal(countProfileSettings[0].AbcCDays)));
                                        itemsWithRequiredCounts.AddRange(items.Where(c => c.StratificationCode == "C").Take(StratificationItemCount).ToList());
                                        if (_logger.IsDebugEnabled) _logger.Debug("Total C items : " + TotalItems.ToString());
                                        if (_logger.IsDebugEnabled) _logger.Debug("C days is set to : " + countProfileSettings[0].AbcCDays.ToString());
                                        if (_logger.IsDebugEnabled) _logger.Debug("Max count for C items is based on the above counts is Total C items/C days is set to i,e " + StratificationItemCount.ToString());
                                    }
                                    if (countProfileSettings[0].AbcDDays > 0)
                                    {
                                        TotalItems = items.Count(c => c.StratificationCode.Equals("D"));
                                        StratificationItemCount = Converter.ToInt32(Math.Ceiling(Converter.ToDecimal(TotalItems) / Converter.ToDecimal(countProfileSettings[0].AbcDDays)));
                                        itemsWithRequiredCounts.AddRange(items.Where(c => c.StratificationCode == "D").Take(StratificationItemCount).ToList());
                                        if (_logger.IsDebugEnabled) _logger.Debug("Total D items : " + TotalItems.ToString());
                                        if (_logger.IsDebugEnabled) _logger.Debug("D days is set to : " + countProfileSettings[0].AbcDDays.ToString());
                                        if (_logger.IsDebugEnabled) _logger.Debug("Max count for D items is based on the above counts is Total D items/D days is set to i,e " + StratificationItemCount.ToString());
                                    }

                                }

                            }
                            foreach (CompanyStratification comStrat in itemsWithRequiredCounts)
                            {
                                int retryCount = 0;
                              while (retryCount < 3)
                              {
                                    retryCount++;
                                using (UnitWrapper wrapper = new UnitWrapper())
                                {
                                    wrapper.Execute(() =>
                                    {
                                        // Get ItemBy Id
                                        DetachedCriteria criteria = DetachedCriteria.For<Item>()
                                                                       .Add(Restrictions.Eq("Id", comStrat.Id.Value));
                                        IList<Item> item = Repositories.Get<Item>().List(criteria);
                                        //
                                        if (item != null && item.Count > 0)
                                        {
                                            task = CreateCycleCount(item[0], companyCode, companylocCode, task, company, cycle, username, availableStatus, countProfileSettings, itemsWithRequiredCounts);
                                        }
                                    });
                                        if(!String.IsNullOrEmpty(wrapper.Error) && wrapper.Error.StartsWith("Row was updated or deleted"))
                                        {
                                            //if (_logger.IsDebugEnabled) _logger.Error(wrapper.Error);
                                            if (_logger.IsDebugEnabled) _logger.Debug("Retrying to create task");
                                            task = null;
                                        }
                                        else  retryCount = 3;
                                }
                              }
                            }
                        }
                    }
                    String serverName = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["ExactaServerName"]);
                    if (!String.IsNullOrEmpty(serverName) && inventoryTasks != null && inventoryTasks.Count > 0)
                    {
                        SendCycleCountDetailsToExacta(serverName, inventoryTasks);
                        inventoryTasks = new List<InventoryTask>();
                    }
                }
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
            }
        }
        private CompanyLocationType GetCompanyLocationType(string compLocTypeId)
        {
            CompanyLocationType companyLocType = null;

            DetachedCriteria query = DetachedCriteria.For<CompanyLocationType>()
                                 .Add(new SimpleExpression("CompanyLocationCode", compLocTypeId, "="));

            IList<CompanyLocationType> list = Repositories.Get<CompanyLocationType>().List(query);
            if (list.Count > 0 && list != null)
            {
                return list[0];
            }
            return companyLocType;
        }

        private Task CreateCycleCount(Item item, string companyCode, string companylocCode, Task task, CompanyLocationType warehouse, TransactionType cycle, String username, StatusCode availableStatus, IList<InventoryCountProfile> countProfiles, IList<CompanyStratification> itemsToCycleCount)
        {
            int maxDailyCount = 0;
            DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
                                                         .CreateAlias("CompanyLocationType", "w", JoinType.LeftOuterJoin)
                                                         .CreateAlias("LicensePlate", "lpn", JoinType.LeftOuterJoin)
                                                         .CreateAlias("Location", "l", JoinType.LeftOuterJoin)
                                                         .CreateAlias("w.CompanyLocation", "cl", JoinType.LeftOuterJoin)
                                                         .CreateAlias("cl.Company", "c", JoinType.LeftOuterJoin)
                                                         .CreateAlias("l.CompanyLocationZone", "clz", JoinType.LeftOuterJoin)
                                                         .Add(Restrictions.Eq("StatusCode.Id", availableStatus.Id))
                                                         .Add(Restrictions.Eq("Item.Id", item.Id))
                                                         .Add(!Restrictions.Eq("Quantity", 0M))
                                                         .Add(Restrictions.Eq("w.CompanyLocationCode", companylocCode))
                                                         .Add(Restrictions.Eq("c.CompanyCode", companyCode));

            IList<InventoryItem> assets = Repositories.Get<InventoryItem>().List(criteria);
            if (assets != null && assets.Count > 0)
            {
                if (countProfiles != null && countProfiles.Count > 0 && !string.IsNullOrEmpty(countProfiles[0].DailyCycleCountType))
                {
                    if ("M".Equals(countProfiles[0].DailyCycleCountType))
                    {
                        switch (item.Stratification.StratificationCode)
                        {
                            case "A":
                                maxDailyCount = countProfiles[0].MaxACounts;
                                break;
                            case "B":
                                maxDailyCount = countProfiles[0].MaxBCounts;
                                break;
                            case "C":
                                maxDailyCount = countProfiles[0].MaxCCounts;
                                break;
                            case "D":
                                maxDailyCount = countProfiles[0].MaxDCounts;
                                break;
                            default:
                                maxDailyCount = 0;
                                break;
                        }
                    }
                    else if ("R".Equals(countProfiles[0].DailyCycleCountType))
                    {
                        int totalItems = itemsToCycleCount.Count(c => c.StratificationCode.Equals(item.Stratification.StratificationCode));
                        maxDailyCount = totalItems;
                        //int days = 0;
                        //switch (item.Stratification.StratificationCode)
                        //{
                        //    case "A":
                        //        days = countProfiles[0].AbcADays;
                        //        break;
                        //    case "B":
                        //        days = countProfiles[0].AbcBDays;
                        //        break;
                        //    case "C":
                        //        days = countProfiles[0].AbcCDays;
                        //        break;
                        //    case "D":
                        //        days = countProfiles[0].AbcDDays;
                        //        break;
                        //}
                        //if (days > 0) maxDailyCount = totalItems / days;
                    }
                    //
                    if (maxDailyCount == 0) return task;

                    //get cycle counts set up on that day for particular type.
                    DetachedCriteria criteriaCount = DetachedCriteria.For<InventoryTask>()
                        .CreateAlias("Item", "Item")
                        .CreateAlias("Item.Stratification", "Stratification")
                        .Add(Restrictions.Eq("Stratification.StratificationCode", item.Stratification.StratificationCode))
                        .Add(Restrictions.IsNull("InventoryItem.Id"))
                        .Add(Restrictions.Eq("Quantity", Converter.ToDecimal(0)))
                        .Add(Restrictions.Ge("DateCreated", StartOfDay(DateTime.Today)))
                        .Add(Restrictions.Le("DateCreated", EndOfDay(DateTime.Today)))
                        .SetProjection(Projections.Count("Id"));

                    int setupCounts = Repositories.Get<int>().Count(criteriaCount);
                    if (setupCounts >= maxDailyCount) return task;

                    assets = assets.OrderBy(c => c.DateCreated).ToList<InventoryItem>();
                }
                //
                if (task == null)
                {
                    task = CreateTask(warehouse, username, cycle);
                    Repositories.Get<Task>().Add(task);
                }

                InventoryTask dummyInventoryTask = CreateDummyInventoryTask(task, item, warehouse);
                foreach (InventoryItem element in assets)
                {
                    InventoryTask count = CreateCycleCountInventoryTask(task, element, warehouse);

                    element.CycleCount = "Y";
                    element.DateModified = DateTime.Now;
                    element.UserModified = username;
                    Repositories.Get<InventoryItem>().Update(element);
                    String serverName = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["ExactaServerName"]);
                    if (!String.IsNullOrEmpty(serverName))
                        inventoryTasks.Add(count);
                }
            }

            return task;
        }

        private Task CreateTask(CompanyLocationType warehouse, String userCreated, TransactionType transType)
        {
            Task task = null;
            try
            {
                if (warehouse == null || string.IsNullOrEmpty(userCreated))
                    return null;

                string taskCode = string.Empty;
                StatusCode openTaskStatus = null;

                //openTaskStatus = _session.Retrieve<StatusCode>(TaskStatuses.Open, FunctionalAreas.Task);
                openTaskStatus = Entity.Retrieve<StatusCode>(TaskStatuses.Open);

                //taskCode = GenerateTaskCode(LookupCodes.Task, agency);
                taskCode = Convert.ToString(GenerateTaskCode(EntityCodes.Task, warehouse));

                if (openTaskStatus == null || transType == null || string.IsNullOrEmpty(taskCode))
                    return null;

                task = new Task();
                task.UserCreated = userCreated;
                task.DateCreated = DateTime.Now;
                task.DateModified = null;
                task.UserModified = null;
                task.Requested = DateTime.Now;
                task.Version = 1;
                task.CompanyLocationType = warehouse;
                task.Agency = null;
                task.StatusCode = openTaskStatus;
                task.TransactionType = transType;
                task.TaskCode = taskCode;

            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
            }

            return task;
        }
        public String GenerateTaskCode(EntityCodes code, CompanyLocationType warehouse)
        {
            StringBuilder taskCode = new StringBuilder();
            string formatString;
            //           
            DetachedCriteria criteria = DetachedCriteria.For<EntityCode>()
                                                      .Add(new SimpleExpression("Code", CodeValue.GetCode(code), "="));
            if (warehouse != null) criteria = criteria.Add(new SimpleExpression("CompanyLocationType.Id", warehouse.Id, "="))
                                                     .SetMaxResults(1);

            IList<EntityCode> list = Repositories.Get<EntityCode>().List(criteria);

            if (list == null && list.Count == 0) throw new Exception(String.Format("Unable to find LookupPrimaryKey for '{0}'.", code));

            formatString = list[0].FormatString;

            list[0].DateModified = DateTime.Now;
            list[0].UserModified = "IRMS BL";


            String tempTaskCode = string.Empty;

            list[0].CurrentValue += list[0].IncrementValue;

            if (!string.IsNullOrEmpty(formatString)) tempTaskCode = string.Format(formatString, list[0].CurrentValue);
            else tempTaskCode = string.Format("{0}", list[0].CurrentValue);

            if (!string.IsNullOrEmpty(list[0].Prefix))
            {
                tempTaskCode = list[0].Prefix + tempTaskCode;
            }

            if (taskCode.ToString().Trim().Length > 0)
            {
                taskCode.Append("," + tempTaskCode);
            }
            else
            {
                taskCode.Append(tempTaskCode);
            }

            Repositories.Get<EntityCode>().Update(list[0]);

            return taskCode.ToString();
        }
        private InventoryTask CreateDummyInventoryTask(Task task, Item item, CompanyLocationType warehouse)
        {
            InventoryTask inventoryTask = null;
            StatusCode openTaskStatus = null;
            Priority priority = null;
            String username = String.Empty;

            username = "eod_manager"; //_parameters["UserName"];           
            openTaskStatus = Entity.Retrieve<StatusCode>(TaskStatuses.Open);

            inventoryTask = CreateInventoryTask(item, openTaskStatus, priority, task, username, warehouse);
            Repositories.Get<InventoryTask>().Add(inventoryTask);
            return inventoryTask;
        }
        private InventoryTask CreateInventoryTask(Item item, StatusCode openTaskStatus, Priority priority, Task task, String username, CompanyLocationType warehouse)
        {
            InventoryTask inventoryTask = null;

            if (item == null || openTaskStatus == null || task == null || String.IsNullOrEmpty(username))
                return inventoryTask;

            inventoryTask = new InventoryTask();
            inventoryTask.CompanyLocationType = warehouse;
            inventoryTask.CompanyLocationZoneFrom = item.CompanyLocationZone;
            inventoryTask.StatusCode = openTaskStatus;
            inventoryTask.Priority = priority;
            inventoryTask.LotNumber = item.LotNumber;
            inventoryTask.DateCreated = DateTime.Now;
            inventoryTask.InventoryItem = null;
            inventoryTask.Quantity = 0M;
            inventoryTask.LocationTo = null;
            inventoryTask.Item = item;
            inventoryTask.UserCreated = username;
            inventoryTask.DateCreated = DateTime.Now;
            inventoryTask.DateModified = null;
            inventoryTask.UserModified = null;
            inventoryTask.Version = 1;
            inventoryTask.Task = task;
            //_session.Save(inventoryTask);

            return inventoryTask;
        }
        private InventoryTask CreateCycleCountInventoryTask(Task task, InventoryItem item, CompanyLocationType warehouse)
        {
            InventoryTask inventoryTask = null;
            StatusCode openTaskStatus = null;
            Priority priority = null;
            String username = String.Empty;

            username = "eod_manager"; //_parameters["UserName"];           
            openTaskStatus = Entity.Retrieve<StatusCode>(TaskStatuses.Open);

            inventoryTask = CreateInventoryTask(item, openTaskStatus, priority, task, username, warehouse);
            Repositories.Get<InventoryTask>().Add(inventoryTask);
            return inventoryTask;
        }
        private InventoryTask CreateInventoryTask(InventoryItem item, StatusCode openTaskStatus, Priority priority, Task task, String username, CompanyLocationType warehouse)
        {
            InventoryTask inventoryTask = null;

            if (item == null || openTaskStatus == null || task == null || String.IsNullOrEmpty(username))
                return inventoryTask;

            inventoryTask = new InventoryTask();
            inventoryTask.CompanyLocationType = warehouse;
            if (item.Location != null)
                inventoryTask.CompanyLocationZoneFrom = item.Location.CompanyLocationZone;
            inventoryTask.StatusCode = openTaskStatus;
            inventoryTask.Priority = priority;
            inventoryTask.LotNumber = item.LotNumber;
            inventoryTask.DateCreated = DateTime.Now;
            inventoryTask.InventoryItem = item;
            inventoryTask.Quantity = Convert.ToDecimal(item.Quantity);
            inventoryTask.LocationFrom = item.Location;
            inventoryTask.Item = item.Item;
            inventoryTask.ItemCode = item.Item?.ItemCode;
            inventoryTask.ItemDescription = item.Item?.Description;
            inventoryTask.LocationCodeFrom = item.Location?.LocationCode;
            inventoryTask.UserCreated = username;
            inventoryTask.DateCreated = DateTime.Now;
            inventoryTask.DateModified = null;
            inventoryTask.UserModified = null;
            inventoryTask.Version = 1;
            inventoryTask.Task = task;
            inventoryTask.TaskCode = task.TaskCode;
            inventoryTask.LicensePlate = item.LicensePlate != null ? item.LicensePlate.ParentLicensePlate : null;
            //_session.Save(inventoryTask);

            return inventoryTask;
        }

       

        #region Get Items-Stratification

        private IList<CompanyStratification> GetItemstoCycleCount(string companyCode, string companylocCode)
        {
            DateTime startDate = new DateTime(2012, 1, 1);
            IList<CompanyStratification> cycleCountItems = new List<CompanyStratification>();
            if (IsHoliday(DateTime.Today.AddDays(1))) return cycleCountItems;
            
            DetachedCriteria criteriaLastCount = DetachedCriteria.For<InventoryTask>()
                                                            .CreateAlias("Item", "i")
                                                            .CreateAlias("Task", "Task")
                                                            .CreateAlias("Task.TransactionType", "TransactionType")
                                                            .SetProjection(Projections.ProjectionList()
                                                            .Add(Projections.Max("DateCreated"), "DateCreated")
                                                            .Add(Projections.GroupProperty("i.Id"), "Id"))
                                                            //.Add(Expression.EqProperty("Items.Id", "i.Id"))
                                                            .Add(Restrictions.Eq("Quantity", Convert.ToDecimal(0)))
                                                            .Add(Restrictions.Eq("TransactionType.TransactionTypeCode", "CCI"))
                                                            .SetResultTransformer(Transformers.AliasToBean<InventoryTask>());
            IList<InventoryTask> invTasks = Repositories.Get<InventoryTask>().List(criteriaLastCount);

            DetachedCriteria criteria = DetachedCriteria.For<CompanyStratification>()
                                                            .CreateAlias("Stratification", "Stratification")
                                                            .CreateAlias("Stratification.Items", "Items")
                                                            .CreateAlias("Items.CompanyLocationType", "w", JoinType.LeftOuterJoin)
                                                            .CreateAlias("w.CompanyLocation", "cl", JoinType.LeftOuterJoin)
                                                            .CreateAlias("cl.Company", "c", JoinType.LeftOuterJoin)
                                                            .CreateAlias("CompanyLocationType", "sw")
                                                            .CreateAlias("sw.CompanyLocation", "scl")
                                                            .CreateAlias("scl.Company", "sc")
                                                            .SetProjection(Projections.ProjectionList()
                                                                .Add(Projections.Property("Items.Id"), "Id")
                                                                .Add(Projections.Property("Items.ItemCode"), "ItemCode")
                                                                .Add(Projections.Property("CycleCountRotation"), "CycleCountRotation")
                                                                .Add(Projections.Property("Stratification.Id"), "StratificationId")
                                                                .Add(Projections.Property("Stratification.StratificationCode"), "StratificationCode"))
                                                                //.Add(Projections.SubQuery(criteriaLastCount), "DateModified"))
                                                            .Add(new SimpleExpression("CycleCountRotation", 0, ">"))
                                                            .Add(new SimpleExpression("Items.Active", "A", "="))
                                                            .Add(Restrictions.Eq("w.CompanyLocationCode", companylocCode))
                                                            .Add(Restrictions.Eq("c.CompanyCode", companyCode))
                                                            .Add(Restrictions.Eq("sw.CompanyLocationCode", companylocCode))
                                                            .Add(Restrictions.Eq("sc.CompanyCode", companyCode))
                                                            //.AddOrder(new Order("DateModified", true))
                                                            .SetResultTransformer(Transformers.AliasToBean<CompanyStratification>());

            IList<CompanyStratification> items = Repositories.Get<CompanyStratification>().List(criteria);
            if (_logger.IsDebugEnabled) _logger.Debug("Total Items available in the warehouse : " + items.Count.ToString());
           items.ToList().ForEach(x =>
            {
                x.DateModified = invTasks.Where(e => e.Id == x.Id).Select(e => e.DateCreated).FirstOrDefault();
            });
            items = items.OrderBy(c => c.DateModified).ToList();
            DateTime toDate = EndOfDay(DateTime.Today.AddDays(1));
            DateTime fromDate = StartOfDay(GetPreviousWorkingDay(DateTime.Today).AddDays(1));

            //If item has open inventory tasks(already set up), then do not allow it to be set up again.
            List<int?> itemIds = items.Select(c => c.Id).ToList<int?>();
            int length = 1000;
            int startIndex = 0;
            List<int> itemsCyclecounted = new List<int>();
            //do
            //{
            //    if (itemIds.Count < startIndex + length)
            //        length = itemIds.Count - startIndex;

            //    DetachedCriteria queryOpenTask = DetachedCriteria.For<InventoryTask>()
            //          .CreateAlias("Task", "Task")
            //          .CreateAlias("Task.TransactionType", "TransactionType")
            //          .CreateAlias("StatusCode", "StatusCode")
            //          .CreateAlias("StatusCode.FunctionalAreaCode", "fac")
            //          //.Add(Restrictions.In("Item.Id", itemIds.GetRange(startIndex, length)))
            //          .Add(Restrictions.Eq("Quantity", Convert.ToDecimal(0)))
            //          .Add(Restrictions.Eq("TransactionType.TransactionTypeCode", "CCI"))
            //          .Add(Restrictions.Eq("StatusCode.Code", CodeValue.GetCode(TaskStatuses.Open)))
            //          .Add(Restrictions.Eq("fac.Code", CodeValue.GetCode(FunctionalAreas.Task)))
            //          .SetProjection(Projections.Property("Item.Id"));

            //    IList<int> openTaskItems = Repositories.Get<int>().List(queryOpenTask);
            //    if (openTaskItems != null) itemsCyclecounted.AddRange(openTaskItems);

            //    startIndex += length;
            //} while (startIndex < itemIds.Count);
            // itemsCyclecounted = itemsCyclecounted.Where(c => c.IsIn(itemIds)).ToList();
            DetachedCriteria queryOpenTask = DetachedCriteria.For<InventoryTask>()
                       .CreateAlias("Task", "Task")
                       .CreateAlias("Task.TransactionType", "TransactionType")
                       .CreateAlias("StatusCode", "StatusCode")
                       .CreateAlias("StatusCode.FunctionalAreaCode", "fac")
                       //.Add(Restrictions.In("Item.Id", itemIds.GetRange(startIndex, length)))
                       .Add(Restrictions.Eq("Quantity", Convert.ToDecimal(0)))
                       .Add(Restrictions.Eq("TransactionType.TransactionTypeCode", "CCI"))
                       .Add(Restrictions.Eq("StatusCode.Code", CodeValue.GetCode(TaskStatuses.Open)))
                       .Add(Restrictions.Eq("fac.Code", CodeValue.GetCode(FunctionalAreas.Task)))
                       .SetProjection(Projections.Property("Item.Id"));

            IList<int> openTaskItems = Repositories.Get<int>().List(queryOpenTask);
            if (openTaskItems != null) itemsCyclecounted.AddRange(openTaskItems);
            itemsCyclecounted = itemsCyclecounted.Distinct().ToList<int>();
            //items = items.Where(c => !itemsCyclecounted.Any(id => id == c.Id)).ToList<CompanyStratification>();
            //
            if (_logger.IsDebugEnabled) _logger.Debug("Total Items available in the inventory which had cycle count flag N or null : " + items.Count.ToString());
            StatusCode availableStatus = Entity.Retrieve<StatusCode>(InventoryStatuses.Available);
                 criteria = DetachedCriteria.For<InventoryItem>()
                                                         .CreateAlias("CompanyLocationType", "w", JoinType.LeftOuterJoin)
                                                         .CreateAlias("w.CompanyLocation", "cl", JoinType.LeftOuterJoin)
                                                         .CreateAlias("cl.Company", "c", JoinType.LeftOuterJoin)
                                                         .CreateAlias("Item", "Item")
                                                         .Add(Restrictions.Eq("StatusCode.Id", availableStatus.Id))
                                                         //.Add(Restrictions.In("Item.Id", itemIds.GetRange(startIndex, length)))
                                                         .Add(!Restrictions.Eq("Quantity", 0M))
                                                         .Add(Restrictions.Eq("w.CompanyLocationCode", companylocCode))
                                                         .Add(Restrictions.Eq("c.CompanyCode", companyCode))
                                                         .SetProjection(Projections.ProjectionList()
                                                                .Add(Projections.Property("Item.Id"), "ItemId"))
                                                         .SetResultTransformer(Transformers.AliasToBean<InventoryItem>());

            IList<InventoryItem> invItems = Repositories.Get<InventoryItem>().List(criteria);

            List<int> inventoryAvailableItems = invItems.Select(c => c.ItemId).Distinct().ToList<int>();
           
            items = items.Where(c => inventoryAvailableItems.Any(id => id == c.Id)).ToList<CompanyStratification>();
            if (_logger.IsDebugEnabled) _logger.Debug("Total available inventory items : " + items.Count.ToString());
            //
            foreach (CompanyStratification item in items)
            {
                int days = item.CycleCountRotation;

                if (item.DateModified.HasValue)
                {
                    startDate = item.DateModified.Value;

                    int noofStartDays = Convert.ToInt32((fromDate - startDate).TotalDays) + 1;
                    int noofEndDays = Convert.ToInt32((toDate - startDate).TotalDays);

                    for (int i = noofStartDays; i <= noofEndDays; i++)
                    {
                        if (i >= days)
                        {
                            cycleCountItems.Add(item);
                            break;
                        }
                        else
                            if (_logger.IsDebugEnabled) _logger.Debug("last cycleCount created for this item is within the stratification days " + days.ToString() + " hence cycle count would not be created for the item : " + item.ItemCode);
                    }
                }
                else
                    cycleCountItems.Add(item);
            }

            cycleCountItems = cycleCountItems.Select(c => c).Distinct().ToList<CompanyStratification>();
            return cycleCountItems;
        }

        #endregion

        #region Get Working Day

        private DateTime GetWorkingDay(DateTime fromDate)
        {
            DateTime toDate = fromDate;

            if (toDate.DayOfWeek.Equals(DayOfWeek.Saturday) || toDate.DayOfWeek.Equals(DayOfWeek.Sunday) || IsHoliday(toDate))
            {
                GetWorkingDay(toDate.AddDays(1));
            }

            return toDate;
        }

        private DateTime GetPreviousWorkingDay(DateTime fromDate)
        {
            DateTime toDate = fromDate;

            if (toDate.DayOfWeek.Equals(DayOfWeek.Saturday) || toDate.DayOfWeek.Equals(DayOfWeek.Sunday) || IsHoliday(toDate))
            {
                GetPreviousWorkingDay(toDate.AddDays(-1));
            }

            return toDate;
        }

        private bool IsHoliday(DateTime toDate)
        {
            bool isHoliday = false;

            //Get Business Rule to omit Weekends/Holidays
            string rule_CycleCount_generation = string.Empty;
            rule_CycleCount_generation = GetParameterValue("4000", "I");
            if (!string.IsNullOrEmpty(rule_CycleCount_generation) && rule_CycleCount_generation.Equals("Y"))
            {
                //Get holiday Schedule by observed and year
                DetachedCriteria criteria = DetachedCriteria.For<HolidaySchedule>()
                                    .Add(Restrictions.Between("Observed", StartOfDay(toDate), EndOfDay(toDate)))
                                    .Add(Restrictions.Eq("Year", toDate.Year));

                IList<HolidaySchedule> holidaySchedule = Repositories.Get<HolidaySchedule>().List(criteria);
                if (holidaySchedule != null && holidaySchedule.Count > 0)
                    isHoliday = true;
            }

            return isHoliday;
        }

        public DateTime StartOfDay(DateTime value)
        {
            return new DateTime(value.Year, value.Month, value.Day, 0, 0, 0, 0);
        }

        public DateTime EndOfDay(DateTime value)
        {
            return new DateTime(value.Year, value.Month, value.Day, 23, 59, 59, 998);
        }

        #endregion

        #region Business Rule
        private string GetParameterValue(string businessRuleCode, string functionalAreaCode)
        {
            string parameterValue = null;

            DetachedCriteria query = DetachedCriteria.For<BusinessRuleParameterValue>()


                .CreateAlias("BusinessRuleParameter", "BusinessRuleParameter")
                .CreateAlias("BusinessRuleParameter.BusinessRule", "BusinessRule")
                .CreateAlias("Parameter", "Parameter")
                .CreateAlias("Parameter.FunctionalAreaCode", "FunctionalAreaCode")
                .SetProjection(Projections.ProjectionList()
                .Add(Projections.Property("ParameterValue"), "ParameterValue"))
                .Add(new SimpleExpression("BusinessRule.BusinessRuleCode", businessRuleCode, "="))
                .Add(new SimpleExpression("BusinessRule.Active", "A", "="))
                .Add(new SimpleExpression("Active", "A", "="))
                .Add(new SimpleExpression("Selected", "Y", "="))
                .SetResultTransformer(Transformers.AliasToBean<BusinessRuleParameterValue>());
            if (!string.IsNullOrEmpty(functionalAreaCode)) query = query.Add(new SimpleExpression("FunctionalAreaCode.Code", functionalAreaCode, "="));
            //
            IList<BusinessRuleParameterValue> businessRuleParameterValues = Repositories.Get<BusinessRuleParameterValue>().List(query);
            if (businessRuleParameterValues != null && businessRuleParameterValues.Count > 0)
            {
                parameterValue = businessRuleParameterValues[0].ParameterValue;
            }
            return parameterValue;
        }
        #endregion

        private IList<InventoryCountProfile> GetInventoryCountProfiles(string companyCode, string companylocCode)
        {
            DetachedCriteria criteria = DetachedCriteria.For<InventoryCountProfile>()
                    .CreateAlias("CompanyLocationType", "CompanyLocationType")
                    .CreateAlias("CompanyLocationType.CompanyLocation", "CompanyLocation")
                    .CreateAlias("CompanyLocation.Company", "Company")
                    .Add(Restrictions.Eq("CompanyLocationType.CompanyLocationCode", companylocCode))
                    .Add(Restrictions.Eq("Company.CompanyCode", companyCode))
                    .SetProjection(Projections.ProjectionList()
                            .Add(Projections.Property("Id"), "Id")
                            .Add(Projections.Property("DailyCycleCountType"), "DailyCycleCountType")
                            .Add(Projections.Property("MaxACounts"), "MaxACounts")
                            .Add(Projections.Property("MaxBCounts"), "MaxBCounts")
                            .Add(Projections.Property("MaxCCounts"), "MaxCCounts")
                            .Add(Projections.Property("MaxDCounts"), "MaxDCounts")
                            .Add(Projections.Property("AbcADays"), "AbcADays")
                            .Add(Projections.Property("AbcBDays"), "AbcBDays")
                            .Add(Projections.Property("AbcCDays"), "AbcCDays")
                            .Add(Projections.Property("AbcDDays"), "AbcDDays"))
                    .SetResultTransformer(Transformers.AliasToBean<InventoryCountProfile>());

            IList<InventoryCountProfile> inventoryCountProfiles = Repositories.Get<InventoryCountProfile>().List(criteria);
            return inventoryCountProfiles;
        }

        private void SendCycleCountDetailsToExacta(string exactaServerName, List<InventoryTask> inventoryTasks)
        {
            try
            {
                if (_logger.IsDebugEnabled) _logger.Debug("Calling Exacta cyclecount interface");
                string exactaApiURL = string.Empty;
                exactaApiURL = "http://" + exactaServerName + "/exactaAPI/import/cc";
                List<InventoryTask> exactaInvTasks = GetExactaInventoryTasks();
                if(exactaInvTasks==null || exactaInvTasks.Count <= 0)
                {
                    if (_logger.IsDebugEnabled) _logger.Debug("No Inventory Tasks Found for Exacta cyclecount interface");
                    if (_logger.IsDebugEnabled) _logger.Debug("End of Calling Exacta cyclecount interface");
                    return;
                }
                string inputRequest = PrepareInputJasonForExactApi(exactaInvTasks);
                if (_logger.IsDebugEnabled) _logger.Debug("input request : " + inputRequest);
                if (!string.IsNullOrEmpty(inputRequest))
                {
                    BradyCustom client = new BradyCustom();
                    client.CallExactaApi(inputRequest, exactaApiURL, false);
                }
                if (_logger.IsDebugEnabled) _logger.Debug("End of Calling Exacta cyclecount interface");
            }
            catch (Exception ex)
            {
                if (_logger.IsDebugEnabled) _logger.Debug("Exception occurred, Error : " + ex.Message + " Inner Exception " + ex.InnerException?.Message);
            }
        }

        private string PrepareInputJasonForExactApi(List<InventoryTask> exactaInventoryTasks)
        {
            if (exactaInventoryTasks != null && exactaInventoryTasks.Count > 0)
            {
                try
                {
                    CycleCountIfaceDataContract contract = new CycleCountIfaceDataContract();
                    //
                    List<CycleCountIface> cycleCountIfaces = new List<CycleCountIface>();
                    foreach (InventoryTask inventoryTask in exactaInventoryTasks)
                    {
                        CycleCountIface cycleCountIface = new CycleCountIface();
                        cycleCountIface.order_name = inventoryTask.Id.ToString();
                        cycleCountIface.order_type = 4;
                        cycleCountIface.prod_name = inventoryTask.ItemCode;
                        cycleCountIface.due_date = DateTime.Now.ToString("yyyyMMdd");
                        cycleCountIface.priority = 0;
                        //
                        List<CycleCountOrdeDetail> cycleCountOrdeDetails = new List<CycleCountOrdeDetail>();
                        //
                        CycleCountOrdeDetail cycleCountOrdeDetail1 = new CycleCountOrdeDetail();
                        cycleCountOrdeDetail1.detail_type = 1002;
                        cycleCountOrdeDetail1.detail_value = inventoryTask.LocationCodeFrom;
                        cycleCountOrdeDetails.Add(cycleCountOrdeDetail1);
                        //
                        CycleCountOrdeDetail cycleCountOrdeDetail2 = new CycleCountOrdeDetail();
                        cycleCountOrdeDetail2.detail_type = 1003;
                        cycleCountOrdeDetail2.detail_value = inventoryTask.TaskCode;
                        cycleCountOrdeDetails.Add(cycleCountOrdeDetail2);
                        //
                        cycleCountIface.ord_details = cycleCountOrdeDetails.ToArray();
                        cycleCountIfaces.Add(cycleCountIface);
                    }
                    contract.records = cycleCountIfaces.ToList();

                    return JsonConvert.SerializeObject(contract);
                }
                catch (Exception ex)
                {
                    if (_logger.IsDebugEnabled) _logger.Debug("error while prearing the input request : " + ex.Message + " Inner Exception " + ex.InnerException?.Message);
                    return "";
                }
            }
            else
            {
                if (_logger.IsDebugEnabled) _logger.Debug("No InventoryTasks found after Consolidation Process");
                return "";
            }
        }

        private List<InventoryTask> GetExactaInventoryTasks()
        {
            try
            {
                if (_logger.IsDebugEnabled) _logger.Debug("Before InventoryTask Consolidation. InventoryTask Count: " + inventoryTasks.Count);
                List<InventoryTask> exactaInvTasks = new List<InventoryTask>();
                exactaInvTasks = inventoryTasks.Where(c => c.CompanyLocationZoneFrom != null && c.CompanyLocationZoneFrom.PickToLight == "Y").Select(c => c).ToList();
                exactaInvTasks = exactaInvTasks
                                        .GroupBy(c => new
                                        {
                                            c.ItemDescription,
                                            c.ItemCode,
                                            c.LocationCodeFrom,
                                            c.TaskCode,
                                        })
                                        .Select(group => new InventoryTask
                                        {
                                            ItemDescription = group.Key.ItemCode,
                                            ItemCode = group.Key.ItemCode,
                                            LocationCodeFrom = group.Key.LocationCodeFrom,
                                            TaskCode = group.Key.TaskCode,
                                            Id = group.First().Id,
                                            Quantity = group.Sum(c => c.Quantity)
                                        }).ToList<InventoryTask>();

                if (_logger.IsDebugEnabled) _logger.Debug("After InventoryTask Consolidation. Exacta InventoryTask Count: " + exactaInvTasks?.Count);

                return exactaInvTasks;
            }
            catch (Exception ex)
            {
                if (_logger.IsDebugEnabled) _logger.Debug("error while consolidating Inventory Tasks : " + ex.Message + " Inner Exception " + ex.InnerException?.Message);
                return null;
            }
        }

        #endregion

        #endregion
    }
}
