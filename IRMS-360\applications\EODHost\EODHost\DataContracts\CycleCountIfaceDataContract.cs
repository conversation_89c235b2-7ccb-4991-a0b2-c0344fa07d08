﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Upp.Irms.Services.Integration.DataContracts
{
    public class CycleCountIfaceDataContract
    {
        [System.Runtime.Serialization.DataMemberAttribute(Name = "records")]
        public IList<CycleCountIface> records { get; set; }
    }

    // Type created for JSON at <<root>>
    [System.Runtime.Serialization.DataContractAttribute()]
    public class CycleCountIface
    {
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string order_name { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute()]
        public int order_type { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute()]
        public string prod_name { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute()]
        public string due_date { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute()]
        public int priority { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute(Name = "order_details", Order = 1002)]
        public CycleCountOrdeDetail[] ord_details { get; set; }
    }

    // Type created for JSON at <<root>>
    [System.Runtime.Serialization.DataContractAttribute()]
    public class CycleCountOrdeDetail
    {
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int detail_type { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute()]
        public string detail_value { get; set; }

    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public class CycleCountIfaceExactaResponce
    {
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Code { get; set; }
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Description { get; set; }
        [System.Runtime.Serialization.DataMemberAttribute()]
        public ExactaResponceContent Content { get; set; }
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public class CycleCountIfaceExactaResponceContent
    {
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string error { get; set; }
    }


}
