using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Upp.Irms.Services.Integration.DataContracts
{
    public class MakeToOrderDataContract
    {
        [System.Runtime.Serialization.DataMemberAttribute(Name = "records")]
        public IList<MakeToOrder> records { get; set; }
    }

    // Type created for JSON at <<root>>
    [System.Runtime.Serialization.DataContractAttribute()]
    public class MakeToOrder
    {

        [System.Runtime.Serialization.DataMemberAttribute()]
        public string cntnr_name { get; set; }
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string qc_required { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute()]
        public string pack_qc_required { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ship_qc_required { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute()]
        public string qc_reason { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ship_sort_code { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute(Name = "routing_zones")]
        public List<routing_zones> routing_zones { get; set; }        
    }
    // Type created for JSON at <<root>>
    [System.Runtime.Serialization.DataContractAttribute()]
    public class routing_zones
    {
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string zone_number { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute()]
        public string is_complete { get; set; }

    }
}
