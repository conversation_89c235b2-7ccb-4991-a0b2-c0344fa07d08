﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Upp.Irms.Services.Integration.DataContracts
{


    public class ProductInterfaceDataContract
    {
        [System.Runtime.Serialization.DataMemberAttribute(Name = "records")]
        public IList<ProductIface> records { get; set; }
    }

    // Type created for JSON at <<root>>
    [System.Runtime.Serialization.DataContractAttribute()]
    public class ProductIface
    {

        [System.Runtime.Serialization.DataMemberAttribute()]
        public string prod_name { get; set; }
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string prod_description { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal retail_value { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute()]
        public string top_off_window_days { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute()]
        public string abc_class_name { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute()]
        public string pick_method { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute()]
        public string prod_status { get; set; }
       

        [System.Runtime.Serialization.DataMemberAttribute(Name = "prod_alias", Order = 1001)]
        public prod_alias[] prod_alias { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute(Name = "prod_details", Order = 1002)]
        public prod_details[] prod_details { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute(Name = "uom_types", Order = 1003)]
        public uom_types[] uom_types { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute(Name = "prod_zones" , Order = 1004)]
        public prod_zones[] prod_zones { get; set; }
    }
    // Type created for JSON at <<root>>
    [System.Runtime.Serialization.DataContractAttribute()]
    public class prod_alias
    {
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int alias_type { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute()]
        public string alias_name { get; set; }

    }
    // Type created for JSON at <<root>>
    [System.Runtime.Serialization.DataContractAttribute()]
    public class prod_details
    {
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int detail_type { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute()]
        public string detail_value { get; set; }

    }
    // Type created for JSON at <<root>>
    [System.Runtime.Serialization.DataContractAttribute()]
    public class uom_types
    {
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int uom_type { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal uom_depth { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal uom_height { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal uom_width { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal uom_weight { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal velocity { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute()]
        public int count_threshold_qty { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute()]
        public int conveyable { get; set; }
  }
    // Type created for JSON at <<root>>
    [System.Runtime.Serialization.DataContractAttribute()]
    public class prod_zones
    {
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string zone_num { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute()]
        public string picking_uom { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute()]
        public string stocking_uom { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute()]
        public string handling_uom { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute()]
        public string zone_max { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute(Name = "uom_zones", Order = 1001)]
        public uom_zones[] uom_zones { get; set; }

    }
    // Type created for JSON at <<root>>
    [System.Runtime.Serialization.DataContractAttribute()]
    public class uom_zones
    {
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int uom_type { get; set; }

        [System.Runtime.Serialization.DataMemberAttribute()]
        public int top_off_method { get; set; }

    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public class ExactaResponce
    {
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Code { get; set; }
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Description { get; set; }
        [System.Runtime.Serialization.DataMemberAttribute()]
        public ExactaResponceContent Content { get; set; }
    }

    [System.Runtime.Serialization.DataContractAttribute()]
    public class ExactaResponceContent
    {
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string error { get; set; }
    }


}
