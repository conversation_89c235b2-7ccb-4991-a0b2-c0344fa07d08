﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;

using log4net;
using Newtonsoft.Json.Linq;
using NHibernate.Criterion;
using NHibernate.SqlCommand;
using NHibernate.Transform;
using Quartz;

using Upp.Irms.Core;
using Upp.Irms.Domain;
using Upp.Shared.Utilities;
using NHibernate;

namespace Upp.Irms.EOD.Host
{
    class DataPurgeJob : IJob
    {
        #region Fields

        ILog _logger = LogManager.GetLogger(typeof(DataPurgeJob));

        const string JParam_CustomerOrder = "CUSTOMERORDER";
        const string JParam_PurchaseOrder = "PURCHASEORDER";
        const string JParam_WorkOrder = "WORKORDER";
        const string JParam_Tasks = "TASKS";
        const string JParam_nextJob = "NEXTJOB";
        const int TasksThereshold = 2000;

        string customerOrder = "";
        string purchaseOrder = "";
        string workOrder = "";
        string task = "";
        string nextJob = "";

        string jobname = "Data Purge job";

        #endregion

        #region Constructor

        public DataPurgeJob()
        {
            //
        }

        #endregion

        #region Methods.Public

        public virtual void Execute(JobExecutionContext context)
        {
            if (JobParametersAreValid(context.MergedJobDataMap) == false)
            {
                JobExecutionException jex = new JobExecutionException("Missing job parameter");
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }

            ParseJobParameters(context.MergedJobDataMap);

            PerformDataPurge();

            NextJobScheduling(jobname);
        }

        #endregion

        #region Methods.Private

        private bool JobParametersAreValid(JobDataMap jobParams)
        {
            bool validity = true;

            if (!jobParams.Contains(JParam_nextJob))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_nextJob);
                validity = false;
            }

            return validity;
        }

        private void ParseJobParameters(JobDataMap jobParams)
        {
            try
            {
                //map the parameters to jobParams              
                this.customerOrder = jobParams.GetString(JParam_CustomerOrder);
                this.purchaseOrder = jobParams.GetString(JParam_PurchaseOrder);
                this.workOrder = jobParams.GetString(JParam_WorkOrder);
                this.task = jobParams.GetString(JParam_Tasks);
                this.nextJob = jobParams.GetString(JParam_nextJob);
            }
            catch (Exception ex)
            {
                JobExecutionException jex = new JobExecutionException("Invalid data type in job parameters", ex);
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }
        }

        private void NextJobScheduling(string jobname)
        {
            if (!String.IsNullOrWhiteSpace(nextJob))
            {
                if (_logger.IsDebugEnabled) _logger.DebugFormat("NextJob:  {0}", nextJob);
                try
                {
                    string[] jobKeys = Service.Instance.Scheduler.GetJobNames("Manual");
                    foreach (string jobKey in jobKeys)
                    {
                        if (jobKey.Equals(nextJob))
                        {
                            SimpleTrigger trigger = new SimpleTrigger();
                            trigger.Name = nextJob + "Trigger";
                            trigger.JobName = nextJob;
                            trigger.JobGroup = "Manual";
                            trigger.Group = "Manual";
                            trigger.StartTimeUtc = DateTime.UtcNow.AddSeconds(30);
                            trigger.RepeatCount = 0;
                            trigger.RepeatInterval = TimeSpan.Zero;
                            Service.Instance.Scheduler.RescheduleJob(nextJob + "Trigger", "Manual", trigger);
                            if (_logger.IsDebugEnabled) _logger.DebugFormat("{1} - Next Job {0} is scheduled: ", nextJob, jobname);
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (_logger.IsErrorEnabled) _logger.ErrorFormat("{1} - Next Job error: {0}", ex.Message, jobname);
                }
            }
        }

        private void PerformDataPurge()
        {
            try
            {
                if (!string.IsNullOrEmpty(this.customerOrder)) DeleteTable(customerOrder, "ORD");

                if (!string.IsNullOrEmpty(this.purchaseOrder)) DeleteTable(purchaseOrder, "PO");
                
				if (!string.IsNullOrEmpty(this.workOrder)) DeleteTable(workOrder, "WO");
                
                if (!string.IsNullOrEmpty(this.task)) DeleteTable(task, "T");
            
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
            }
        }

        private void DeleteTable(string param, string fac)
        {
            string warehouse = "";
            string months = "";

            if (!string.IsNullOrWhiteSpace(param))
            {
                char[] delimiter = { ':' };
                string[] parameters = param.Split(delimiter);

                if (parameters != null && parameters.Length == 2)
                {
                    warehouse = parameters[0];
                    months = parameters[1];
                    //
                    switch (fac.ToUpper())
                    {
                        case "ORD":
                            ProcessCustomerOrder(warehouse, Convert.ToInt32(months));
                            break;
                        case "PO":
                            ProcessPurchaseOrder(warehouse, Convert.ToInt32(months));
                            break;
                        case "WO":
                            ProcessWorkOrder(warehouse, Convert.ToInt32(months));
                            break;
                        case "T":
                            ProcessDeleteTask(warehouse, Convert.ToInt32(months));
                            break;

                    }
                }

            }
        }

        private void ProcessCustomerOrder(string warehouseParam, int months)
        {
            IList<OrderHeader> orders = new List<OrderHeader>();
            //
            using (UnitWrapper wrapper = new UnitWrapper())
            {
                DateTime retainMonths = DateTime.Now.AddMonths(-1 * months);
                //
                wrapper.Execute(() =>
                {
                    try
                    {
                        DetachedCriteria criteria = DetachedCriteria.For<OrderHeader>()
                             .CreateAlias("StatusCode", "StatusCode")
                             .CreateAlias("CompanyLocationType", "CompanyLocationType")
                             .SetProjection(Projections.ProjectionList()
                                .Add(Projections.Property("Id"), "Id")
                                .Add(Projections.Property("OrderCode"), "OrderCode")
                                .Add(Projections.Property("OrderSuffix"), "OrderSuffix")
                                .Add(Projections.Property("Version"), "Version"))
                             .Add(Restrictions.Lt("StatusDate", retainMonths))
                             .Add(new SimpleExpression("StatusCode.Code", "S", "="))
                             .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.OrderHeader>());

                        if (!string.IsNullOrEmpty(warehouseParam) && !warehouseParam.Equals("*"))
                        {
                            string[] warehouses = warehouseParam.Split(',');
                            criteria = criteria.Add(Restrictions.In("CompanyLocationType.CompanyLocationCode", warehouses));

                            //Disjunction or = Restrictions.Disjunction();
                            //foreach (string warehouse in warehouses)
                            //{
                            //	or.Add(new SimpleExpression("CompanyLocationType.CompanyLocationCode", warehouse.Split('|').GetValue(0).ToString(), "="));
                            //}
                            //criteria.Add(or);
                        }

                        orders = Repositories.Get<OrderHeader>().List(criteria);
                    }
                    catch (Exception ex)
                    {
                        ErrorFormat(string.Format("{1} - Error while deleting the customer orders: {0}", ex.Message, jobname));
                    }
                });
            }

            try
            {
                if (orders != null && orders.Count > 0)
                {
                    DebugFormat(string.Format("{0}  - customer orders found to perform the CO purge - {1}", jobname, orders.Count));
                    foreach (OrderHeader order in orders) DeleteOrder(order);
                }
                else
                    DebugFormat("Customer order purge no order founds for deleting");

                DebugFormat(string.Format("{0}  - End of CO Purge ", jobname));
            }
            catch (Exception ex)
            {
                ErrorFormat(string.Format("{1} - Error while deleting the customer orders: {0}", ex.Message, jobname));
            }
        }

        private void ProcessPurchaseOrder(string warehouseParam, int months)
        {
            IList<PurchaseOrderHeader> porders = new List<PurchaseOrderHeader>();
            using (UnitWrapper wrapper = new UnitWrapper())
            {
                DateTime retainMonths = DateTime.Now.AddMonths(-1 * months);
                //
                wrapper.Execute(() =>
                {
                    try
                    {
                        DetachedCriteria detachedUnavStatus = DetachedCriteria.For<StatusCode>()
                                .SetProjection(Projections.ProjectionList()
                                    .Add(Projections.Max("Id"), "Id"))
                                .Add(new SimpleExpression("Code", "U", "="))
                                .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.StatusCode>());

                        DetachedCriteria criteria = DetachedCriteria.For<PurchaseOrderHeader>()
                             .CreateAlias("PurchaseOrderStatuses", "POrderStatuses")
                             .CreateAlias("POrderStatuses.StatusCode", "Statuscode")
                             .CreateAlias("CompanyLocationType", "CompanyLocationType")
                             .CreateAlias("InvoiceHeaders", "InvoiceHeaders", NHibernate.SqlCommand.JoinType.LeftOuterJoin)
                             .CreateAlias("InvoiceDetails", "InvoiceDetails", NHibernate.SqlCommand.JoinType.LeftOuterJoin)
                             .CreateAlias("PurchaseOrderServices", "PurchaseOrderServices", NHibernate.SqlCommand.JoinType.LeftOuterJoin)
                             .CreateAlias("PurchaseOrderDetails", "PurchaseOrderDetails", NHibernate.SqlCommand.JoinType.LeftOuterJoin)
                             .CreateAlias("PurchaseOrderDetails.ReceiptDetails", "ReceiptDetails", NHibernate.SqlCommand.JoinType.LeftOuterJoin)
                             .CreateAlias("ReceiptDetails.InventoryItems", "InventoryItems", NHibernate.SqlCommand.JoinType.LeftOuterJoin)
                             .CreateAlias("InventoryItems.InventoryItemDetails", "InventoryItemDetails", NHibernate.SqlCommand.JoinType.LeftOuterJoin)
                             .SetProjection(Projections.Distinct(Projections.ProjectionList()
                                .Add(Projections.Property("Id"), "Id")
                                .Add(Projections.Property("Version"), "Version")
                                .Add(Projections.Property("PurchaseOrderCode"), "PurchaseOrderCode")
                                .Add(Projections.Property("PurchaseOrderSuffix"), "PurchaseOrderSuffix")))
                             .Add(Restrictions.Lt("POrderStatuses.Occurred", retainMonths))
                             .Add(new SimpleExpression("Statuscode.Code", "C", "="))
                             .Add(Restrictions.Or(
                                Subqueries.PropertyIn("InventoryItems.StatusCode.Id", detachedUnavStatus),
                                Restrictions.Eq("InventoryItemDetails.Quantity", Convert.ToDecimal(0))))
                             .Add(Expression.IsNull("InvoiceHeaders.PurchaseOrderHeader.Id"))
                             .Add(Expression.IsNull("InvoiceDetails.PurchaseOrderHeader.Id"))
                             .Add(Expression.IsNull("PurchaseOrderServices.PurchaseOrderHeader.Id"))
                             .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.PurchaseOrderHeader>());

                        if (!string.IsNullOrEmpty(warehouseParam) && !warehouseParam.Equals("*"))
                        {
                            string[] warehouses = warehouseParam.Split(',');
                            criteria = criteria.Add(Restrictions.In("CompanyLocationType.CompanyLocationCode", warehouses));
                        }

                        porders = Repositories.Get<PurchaseOrderHeader>().List(criteria);

                        if (_logger.IsDebugEnabled)
                        {
                            if (porders != null && porders.Count > 0) _logger.DebugFormat("{0}  - purchase orders found to perform the PO purge - {1}", jobname, porders.Count);
                            else _logger.DebugFormat("{0}  - No purchase orders found to perform the PO purge ", jobname);
                        }
                    }
                    catch (Exception ex)
                    {
                        if (_logger.IsErrorEnabled) _logger.ErrorFormat("{1}  - Error while deleting the purchase orders: {0}", ex.Message, jobname);
                    }
                });
            }

            try
            {
                foreach (PurchaseOrderHeader porder in porders) DeletePurchaseOrder(porder);

                if (_logger.IsDebugEnabled)
                {
                    _logger.DebugFormat("{0}  - End of PO Purge ", jobname);
                }
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("{1}  - Error while deleting the purchase orders: {0}", ex.Message, jobname);
            }
        }


        private void ProcessWorkOrder(string warehouseParam, int months)
        {
            IList<WorkOrderHeader> workOrders = new List<WorkOrderHeader>();
            //
            using (UnitWrapper wrapper = new UnitWrapper())
            {
                DateTime retainMonths = DateTime.Now.AddMonths(-1 * months);
                //
                wrapper.Execute(() =>
                {
                    try
                    {
                        DetachedCriteria criteria = DetachedCriteria.For<WorkOrderHeader>()
                             .CreateAlias("WorkOrderStatuses", "WOrderStatus")
                             .CreateAlias("WOrderStatus.StatusCode", "StatusCodes")
                             .CreateAlias("CompanyLocationType", "CompanyLocationType")
                             .CreateAlias("InvoiceHeaders", "InvoiceHeaders", NHibernate.SqlCommand.JoinType.LeftOuterJoin)
                             .CreateAlias("InvoiceDetails", "InvoiceDetails", NHibernate.SqlCommand.JoinType.LeftOuterJoin)
                             .CreateAlias("WorkOrderServices", "WorkOrderServices", NHibernate.SqlCommand.JoinType.LeftOuterJoin)
                             .SetProjection(Projections.ProjectionList()
                                .Add(Projections.Property("Id"), "Id")
                                .Add(Projections.Property("Version"), "Version")
                                .Add(Projections.Property("WorkOrderCode"), "WorkOrderCode")
                                .Add(Projections.Property("Suffix"), "Suffix"))
                             .Add(Restrictions.Lt("WOrderStatus.Occurred", retainMonths))
                             .Add(new SimpleExpression("StatusCodes.Code", "C", "="))
                             .Add(Expression.IsNull("InvoiceHeaders.WorkOrderHeader.Id"))
                             .Add(Expression.IsNull("InvoiceDetails.WorkOrderHeader.Id"))
                             .Add(Expression.IsNull("WorkOrderServices.WorkOrderHeader.Id"))
                             .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.WorkOrderHeader>());

                        if (!string.IsNullOrEmpty(warehouseParam) && !warehouseParam.Equals("*"))
                        {
                            string[] warehouses = warehouseParam.Split(',');
                            criteria = criteria.Add(Restrictions.In("CompanyLocationType.CompanyLocationCode", warehouses));
                        }

                        workOrders = Repositories.Get<WorkOrderHeader>().List(criteria);

                        if (_logger.IsDebugEnabled)
                        {
                            if (workOrders != null && workOrders.Count > 0) _logger.DebugFormat("{0}  - work orders found to perform the WO purge - {1}", jobname, workOrders.Count);
                            else _logger.DebugFormat("{0}  - No work orders found to perform the WO purge ", jobname);
                        }
                    }
                    catch (Exception ex)
                    {
                        if (_logger.IsErrorEnabled) _logger.ErrorFormat("{1}  - Error while deleting the work orders: {0}", ex.Message, jobname);
                    }
                });
            }

            try
            {
                foreach (WorkOrderHeader workOrder in workOrders) DeleteWorkOrder(workOrder);

                if (_logger.IsDebugEnabled)
                {
                    _logger.DebugFormat("{0}  - End of WO Purge ", jobname);
                }
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("{1}  - Error while deleting the work orders: {0}", ex.Message, jobname);
            }
        }


        private void ProcessDeleteTask(string warehouseParam, int months)
        {
            IList<Task> Tasks = new List<Task>();
            List<int?> TaskIds = new List<int?>();
            using (UnitWrapper wrapper = new UnitWrapper())
            {
                DateTime retainMonths = DateTime.Now.AddMonths(-1 * months);
                //
                wrapper.Execute(() =>
                {
                    try
                    {
                        DetachedCriteria invDetailsCriteria = DetachedCriteria.For<Task>()
                             .CreateAlias("ItemTransactions", "ItemTransactions", NHibernate.SqlCommand.JoinType.LeftOuterJoin)
                             .CreateAlias("ItemTransactions.InvoiceDetails", "InvoiceDetails", NHibernate.SqlCommand.JoinType.LeftOuterJoin)
                             .SetProjection(Projections.Distinct(Projections.ProjectionList()
                                .Add(Projections.Property("Id"), "Id")))
                             .Add(Restrictions.Lt("Completed", retainMonths))
                             .Add(Restrictions.IsNotNull("InvoiceDetails.Id"))
                             .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.Task>());

                        DetachedCriteria criteria = DetachedCriteria.For<Task>()
                             .CreateAlias("StatusCode", "StatusCode")
                             .CreateAlias("CompanyLocationType", "CompanyLocationType")
                             .SetProjection(Projections.Distinct(Projections.ProjectionList()
                                .Add(Projections.Property("Id"), "Id")
                                .Add(Projections.Property("Version"), "Version")))
                             .Add(Restrictions.Lt("Completed", retainMonths))
                             .Add(new SimpleExpression("StatusCode.Code", "C", "="))
                             .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.Task>());

                        if (!string.IsNullOrEmpty(warehouseParam) && !warehouseParam.Equals("*"))
                        {
                            string[] warehouses = warehouseParam.Split(',');
                            criteria = criteria.Add(Restrictions.In("CompanyLocationType.CompanyLocationCode", warehouses));
                        }

                        criteria = criteria.Add(Subqueries.PropertyNotIn("Id", invDetailsCriteria));

                        Tasks = Repositories.Get<Task>().List(criteria);

                        if (_logger.IsDebugEnabled)
                        {
                            if (Tasks != null && Tasks.Count > 0) _logger.DebugFormat("{0}  - {1} tasks found to perform the Tasks purge", jobname, Tasks.Count);
                            else _logger.DebugFormat("{0}  - No tasks found to perform the Tasks purge ", jobname);
                        }

                        TaskIds = Tasks.Select(e => e.Id).ToList<int?>();
                    }
                    catch (Exception ex)
                    {
                        if (_logger.IsErrorEnabled) _logger.ErrorFormat("{1}  - Error while deleting the tasks : {0}", ex.Message, jobname);
                    }
                });
            }

            try
            {
                int startIndex = 0, length = TasksThereshold;
                do
                {
                    if (TaskIds.Count > 0)
                    {
                        if (TaskIds.Count < startIndex + length)
                        {
                            length = TaskIds.Count - startIndex;
                        }

                        DeleteTask(TaskIds.GetRange(startIndex, length), startIndex + 1, startIndex + length);
                        startIndex += length;
                    }

                } while (startIndex < TaskIds.Count);

                if (_logger.IsDebugEnabled)
                {
                    _logger.DebugFormat("{0}  - End of Tasks Purge ", jobname);
                }
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("{1}  - Error while deleting the tasks : {0}", ex.Message, jobname);
            }
        }

        #endregion

        #region Delete CO
        private void DeleteOrder(OrderHeader orderHeader)
        {
            orderHeader.OrderCode = orderHeader.OrderCodeSuffix();
            //
            DebugFormat(string.Format("{1} - Start Purge Customer Order {0}", orderHeader.OrderCode, jobname));

            using (UnitWrapper wrapper = new UnitWrapper())
            {
                wrapper.Execute(() =>
                {
                    try
                    {
                        #region Validation

                        DetachedCriteria criteria = DetachedCriteria.For<InvoiceHeader>()
                                                                                   .SetProjection(Projections.CountDistinct("Id"))
                                                                                   .Add(Restrictions.Eq("OrderHeader.Id", orderHeader.Id));

                        var invoiceHeaderCount = Repositories.Get<int>().Count(criteria);

                        criteria = DetachedCriteria.For<InvoiceDetail>()
                                                   .CreateAlias("ItemTransaction", "ItemTransaction", JoinType.LeftOuterJoin)
                                                   .SetProjection(Projections.CountDistinct("Id"))
                                                   .Add(Restrictions.Eq("ItemTransaction.OrderHeader.Id", orderHeader.Id));

                        var invoiceDetailCount = Repositories.Get<int>().Count(criteria);

                        criteria = DetachedCriteria.For<InvoiceDetail>()
                                                   .SetProjection(Projections.CountDistinct("Id"))
                                                   .Add(Restrictions.Eq("OrderHeader.Id", orderHeader.Id));

                        invoiceDetailCount += Repositories.Get<int>().Count(criteria);

                        criteria = DetachedCriteria.For<InvoiceDetail>()
                                                   .SetProjection(Projections.CountDistinct("Id"))
                                                   .Add(Restrictions.Eq("OrderHeader.Id", orderHeader.Id));

                        invoiceDetailCount += Repositories.Get<int>().Count(criteria);

                        criteria = DetachedCriteria.For<OrderService>()
                                                   .SetProjection(Projections.CountDistinct("Id"))
                                                   .Add(Restrictions.Eq("OrderHeader.Id", orderHeader.Id));

                        var orderStatusCount = Repositories.Get<int>().Count(criteria); 

                        #endregion

                        if (invoiceHeaderCount == 0 && invoiceDetailCount == 0 && orderStatusCount == 0)
                        {

                            #region Comments
                            //Order comments
                            criteria = DetachedCriteria.For<OrderComment>()
                                                                    .SetProjection(Projections.ProjectionList()
                                                                        .Add(Projections.Property("Id"), "Id")
                                                                        .Add(Projections.Property("Version"), "Version"))
                                                                    .Add(Restrictions.Eq("OrderHeader.Id", orderHeader.Id))
                                                                    .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.OrderComment>());
                            var orderComments = Repositories.Get<OrderComment>().List(criteria);

                            if (orderComments != null && orderComments.Count > 0) DebugFormat(string.Format(" {0}  - orders header comments found to perform the CO purge - {1}", jobname, orderComments.Count));
                            else DebugFormat(string.Format("{0}  - No order header comments found to perform the CO purge ", jobname));

                            if (orderComments != null && orderComments.Count != 0)
                            {
                                string orderCommentIds = string.Join(",", orderComments.Select(c => c.Id.Value).ToList<Int32>());
                                Repositories.Get<OrderComment>().SQLQuery("delete from order_comments where order_comment_id in(" + orderCommentIds + ")").ExecuteUpdate();
                            }
                            #endregion

                            #region Order Charges
                            //Order Charges
                            criteria = DetachedCriteria.For<OrderCharge>()
                                                                    .SetProjection(Projections.ProjectionList()
                                                                        .Add(Projections.Property("Id"), "Id")
                                                                        .Add(Projections.Property("Version"), "Version"))
                                                                    .Add(Restrictions.Eq("OrderHeader.Id", orderHeader.Id))
                                                                    .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.OrderCharge>());
                            var orderCharges = Repositories.Get<OrderCharge>().List(criteria);

                            if (orderCharges != null && orderCharges.Count > 0) DebugFormat(string.Format(" {0}  - orders header charges found to perform the CO purge - {1}", jobname, orderCharges.Count));
                            else DebugFormat(string.Format("{0}  - No order header charges found to perform the CO purge ", jobname));

                            if (orderCharges != null && orderCharges.Count != 0)
                            {
                                string orderChargeIds = string.Join(",", orderCharges.Select(c => c.Id.Value).ToList<Int32>());
                                Repositories.Get<OrderCharge>().SQLQuery("delete from order_charges where order_charge_id in(" + orderChargeIds + ")").ExecuteUpdate();
                            }
                            #endregion

                            #region Order References
                            //Order References
                            criteria = DetachedCriteria.For<OrderReferenceCode>()
                                                                            .SetProjection(Projections.ProjectionList()
                                                                                .Add(Projections.Property("Id"), "Id")
                                                                                .Add(Projections.Property("Version"), "Version"))
                                                                            .Add(Restrictions.Eq("OrderHeader.Id", orderHeader.Id))
                                                                            .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.OrderReferenceCode>());
                            var orderReferences = Repositories.Get<OrderReferenceCode>().List(criteria);

                            if (orderReferences != null && orderReferences.Count > 0) DebugFormat(string.Format(" {0}  - orders header references found to perform the CO purge - {1}", jobname, orderReferences.Count));
                            else DebugFormat(string.Format("{0}  - No order header references found to perform the CO purge ", jobname));

                            if (orderReferences != null && orderReferences.Count != 0)
                            {
                                string orderreferenceIds = string.Join(",", orderReferences.Select(c => c.Id.Value).ToList<Int32>());
                                Repositories.Get<OrderReferenceCode>().SQLQuery("delete from order_reference_codes where order_reference_code_id in(" + orderreferenceIds + ")").ExecuteUpdate();
                            }
                            #endregion

                            #region item transactions
                            //item transactions
                            criteria = DetachedCriteria.For<ItemTransaction>()
                                                    .SetProjection(Projections.ProjectionList()
                                                    .Add(Projections.Property("Id"), "Id")
                                                    .Add(Projections.Property("Version"), "Version"))
                                                    .Add(Restrictions.Eq("OrderHeader.Id", orderHeader.Id))
                                                    .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ItemTransaction>());
                            var itemTransactions = Repositories.Get<ItemTransaction>().List(criteria);

                            if (itemTransactions != null && itemTransactions.Count > 0) DebugFormat(string.Format(" {0}  - ItemTransactions found to perform the CO purge - {1}", jobname, itemTransactions.Count));
                            else DebugFormat(string.Format("{0}  - No ItemTransactions found to perform the CO purge ", jobname));

                            if (itemTransactions != null && itemTransactions.Count != 0)
                            {
                                //
                                string itemTransactionsIds = string.Join(",", itemTransactions.Where(c => c.Id.HasValue).Select(c => c.Id.Value).Distinct().ToList<Int32>());
                                Repositories.Get<ItemTransaction>().SQLQuery("delete from item_transactions where item_transaction_id in(" + itemTransactionsIds + ")").ExecuteUpdate();
                            }
                            #endregion

                            #region Order Details
                            //Order Details
                            criteria = DetachedCriteria.For<OrderDetail>()
                                                    .SetProjection(Projections.ProjectionList()
                                                    .Add(Projections.Property("Id"), "Id")
                                                    .Add(Projections.Property("Version"), "Version"))
                                                    .Add(Restrictions.Eq("OrderHeader.Id", orderHeader.Id))
                                                    .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.OrderDetail>());
                            var orderDetails = Repositories.Get<OrderDetail>().List(criteria);

                            if (orderDetails != null && orderDetails.Count > 0) DebugFormat(string.Format(" {0}  - orders lines found to perform the CO purge - {1}", jobname, orderDetails.Count));
                            else DebugFormat(string.Format("{0}  - No order lines found to perform the CO purge ", jobname));

                            List<int> fulfillmentCartonIds = new List<int>();

                            if (orderDetails != null && orderDetails.Count != 0)
                            {
                                foreach (OrderDetail orderDetail in orderDetails)
                                {
                                    List<int> currentFulfillmentCartonIds = new List<int>();
                                    DeleteOrderDetail(orderDetail, orderHeader, out currentFulfillmentCartonIds);

                                    if (currentFulfillmentCartonIds != null && currentFulfillmentCartonIds.Count > 0)
                                        fulfillmentCartonIds.AddRange(currentFulfillmentCartonIds);
                                }
                            }
                            #endregion

                            #region Carton Headers

                            //Carton Headers
                            criteria = DetachedCriteria.For<CartonHeader>()
                                                                .SetProjection(Projections.ProjectionList()
                                                                    .Add(Projections.Property("Id"), "Id")
                                                                    .Add(Projections.Property("Version"), "Version"))
                                                                .Add(Restrictions.Eq("OrderHeader.Id", orderHeader.Id))
                                                                .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.CartonHeader>());
                            var cartons = Repositories.Get<CartonHeader>().List(criteria);

                            if (cartons != null && cartons.Count > 0) DebugFormat(string.Format(" {0}  - carton headers found to perform the CO purge - {1}", jobname, cartons.Count));
                            else DebugFormat(string.Format("{0}  - No carton headers found to perform the CO purge ", jobname));

                            if (cartons != null && cartons.Count != 0)
                            {
                                List<Int32> cartonIdList = cartons.Select(c => c.Id.Value).ToList<Int32>();

                                List<Int32> orderCartonIdList = fulfillmentCartonIds != null && fulfillmentCartonIds.Count > 0 ? cartonIdList.Where(c => fulfillmentCartonIds.Any(p => p == c)).Select(c => c).Distinct().ToList<int>() : null;
                                List<Int32> otherOrderCartonIdList = fulfillmentCartonIds != null && fulfillmentCartonIds.Count > 0 ? cartonIdList.Where(c => !fulfillmentCartonIds.Any(p => p == c)).Select(c => c).Distinct().ToList<int>() : null;

                                string cartonIds = orderCartonIdList != null ? string.Join(",", orderCartonIdList) : string.Empty;
                                string otherOrderCartonIds = otherOrderCartonIdList != null ? string.Join(",", otherOrderCartonIdList) : string.Empty;

                                int length = 1000;
                                int startIndex = 0;

                                List<ShipmentDetail> shipmentDetails = new List<ShipmentDetail>();

                                if (cartonIdList != null && cartonIdList.Count > 0)
                                {
                                    do
                                    {
                                        if (cartonIdList.Count < startIndex + length)
                                            length = cartonIdList.Count - startIndex;

                                        criteria = DetachedCriteria.For<ShipmentDetail>()
                                                             .SetProjection(Projections.ProjectionList()
                                                                        .Add(Projections.Property("Id"), "Id")
                                                                        .Add(Projections.Property("Version"), "Version"))
                                                                    .Add(Restrictions.In("CartonHeader.Id", cartonIdList))
                                                                    .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ShipmentDetail>());

                                        var currentShipmentDetails = Repositories.Get<ShipmentDetail>().List(criteria);

                                        if (currentShipmentDetails != null && currentShipmentDetails.Count > 0)
                                            shipmentDetails.AddRange(currentShipmentDetails);

                                        startIndex += length;
                                    } while (startIndex < cartonIdList.Count);
                                }

                                if (shipmentDetails != null && shipmentDetails.Count > 0) DebugFormat(string.Format(" {0}  - Shipment details found to perform the CO purge - {1}", jobname, shipmentDetails.Count));
                                else DebugFormat(string.Format("{0}  - No shipment details found to perform the CO purge ", jobname));

                                if (shipmentDetails != null && shipmentDetails.Count != 0)
                                {
                                    foreach (ShipmentDetail shipmentDetail in shipmentDetails)
                                    {
                                        DeleteShipmentDetail(shipmentDetail);
                                    }
                                }
                                if (!string.IsNullOrWhiteSpace(cartonIds))
                                    Repositories.Get<CartonHeader>().SQLQuery("delete from carton_headers where carton_header_id in(" + cartonIds + ")").ExecuteUpdate();

                                if (!string.IsNullOrWhiteSpace(otherOrderCartonIds))
                                    Repositories.Get<CartonHeader>().SQLQuery("update carton_headers set order_header_id=null,user_modified='eod_datapurge',date_modified=getdate() where carton_header_id in(" + otherOrderCartonIds + ")").ExecuteUpdate();
                            }

                            #endregion

                            #region Order Header Statuses
                            //Order Header Statuses
                            criteria = DetachedCriteria.For<OrderStatus>()
                                                                    .SetProjection(Projections.ProjectionList()
                                                                        .Add(Projections.Property("Id"), "Id")
                                                                        .Add(Projections.Property("Version"), "Version"))
                                                                    .Add(Restrictions.Eq("OrderHeader.Id", orderHeader.Id))
                                                                    .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.OrderStatus>());
                            var orderStatuses = Repositories.Get<OrderStatus>().List(criteria);

                            if (orderComments != null && orderComments.Count > 0) DebugFormat(string.Format(" {0}  - orders header comments found to perform the CO purge - {1}", jobname, orderComments.Count));
                            else DebugFormat(string.Format("{0}  - No order header comments found to perform the CO purge ", jobname));

                            if (orderStatuses != null && orderStatuses.Count != 0)
                            {
                                string orderStatusIds = string.Join(",", orderStatuses.Select(c => c.Id.Value).ToList<Int32>());
                                Repositories.Get<OrderStatus>().SQLQuery("delete from order_statuses where order_status_id in(" + orderStatusIds + ")").ExecuteUpdate();
                            }
                            #endregion

                            #region Order Header Locations
                            //Order Header Locations
                            criteria = DetachedCriteria.For<OrderLocation>()
                                                                      .SetProjection(Projections.ProjectionList()
                                                                          .Add(Projections.Property("Id"), "Id")
                                                                          .Add(Projections.Property("Version"), "Version"))
                                                                      .Add(Restrictions.Eq("OrderHeader.Id", orderHeader.Id))
                                                                      .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.OrderLocation>());
                            var orderLocations = Repositories.Get<OrderLocation>().List(criteria);

                            if (orderLocations != null && orderLocations.Count > 0) DebugFormat(string.Format(" {0}  - orders header locations found to perform the CO purge - {1}", jobname, orderLocations.Count));
                            else DebugFormat(string.Format("{0}  - No order header locations found to perform the CO purge ", jobname));

                            if (orderLocations != null && orderLocations.Count != 0)
                            {
                                string orderLocationIds = string.Join(",", orderLocations.Select(c => c.Id.Value).ToList<Int32>());
                                Repositories.Get<OrderLocation>().SQLQuery("delete from order_locations where order_location_id in(" + orderLocationIds + ")").ExecuteUpdate();
                            }
                            #endregion

                            #region transactions
                            //transactions
                            criteria = DetachedCriteria.For<Transaction>()
                                                    .SetProjection(Projections.ProjectionList()
                                                    .Add(Projections.Property("Id"), "Id")
                                                    .Add(Projections.Property("Version"), "Version"))
                                                    .Add(Restrictions.Eq("OrderHeader.Id", orderHeader.Id))
                                                    .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.Transaction>());
                            var transactions = Repositories.Get<Transaction>().List(criteria);

                            if (transactions != null && transactions.Count > 0) DebugFormat(string.Format(" {0}  - transactions found to perform the CO purge - {1}", jobname, transactions.Count));
                            else DebugFormat(string.Format("{0}  - No transactions found to perform the CO purge ", jobname));


                            if (transactions != null && transactions.Count != 0)
                            {
                                string transactionsIds = string.Join(",", transactions.Select(c => c.Id.Value).ToList<Int32>());
                                Repositories.Get<Transaction>().SQLQuery("delete from transactions where transaction_id in(" + transactionsIds + ")").ExecuteUpdate();
                            }
                            #endregion

                            #region Other order header dependencies 

                            if (orderHeader.Id.HasValue)
                                UpdateClaimHeaderLinkedToOrderHeader(orderHeader.Id.Value);

                            #endregion

                            //Order Header
                            Repositories.Get<OrderHeader>().SQLQuery("delete from order_headers where order_header_id in (" + orderHeader.Id + ")").ExecuteUpdate();

                            DebugFormat(string.Format("{1} - customer Order#: {0} deleted successfully.", orderHeader.OrderCode, jobname));
                        }
                        else
                            DebugFormat(string.Format("{1} - Can't delete Order#: {0} because it is invoiced.", orderHeader.OrderCode, jobname));


                        DebugFormat(string.Format("{1} - End Purge Customer Order {0}", orderHeader.OrderCode, jobname));
                    }
                    catch (Exception ex)
                    {
                        ErrorFormat(string.Format("{1} - Error while deleting the custoemr order {2} : {0}", ex.Message, jobname, orderHeader.OrderCode));
                        //
                        throw ex;
                    }

                });
            }
        }

        private void DeleteOrderDetail(OrderDetail orderDetail, OrderHeader orderHeader,out List<int> cartonIds)
        {
            try
            {
                cartonIds = new List<int>();

                //Order Details
                DetachedCriteria criteria = DetachedCriteria.For<ItemFulfillment>()
                                        .SetProjection(Projections.ProjectionList()
                                        .Add(Projections.Property("Id"), "Id")
                                        .Add(Projections.Property("CartonHeader.Id"), "CartonId")
                                        .Add(Projections.Property("Version"), "Version"))
                                        .Add(Restrictions.Eq("OrderDetail.Id", orderDetail.Id))
                                        .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ItemFulfillment>());
                var itemfulfillments = Repositories.Get<ItemFulfillment>().List(criteria);

                if (itemfulfillments != null && itemfulfillments.Count > 0)
                {
                    cartonIds = itemfulfillments.Where(c => c.CartonId.HasValue).Select(c => c.CartonId.Value).ToList<int>();

                    DebugFormat(string.Format(" {0}  - order line itemfulfillments found to perform the CO purge - {1}", jobname, itemfulfillments.Count));
                }
                else DebugFormat(string.Format("{0}  - No order line itemfulfillments found to perform the CO purge ", jobname));

                if (itemfulfillments != null && itemfulfillments.Count != 0)
                {
                    foreach (ItemFulfillment itemfulfillment in itemfulfillments)
                    {
                        DeleteItemfulfillments(itemfulfillment);
                    }
                }

                //Inventory Item Details
                criteria = DetachedCriteria.For<InventoryItemDetail>()
                                                        .CreateAlias("InventoryItem", "InventoryItem", NHibernate.SqlCommand.JoinType.LeftOuterJoin)
                                                        .SetProjection(Projections.ProjectionList()
                                                            .Add(Projections.Property("Id"), "Id")
                                                            .Add(Projections.Property("InventoryItem.Id"), "OrderHeaderId")
                                                            .Add(Projections.Property("Version"), "Version"))
                                                        .Add(Restrictions.Eq("OrderDetail", orderDetail))
                                                        .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.InventoryItemDetail>());
                var inventoryItemDetails = Repositories.Get<InventoryItemDetail>().List(criteria);

                if (inventoryItemDetails != null && inventoryItemDetails.Count > 0) DebugFormat(string.Format(" {0}  - order line inventoryItemDetails found to perform the CO purge - {1}", jobname, inventoryItemDetails.Count));
                else DebugFormat(string.Format("{0}  - No order line inventoryItemDetails found to perform the CO purge ", jobname));

                if (inventoryItemDetails != null && inventoryItemDetails.Count != 0)
                {
                    string inventoryItemDetailIds = string.Join(",", inventoryItemDetails.Select(c => c.Id.Value).ToList<Int32>());
                    Repositories.Get<InventoryItemDetail>().SQLQuery("delete from inventory_item_details where inventory_item_detail_id in(" + inventoryItemDetailIds + ")").ExecuteUpdate();
                }
                //return details
                criteria = DetachedCriteria.For<ReturnDetail>()
                                                       .CreateAlias("InventoryItem", "InventoryItem", NHibernate.SqlCommand.JoinType.LeftOuterJoin)
                                                       .SetProjection(Projections.ProjectionList()
                                                           .Add(Projections.Property("Id"), "Id")
                                                           .Add(Projections.Property("Version"), "Version"))
                                                       .Add(Restrictions.Eq("OrderDetail.Id", orderDetail.Id))
                                                       .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.InventoryItemDetail>());
                var returnDetails = Repositories.Get<InventoryItemDetail>().List(criteria);

                if (returnDetails != null && returnDetails.Count > 0)
                {
                    string returnDetailIds = string.Join(",", returnDetails.Select(c => c.Id.Value).ToList<Int32>());
                    Repositories.Get<InventoryItemDetail>().SQLQuery("update return_details set order_detail_id=null,user_modified='eod_datapurge',date_modified=getdate() where return_detail_id in(" + returnDetailIds + ")").ExecuteUpdate();
                }

                //Order Detail
                Repositories.Get<OrderDetail>().SQLQuery("delete from order_details where order_detail_id in(" + orderDetail.Id + ")").ExecuteUpdate();
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        private void DeleteItemfulfillments(ItemFulfillment itemfulfillment)
        {
            try
            {
                //Inventory Picks
                DetachedCriteria criteria = DetachedCriteria.For<InventoryPick>()
                                        .SetProjection(Projections.ProjectionList()
                                        .Add(Projections.Property("Id"), "Id")
                                        .Add(Projections.Property("Version"), "Version"))
                                        .Add(Restrictions.Eq("ItemFulfillment.Id", itemfulfillment.Id))
                                        .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.InventoryPick>());
                var inventoryPicks = Repositories.Get<InventoryPick>().List(criteria);

                if (inventoryPicks != null && inventoryPicks.Count > 0) DebugFormat(string.Format(" {0}  - order line inventoryPicks found to perform the CO purge - {1}", jobname, inventoryPicks.Count));
                else DebugFormat(string.Format("{0}  - No order line inventoryPicks found to perform the CO purge ", jobname));

                if (inventoryPicks != null && inventoryPicks.Count != 0)
                {
                    foreach (InventoryPick inventoryPick in inventoryPicks)
                    {
                        DeleteInventoryPicks(inventoryPick);
                    }
                }
                //License Plates
                criteria = DetachedCriteria.For<LicensePlate>()
                                        .SetProjection(Projections.ProjectionList()
                                        .Add(Projections.Property("Id"), "Id")
                                        .Add(Projections.Property("Version"), "Version"))
                                        .Add(Restrictions.Eq("ItemFulfillment.Id", itemfulfillment.Id))
                                        .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.LicensePlate>());
                var licensePlates = Repositories.Get<LicensePlate>().List(criteria);

                if (licensePlates != null && licensePlates.Count > 0) DebugFormat(string.Format(" {0}  - order line LicensePlates associated to itemfulfillments found to perform the CO purge - {1}", jobname, licensePlates.Count));
                else DebugFormat(string.Format("{0}  - No order line LicensePlates associated to itemfulfillments found to perform the CO purge ", jobname));

                if (licensePlates != null && licensePlates.Count != 0)
                {
                    foreach (LicensePlate licensePlate in licensePlates)
                    {
                        DeleteLicensePlate(licensePlate);
                    }
                }

                //Itemfulfillment
                Repositories.Get<ItemFulfillment>().SQLQuery("delete from item_fulfillments where item_fulfillment_id in(" + itemfulfillment.Id + ")").ExecuteUpdate();
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        private void DeleteInventoryPicks(InventoryPick inventoryPick)
        {
            try
            {
                //Carton Details
                DetachedCriteria criteria = DetachedCriteria.For<CartonDetail>()
                                        .SetProjection(Projections.ProjectionList()
                                        .Add(Projections.Property("Id"), "Id")
                                        .Add(Projections.Property("Version"), "Version"))
                                        .Add(Restrictions.Eq("InventoryPick.Id", inventoryPick.Id))
                                        .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.CartonDetail>());
                var cartonDetails = Repositories.Get<CartonDetail>().List(criteria);

                if (cartonDetails != null && cartonDetails.Count > 0) DebugFormat(string.Format(" {0}  - carton details found to perform the CO purge - {1}", jobname, cartonDetails.Count));
                else DebugFormat(string.Format("{0}  - No carton details found to perform the CO purge ", jobname));

                if (cartonDetails != null && cartonDetails.Count != 0)
                {
                    string cartonDetailsIds = string.Join(",", cartonDetails.Select(c => c.Id.Value).ToList<Int32>());
                    Repositories.Get<CartonDetail>().SQLQuery("delete from carton_details where carton_detail_id in(" + cartonDetailsIds + ")").ExecuteUpdate();
                }
                //Shipment Details
                criteria = DetachedCriteria.For<ShipmentDetail>()
                                        .SetProjection(Projections.ProjectionList()
                                        .Add(Projections.Property("Id"), "Id")
                                        .Add(Projections.Property("Version"), "Version"))
                                        .Add(Restrictions.Eq("InventoryPick.Id", inventoryPick.Id))
                                        .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ShipmentDetail>());
                var shipmentDetails = Repositories.Get<ShipmentDetail>().List(criteria);

                if (cartonDetails != null && cartonDetails.Count > 0) DebugFormat(string.Format(" {0}  - Shipment details found to perform the CO purge - {1}", jobname, cartonDetails.Count));
                else DebugFormat(string.Format("{0}  - No shipment details found to perform the CO purge ", jobname));

                if (shipmentDetails != null && shipmentDetails.Count != 0)
                {
                    foreach (ShipmentDetail shipmentDetail in shipmentDetails)
                    {
                        DeleteShipmentDetail(shipmentDetail);
                    }
                }
               
                //inventoryPicks
                Repositories.Get<InventoryPick>().SQLQuery("delete from inventory_picks where inventory_pick_id in(" + inventoryPick.Id + ")").ExecuteUpdate();
            }
            catch (Exception ex)
            {

                throw ex;
            }
        }

        private void DeleteLicensePlate(LicensePlate licensePlate)
        {
            try
            {
                //Shipment Details
                DetachedCriteria criteria = DetachedCriteria.For<ShipmentDetail>()
                                        .SetProjection(Projections.ProjectionList()
                                        .Add(Projections.Property("Id"), "Id")
                                        .Add(Projections.Property("ShipmentHeader.Id"), "ShipmentHeaderId")
                                        .Add(Projections.Property("Version"), "Version"))
                                        .Add(Restrictions.Eq("LicensePlate.Id", licensePlate.Id))
                                        .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ShipmentDetail>());
                var shipmentDetails = Repositories.Get<ShipmentDetail>().List(criteria);

                if (shipmentDetails != null && shipmentDetails.Count != 0)
                {
                    foreach (ShipmentDetail shipmentDetail in shipmentDetails)
                    {
                        DeleteShipmentDetail(shipmentDetail);
                    }
                }
                //License Plate Locations
                criteria = DetachedCriteria.For<LicensePlateLocation>()
                                        .SetProjection(Projections.ProjectionList()
                                        .Add(Projections.Property("Id"), "Id")
                                        .Add(Projections.Property("Version"), "Version"))
                                        .Add(Restrictions.Eq("LicensePlate.Id", licensePlate.Id))
                                        .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.LicensePlateLocation>());
                var lpnLocations = Repositories.Get<LicensePlateLocation>().List(criteria);

                if (lpnLocations != null && lpnLocations.Count > 0) DebugFormat(string.Format(" {0}  - LPN locations found to perform the CO purge - {1}", jobname, lpnLocations.Count));
                else DebugFormat(string.Format("{0}  - No shipment line charges found to perform the CO purge ", jobname));

                if (lpnLocations != null && lpnLocations.Count != 0)
                {
                    string lpnLocationsids = string.Join(",", lpnLocations.Select(c => c.Id.Value).ToList<Int32>());
                    Repositories.Get<LicensePlateLocation>().SQLQuery("delete from license_plate_locations where license_plate_location_id in(" + lpnLocationsids + ")").ExecuteUpdate();
                }

                //License Plate statuses
                criteria = DetachedCriteria.For<LicensePlateStatus>()
                                        .SetProjection(Projections.ProjectionList()
                                        .Add(Projections.Property("Id"), "Id")
                                        .Add(Projections.Property("Version"), "Version"))
                                        .Add(Restrictions.Eq("LicensePlate.Id", licensePlate.Id))
                                        .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.LicensePlateStatus>());
                var lpnStatuses = Repositories.Get<LicensePlateStatus>().List(criteria);

                if (lpnStatuses != null && lpnStatuses.Count > 0) DebugFormat(string.Format(" {0}  - LPN statuses found to perform the CO purge - {1}", jobname, lpnStatuses.Count));
                else DebugFormat(string.Format("{0}  - No LPN statuses found to perform the CO purge ", jobname));

                if (lpnStatuses != null && lpnStatuses.Count != 0)
                {
                    string lpnStatusesids = string.Join(",", lpnStatuses.Select(c => c.Id.Value).ToList<Int32>());
                    Repositories.Get<LicensePlateStatus>().SQLQuery("delete from license_plate_statuses where license_plate_statuse_id in(" + lpnStatusesids + ")").ExecuteUpdate();
                }
                //Item Transactions
                criteria = DetachedCriteria.For<ItemTransaction>()
                                        .SetProjection(Projections.ProjectionList()
                                        .Add(Projections.Property("Id"), "Id")
                                        .Add(Projections.Property("Version"), "Version"))
                                        .Add(Restrictions.Or(Restrictions.Eq("LicensePlateFrom.Id", licensePlate.Id),
                                        Restrictions.Eq("LicensePlateTo.Id", licensePlate.Id)))
                                        .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ItemTransaction>());
                var itemTransactions = Repositories.Get<ItemTransaction>().List(criteria);

                if (itemTransactions != null && itemTransactions.Count > 0) DebugFormat(string.Format(" {0}  - ItemTransactions found to perform the CO purge - {1}", jobname, itemTransactions.Count));
                else DebugFormat(string.Format("{0}  - No ItemTransactions found to perform the CO purge ", jobname));

                if (itemTransactions != null && itemTransactions.Count != 0)
                {
                    string itemTransactionsIds = string.Join(",", itemTransactions.Where(c => c.Id.HasValue).Select(c => c.Id.Value).Distinct().ToList<Int32>());
                    Repositories.Get<ItemTransaction>().SQLQuery("delete from item_transactions where item_transaction_id in(" + itemTransactionsIds + ")").ExecuteUpdate();
                }
                //licenseplates
                Repositories.Get<LicensePlate>().SQLQuery("delete from license_plates where license_plate_id in(" + licensePlate.Id + ")").ExecuteUpdate();
            }
            catch (Exception ex)
            {

                throw ex;
            }
        }

        private void DeleteShipmentDetail(ShipmentDetail shipmentDetail)
        {
            try
            {
                //Shipment Detail Charges

                DetachedCriteria criteria = DetachedCriteria.For<ShipmentCharge>()
                                                            .SetProjection(Projections.ProjectionList()
                                                                .Add(Projections.Property("Id"), "Id")
                                                                .Add(Projections.Property("Version"), "Version"))
                                                            .Add(Restrictions.Eq("ShipmentDetail", shipmentDetail))
                                                            .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ShipmentCharge>());
                var shipmentDetailCharges = Repositories.Get<ShipmentCharge>().List(criteria);

                if (shipmentDetailCharges != null && shipmentDetailCharges.Count > 0) DebugFormat(string.Format(" {0}  - shipment line charges found to perform the CO purge - {1}", jobname, shipmentDetailCharges.Count));
                else DebugFormat(string.Format("{0}  - No shipment line charges found to perform the CO purge ", jobname));

                if (shipmentDetailCharges != null && shipmentDetailCharges.Count != 0)
                {
                    string shipmentDetailChargesids = string.Join(",", shipmentDetailCharges.Select(c => c.Id.Value).ToList<Int32>());
                    Repositories.Get<ShipmentCharge>().SQLQuery("delete from shipment_charges where shipment_charge_id in(" + shipmentDetailChargesids + ")").ExecuteUpdate();
                }

                //Shipment Detail Charges
                criteria = DetachedCriteria.For<ShipmentReferenceCode>()
                                                        .SetProjection(Projections.ProjectionList()
                                                            .Add(Projections.Property("Id"), "Id")
                                                            .Add(Projections.Property("Version"), "Version"))
                                                        .Add(Restrictions.Eq("ShipmentDetail", shipmentDetail))
                                                        .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ShipmentReferenceCode>());
                var shipmentReferenceCodes = Repositories.Get<ShipmentReferenceCode>().List(criteria);

                if (shipmentReferenceCodes != null && shipmentReferenceCodes.Count > 0) DebugFormat(string.Format(" {0}  - shipment line reference codes found to perform the CO purge - {1}", jobname, shipmentReferenceCodes.Count));
                else DebugFormat(string.Format("{0}  - No shipment line referenece codes found to perform the CO purge ", jobname));

                if (shipmentReferenceCodes != null && shipmentReferenceCodes.Count != 0)
                {
                    string shipmentReferenceCodesids = string.Join(",", shipmentReferenceCodes.Select(c => c.Id.Value).ToList<Int32>());
                    Repositories.Get<ShipmentReferenceCode>().SQLQuery("delete from shipment_reference_codes where shipment_reference_code_id in(" + shipmentReferenceCodesids + ")").ExecuteUpdate();
                }

                //Shipment Detail
                Repositories.Get<ShipmentDetail>().SQLQuery("delete from shipment_details where shipment_detail_id in(" + shipmentDetail.Id + ")").ExecuteUpdate();
            }
            catch (Exception ex)
            {

                throw ex;
            }
        }

        private void UpdateClaimHeaderLinkedToOrderHeader(int orderHeaderId)
        {
            DetachedCriteria criteria = DetachedCriteria.For<ClaimHeader>()
                                        .SetProjection(Projections.ProjectionList()
                                        .Add(Projections.Property("Id"), "Id")
                                        .Add(Projections.Property("Version"), "Version"))
                                        .Add(Restrictions.Eq("OrderHeader.Id", orderHeaderId))
                                        .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ClaimHeader>());
            var claimHeaders = Repositories.Get<ClaimHeader>().List(criteria);

            if (claimHeaders != null && claimHeaders.Count > 0)
            {
                DebugFormat(string.Format(" {0}  - claim headers found  has to perform the CO purge - {1}", jobname, claimHeaders.Count));
                //
                string claimHeaderIds = string.Join(",", claimHeaders.Where(c => c.Id.HasValue).Select(c => c.Id.Value).ToList<int>());
                Repositories.Get<ItemTransaction>().SQLQuery("update claim_headers set order_header_id=null,user_modified='eod_datapurge',date_modified=getdate()  where claim_header_id  in(" + claimHeaderIds + ")").ExecuteUpdate();
            }
            else DebugFormat(string.Format("{0}  - No claim headers found to perform the CO purge ", jobname));
        }

        #endregion

        #region Delete PO

        private void DeletePurchaseOrder(PurchaseOrderHeader porderHeader)
        {
            porderHeader.PurchaseOrderCode = porderHeader.FindFullCode();
            //
            DebugFormat(string.Format("{1} - Start Purge Purchase Order Code {0}", porderHeader.PurchaseOrderCode, jobname));

            using (UnitWrapper wrapper = new UnitWrapper())
            {
                wrapper.Execute(() =>
                {
                    try
                    {
                        //Purchase Order Details
                        DetachedCriteria criteria = DetachedCriteria.For<PurchaseOrderDetail>()
                                                .SetProjection(Projections.ProjectionList()
                                                .Add(Projections.Property("Id"), "Id")
                                                .Add(Projections.Property("Version"), "Version"))
                                                .Add(Restrictions.Eq("PurchaseOrderHeader.Id", porderHeader.Id))
                                                .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.PurchaseOrderDetail>());
                        var porderDetails = Repositories.Get<PurchaseOrderDetail>().List(criteria);

                        if (_logger.IsDebugEnabled)
                        {
                            if (porderDetails != null && porderDetails.Count > 0) _logger.DebugFormat("{0}  - purchase order lines found to perform the PO purge - {1}", jobname, porderDetails.Count);
                            else _logger.DebugFormat("{0}  - No purchase order lines found to perform the PO purge ", jobname);
                        }

                        if (porderDetails != null && porderDetails.Count != 0)
                        {
                            foreach (PurchaseOrderDetail porderDetail in porderDetails)
                            {
                                DeletePurchaseOrderDetail(porderDetail, porderHeader);
                            }
                        }

                        //Purchase Order Header Statuses
                        criteria = DetachedCriteria.For<PurchaseOrderStatus>()
                                                                .SetProjection(Projections.ProjectionList()
                                                                    .Add(Projections.Property("Id"), "Id")
                                                                    .Add(Projections.Property("Version"), "Version"))
                                                                .Add(Restrictions.Eq("PurchaseOrderHeader.Id", porderHeader.Id))
                                                                .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.PurchaseOrderStatus>());
                        var porderStatuses = Repositories.Get<PurchaseOrderStatus>().List(criteria);

                        if (_logger.IsDebugEnabled)
                        {
                            if (porderStatuses != null && porderStatuses.Count > 0) _logger.DebugFormat("{0}  - purchase order statuses found to perform the PO purge - {1}", jobname, porderStatuses.Count);
                            else _logger.DebugFormat("{0}  - No purchase order header comments found to perform the PO purge ", jobname);
                        }

                        if (porderStatuses != null && porderStatuses.Count != 0)
                        {
                            string porderStatusIds = string.Join(",", porderStatuses.Select(c => c.Id.Value).ToList<Int32>());
                            Repositories.Get<PurchaseOrderHeader>().SQLQuery("delete from purchase_order_statuses where purchase_order_status_id in(" + porderStatusIds + ")").ExecuteUpdate();
                        }
                        //Purchase Order Header Locations
                        criteria = DetachedCriteria.For<PurchaseOrderLocation>()
                                                                  .SetProjection(Projections.ProjectionList()
                                                                      .Add(Projections.Property("Id"), "Id")
                                                                      .Add(Projections.Property("Version"), "Version"))
                                                                  .Add(Restrictions.Eq("PurchaseOrderHeader.Id", porderHeader.Id))
                                                                  .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.PurchaseOrderLocation>());
                        var porderLocations = Repositories.Get<PurchaseOrderLocation>().List(criteria);

                        if (_logger.IsDebugEnabled)
                        {
                            if (porderLocations != null && porderLocations.Count > 0) _logger.DebugFormat("{0}  - purchase order locations found to perform the PO purge - {1}", jobname, porderLocations.Count);
                            else _logger.DebugFormat("{0}  - No purchase order header locations found to perform the PO purge ", jobname);
                        }

                        if (porderLocations != null && porderLocations.Count != 0)
                        {
                            string porderLocationIds = string.Join(",", porderLocations.Select(c => c.Id.Value).ToList<Int32>());
                            Repositories.Get<PurchaseOrderHeader>().SQLQuery("delete from purchase_order_locations where purchase_order_location_id in(" + porderLocationIds + ")").ExecuteUpdate();
                        }

                        //Purchase Order Header Charge
                        criteria = DetachedCriteria.For<PurchaseOrderCharge>()
                                                                        .SetProjection(Projections.ProjectionList()
                                                                            .Add(Projections.Property("Id"), "Id")
                                                                            .Add(Projections.Property("Version"), "Version"))
                                                                        .Add(Restrictions.Eq("PurchaseOrderHeader.Id", porderHeader.Id))
                                                                        .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.PurchaseOrderCharge>());
                        var porderCharges = Repositories.Get<PurchaseOrderCharge>().List(criteria);

                        if (_logger.IsDebugEnabled)
                        {
                            if (porderCharges != null && porderCharges.Count > 0) _logger.DebugFormat("{0}  - purchase order charges found to perform the PO purge - {1}", jobname, porderCharges.Count);
                            else _logger.DebugFormat("{0}  - No purchase order header charges found to perform the PO purge ", jobname);
                        }

                        if (porderCharges != null && porderCharges.Count != 0)
                        {
                            string porderChargeIds = string.Join(",", porderCharges.Select(c => c.Id.Value).ToList<Int32>());
                            Repositories.Get<PurchaseOrderHeader>().SQLQuery("delete from purchase_order_charges where purchase_order_charge_id in(" + porderChargeIds + ")").ExecuteUpdate();
                        }
                        //Purchase Order Header References
                        criteria = DetachedCriteria.For<PurchaseOrderReference>()
                                                                        .SetProjection(Projections.ProjectionList()
                                                                            .Add(Projections.Property("Id"), "Id")
                                                                            .Add(Projections.Property("Version"), "Version"))
                                                                        .Add(Restrictions.Eq("PurchaseOrderHeader.Id", porderHeader.Id))
                                                                        .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.PurchaseOrderReference>());
                        var porderReferences = Repositories.Get<PurchaseOrderReference>().List(criteria);

                        if (_logger.IsDebugEnabled)
                        {
                            if (porderReferences != null && porderReferences.Count > 0) _logger.DebugFormat("{0}  - purchase order references found to perform the PO purge - {1}", jobname, porderReferences.Count);
                            else _logger.DebugFormat("{0}  - No purchase order header references found to perform the PO purge ", jobname);
                        }

                        if (porderReferences != null && porderReferences.Count != 0)
                        {
                            string porderreferenceIds = string.Join(",", porderReferences.Select(c => c.Id.Value).ToList<Int32>());
                            Repositories.Get<PurchaseOrderHeader>().SQLQuery("delete from purchase_order_references where purchase_order_reference_id in(" + porderreferenceIds + ")").ExecuteUpdate();
                        }

                        //Carrier Appointments
                        criteria = DetachedCriteria.For<CarrierAppointment>()
                                                            .SetProjection(Projections.ProjectionList()
                                                                .Add(Projections.Property("Id"), "Id")
                                                                .Add(Projections.Property("Version"), "Version"))
                                                            .Add(Restrictions.Eq("PurchaseOrderHeader.Id", porderHeader.Id))
                                                            .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.CarrierAppointment>());
                        var carrierAppointments = Repositories.Get<CarrierAppointment>().List(criteria);

                        if (_logger.IsDebugEnabled)
                        {
                            if (carrierAppointments != null && carrierAppointments.Count > 0) _logger.DebugFormat("{0}  - carrier appointments found to perform the PO purge - {1}", jobname, carrierAppointments.Count);
                            else _logger.DebugFormat("{0}  - No carrier appointments found to perform the PO purge ", jobname);
                        }

                        if (carrierAppointments != null && carrierAppointments.Count != 0)
                        {
                            foreach (CarrierAppointment carrierAppointment in carrierAppointments)
                            {
                                DeleteCarrierAppointment(carrierAppointment, porderHeader);
                            }
                        }

                        //Receipt Headers
                        criteria = DetachedCriteria.For<ReceiptHeader>()
                                                                        .SetProjection(Projections.ProjectionList()
                                                                            .Add(Projections.Property("Id"), "Id")
                                                                            .Add(Projections.Property("Version"), "Version"))
                                                                        .Add(Restrictions.Eq("PurchaseOrderHeader.Id", porderHeader.Id))
                                                                        .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ReceiptHeader>());
                        var receiptHeaders = Repositories.Get<ReceiptHeader>().List(criteria);

                        if (_logger.IsDebugEnabled)
                        {
                            if (receiptHeaders != null && receiptHeaders.Count > 0) _logger.DebugFormat("{0}  - receipt headers found to perform the PO purge - {1}", jobname, receiptHeaders.Count);
                            else _logger.DebugFormat("{0}  - No receipt headers found to perform the PO purge ", jobname);
                        }

                        if (receiptHeaders != null && receiptHeaders.Count != 0)
                        {
                            string receiptHeaderIds = string.Join(",", receiptHeaders.Select(c => c.Id.Value).ToList<Int32>());
                            Repositories.Get<PurchaseOrderHeader>().SQLQuery("delete from receipt_headers where receipt_header_id in(" + receiptHeaderIds + ")").ExecuteUpdate();
                        }

                        //Purchase Order Header
                        Repositories.Get<PurchaseOrderHeader>().SQLQuery("delete from purchase_order_headers where purchase_order_header_id in (" + porderHeader.Id + ")").ExecuteUpdate();

                        DebugFormat(string.Format("{1} - End Purge Purchase Order Code {0}", porderHeader.PurchaseOrderCode, jobname));


                    }
                    catch (Exception ex)
                    {
                        if (_logger.IsErrorEnabled) _logger.ErrorFormat("{1}  - Error while deleting the purchase orders: {0}", ex.Message, jobname);
                        //
                        throw ex;
                    }


                });
            }

        }

        private void DeletePurchaseOrderDetail(PurchaseOrderDetail porderDetail, PurchaseOrderHeader porderHeader)
        {
            //Purchase Order Details
            DetachedCriteria criteria = DetachedCriteria.For<ItemFulfillment>()
                                    .SetProjection(Projections.ProjectionList()
                                    .Add(Projections.Property("Id"), "Id")
                                    .Add(Projections.Property("Version"), "Version"))
                                    .Add(Restrictions.Eq("PurchaseOrderDetail.Id", porderDetail.Id))
                                    .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ItemFulfillment>());
            var itemfulfillments = Repositories.Get<ItemFulfillment>().List(criteria);

            if (_logger.IsDebugEnabled)
            {
                if (itemfulfillments != null && itemfulfillments.Count > 0) _logger.DebugFormat("{0}  - purchase order line item fulfillments found to perform the PO purge - {1}", jobname, itemfulfillments.Count);
                else _logger.DebugFormat("{0}  - No purchase order line item fulfillments found to perform the PO purge ", jobname);
            }

            if (itemfulfillments != null && itemfulfillments.Count != 0)
            {
                string itemFulfillmentIds = string.Join(",", itemfulfillments.Select(c => c.Id.Value).ToList<Int32>());
                Repositories.Get<PurchaseOrderDetail>().SQLQuery("delete from item_fulfillments where item_fulfillment_id in(" + itemFulfillmentIds + ")").ExecuteUpdate();
            }
            //Purchase Order Detail Charges
            criteria = DetachedCriteria.For<PurchaseOrderCharge>()
                                                            .SetProjection(Projections.ProjectionList()
                                                                .Add(Projections.Property("Id"), "Id")
                                                                .Add(Projections.Property("Version"), "Version"))
                                                            .Add(Restrictions.Eq("PurchaseOrderDetail.Id", porderDetail.Id))
                                                            .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.PurchaseOrderCharge>());
            var porderCharges = Repositories.Get<PurchaseOrderCharge>().List(criteria);

            if (_logger.IsDebugEnabled)
            {
                if (porderCharges != null && porderCharges.Count > 0) _logger.DebugFormat("{0}  - purchase order line charges found to perform the PO purge - {1}", jobname, porderCharges.Count);
                else _logger.DebugFormat("{0}  - No purchase order line charges found to perform the PO purge ", jobname);
            }

            if (porderCharges != null && porderCharges.Count != 0)
            {
                string porderChargeIds = string.Join(",", porderCharges.Select(c => c.Id.Value).ToList<Int32>());
                Repositories.Get<PurchaseOrderDetail>().SQLQuery("delete from purchase_order_charges where purchase_order_charge_id in(" + porderChargeIds + ")").ExecuteUpdate();
            }
            //Purchase Order Fundings
            criteria = DetachedCriteria.For<PurchaseOrderFunding>()
                                                                    .SetProjection(Projections.ProjectionList()
                                                                        .Add(Projections.Property("Id"), "Id"))
                                                                    .Add(Restrictions.Eq("PurchaseOrderDetail.Id", porderDetail.Id))
                                                                    .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.PurchaseOrderFunding>());
            var porderDetailFundings = Repositories.Get<PurchaseOrderFunding>().List(criteria);

            if (_logger.IsDebugEnabled)
            {
                if (porderDetailFundings != null && porderDetailFundings.Count > 0) _logger.DebugFormat("{0}  - purchase order line fundings found to perform the PO purge - {1}", jobname, porderDetailFundings.Count);
                else _logger.DebugFormat("{0}  - No purchase order line fundings found to perform the PO purge ", jobname);
            }

            if (porderDetailFundings != null && porderDetailFundings.Count != 0)
            {
                string porderDetailFundingIds = string.Join(",", porderDetailFundings.Select(c => c.Id.Value).ToList<Int32>());
                Repositories.Get<PurchaseOrderDetail>().SQLQuery("delete from purchase_order_fundings where purchase_order_funding_id in(" + porderDetailFundingIds + ")").ExecuteUpdate();
            }

            //item transactions
            criteria = DetachedCriteria.For<ItemTransaction>()
                                    .SetProjection(Projections.ProjectionList()
                                    .Add(Projections.Property("Id"), "Id")
                                    .Add(Projections.Property("Version"), "Version"))
                                    .Add(Restrictions.Eq("PurchaseOrderDetail.Id", porderDetail.Id))
                                    .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ItemTransaction>());
            var ItemTransactions = Repositories.Get<ItemTransaction>().List(criteria);

            if (_logger.IsDebugEnabled)
            {
                if (ItemTransactions != null && ItemTransactions.Count > 0) _logger.DebugFormat("{0}  - purchase order line item trasactions found to perform the PO purge - {1}", jobname, ItemTransactions.Count);
                else _logger.DebugFormat("{0}  - No purchase order line item trasactions found to perform the PO purge ", jobname);
            }

            if (ItemTransactions != null && ItemTransactions.Count != 0)
            {
                string ItemTransactionsIds = string.Join(",", ItemTransactions.Select(c => c.Id.Value).ToList<Int32>());
                Repositories.Get<PurchaseOrderDetail>().SQLQuery("delete from item_transactions where item_transaction_id in(" + ItemTransactionsIds + ")").ExecuteUpdate();
            }

            // Receipt Details
            criteria = DetachedCriteria.For<ReceiptDetail>()
                                    .SetProjection(Projections.ProjectionList()
                                    .Add(Projections.Property("Id"), "Id")
                                    .Add(Projections.Property("Version"), "Version"))
                                    .Add(Restrictions.Eq("PurchaseOrderDetail.Id", porderDetail.Id))
                                    .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ReceiptDetail>());
            var receiptDetails = Repositories.Get<ReceiptDetail>().List(criteria);

            if (_logger.IsDebugEnabled)
            {
                if (receiptDetails != null && receiptDetails.Count > 0) _logger.DebugFormat("{0}  - purchase order line receipt details found to perform the PO purge - {1}", jobname, receiptDetails.Count);
                else _logger.DebugFormat("{0}  - No purchase order line receipt details found to perform the PO purge ", jobname);
            }

            if (receiptDetails != null && receiptDetails.Count != 0)
            {
                foreach (ReceiptDetail receiptDetail in receiptDetails)
                {
                    DeletePOReceiptDetail(receiptDetail, porderDetail);
                }
            }
            // Return Details
            criteria = DetachedCriteria.For<ReturnDetail>()
                                  .SetProjection(Projections.ProjectionList()
                                  .Add(Projections.Property("Id"), "Id")
                                  .Add(Projections.Property("ReturnHeader"), "ReturnHeader")
                                  .Add(Projections.Property("Version"), "Version"))
                                  .Add(Restrictions.Eq("PurchaseOrderDetail.Id", porderDetail.Id))
                                  .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ReturnDetail>());
            var returnDetails = Repositories.Get<ReturnDetail>().List(criteria);

            if (_logger.IsDebugEnabled)
            {
                if (returnDetails != null && returnDetails.Count > 0) _logger.DebugFormat("{0}  - return details for purchase order line found to perform the PO purge - {1}", jobname, returnDetails.Count);
                else _logger.DebugFormat("{0}  - No return details for purchase order line found to perform the PO purge ", jobname);
            }

            if (returnDetails != null && returnDetails.Count != 0)
            {
                string returnDetailIds = string.Join(",", returnDetails.Select(c => c.Id.Value).ToList<Int32>());
                string returnHeaderIds = string.Join(",", returnDetails.Select(c => c.ReturnHeader.Id.Value).Distinct().ToList<Int32>());
                List<Int32> returnHeadersList = returnDetails.Select(c => c.ReturnHeader.Id.Value).Distinct().ToList<Int32>();

                if (_logger.IsDebugEnabled)
                {
                    if (returnHeaderIds != null && returnHeadersList.Count > 0) _logger.DebugFormat("{0}  - return headers found to perform the PO purge - {1}", jobname, returnHeadersList.Count);
                    else _logger.DebugFormat("{0}  - No return headers to perform the PO purge ", jobname);
                }

                if (returnHeadersList.Count > 0)
                {
                    //Return Reason Details
                    criteria = DetachedCriteria.For<ReturnReasonDetail>()
                                        .SetProjection(Projections.ProjectionList()
                                        .Add(Projections.Property("Id"), "Id")
                                        .Add(Projections.Property("Version"), "Version"))
                                        .Add(Restrictions.In("ReturnHeader.Id", returnHeadersList))
                                        .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ReturnReasonDetail>());
                    var returnReasonDetails = Repositories.Get<ReturnReasonDetail>().List(criteria);

                    if (_logger.IsDebugEnabled)
                    {
                        if (returnReasonDetails != null && returnReasonDetails.Count > 0) _logger.DebugFormat("{0}  - return reason details for return headers found to perform the PO purge - {1}", jobname, returnReasonDetails.Count);
                        else _logger.DebugFormat("{0}  - No return reason details for return headers found to perform the PO purge ", jobname);
                    }

                    if (returnReasonDetails != null && returnReasonDetails.Count != 0)
                    {
                        string returnReasonDetailIds = string.Join(",", returnReasonDetails.Select(c => c.Id.Value).ToList<Int32>());
                        Repositories.Get<ReceiptDetail>().SQLQuery("delete from return_reason_details where return_reason_detail_id in(" + returnReasonDetailIds + ")").ExecuteUpdate();
                    }

                    //Return Statuses
                    criteria = DetachedCriteria.For<ReturnStatus>()
                                            .SetProjection(Projections.ProjectionList()
                                            .Add(Projections.Property("Id"), "Id"))
                                            .Add(Restrictions.In("ReturnHeader.Id", returnHeadersList))
                                            .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ReturnStatus>());
                    var returnStatuses = Repositories.Get<ReturnStatus>().List(criteria);

                    if (_logger.IsDebugEnabled)
                    {
                        if (returnStatuses != null && returnStatuses.Count > 0) _logger.DebugFormat("{0}  - return statuses for return headers found to perform the PO purge - {1}", jobname, returnStatuses.Count);
                        else _logger.DebugFormat("{0}  - No return statuses for return headers found to perform the PO purge ", jobname);
                    }

                    if (returnStatuses != null && returnStatuses.Count != 0)
                    {
                        string returnStatusIds = string.Join(",", returnStatuses.Select(c => c.Id.Value).ToList<Int32>());
                        Repositories.Get<ReceiptDetail>().SQLQuery("delete from return_statuses where return_status_id in(" + returnStatusIds + ")").ExecuteUpdate();
                    }

                    //Return Locations
                    criteria = DetachedCriteria.For<ReturnLocation>()
                                            .SetProjection(Projections.ProjectionList()
                                            .Add(Projections.Property("Id"), "Id"))
                                            .Add(Restrictions.In("ReturnHeader.Id", returnHeadersList))
                                            .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ReturnLocation>());
                    var returnLocations = Repositories.Get<ReturnLocation>().List(criteria);

                    if (_logger.IsDebugEnabled)
                    {
                        if (returnLocations != null && returnLocations.Count > 0) _logger.DebugFormat("{0}  - return locations for return headers found to perform the PO purge - {1}", jobname, returnLocations.Count);
                        else _logger.DebugFormat("{0}  - No return locations for return headers found to perform the PO purge ", jobname);
                    }

                    if (returnLocations != null && returnLocations.Count != 0)
                    {
                        string returnLocationIds = string.Join(",", returnLocations.Select(c => c.Id.Value).ToList<Int32>());
                        Repositories.Get<ReceiptDetail>().SQLQuery("delete from return_locations where return_location_id in(" + returnLocationIds + ")").ExecuteUpdate();
                    }
                }

                Repositories.Get<PurchaseOrderDetail>().SQLQuery("delete from return_details where return_detail_id in(" + returnDetailIds + ")").ExecuteUpdate();

                if (returnHeadersList.Count > 0)
                {
                    Repositories.Get<PurchaseOrderDetail>().SQLQuery("delete from return_headers where return_header_id in(" + returnHeaderIds + ")").ExecuteUpdate();
                }
            }

            //Purchase Order Detail
            Repositories.Get<PurchaseOrderDetail>().SQLQuery("delete from purchase_order_details where purchase_order_detail_id in(" + porderDetail.Id + ")").ExecuteUpdate();

        }

        private void DeletePOReceiptDetail(ReceiptDetail receiptDetail, PurchaseOrderDetail porderDetail)
        {
            //Set the Receipt Details as NULL for Inventory item 
            DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
                                                                   .SetProjection(Projections.ProjectionList()
                                                                       .Add(Projections.Property("Id"), "Id")
                                                                       .Add(Projections.Property("Version"), "Version"))
                                                                   .Add(Restrictions.Eq("ReceiptDetail.Id", receiptDetail.Id))
                                                                   .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.InventoryItem>());
            var inventoryItems = Repositories.Get<InventoryItem>().List(criteria);

            if (_logger.IsDebugEnabled)
            {
                if (inventoryItems != null && inventoryItems.Count > 0) _logger.DebugFormat("{0}  - inventory items for purchase order line receipt detail - {2} found to perform the PO purge - {1}", jobname, inventoryItems.Count, receiptDetail.Id);
                else _logger.DebugFormat("{0}  - No inventory items for purchase order line receipt detail - {1} found to perform the PO purge ", jobname, receiptDetail.Id);
            }

            if (inventoryItems != null && inventoryItems.Count != 0)
            {
                string inventoryItemsIds = string.Join(",", inventoryItems.Select(c => c.Id.Value).ToList<Int32>());
                Repositories.Get<ReceiptDetail>().SQLQuery("update inventory_items set receipt_detail_id=NULL,user_modified='ircodn_buslog',date_modified=getdate() where inventory_item_id in(" + inventoryItemsIds + ")").ExecuteUpdate();
            }

            //Receipt Documents
            criteria = DetachedCriteria.For<ReceiptDocument>()
                                    .SetProjection(Projections.ProjectionList()
                                    .Add(Projections.Property("Id"), "Id")
                                    .Add(Projections.Property("Version"), "Version"))
                                    .Add(Restrictions.Eq("ReceiptDetail.Id", receiptDetail.Id))
                                    .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ReceiptDocument>());
            var receiptDocuments = Repositories.Get<ReceiptDocument>().List(criteria);

            if (_logger.IsDebugEnabled)
            {
                if (receiptDocuments != null && receiptDocuments.Count > 0) _logger.DebugFormat("{0}  - receipt documents for purchase order line receipt detail - {2} found to perform the PO purge - {1}", jobname, receiptDocuments.Count, receiptDetail.Id);
                else _logger.DebugFormat("{0}  - No receipt documents for purchase order line receipt details - {1} found to perform the PO purge ", jobname, receiptDetail.Id);
            }

            if (receiptDocuments != null && receiptDocuments.Count != 0)
            {
                string receiptDocumentIds = string.Join(",", receiptDocuments.Select(c => c.Id.Value).ToList<Int32>());
                Repositories.Get<ReceiptDetail>().SQLQuery("delete from receipt_documents where receipt_document_id in(" + receiptDocumentIds + ")").ExecuteUpdate();
            }

            //Receipt Return Details
            criteria = DetachedCriteria.For<ReceiptReturnDetail>()
                                    .SetProjection(Projections.ProjectionList()
                                    .Add(Projections.Property("Id"), "Id"))
                                    .Add(Restrictions.Eq("ReceiptDetail.Id", receiptDetail.Id))
                                    .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ReceiptReturnDetail>());
            var receiptReturndetails = Repositories.Get<ReceiptReturnDetail>().List(criteria);

            if (_logger.IsDebugEnabled)
            {
                if (receiptReturndetails != null && receiptReturndetails.Count > 0) _logger.DebugFormat("{0}  - receipt return details for purchase order line receipt detail - {2} found to perform the PO purge - {1}", jobname, receiptReturndetails.Count, receiptDetail.Id);
                else _logger.DebugFormat("{0}  - No receipt return details for purchase order line receipt detail - {1}, found to perform the PO purge ", jobname, receiptDetail.Id);
            }

            if (receiptReturndetails != null && receiptReturndetails.Count != 0)
            {
                string receiptReturndetailIds = string.Join(",", receiptReturndetails.Select(c => c.Id.Value).ToList<Int32>());
                Repositories.Get<ReceiptDetail>().SQLQuery("delete from receipt_return_details where receipt_return_detail_id in(" + receiptReturndetailIds + ")").ExecuteUpdate();
            }

            //item transactions
            criteria = DetachedCriteria.For<ItemTransaction>()
                                    .SetProjection(Projections.ProjectionList()
                                    .Add(Projections.Property("Id"), "Id")
                                    .Add(Projections.Property("Version"), "Version"))
                                    .Add(Restrictions.Eq("ReceiptDetail.Id", receiptDetail.Id))
                                    .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ItemTransaction>());
            var ItemTransactions = Repositories.Get<ItemTransaction>().List(criteria);

            if (_logger.IsDebugEnabled)
            {
                if (ItemTransactions != null && ItemTransactions.Count > 0) _logger.DebugFormat("{0}  - item transactions for purchase order line receipt detail - {2} found to perform the PO purge - {1}", jobname, ItemTransactions.Count, receiptDetail.Id);
                else _logger.DebugFormat("{0}  - No item transactions for purchase order line receipt detail - {1} found to perform the PO purge ", jobname, receiptDetail.Id);
            }

            if (ItemTransactions != null && ItemTransactions.Count != 0)
            {
                string ItemTransactionsIds = string.Join(",", ItemTransactions.Select(c => c.Id.Value).ToList<Int32>());
                List<Int32> ItemTransactionsIdList = ItemTransactions.Select(c => c.Id.Value).ToList<Int32>();

                Repositories.Get<ReceiptDetail>().SQLQuery("delete from item_transactions where item_transaction_id in(" + ItemTransactionsIds + ")").ExecuteUpdate();
            }

            //Delete Receipt Detail
            Repositories.Get<ReceiptDetail>().SQLQuery("delete from receipt_details where receipt_detail_id in(" + receiptDetail.Id + ")").ExecuteUpdate();
        }

        private void DeleteCarrierAppointment(CarrierAppointment carrierAppointment, PurchaseOrderHeader porderHeader)
        {
            DetachedCriteria criteria = DetachedCriteria.For<ShipmentHeader>()
                                                                   .SetProjection(Projections.ProjectionList()
                                                                       .Add(Projections.Property("Id"), "Id")
                                                                       .Add(Projections.Property("Version"), "Version"))
                                                                   .Add(Restrictions.Eq("CarrierAppointment.Id", carrierAppointment.Id))
                                                                   .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ShipmentHeader>());
            var shipmentHeaders = Repositories.Get<ShipmentHeader>().List(criteria);

            if (_logger.IsDebugEnabled)
            {
                if (shipmentHeaders != null && shipmentHeaders.Count > 0) _logger.DebugFormat("{0}  - shipment headers for carrier appointment {2} found to perform the PO purge - {1}", jobname, shipmentHeaders.Count, carrierAppointment.Id);
                else _logger.DebugFormat("{0}  - No shipment headers for carrier appointment {1} found to perform the PO purge ", jobname, carrierAppointment.Id);
            }

            if (shipmentHeaders != null && shipmentHeaders.Count != 0)
            {
                foreach (ShipmentHeader shipmentHeader in shipmentHeaders)
                    DeleteShipmentHeader(shipmentHeader);
            }

            //Carrier Appointment
            Repositories.Get<ReceiptDetail>().SQLQuery("delete from carrier_appointments where carrier_appointment_id in(" + carrierAppointment.Id + ")").ExecuteUpdate();

        }

        private void DeleteShipmentHeader(ShipmentHeader shipmentHeader)
        {
            DetachedCriteria criteria = DetachedCriteria.For<ShipmentDetail>()
                                                                  .SetProjection(Projections.ProjectionList()
                                                                      .Add(Projections.Property("Id"), "Id")
                                                                      .Add(Projections.Property("Version"), "Version"))
                                                                  .Add(Restrictions.Eq("ShipmentHeader.Id", shipmentHeader.Id))
                                                                  .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ShipmentDetail>());
            var shipmentDetails = Repositories.Get<ShipmentDetail>().List(criteria);

            if (_logger.IsDebugEnabled)
            {
                if (shipmentDetails != null && shipmentDetails.Count > 0) _logger.DebugFormat("{0}  - shipment details for shipment header {2} found to perform the PO purge - {1}", jobname, shipmentDetails.Count, shipmentHeader.Id);
                else _logger.DebugFormat("{0}  - No shipment details for shipment header {1} found to perform the PO purge ", jobname, shipmentHeader.Id);
            }

            if (shipmentDetails != null && shipmentDetails.Count != 0)
            {
                foreach (ShipmentDetail shipmentDetail in shipmentDetails)
                {
                    DeletePOShipmentDetails(shipmentDetail, shipmentHeader);
                }
            }

            //Delete Shipment Locations
            criteria = DetachedCriteria.For<ShipmentLocation>()
                                    .SetProjection(Projections.ProjectionList()
                                    .Add(Projections.Property("Id"), "Id")
                                    .Add(Projections.Property("Version"), "Version"))
                                    .Add(Restrictions.Eq("ShipmentHeader.Id", shipmentHeader.Id))
                                    .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ShipmentLocation>());
            var shipmentLocations = Repositories.Get<ShipmentLocation>().List(criteria);

            if (_logger.IsDebugEnabled)
            {
                if (shipmentLocations != null && shipmentLocations.Count > 0) _logger.DebugFormat("{0}  - shipment locations for shipment header {2} found to perform the PO purge - {1}", jobname, shipmentLocations.Count, shipmentHeader.Id);
                else _logger.DebugFormat("{0}  - No shipment locations for shipment header {1} found to perform the PO purge ", jobname, shipmentHeader.Id);
            }

            if (shipmentLocations != null && shipmentLocations.Count != 0)
            {
                string shipmentLocationIds = string.Join(",", shipmentLocations.Select(c => c.Id.Value).ToList<Int32>());
                Repositories.Get<ShipmentHeader>().SQLQuery("delete from shipment_locations where shipment_location_id in(" + shipmentLocationIds + ")").ExecuteUpdate();
            }

            // Delete Shipment Statuses
            criteria = DetachedCriteria.For<ShipmentStatus>()
                                    .SetProjection(Projections.ProjectionList()
                                    .Add(Projections.Property("Id"), "Id")
                                    .Add(Projections.Property("Version"), "Version"))
                                    .Add(Restrictions.Eq("ShipmentHeader.Id", shipmentHeader.Id))
                                    .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ShipmentStatus>());
            var shipmentStatuses = Repositories.Get<ShipmentStatus>().List(criteria);

            if (_logger.IsDebugEnabled)
            {
                if (shipmentStatuses != null && shipmentStatuses.Count > 0) _logger.DebugFormat("{0}  - shipment statuses for shipment header {2} found to perform the PO purge - {1}", jobname, shipmentStatuses.Count, shipmentHeader.Id);
                else _logger.DebugFormat("{0}  - No shipment statuses for shipment header {1} found to perform the PO purge ", jobname, shipmentHeader.Id);
            }

            if (shipmentStatuses != null && shipmentStatuses.Count != 0)
            {
                string shipmentStatusIds = string.Join(",", shipmentStatuses.Select(c => c.Id.Value).ToList<Int32>());
                Repositories.Get<ShipmentHeader>().SQLQuery("delete from shipment_statuses where shipment_status_id in(" + shipmentStatusIds + ")").ExecuteUpdate();
            }

            Repositories.Get<ShipmentHeader>().SQLQuery("delete from shipment_headers where shipment_header_id in(" + shipmentHeader.Id + ")").ExecuteUpdate();

        }

        private void DeletePOShipmentDetails(ShipmentDetail shipmentDetail, ShipmentHeader shipmentHeader)
        {
            //Recall Items
            DetachedCriteria criteria = DetachedCriteria.For<RecallItem>()
                                                                 .SetProjection(Projections.ProjectionList()
                                                                     .Add(Projections.Property("Id"), "Id"))
                                                                 .Add(Restrictions.Eq("ShipmentDetail.Id", shipmentDetail.Id))
                                                                 .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.RecallItem>());
            var recallItems = Repositories.Get<RecallItem>().List(criteria);

            if (_logger.IsDebugEnabled)
            {
                if (recallItems != null && recallItems.Count > 0) _logger.DebugFormat("{0}  - recall items for shipment detail {2} found to perform the PO purge - {1}", jobname, recallItems.Count, shipmentDetail.Id);
                else _logger.DebugFormat("{0}  - No recall items for shipment detail {1} found to perform the PO purge ", jobname, shipmentDetail.Id);
            }

            if (recallItems != null && recallItems.Count != 0)
            {
                string recallItemIds = string.Join(",", recallItems.Select(c => c.Id.Value).ToList<Int32>());
                Repositories.Get<ShipmentDetail>().SQLQuery("delete from recall_items where recall_item_id in(" + recallItemIds + ")").ExecuteUpdate();
            }

            //Shipment Reference Codes
            criteria = DetachedCriteria.For<ShipmentReferenceCode>()
                                                                 .SetProjection(Projections.ProjectionList()
                                                                     .Add(Projections.Property("Id"), "Id")
                                                                     .Add(Projections.Property("Version"), "Version"))
                                                                 .Add(Restrictions.Eq("ShipmentDetail.Id", shipmentDetail.Id))
                                                                 .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ShipmentReferenceCode>());
            var shipmentReferenceCodes = Repositories.Get<ShipmentReferenceCode>().List(criteria);

            if (_logger.IsDebugEnabled)
            {
                if (shipmentReferenceCodes != null && shipmentReferenceCodes.Count > 0) _logger.DebugFormat("{0}  - shipment reference codes for shipment detail {2} found to perform the PO purge - {1}", jobname, shipmentReferenceCodes.Count, shipmentDetail.Id);
                else _logger.DebugFormat("{0}  - No shipment reference codes for shipment detail {1} found to perform the PO purge ", jobname, shipmentDetail.Id);
            }

            if (shipmentReferenceCodes != null && shipmentReferenceCodes.Count != 0)
            {
                string shipmentReferenceCodeIds = string.Join(",", shipmentReferenceCodes.Select(c => c.Id.Value).ToList<Int32>());
                Repositories.Get<ShipmentDetail>().SQLQuery("delete from shipment_reference_codes where shipment_reference_code_id in(" + shipmentReferenceCodeIds + ")").ExecuteUpdate();
            }

            //Shipment Charges
            criteria = DetachedCriteria.For<ShipmentCharge>()
                                                                 .SetProjection(Projections.ProjectionList()
                                                                     .Add(Projections.Property("Id"), "Id")
                                                                     .Add(Projections.Property("Version"), "Version"))
                                                                 .Add(Restrictions.Eq("ShipmentDetail.Id", shipmentDetail.Id))
                                                                 .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ShipmentCharge>());
            var ShipmentCharges = Repositories.Get<ShipmentCharge>().List(criteria);

            if (_logger.IsDebugEnabled)
            {
                if (ShipmentCharges != null && ShipmentCharges.Count > 0) _logger.DebugFormat("{0}  - shipment charges for shipment detail {2} found to perform the PO purge - {1}", jobname, ShipmentCharges.Count, shipmentDetail.Id);
                else _logger.DebugFormat("{0}  - No shipment charges for shipment detail {1} found to perform the PO purge ", jobname, shipmentDetail.Id);
            }

            if (ShipmentCharges != null && ShipmentCharges.Count != 0)
            {
                string ShipmentChargeIds = string.Join(",", ShipmentCharges.Select(c => c.Id.Value).ToList<Int32>());
                Repositories.Get<ShipmentDetail>().SQLQuery("delete from shipment_charges where shipment_charge_id in(" + ShipmentChargeIds + ")").ExecuteUpdate();
            }

            Repositories.Get<ShipmentDetail>().SQLQuery("delete from shipment_details where shipment_detail_id in(" + shipmentDetail.Id + ")").ExecuteUpdate();
        }

        #endregion


        #region Delete Work Order

        private void DeleteWorkOrder(WorkOrderHeader workOrderHeader)
        {
            if (String.IsNullOrEmpty(workOrderHeader.Suffix)) DebugFormat(string.Format("{1} - Start Purge Work Order Code {0}", workOrderHeader.WorkOrderCode, jobname));
            else DebugFormat(string.Format("{1} - Start Purge Work Order Code {0} - {2}", workOrderHeader.WorkOrderCode, jobname, workOrderHeader.Suffix));

            using (UnitWrapper wrapper = new UnitWrapper())
            {
                wrapper.Execute(() =>
                {
                    try
                    {
                        // Work Order Details
                        DetachedCriteria criteria = DetachedCriteria.For<WorkOrderDetail>()
                                                .SetProjection(Projections.ProjectionList()
                                                .Add(Projections.Property("Id"), "Id")
                                                .Add(Projections.Property("Version"), "Version"))
                                                .Add(Restrictions.Eq("WorkOrderHeader.Id", workOrderHeader.Id))
                                                .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.WorkOrderDetail>());
                        var workOrderDetails = Repositories.Get<WorkOrderDetail>().List(criteria);

                        if (_logger.IsDebugEnabled)
                        {
                            if (workOrderDetails != null && workOrderDetails.Count > 0) _logger.DebugFormat("{0}  - work order lines found to perform the WO purge - {1}", jobname, workOrderDetails.Count);
                            else _logger.DebugFormat("{0}  - No work order lines found to perform the WO purge ", jobname);
                        }

                        if (workOrderDetails != null && workOrderDetails.Count != 0)
                        {
                            foreach (WorkOrderDetail workOrderDetail in workOrderDetails)
                            {
                                DeleteWorkOrderDetail(workOrderDetail, workOrderHeader);
                            }
                        }

                        //Work Order Header Comments
                        criteria = DetachedCriteria.For<WorkOrderComment>()
                                                                .SetProjection(Projections.ProjectionList()
                                                                    .Add(Projections.Property("Id"), "Id")
                                                                    .Add(Projections.Property("Version"), "Version"))
                                                                .Add(Restrictions.Eq("WorkOrderHeader.Id", workOrderHeader.Id))
                                                                .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.WorkOrderComment>());
                        var workOrderComments = Repositories.Get<WorkOrderComment>().List(criteria);

                        if (_logger.IsDebugEnabled)
                        {
                            if (workOrderComments != null && workOrderComments.Count > 0) _logger.DebugFormat("{0}  - work order comments found to perform the WO purge - {1}", jobname, workOrderComments.Count);
                            else _logger.DebugFormat("{0}  - No work order comments found to perform the WO purge ", jobname);
                        }

                        if (workOrderComments != null && workOrderComments.Count != 0)
                        {
                            string workOrderCommentIds = string.Join(",", workOrderComments.Select(c => c.Id.Value).ToList<Int32>());
                            Repositories.Get<WorkOrderHeader>().SQLQuery("delete from work_order_comments where work_order_comment_id in(" + workOrderCommentIds + ")").ExecuteUpdate();
                        }

                        //Work Order Header Document
                        criteria = DetachedCriteria.For<WorkOrderDocument>()
                                                                .SetProjection(Projections.ProjectionList()
                                                                    .Add(Projections.Property("Id"), "Id")
                                                                    .Add(Projections.Property("Version"), "Version"))
                                                                .Add(Restrictions.Eq("WorkOrderHeader.Id", workOrderHeader.Id))
                                                                .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.WorkOrderDocument>());
                        var workOrderDocuments = Repositories.Get<WorkOrderDocument>().List(criteria);

                        if (_logger.IsDebugEnabled)
                        {
                            if (workOrderDocuments != null && workOrderDocuments.Count > 0) _logger.DebugFormat("{0}  - work order documents found to perform the WO purge - {1}", jobname, workOrderDocuments.Count);
                            else _logger.DebugFormat("{0}  - No work order documents found to perform the WO purge ", jobname);
                        }

                        if (workOrderDocuments != null && workOrderDocuments.Count != 0)
                        {
                            string workOrderDocumentIds = string.Join(",", workOrderDocuments.Select(c => c.Id.Value).ToList<Int32>());
                            Repositories.Get<WorkOrderHeader>().SQLQuery("delete from work_order_documents where work_order_document_id in(" + workOrderDocumentIds + ")").ExecuteUpdate();
                        }

                        //Work Order Header Status
                        criteria = DetachedCriteria.For<WorkOrderStatus>()
                                                                .SetProjection(Projections.ProjectionList()
                                                                    .Add(Projections.Property("Id"), "Id")
                                                                    .Add(Projections.Property("Version"), "Version"))
                                                                .Add(Restrictions.Eq("WorkOrderHeader.Id", workOrderHeader.Id))
                                                                .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.WorkOrderStatus>());
                        var workOrderStatuses = Repositories.Get<WorkOrderStatus>().List(criteria);

                        if (_logger.IsDebugEnabled)
                        {
                            if (workOrderStatuses != null && workOrderStatuses.Count > 0) _logger.DebugFormat("{0}  - work order statuses found to perform the WO purge - {1}", jobname, workOrderStatuses.Count);
                            else _logger.DebugFormat("{0}  - No work order statuses found to perform the WO purge ", jobname);
                        }

                        if (workOrderStatuses != null && workOrderStatuses.Count != 0)
                        {
                            string workOrderStatusIds = string.Join(",", workOrderStatuses.Select(c => c.Id.Value).ToList<Int32>());
                            Repositories.Get<WorkOrderHeader>().SQLQuery("delete from work_order_statuses where work_order_status_id in(" + workOrderStatusIds + ")").ExecuteUpdate();
                        }

                        //Transactions
                        criteria = DetachedCriteria.For<Transaction>()
                                                            .SetProjection(Projections.ProjectionList()
                                                                .Add(Projections.Property("Id"), "Id")
                                                                .Add(Projections.Property("Version"), "Version"))
                                                            .Add(Restrictions.Eq("WorkOrderHeader.Id", workOrderHeader.Id))
                                                            .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.Transaction>());
                        var transactions = Repositories.Get<Transaction>().List(criteria);
                        if (_logger.IsDebugEnabled)
                        {
                            if (transactions != null && transactions.Count > 0) _logger.DebugFormat("{0}  - transactions found to perform the WO purge - {1}", jobname, transactions.Count);
                            else _logger.DebugFormat("{0}  - No transactions found to perform the WO purge ", jobname);
                        }

                        if (transactions != null && transactions.Count != 0)
                        {
                            string transactionsIds = string.Join(",", transactions.Select(c => c.Id.Value).ToList<Int32>());
                            Repositories.Get<WorkOrderHeader>().SQLQuery("delete from era_claim_postings where transaction_id in(" + transactionsIds + ")").ExecuteUpdate();
                            Repositories.Get<WorkOrderHeader>().SQLQuery("delete from transactions where transaction_id in(" + transactionsIds + ")").ExecuteUpdate();
                        }

                        //Work Order Header
                        Repositories.Get<WorkOrderHeader>().SQLQuery("delete from work_order_headers where work_order_header_id in (" + workOrderHeader.Id + ")").ExecuteUpdate();

                        if (String.IsNullOrEmpty(workOrderHeader.Suffix)) DebugFormat(string.Format("{1} - End Purge Work Order Code {0}", workOrderHeader.WorkOrderCode, jobname));
                        else DebugFormat(string.Format("{1} - End Purge Work Order Code {0} - {2}", workOrderHeader.WorkOrderCode, jobname, workOrderHeader.Suffix));
                    }
                    catch (Exception ex)
                    {
                        if (_logger.IsErrorEnabled) _logger.ErrorFormat("{1}  - Error while deleting the work orders: {0}", ex.Message, jobname);
                        //
                        throw ex;
                    }

                });
            }
        }

        private void DeleteWorkOrderDetail(WorkOrderDetail workOrderDetail, WorkOrderHeader workOrderHeader)
        {
            //Item Fulfillments
            DetachedCriteria criteria = DetachedCriteria.For<ItemFulfillment>()
                                    .SetProjection(Projections.ProjectionList()
                                    .Add(Projections.Property("Id"), "Id")
                                    .Add(Projections.Property("Version"), "Version"))
                                    .Add(Restrictions.Eq("WorkOrderDetail.Id", workOrderDetail.Id))
                                    .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ItemFulfillment>());
            var itemfulfillments = Repositories.Get<ItemFulfillment>().List(criteria);

            if (_logger.IsDebugEnabled)
            {
                if (itemfulfillments != null && itemfulfillments.Count > 0) _logger.DebugFormat("{0}  - item fulfillments for work order line - {2} found to perform the WO purge - {1}", jobname, itemfulfillments.Count, workOrderDetail.Id);
                else _logger.DebugFormat("{0}  - No item fulfillments for work order line - {1} found to perform the WO purge ", jobname, workOrderDetail.Id);
            }

            if (itemfulfillments != null && itemfulfillments.Count != 0)
            {
                string itemFulfillmentIds = string.Join(",", itemfulfillments.Select(c => c.Id.Value).ToList<Int32>());
                Repositories.Get<WorkOrderDetail>().SQLQuery("delete from item_fulfillments where item_fulfillment_id in(" + itemFulfillmentIds + ")").ExecuteUpdate();
            }

            //Inventory Task
            criteria = DetachedCriteria.For<InventoryTask>()
                                                    .SetProjection(Projections.ProjectionList()
                                                        .Add(Projections.Property("Id"), "Id")
                                                        .Add(Projections.Property("Version"), "Version"))
                                                    .Add(Restrictions.Eq("WorkOrderDetail.Id", workOrderDetail.Id))
                                                    .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.InventoryTask>());
            var inventoryTasks = Repositories.Get<InventoryTask>().List(criteria);

            if (_logger.IsDebugEnabled)
            {
                if (inventoryTasks != null && inventoryTasks.Count > 0) _logger.DebugFormat("{0}  - inventory tasks for work order line - {2} found to perform the WO purge - {1}", jobname, inventoryTasks.Count, workOrderDetail.Id);
                else _logger.DebugFormat("{0}  - No inventory tasks for work order line - {1} found to perform the WO purge ", jobname, workOrderDetail.Id);
            }

            if (inventoryTasks != null && inventoryTasks.Count != 0)
            {
                string inventoryTaskIds = string.Join(",", inventoryTasks.Select(c => c.Id.Value).ToList<Int32>());
                Repositories.Get<WorkOrderDetail>().SQLQuery("delete from inventory_tasks where inventory_task_id in(" + inventoryTaskIds + ")").ExecuteUpdate();
            }

            //Item transactions
            criteria = DetachedCriteria.For<ItemTransaction>()
                                    .SetProjection(Projections.ProjectionList()
                                    .Add(Projections.Property("Id"), "Id")
                                    .Add(Projections.Property("Version"), "Version"))
                                    .Add(Restrictions.Eq("WorkOrderDetail.Id", workOrderDetail.Id))
                                    .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ItemTransaction>());
            var ItemTransactions = Repositories.Get<ItemTransaction>().List(criteria);

            if (_logger.IsDebugEnabled)
            {
                if (ItemTransactions != null && ItemTransactions.Count > 0) _logger.DebugFormat("{0}  - item transactions for work order line - {2} found to perform the WO purge - {1}", jobname, ItemTransactions.Count, workOrderDetail.Id);
                else _logger.DebugFormat("{0}  - No item transactions for work order line - {1} found to perform the WO purge ", jobname, workOrderDetail.Id);
            }

            if (ItemTransactions != null && ItemTransactions.Count != 0)
            {
                string ItemTransactionsIds = string.Join(",", ItemTransactions.Select(c => c.Id.Value).ToList<Int32>());
                Repositories.Get<WorkOrderDetail>().SQLQuery("delete from item_transactions where item_transaction_id in(" + ItemTransactionsIds + ")").ExecuteUpdate();
            }

            //Work Order Detail Comments
            criteria = DetachedCriteria.For<WorkOrderComment>()
                                                    .SetProjection(Projections.ProjectionList()
                                                        .Add(Projections.Property("Id"), "Id")
                                                        .Add(Projections.Property("Version"), "Version"))
                                                    .Add(Restrictions.Eq("WorkOrderDetail.Id", workOrderHeader.Id))
                                                    .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.WorkOrderComment>());
            var workOrderComments = Repositories.Get<WorkOrderComment>().List(criteria);

            if (_logger.IsDebugEnabled)
            {
                if (workOrderComments != null && workOrderComments.Count > 0) _logger.DebugFormat("{0}  - WO comments for work order line - {2} found to perform the WO purge - {1}", jobname, workOrderComments.Count, workOrderDetail.Id);
                else _logger.DebugFormat("{0}  - No WO comments for work order line - {1} found to perform the WO purge ", jobname, workOrderDetail.Id);
            }

            if (workOrderComments != null && workOrderComments.Count != 0)
            {
                string workOrderCommentIds = string.Join(",", workOrderComments.Select(c => c.Id.Value).ToList<Int32>());
                Repositories.Get<WorkOrderDetail>().SQLQuery("delete from work_order_comments where work_order_comment_id in(" + workOrderCommentIds + ")").ExecuteUpdate();
            }

            //Work Order Detail Document
            criteria = DetachedCriteria.For<WorkOrderDocument>()
                                                    .SetProjection(Projections.ProjectionList()
                                                        .Add(Projections.Property("Id"), "Id")
                                                        .Add(Projections.Property("Version"), "Version"))
                                                    .Add(Restrictions.Eq("WorkOrderDetail.Id", workOrderDetail.Id))
                                                    .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.WorkOrderDocument>());
            var workOrderDocuments = Repositories.Get<WorkOrderDocument>().List(criteria);

            if (_logger.IsDebugEnabled)
            {
                if (workOrderDocuments != null && workOrderDocuments.Count > 0) _logger.DebugFormat("{0}  - WO documents for work order line - {2} found to perform the WO purge - {1}", jobname, workOrderDocuments.Count, workOrderDetail.Id);
                else _logger.DebugFormat("{0}  - No WO documents for work order line - {1} found to perform the WO purge ", jobname, workOrderDetail.Id);
            }

            if (workOrderDocuments != null && workOrderDocuments.Count != 0)
            {
                string workOrderDocumentIds = string.Join(",", workOrderDocuments.Select(c => c.Id.Value).ToList<Int32>());
                Repositories.Get<WorkOrderDetail>().SQLQuery("delete from work_order_documents where work_order_document_id in(" + workOrderDocumentIds + ")").ExecuteUpdate();
            }

            //Work Order Detail Item
            criteria = DetachedCriteria.For<WorkOrderItem>()
                                                    .SetProjection(Projections.ProjectionList()
                                                        .Add(Projections.Property("Id"), "Id")
                                                        .Add(Projections.Property("Version"), "Version"))
                                                    .Add(Restrictions.Eq("WorkOrderDetail.Id", workOrderDetail.Id))
                                                    .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.WorkOrderItem>());
            var workOrderItems = Repositories.Get<WorkOrderItem>().List(criteria);

            if (_logger.IsDebugEnabled)
            {
                if (workOrderItems != null && workOrderItems.Count > 0) _logger.DebugFormat("{0}  - WO items for work order line - {2} found to perform the WO purge - {1}", jobname, workOrderItems.Count, workOrderDetail.Id);
                else _logger.DebugFormat("{0}  - No WO items for work order line - {1} found to perform the WO purge ", jobname, workOrderDetail.Id);
            }

            if (workOrderItems != null && workOrderItems.Count != 0)
            {
                string workOrderItemIds = string.Join(",", workOrderItems.Select(c => c.Id.Value).ToList<Int32>());
                Repositories.Get<WorkOrderDetail>().SQLQuery("delete from work_order_items where work_order_item_id in(" + workOrderItemIds + ")").ExecuteUpdate();
            }

            //Work Order Detail Meter Readings
            criteria = DetachedCriteria.For<WorkOrderMeterReading>()
                                                    .SetProjection(Projections.ProjectionList()
                                                        .Add(Projections.Property("Id"), "Id")
                                                        .Add(Projections.Property("Version"), "Version"))
                                                    .Add(Restrictions.Eq("WorkOrderDetail.Id", workOrderDetail.Id))
                                                    .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.WorkOrderMeterReading>());
            var workOrderMeterReadings = Repositories.Get<WorkOrderMeterReading>().List(criteria);

            if (_logger.IsDebugEnabled)
            {
                if (workOrderMeterReadings != null && workOrderMeterReadings.Count > 0) _logger.DebugFormat("{0}  - WO meter readings for work order line - {2} found to perform the WO purge - {1}", jobname, workOrderMeterReadings.Count, workOrderDetail.Id);
                else _logger.DebugFormat("{0}  - No WO meter readings for work order line - {1} found to perform the WO purge ", jobname, workOrderDetail.Id);
            }

            if (workOrderMeterReadings != null && workOrderMeterReadings.Count != 0)
            {
                string workOrderMeterReadingIds = string.Join(",", workOrderMeterReadings.Select(c => c.Id.Value).ToList<Int32>());
                Repositories.Get<WorkOrderDetail>().SQLQuery("delete from work_order_meter_readings where work_order_meter_reading_id in(" + workOrderMeterReadingIds + ")").ExecuteUpdate();
            }

            //Work Order Details
            Repositories.Get<WorkOrderDetail>().SQLQuery("delete from work_order_details where work_order_detail_id in (" + workOrderDetail.Id + ")").ExecuteUpdate();

        }

        #endregion

        #region Delete Tasks

        private void DeleteTask(List<int?> TaskIds, int StartIndex, int EndIndex)
        {
            using (UnitWrapper wrapper = new UnitWrapper())
            {
                wrapper.Execute(() =>
                {
                    try
                    {
                        string taskIds = string.Join(",", TaskIds);
                        //item transactions
                        DetachedCriteria criteria = DetachedCriteria.For<ItemTransaction>()
                                                .SetProjection(Projections.ProjectionList()
                                                .Add(Projections.Property("Id"), "Id")
                                                .Add(Projections.Property("Version"), "Version"))
                                                .Add(Restrictions.In("Task.Id", TaskIds))
                                                .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ItemTransaction>());
                        var ItemTransactions = Repositories.Get<ItemTransaction>().List(criteria);

                        if (_logger.IsDebugEnabled)
                        {
                            if (ItemTransactions != null && ItemTransactions.Count > 0) _logger.DebugFormat("{0}  -  {3} item transactions found to perform the Tasks purge, for tasks {1} - {2}", jobname, StartIndex, EndIndex, ItemTransactions.Count);
                            else _logger.DebugFormat("{0}  -  No item transactions found to perform the Tasks purge, for tasks {1} - {2} ", jobname, StartIndex, EndIndex);
                        }

                        if (ItemTransactions != null && ItemTransactions.Count != 0)
                        {
                            string ItemTransactionsIds = string.Join(",", ItemTransactions.Select(c => c.Id.Value).ToList<Int32>());
                            List<Int32> ItemTransactionsIdList = ItemTransactions.Select(c => c.Id.Value).ToList<Int32>();
                            List<Int32> ItemTransactionsIdSubList = new List<Int32>();
                            string ItemTransactionsSubIds = string.Empty;


                            int startIndex = 0, length = TasksThereshold;
                            do
                            {
                                if (ItemTransactionsIdList.Count < startIndex + length)
                                {
                                    length = ItemTransactionsIdList.Count - startIndex;
                                }

                                ItemTransactionsIdSubList = ItemTransactionsIdList.GetRange(startIndex, length);
                                startIndex += length;

                                ItemTransactionsSubIds = string.Join(",", ItemTransactionsIdSubList);
                                Repositories.Get<ReceiptDetail>().SQLQuery("delete from item_transactions where item_transaction_id in(" + ItemTransactionsSubIds + ")").SetTimeout(1200).ExecuteUpdate();

                            } while (startIndex < ItemTransactionsIdList.Count);

                        }

                        //Claim Tasks
                        criteria = DetachedCriteria.For<ClaimTask>()
                                                 .SetProjection(Projections.ProjectionList()
                                                 .Add(Projections.Property("Id"), "Id")
                                                 .Add(Projections.Property("Version"), "Version"))
                                                 .Add(Restrictions.In("Task.Id", TaskIds))
                                                 .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ClaimTask>());
                        var claimTasks = Repositories.Get<ClaimTask>().List(criteria);

                        if (_logger.IsDebugEnabled)
                        {
                            if (claimTasks != null && claimTasks.Count > 0) _logger.DebugFormat("{0}  -  {3} claim tasks found to perform the Tasks purge, for tasks {1} - {2}", jobname, StartIndex, EndIndex, claimTasks.Count);
                            else _logger.DebugFormat("{0}  -  No claim tasks found to perform the Tasks purge, for tasks {1} - {2} ", jobname, StartIndex, EndIndex);
                        }

                        if (claimTasks != null && claimTasks.Count != 0)
                        {
                            string claimTaskIds = string.Join(",", claimTasks.Select(c => c.Id.Value).ToList<Int32>());
                            Repositories.Get<Task>().SQLQuery("delete from claim_tasks where claim_task_id in(" + claimTaskIds + ")").ExecuteUpdate();
                        }

                        //Task Job Executions
                        criteria = DetachedCriteria.For<JobExecution>()
                                                                        .SetProjection(Projections.ProjectionList()
                                                                            .Add(Projections.Property("Id"), "Id")
                                                                            .Add(Projections.Property("Version"), "Version"))
                                                                        .Add(Restrictions.In("Task.Id", TaskIds))
                                                                        .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.JobExecution>());
                        var jobExecutions = Repositories.Get<JobExecution>().List(criteria);

                        if (_logger.IsDebugEnabled)
                        {
                            if (jobExecutions != null && jobExecutions.Count > 0) _logger.DebugFormat("{0}  -  {3} job executions found to perform the Tasks purge, for tasks {1} - {2}", jobname, StartIndex, EndIndex, jobExecutions.Count);
                            else _logger.DebugFormat("{0}  -  No job executions found to perform the Tasks purge, for tasks {1} - {2} ", jobname, StartIndex, EndIndex);
                        }

                        if (jobExecutions != null && jobExecutions.Count != 0)
                        {
                            string jobExecutionIds = string.Join(",", jobExecutions.Select(c => c.Id.Value).ToList<Int32>());
                            Repositories.Get<Task>().SQLQuery("delete from job_executions where job_execution_id in(" + jobExecutionIds + ")").ExecuteUpdate();
                        }

                        //Inventory Task
                        criteria = DetachedCriteria.For<InventoryTask>()
                                                                .SetProjection(Projections.ProjectionList()
                                                                    .Add(Projections.Property("Id"), "Id")
                                                                    .Add(Projections.Property("Version"), "Version"))
                                                                .Add(Restrictions.In("Task.Id", TaskIds))
                                                                .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.InventoryTask>());
                        var inventoryTasks = Repositories.Get<InventoryTask>().List(criteria);

                        if (_logger.IsDebugEnabled)
                        {
                            if (inventoryTasks != null && inventoryTasks.Count > 0) _logger.DebugFormat("{0}  -  {3} inventory tasks found to perform the Tasks purge, for tasks {1} - {2}", jobname, StartIndex, EndIndex, inventoryTasks.Count);
                            else _logger.DebugFormat("{0}  -  No inventory tasks found to perform the Tasks purge, for tasks {1} - {2} ", jobname, StartIndex, EndIndex);
                        }

                        if (inventoryTasks != null && inventoryTasks.Count != 0)
                        {
                            string inventoryTaskIds = string.Join(",", inventoryTasks.Select(c => c.Id.Value).ToList<Int32>());
                            Repositories.Get<Task>().SQLQuery("delete from inventory_tasks where task_id in(" + taskIds + ")").SetTimeout(1200).ExecuteUpdate();
                        }

                        //Task Assignment
                        criteria = DetachedCriteria.For<TaskAssignment>()
                                                                .SetProjection(Projections.ProjectionList()
                                                                    .Add(Projections.Property("Id"), "Id")
                                                                    .Add(Projections.Property("Version"), "Version"))
                                                                .Add(Restrictions.In("Task.Id", TaskIds))
                                                                .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.TaskAssignment>());
                        var taskAssigments = Repositories.Get<TaskAssignment>().List(criteria);

                        if (_logger.IsDebugEnabled)
                        {
                            if (taskAssigments != null && taskAssigments.Count > 0) _logger.DebugFormat("{0}  -  {3} task assignments found to perform the Tasks purge, for tasks {1} - {2}", jobname, StartIndex, EndIndex, taskAssigments.Count);
                            else _logger.DebugFormat("{0}  -  No task assignments found to perform the Tasks purge, for tasks {1} - {2} ", jobname, StartIndex, EndIndex);
                        }

                        if (taskAssigments != null && taskAssigments.Count != 0)
                        {
                            string taskAssigmentIds = string.Join(",", taskAssigments.Select(c => c.Id.Value).ToList<Int32>());
                            Repositories.Get<Task>().SQLQuery("delete from task_assignments where task_assignment_id in(" + taskAssigmentIds + ")").ExecuteUpdate();
                        }

                        //Waves
                        criteria = DetachedCriteria.For<Wave>()
                                                            .SetProjection(Projections.ProjectionList()
                                                                .Add(Projections.Property("Id"), "Id")
                                                                .Add(Projections.Property("Version"), "Version"))
                                                            .Add(Restrictions.In("Task.Id", TaskIds))
                                                            .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.Wave>());
                        var waves = Repositories.Get<Wave>().List(criteria);

                        if (_logger.IsDebugEnabled)
                        {
                            if (waves != null && waves.Count > 0) _logger.DebugFormat("{0}  -  {3} waves found to perform the Tasks purge, for tasks {1} - {2}", jobname, StartIndex, EndIndex, waves.Count);
                            else _logger.DebugFormat("{0}  -  No waves found to perform the Tasks purge, for tasks {1} - {2} ", jobname, StartIndex, EndIndex);
                        }

                        List<int?> waveIds = waves.Select(c => c.Id).ToList<int?>();
                        if (waves != null && waves.Count != 0)
                        {
                            DeleteWave(waveIds, StartIndex, EndIndex);
                        }
   
                        //Delete Tasks
                        Repositories.Get<Task>().SQLQuery("delete from tasks where task_id in (" + taskIds + ")").ExecuteUpdate();
              
                    }
                    catch (Exception ex)
                    {
                        if (_logger.IsErrorEnabled) _logger.ErrorFormat("{1}  - Error while deleting the tasks: {0}", ex.Message, jobname);
                        //
                        throw ex;
                    }


                });
            }

        }

        private void DeleteWave(List<int?> waveIds, int StartIndex, int EndIndex)
        {

            //Item Fulfillments
            DetachedCriteria criteria = DetachedCriteria.For<ItemFulfillment>()
                                    .SetProjection(Projections.ProjectionList()
                                    .Add(Projections.Property("Id"), "Id")
                                    .Add(Projections.Property("Version"), "Version"))
                                    .Add(Restrictions.In("Wave.Id", waveIds))
                                    .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.ItemFulfillment>());
            var itemfulfillments = Repositories.Get<ItemFulfillment>().List(criteria);

            if (_logger.IsDebugEnabled)
            {
                if (itemfulfillments != null && itemfulfillments.Count > 0) _logger.DebugFormat("{0}  -   {3} item fulfillments found for waves to perform the Tasks purge, for tasks {1} - {2}", jobname, StartIndex, EndIndex, itemfulfillments.Count);
                else _logger.DebugFormat("{0}  -   No item fulfillments found for waves to perform the Tasks purge, for tasks {1} - {2} ", jobname, StartIndex, EndIndex);
            }

            if (itemfulfillments != null && itemfulfillments.Count != 0)
            {
                string itemFulfillmentIds = string.Join(",", itemfulfillments.Select(c => c.Id.Value).ToList<Int32>());
                Repositories.Get<Wave>().SQLQuery("delete from item_fulfillments where item_fulfillment_id in(" + itemFulfillmentIds + ")").ExecuteUpdate();
            }

            //Carton Headers
            criteria = DetachedCriteria.For<CartonHeader>()
                                                .SetProjection(Projections.ProjectionList()
                                                    .Add(Projections.Property("Id"), "Id")
                                                    .Add(Projections.Property("Version"), "Version"))
                                                .Add(Restrictions.In("Wave.Id", waveIds))
                                                .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.CartonHeader>());
            var cartons = Repositories.Get<CartonHeader>().List(criteria);

            if (_logger.IsDebugEnabled)
            {
                if (itemfulfillments != null && itemfulfillments.Count > 0) _logger.DebugFormat("{0}  -   {3} carton headers found for waves to perform the Tasks purge, for tasks {1} - {2}", jobname, StartIndex, EndIndex, cartons.Count);
                else _logger.DebugFormat("{0}  -   No carton headers found for waves to perform the Tasks purge, for tasks {1} - {2} ", jobname, StartIndex, EndIndex);
            }

            if (cartons != null && cartons.Count != 0)
            {
                string cartonIds = string.Join(",", cartons.Select(c => c.Id.Value).ToList<Int32>());
                Repositories.Get<Wave>().SQLQuery("delete from carton_headers where carton_header_id in(" + cartonIds + ")").ExecuteUpdate();
            }

            //Transactions
            criteria = DetachedCriteria.For<Transaction>()
                                                .SetProjection(Projections.ProjectionList()
                                                    .Add(Projections.Property("Id"), "Id")
                                                    .Add(Projections.Property("Version"), "Version"))
                                                .Add(Restrictions.In("Wave.Id", waveIds))
                                                .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.Transaction>());
            var transactions = Repositories.Get<Transaction>().List(criteria);

            if (_logger.IsDebugEnabled)
            {
                if (transactions != null && transactions.Count > 0) _logger.DebugFormat("{0}  -   {3} transactions found for waves to perform the Tasks purge, for tasks {1} - {2}", jobname, StartIndex, EndIndex, transactions.Count);
                else _logger.DebugFormat("{0}  -   No transactions found for waves to perform the Tasks purge, for tasks {1} - {2} ", jobname, StartIndex, EndIndex);
            }

            if (transactions != null && transactions.Count != 0)
            {
                string transactionsIds = string.Join(",", transactions.Select(c => c.Id.Value).ToList<Int32>());
                Repositories.Get<Wave>().SQLQuery("delete from era_claim_postings where transaction_id in(" + transactionsIds + ")").ExecuteUpdate();
                Repositories.Get<Wave>().SQLQuery("delete from transactions where transaction_id in(" + transactionsIds + ")").ExecuteUpdate();
            }

            //Wave Assignments
            criteria = DetachedCriteria.For<WaveAssignment>()
                                                 .SetProjection(Projections.ProjectionList()
                                                     .Add(Projections.Property("Id"), "Id")
                                                     .Add(Projections.Property("Version"), "Version"))
                                                 .Add(Restrictions.In("Wave.Id", waveIds))
                                                 .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.WaveAssignment>());
            var waveAssignments = Repositories.Get<WaveAssignment>().List(criteria);

            if (_logger.IsDebugEnabled)
            {
                if (waveAssignments != null && waveAssignments.Count > 0) _logger.DebugFormat("{0}  -   {3} wave assignments found for waves to perform the Tasks purge, for tasks {1} - {2}", jobname, StartIndex, EndIndex, waveAssignments.Count);
                else _logger.DebugFormat("{0}  -   No wave assignments found for waves to perform the Tasks purge, for tasks {1} - {2} ", jobname, StartIndex, EndIndex);
            }

            if (waveAssignments != null && waveAssignments.Count != 0)
            {
                string waveAssignmentIds = string.Join(",", waveAssignments.Select(c => c.Id.Value).ToList<Int32>());
                Repositories.Get<Wave>().SQLQuery("delete from wave_assignments where wave_assignment_id in(" + waveAssignmentIds + ")").ExecuteUpdate();
            }

            string WaveIds = string.Join(",", waveIds);
            //Waves
            Repositories.Get<Wave>().SQLQuery("delete from waves where wave_id in(" + WaveIds + ")").ExecuteUpdate();
        }

        #endregion


        #region Logging

        private void DebugFormat(string input)
        {
            if (_logger.IsDebugEnabled) _logger.DebugFormat(input);
        }

        private void ErrorFormat(string input)
        {
            if (_logger.IsErrorEnabled) _logger.ErrorFormat(input);
        } 

        #endregion



    }
}
