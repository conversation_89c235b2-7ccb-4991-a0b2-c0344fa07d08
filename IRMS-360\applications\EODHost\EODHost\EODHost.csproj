﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x86</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{26AF379C-636F-43F7-9471-C53DDA20A5BA}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Upp.Irms.EOD.Host</RootNamespace>
    <AssemblyName>Upp.Irms.EOD.Host</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <TargetFrameworkProfile>
    </TargetFrameworkProfile>
    <FileAlignment>512</FileAlignment>
    <IsWebBootstrapper>true</IsWebBootstrapper>
    <PublishUrl>http://localhost/Upp.Irms.AlertGenerator.Host/</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Web</InstallFrom>
    <UpdateEnabled>true</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>TRACE;DEBUG;NHPROF</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisIgnoreBuiltInRuleSets>false</CodeAnalysisIgnoreBuiltInRuleSets>
    <CodeAnalysisIgnoreBuiltInRules>false</CodeAnalysisIgnoreBuiltInRules>
    <UseVSHostingProcess>true</UseVSHostingProcess>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|AnyCPU'">
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>none</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <UseVSHostingProcess>false</UseVSHostingProcess>
  </PropertyGroup>
  <PropertyGroup>
    <NoWin32Manifest>true</NoWin32Manifest>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject>
    </StartupObject>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisIgnoreBuiltInRuleSets>false</CodeAnalysisIgnoreBuiltInRuleSets>
    <CodeAnalysisIgnoreBuiltInRules>false</CodeAnalysisIgnoreBuiltInRules>
    <CodeAnalysisFailOnMissingRules>false</CodeAnalysisFailOnMissingRules>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <PlatformTarget>x64</PlatformTarget>
    <UseVSHostingProcess>false</UseVSHostingProcess>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisIgnoreBuiltInRuleSets>false</CodeAnalysisIgnoreBuiltInRuleSets>
    <CodeAnalysisIgnoreBuiltInRules>false</CodeAnalysisIgnoreBuiltInRules>
    <CodeAnalysisFailOnMissingRules>false</CodeAnalysisFailOnMissingRules>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Common.Logging, Version=*******, Culture=neutral, PublicKeyToken=af08829b84f0328e">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Common.Logging.dll</HintPath>
    </Reference>
    <Reference Include="HibernatingRhinos.Profiler.Appender">
      <HintPath>..\..\..\..\..\irms-360-1.0.0\IRMS-360-Web\IRMS-360\libraries\_bin\HibernatingRhinos.Profiler.Appender.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.CrystalReports.Engine, Version=13.0.3500.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\IRMS-360-Web\IRMS-360\libraries\_bin\CrystalDecisions.CrystalReports.Engine.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.Shared, Version=13.0.3500.0, Culture=neutral, PublicKeyToken=692fbea5521e1304" />
    <Reference Include="log4net, Version=1.2.10.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Newtonsoft.Json">
      <HintPath>..\..\..\libraries\_bin\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NHibernate">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\libraries\_bin\NHibernate.dll</HintPath>
    </Reference>
    <Reference Include="Remotion.Linq">
      <HintPath>..\..\..\libraries\_bin\Remotion.Linq.dll</HintPath>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="Remotion.Linq.EagerFetching">
      <HintPath>..\..\..\libraries\_bin\Remotion.Linq.EagerFetching.dll</HintPath>
    </Reference>
    <Reference Include="Quartz">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Quartz.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Configuration.Install" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data" />
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Xml" />
    <Reference Include="Upp.Shared">
      <HintPath>..\..\..\..\_libraries\_bin\Upp.Shared.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="UnavailableExpirationOrThreshold.cs" />
    <Compile Include="AutoCloseDockForScheduledCarrierJob.cs" />
    <Compile Include="AutoCloseDockJob.cs" />
    <Compile Include="DataContracts\MakeToOrderDataContract.cs" />
    <Compile Include="InventoryReconciliationJob.cs" />
    <Compile Include="AutoPickPackShipJob.cs" />
    <Compile Include="AutoPostERAVouchersJob.cs" />
    <Compile Include="CustomClient\BradyCustom.cs" />
    <Compile Include="DataContracts\CycleCountIfaceDataContract.cs" />
    <Compile Include="DataContracts\ProductInterfaceDataContract.cs" />
    <Compile Include="DataPurgeJob.cs" />
    <Compile Include="FFDMPostedOrderSnapshotJob.cs" />
    <Compile Include="FTPJob.cs" />
    <Compile Include="FuelPriceJob.cs" />
    <Compile Include="GRSIPickingPackingJob.cs" />
    <Compile Include="HMIOrderDropPickPackJob.cs" />
    <Compile Include="MakeToOrderJob.cs" />
    <Compile Include="ProductInterfaceJob.cs" />
    <Compile Include="PartFlipAlertJob.cs" />
    <Compile Include="CustomerInventoryExtractJob.cs" />
    <Compile Include="CustomerShipFileJob.cs" />
    <Compile Include="InventorySnapshotJob.cs" />
    <Compile Include="InventoryUpdateJob.cs" />
    <Compile Include="EmployeeVerificationJob.cs" />
    <Compile Include="FFDMInventorySnapshot.cs" />
    <Compile Include="FFDMOrderSnapshot.cs" />
    <Compile Include="ItemExtractJob.cs" />
    <Compile Include="LocationExtractJob.cs" />
    <Compile Include="MaintainInventoryStatusJob.cs" />
    <Compile Include="IncubationHoldJob.cs" />
    <Compile Include="MonthlyReconciliationJob.cs" />
    <Compile Include="InventoryHistoryJob.cs" />
    <Compile Include="CycleCountSetupJob.cs" />
    <Compile Include="ManualTriggerJob.cs" />
    <Compile Include="OrderHoldJob.cs" />
    <Compile Include="OrderStatusUpdateJob.cs" />
    <Compile Include="PreReceiptBillingJob.cs" />
    <Compile Include="PreReceiptUpdateJob.cs" />
    <Compile Include="RecalculateABCClassificationJob.cs" />
    <Compile Include="RenameOrdersJob.cs" />
    <Compile Include="ShippingThresholdJob.cs" />
    <Compile Include="ItemHistoryReportJob.cs" />
    <Compile Include="FourWallReportJob.cs" />
    <Compile Include="PackedCardReportJob.cs" />
    <Compile Include="FlatFileDataFormat.cs" />
    <Compile Include="FlatFileGenerator.cs" />
    <Compile Include="HostService.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="HostService.Designer.cs">
      <DependentUpon>HostService.cs</DependentUpon>
    </Compile>
    <Compile Include="JobListenerSupport.cs" />
    <Compile Include="PasswordExpiration.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="ProjectInstaller.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProjectInstaller.Designer.cs">
      <DependentUpon>ProjectInstaller.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Service.cs" />
    <Compile Include="ServiceEngine.cs" />
    <Compile Include="ShelfShipReportJob.cs" />
    <Compile Include="UnshippedLinesJob.cs" />
    <Compile Include="Utilities.cs" />
    <Compile Include="WayFairInventorySnapshotJob.cs" />
    <Compile Include="ZipHelper.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config">
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.7.2">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4 %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.3.1">
      <Visible>False</Visible>
      <ProductName>Windows Installer 3.1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <Content Include="quartz_jobs.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <SubType>Designer</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\libraries\Constants\Constants\Constants.csproj">
      <Project>{34CD49DA-7DBB-4A30-82F9-314BC22D14BF}</Project>
      <Name>Constants</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\libraries\Core\Core\Core.csproj">
      <Project>{6911B114-73DA-4E9E-B86B-4E5558149ECA}</Project>
      <Name>Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\libraries\Domain\Domain\Domain.csproj">
      <Project>{8302CA83-B340-4F77-8F98-FE0A60EB9C5C}</Project>
      <Name>Domain</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>