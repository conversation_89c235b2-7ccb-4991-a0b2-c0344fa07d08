﻿
using System;
using System.Configuration;
using System.Collections;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Reflection;
using System.Linq;
using NHibernate.Criterion;
using NHibernate.SqlCommand;
using NHibernate.Transform;
using Upp.Irms.Domain;
using Upp.Irms.Core;
using Upp.Irms.Constants;
using Upp.Shared.Application;
using Upp.Shared.Utilities;
using log4net;
using Quartz;

namespace Upp.Irms.EOD.Host
{
   public class EmployeeVerificationJob : IJob
    {
        ILog _logger = LogManager.GetLogger(typeof(EmployeeVerificationJob));       
        string _jobName = string.Empty;
        const string JParam_Warehouses = "WAREHOUSES";
        string warehouses = "";
        const string JParam_nextJob = "NEXTJOB";
        string nextJob = "";
        string jobname = "Employee Verification Job";
        const string messageText = @"<html><body>The following is an automated notification from IRMS 360.<br></body></html>";

        #region Properties
        public string JobName
        {
            get { return _jobName; }
            set { _jobName = value; }
        }
        #endregion

        #region Constructor
        public EmployeeVerificationJob()
        {

        }
        #endregion

        #region Methods.Public
        public void Execute(JobExecutionContext context)
        {
            if (JobParametersAreValid(context.MergedJobDataMap) == false)
            {
                JobExecutionException jex = new JobExecutionException("Missing job parameter");
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }

            ParseJobParameters(context.MergedJobDataMap);
            GetParticipantRolesWithPickVerification();
            NextJobScheduling(jobname);
        }
        #endregion

        #region Methods.Private

        private bool JobParametersAreValid(JobDataMap jobParams)
        {
            bool validity = true;

            if (!jobParams.Contains(JParam_Warehouses))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_Warehouses);
                validity = false;
            }
            if (!jobParams.Contains(JParam_nextJob))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_nextJob);
                validity = false;
            }
            return validity;
        }
        // helper method to parse job parameters
        private void ParseJobParameters(JobDataMap jobParams)
        {
            //Ensure that we catch all other types of exceptions
            // and throw only a JobExecutionException
            try
            {
                //map the parameters to jobParams               
                this.warehouses = jobParams.GetString(JParam_Warehouses);
                this.nextJob = jobParams.GetString(JParam_nextJob);
            }
            catch (Exception ex)
            {
                JobExecutionException jex = new JobExecutionException("Invalid data type in job parameters", ex);
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }
        }
        private void NextJobScheduling(string jobname)
        {

            if (!String.IsNullOrWhiteSpace(nextJob))
            {
                if (_logger.IsDebugEnabled) _logger.DebugFormat("         »  NextJob:  {0}", nextJob);
                try
                {
                    string[] jobKeys = Service.Instance.Scheduler.GetJobNames("Manual");
                    foreach (string jobKey in jobKeys)
                    {
                        if (jobKey.Equals(nextJob))
                        {
                            SimpleTrigger trigger = new SimpleTrigger();
                            trigger.Name = nextJob + "Trigger";
                            trigger.JobName = nextJob;
                            trigger.JobGroup = "Manual";
                            trigger.Group = "Manual";
                            trigger.StartTimeUtc = DateTime.UtcNow.AddSeconds(30);
                            trigger.RepeatCount = 0;
                            trigger.RepeatInterval = TimeSpan.Zero;
                            Service.Instance.Scheduler.RescheduleJob(nextJob + "Trigger", "Manual", trigger);
                            if (_logger.IsDebugEnabled) _logger.DebugFormat("{1} - Next Job {0} is scheduled: ", nextJob, jobname);
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (_logger.IsErrorEnabled) _logger.ErrorFormat("{1} - Next Job error: {0}", ex.Message, jobname);
                }
            }
        }

        private void GetParticipantRolesWithPickVerification()
        {
            using (UnitWrapper wrapper = new UnitWrapper())
            {
                wrapper.Execute(() =>
                {
                    DateTime EODrunDate = DateTime.Now;

                    DetachedCriteria participantPickVerification = DetachedCriteria.For<ParticipantRole>()
                                                                                  //  .CreateAlias("RoleCode", "RoleCode")
                                                                                    .SetProjection(Projections.ProjectionList()
                                                                                    .Add(Projections.Property("Id"), "Id")
                                                                                    .Add(Projections.Property("PickVerification"), "PickVerification")
                                                                                    .Add(Projections.Property("PickVerificationEnd"), "PickVerificationEnd"))
                                                                                  //  .Add(new SimpleExpression("RoleCode.Code", "E", "="))
                                                                                    .Add(Restrictions.Le("PickVerificationEnd", EODrunDate))
                                                                                    .Add(Restrictions.Or(Restrictions.IsNull("PickVerification"), Restrictions.Eq("PickVerification", "Y")))
                                                                                    .SetResultTransformer(Transformers.AliasToBean<ParticipantRole>());

                    IList<ParticipantRole> list = Repositories.Get<ParticipantRole>().List(participantPickVerification);

                    foreach (ParticipantRole partRole in list)
                    {
                        if (partRole.PickVerificationEnd.HasValue)
                        {

                            ParticipantRole participantRole = Repositories.Get<ParticipantRole>().Retrieve(partRole.Id);
                            if (participantRole.PickVerificationEnd <= EODrunDate) //pick_verification_end’ date is equal to or prior to the EOD run date.
                            {
                                participantRole.PickVerification = "N";
                                participantRole.DateModified = DateTime.Now;
                                participantRole.UserModified = "eod_manager";
                                Repositories.Get<ParticipantRole>().Update(participantRole);
                            }
                        }
                    }
                    if(list != null)
                        if (_logger.IsDebugEnabled) _logger.DebugFormat("Employee Verification Processed items:{0}", list.Count.ToString());
                   
                });
            }

        }
      
        #endregion

    }
}
