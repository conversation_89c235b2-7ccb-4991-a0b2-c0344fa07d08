﻿using log4net;
using Quartz;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Upp.Irms.EOD.Host
{
    class FTPJob : IJob
    {

        #region Fields

        ILog _logger = LogManager.GetLogger(typeof(FTPJob));

        const string JParam_FTPDomain = "FTPDOMAIN";
        const string JParam_FTPInputPath = "FTPINPUTPATH";
        const string JParam_FTPOutputPath = "FTPOUTPUTPATH";
        const string JParam_FTPUsername = "FTPUSERNAME";
        const string JParam_FTPPassword = "FTPPASSWORD";
        const string JParam_nextJob = "NEXTJOB";

        string ftpDomain = "";
        string ftpInputPath = "";
        string ftpOutputPath = "";
        string ftpUsername = "";
        string ftpPassword = "";
        string nextJob = "";

        string jobname = "FTPJob";
        string logDateTimeFormat = "dd/MM/yy HH:mm:ss.fff";

        #endregion

        #region Constructor

        public FTPJob()
        {
        }

        #endregion

        #region Methods.Public

        public virtual void Execute(JobExecutionContext context)
        {
            if (JobParametersAreValid(context.MergedJobDataMap) == false)
            {
                JobExecutionException jex = new JobExecutionException("Missing job parameter");
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }

            ParseJobParameters(context.MergedJobDataMap);

            CopyFilesFromSharedFolderToFTPPath();

            NextJobScheduling(jobname);
        }

        #endregion


        #region Methods.Private

        private bool JobParametersAreValid(JobDataMap jobParams)
        {
            bool validity = true;

            if (!jobParams.Contains(JParam_FTPDomain))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_FTPDomain);
                validity = false;
            }
            if (!jobParams.Contains(JParam_FTPInputPath))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_FTPInputPath);
                validity = false;
            }
            if (!jobParams.Contains(JParam_FTPOutputPath))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_FTPOutputPath);
                validity = false;
            }
            if (!jobParams.Contains(JParam_FTPUsername))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_FTPUsername);
                validity = false;
            }
            if (!jobParams.Contains(JParam_FTPPassword))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_FTPPassword);
                validity = false;
            }
            if (!jobParams.Contains(JParam_nextJob))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_nextJob);
                validity = false;
            }

            return validity;
        }

        private void ParseJobParameters(JobDataMap jobParams)
        {
            try
            {
                //map the parameters to jobParams              
                this.ftpDomain = jobParams.GetString(JParam_FTPDomain);
                this.ftpInputPath = jobParams.GetString(JParam_FTPInputPath);
                this.ftpOutputPath = jobParams.GetString(JParam_FTPOutputPath);
                this.ftpUsername = jobParams.GetString(JParam_FTPUsername);
                this.ftpPassword = jobParams.GetString(JParam_FTPPassword);
                this.nextJob = jobParams.GetString(JParam_nextJob);
            }
            catch (Exception ex)
            {
                JobExecutionException jex = new JobExecutionException("Invalid data type in job parameters", ex);
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }
        }

        private void NextJobScheduling(string jobname)
        {
            if (!String.IsNullOrWhiteSpace(nextJob))
            {
                if (_logger.IsDebugEnabled) _logger.DebugFormat("NextJob:  {0}", nextJob);
                try
                {
                    string[] jobKeys = Service.Instance.Scheduler.GetJobNames("Manual");
                    foreach (string jobKey in jobKeys)
                    {
                        if (jobKey.Equals(nextJob))
                        {
                            SimpleTrigger trigger = new SimpleTrigger();
                            trigger.Name = nextJob + "Trigger";
                            trigger.JobName = nextJob;
                            trigger.JobGroup = "Manual";
                            trigger.Group = "Manual";
                            trigger.StartTimeUtc = DateTime.UtcNow.AddSeconds(30);
                            trigger.RepeatCount = 0;
                            trigger.RepeatInterval = TimeSpan.Zero;
                            Service.Instance.Scheduler.RescheduleJob(nextJob + "Trigger", "Manual", trigger);
                            if (_logger.IsDebugEnabled) _logger.DebugFormat("{1} - Next Job {0} is scheduled: ", nextJob, jobname);
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (_logger.IsErrorEnabled) _logger.ErrorFormat("{1} - Next Job error: {0}", ex.Message, jobname);
                }
            }
        }

        private void CopyFilesFromSharedFolderToFTPPath()
        {
            try
            {
                if (_logger.IsDebugEnabled) _logger.DebugFormat("START - EOD Job :  {0}", jobname);

                //Check the ftpDomain
                if (string.IsNullOrWhiteSpace(ftpDomain))
                {
                    _logger.ErrorFormat("FTPDOMAIN not specified :" + ftpOutputPath);
                    return;
                }

                string split = "://";
                int index = ftpDomain.LastIndexOf(split);
                if(index > 0)
                    ftpDomain = ftpDomain.Substring(index + split.Length, ftpDomain.Length - (index + split.Length));
                ftpDomain = "ftp" + split + ftpDomain;

                //Check the ftpInputPath exist or not
                if (string.IsNullOrWhiteSpace(ftpInputPath) || !Directory.Exists(ftpInputPath))
                {
                    _logger.ErrorFormat("Directory not Found/Access Denied to folder :" + ftpInputPath);
                    return;
                }

                //Create backUp folder to move the input files after processing 
                string ftpinputPathBackUpFolder = string.Empty;
                StringBuilder sb = new StringBuilder();
                if (ftpInputPath.EndsWith("\\"))
                    sb.Append(ftpInputPath);
                else
                    sb.Append(ftpInputPath + "\\");

                ftpInputPath = sb.ToString();
                sb.Append("backUp\\");
                ftpinputPathBackUpFolder = sb.ToString();

                if (!Directory.Exists(ftpinputPathBackUpFolder))
                {
                    Directory.CreateDirectory(ftpinputPathBackUpFolder);
                }

                //Check the ftpOutputPath
                if (string.IsNullOrWhiteSpace(ftpOutputPath))
                {
                    _logger.ErrorFormat("FTPOUTPUTPATH not specified :" + ftpOutputPath);
                    return;
                }
                if (ftpOutputPath.StartsWith("//"))
                    ftpDomain = ftpDomain + ftpOutputPath;
                else if (ftpOutputPath.StartsWith("/"))
                    ftpDomain = ftpDomain + "/" + ftpOutputPath;
                else
                    ftpDomain = ftpDomain + "//" + ftpOutputPath;

                //Read Files from ftpInputPath directory
                DirectoryInfo dinfo = new DirectoryInfo(ftpInputPath);
                FileInfo[] Files = dinfo.GetFiles();
                FlatFileGenerator fg = new FlatFileGenerator();
                foreach (FileInfo file in Files)
                {
                    //Call to generic FTP Upload function
                    fg.FtpUpload(ftpDomain, ftpUsername, ftpPassword, file.Name, ftpInputPath + file.Name);
                    if (_logger.IsDebugEnabled) _logger.DebugFormat("Uploaded {0} to FTP path {1}", file.Name, ftpDomain);

                    //Copy to backUp folder
                    file.CopyTo(Path.Combine(ftpinputPathBackUpFolder, file.Name), true);
                    file.Delete();
                }

                if (_logger.IsDebugEnabled) _logger.DebugFormat("END - EOD Job :  {0}", jobname);
            }
            catch (Exception ex)
            {
                _logger.ErrorFormat("Error at FTPJob::CopyFilesFromSharedFolderToFTPPath() " + ex.Message);
            }
        }

        #endregion

    }
}
