﻿using System;
using System.Collections.Generic;

namespace Upp.Irms.EOD.Host
{
	public class FlatFileDataFormat : IDisposable
	{
		#region Fields
        
        private string _field = string.Empty;
      
        private string _description = string.Empty;

        private int _width = 0;
        
        private string format = string.Empty;
       
        private int _alignment = -1;

        private bool _hide = false;

        private char _paddingChar = ' ';

        private string _fieldDelimiter = string.Empty;

                     
		#endregion

		#region Properties

        public string Field
        {
            get { return _field; }
            set { _field = value; }
        }

        public string Description
        {
            get { return _description; }
            set { _description = value; }
        }

        public int Width
        {
            get { return _width; }
            set { _width = value; }
        }
        public string Format
        {
            get { return format; }
            set { format = value; }
        }

        public int Alignment
        {
            get { return _alignment; }
            set { _alignment = value; }
        }

        public bool Hide
        {
            get { return _hide; }
            set { _hide = value; }
        }

        public char PaddingChar
        {
            get { return _paddingChar; }
            set { _paddingChar = value; }
        }
        public string FieldDelimiter
        {
            get { return _fieldDelimiter; }
            set { _fieldDelimiter = value; }
        }

		#endregion

		#region Constructors

		public FlatFileDataFormat()
		{
			//
		}
              
		#endregion

		#region Methods.IDisposable

		public void Dispose()
		{
			
		}

		#endregion

		#region Methods.Public

        public static List<FlatFileDataFormat> Prepare(string dataFormat)
        {
             char[] fieldFormatSeparator = { ';' };

             char[] formatSeparator = { ':' };

            List<FlatFileDataFormat> dataFormatList = new List<FlatFileDataFormat>();

            dataFormat = dataFormat.TrimEnd(fieldFormatSeparator);
            dataFormat = dataFormat.TrimEnd(formatSeparator);

            string[] rowFormats = dataFormat.Split(fieldFormatSeparator);
            foreach (string rowFormat in rowFormats)
            {
                FlatFileDataFormat fileDataFormat = new FlatFileDataFormat();

                string[] fieldFormatDescriptions = rowFormat.Split(formatSeparator);
                foreach (string descriptor in fieldFormatDescriptions)
                {
                    switch (descriptor.Substring(0, 2))
                    {
                        case "P$":
                            fileDataFormat.Field = descriptor.Substring(2);
                            break;
                        case "D$":
                            fileDataFormat.Description = descriptor.Substring(2);
                            break;
                        case "W$":
                            fileDataFormat.Width = Convert.ToInt32(descriptor.Substring(2));
                            break;
                        case "F$":
                            fileDataFormat.Format = descriptor.Substring(2);
                            break;
                        case "A$":
                            fileDataFormat.Alignment = (descriptor.Substring(2).ToLower() == "right" ? 1 : -1);
                            break;
                        case "H$":
                            fileDataFormat.Hide = Convert.ToBoolean(descriptor.Substring(2));
                            break;
                        case "T$":
                            fileDataFormat.PaddingChar = Convert.ToChar(descriptor.Substring(2,1));
                            break;
                        case "S$":
                            fileDataFormat.FieldDelimiter = descriptor.Substring(2);
                            break;

                        default:
                            break;
                    }
                }

                dataFormatList.Add(fileDataFormat);
            }

            return dataFormatList;           
        }
        
		#endregion
	}
}
