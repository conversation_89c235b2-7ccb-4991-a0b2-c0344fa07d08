﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Net.Security;
using System.Reflection;
using System.Security.Cryptography.X509Certificates;
using System.Text;

using log4net;

using Upp.Irms.Core;
using Upp.Irms.Domain;
using Upp.Shared.Extensions;
using Upp.Shared.Utilities;

namespace Upp.Irms.EOD.Host
{
	public class FlatFileGenerator : IDisposable
	{
		#region Fields
        ILog _logger = LogManager.GetLogger(typeof(FlatFileGenerator));
        private const char lineChar = '=';
        private string _filePath = System.Configuration.ConfigurationManager.AppSettings["FileDestination"].ToString();
        private string _fileName = string.Empty;       
        private string _dataFormat = string.Empty;
        private bool _showHeader = false;
        private bool _showFooter = false;      
        private int _totalWidth = 0;
        private string _line = string.Empty;
        private string _title = string.Empty;
        //FTP Fields
        private string host = null;
        private string user = null;
        private string pass = null;
        private FtpWebRequest ftpRequest = null;
        private FtpWebResponse ftpResponse = null;
        private Stream ftpStream = null;
        private int bufferSize = 2048;
     
		#endregion

		#region Properties
            

        public string FilePath
        {
            get { return _filePath; }
            set { _filePath = value; }
        }

        public string DataFormat
        {
            get { return _dataFormat; }
            set { _dataFormat = value; }
        }

        public bool ShowHeader
        {
            get { return _showHeader; }
            set { _showHeader = value; }
        }

        public bool ShowFooter
        {
            get { return _showFooter; }
            set { _showFooter = value; }
        }

        public string Title
        {
            get { return _title; }
            set { _title = value; }
        }

        public string FileName
        {
            get { return _fileName; }
            set { _fileName = value; }
        }
		#endregion

		#region Constructors

		public FlatFileGenerator()
		{
			//
		}
      
		#endregion

		#region Methods.IDisposable

		public void Dispose()
		{
			
		}

		#endregion

		#region Methods.Data
                
        private bool IsValidData<T>(List<T> sourceList)
        {
            bool isValidData = true;

            if (sourceList == null)
                isValidData = false;

            return isValidData;
        }

        private bool IsValidDataFormat(string dataFormat)
        {
            bool isValidFormat = true;

            if (string.IsNullOrEmpty(dataFormat))
                isValidFormat = false;
           
            return isValidFormat;
        }

        private string FillHeader(List<FlatFileDataFormat> formats, string title)
        {
            StringBuilder data = new StringBuilder();
            data.Append(_line);

            char[] titleDelimit = { '-' };
            string[] titles = title.Split(titleDelimit);

            if (titles.Length == 1)
            {
                data.Append(string.Format("{0," + Convert.ToString(-1 * _totalWidth) + "}", title).Substring(0, _totalWidth) + Environment.NewLine);
            }
            else if (titles.Length == 2)
            {
                if (_totalWidth % 2 == 0)
                    data.Append(string.Format("{0," + Convert.ToString(-1 * _totalWidth / 2) + "}", titles[0]).Substring(0, _totalWidth / 2) + string.Format("{0," + Convert.ToString(_totalWidth / 2) + "}", titles[1]).Substring(0, _totalWidth / 2) + Environment.NewLine);
                else
                    data.Append(string.Format("{0," + Convert.ToString(-1 * _totalWidth / 2) + "}", titles[0]).Substring(0, _totalWidth / 2) + string.Format("{0," + Convert.ToString((_totalWidth / 2) + 1) + "}", titles[1]).Substring(0, (_totalWidth / 2) + 1) + Environment.NewLine);
            }

            //data.Append(Line);
            //foreach (FlatFileDataFormat format in formats)
            //{
            //    if (format.Hide == false && format.Width > 0)
            //        data.Append(string.Format("{0," + (format.Alignment * format.Width).ToString() + "}", format.Description).Substring(0, format.Width));
            //}
            //data.Append(System.Environment.NewLine);
          //  data.Append(Line);

            return data.ToString();
        }

        private string FillHeaderWithFields(List<FlatFileDataFormat> formats, string title, string fieldDelimiter)
        {
            StringBuilder data = new StringBuilder();
          //  data.Append(_line);
            if (!String.IsNullOrWhiteSpace(title))
            {
                char[] titleDelimit = { '-' };
                string[] titles = title.Split(titleDelimit);

                if (titles.Length == 1)
                {
                    data.Append(string.Format("{0," + Convert.ToString(-1 * _totalWidth) + "}", title).Substring(0, _totalWidth) + Environment.NewLine);
                }
                else if (titles.Length == 2)
                {
                    if (_totalWidth % 2 == 0)
                        data.Append(string.Format("{0," + Convert.ToString(-1 * _totalWidth / 2) + "}", titles[0]).Substring(0, _totalWidth / 2) + string.Format("{0," + Convert.ToString(_totalWidth / 2) + "}", titles[1]).Substring(0, _totalWidth / 2) + Environment.NewLine);
                    else
                        data.Append(string.Format("{0," + Convert.ToString(-1 * _totalWidth / 2) + "}", titles[0]).Substring(0, _totalWidth / 2) + string.Format("{0," + Convert.ToString((_totalWidth / 2) + 1) + "}", titles[1]).Substring(0, (_totalWidth / 2) + 1) + Environment.NewLine);
                }
            }

          //  data.Append(_line);
            
            int flag = 1;
            foreach (FlatFileDataFormat format in formats)
            {
                
                if (format.Hide == false && format.Width > 0)
                {
                    data.Append(string.Format("{0," + (format.Alignment * format.Width).ToString() + "}", format.Description).Substring(0, format.Width));

                    if (!string.IsNullOrEmpty(fieldDelimiter) && flag != formats.Count)
                    {
                        if (fieldDelimiter.ToLower() == "tab")
                            data.AppendFormat("\t");
                        else if (fieldDelimiter.ToLower() == "space")
                            data.AppendFormat(" ");
                        else
                            data.AppendFormat(fieldDelimiter);
                    }
                }
                else if (format.Hide == false && format.Width == 0)
                {
                    data.Append(string.Format("{0," + (format.Alignment * format.Width).ToString() + "}", format.Description));
                    if (!string.IsNullOrEmpty(fieldDelimiter) && flag != formats.Count)
                    {
                        if (fieldDelimiter.ToLower() == "tab")
                            data.AppendFormat("\t");
                        else if (fieldDelimiter.ToLower() == "space")
                            data.AppendFormat(" ");
                        else
                            data.AppendFormat(fieldDelimiter);
                    }
                }
                flag = flag + 1;


            }
            data.Append(System.Environment.NewLine);
         //   data.Append(_line);

            return data.ToString();
        }

        private string FillFooter()
        {
            StringBuilder data = new StringBuilder();

          //  data.Append(_line);
            data = data.Append(string.Format("{0," + _totalWidth.ToString() + "}", "DateTime: " + DateTime.Now.ToString()).Substring(0, _totalWidth) + Environment.NewLine);
          //  data.Append(_line);

            return data.ToString();
        }

        //private string PrepareData<T>(List<T> sourceList, List<FlatFileDataFormat> formats, bool showHeader, string subTitle)
        //{
        //    StringBuilder data = new StringBuilder();

        //    if (formats.Count == 0)
        //        return data.ToString();

        //    if (sourceList == null)
        //        return data.ToString();

        //    if (showHeader)
        //    {
        //        data.Append(FillSubHeader(formats,subTitle));               
        //    }

        //    data.Append(FillData(sourceList, formats));
                       
        //    return data.ToString();
        //}

        private string PrepareData<T>(List<T> sourceList, List<FlatFileDataFormat> formats, string fieldDelimiter, bool dataDoubleQuotation=false)
        {
            try
            {
                StringBuilder data = new StringBuilder();

                if (formats.Count == 0)
                    return data.ToString();

                if (sourceList == null)
                    return data.ToString();

                data.Append(FillData(sourceList, formats, fieldDelimiter, dataDoubleQuotation));

                return data.ToString();
            }             
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
                return "";
            }
        }    

        private string FillSubHeader(List<FlatFileDataFormat> formats,string subTitle)
        {
            StringBuilder data = new StringBuilder();
            data.Append(_line);

            char[] titleDelimit = {'-'};
            string[] titles = subTitle.Split(titleDelimit);

            if (titles.Length == 1)
            {
                data.Append(string.Format("{0," + Convert.ToString(-1 * _totalWidth) + "}", subTitle).Substring(0, _totalWidth) + Environment.NewLine);
            }
            else if (titles.Length == 2)
            {
                data.Append(string.Format("{0," + Convert.ToString(-1 * _totalWidth / 2) + "}", titles[0]).Substring(0, _totalWidth / 2) + string.Format("{0," + Convert.ToString(_totalWidth / 2) + "}", titles[1]).Substring(0, _totalWidth / 2) + Environment.NewLine);
            }

            data.Append(_line);
            foreach (FlatFileDataFormat format in formats)
            {
                if (format.Hide == false && format.Width > 0)
                    data.Append(string.Format("{0," + (format.Alignment * format.Width).ToString() + "}", format.Description).Substring(0, format.Width));
                else if (format.Hide == false && format.Width == 0)
                    data.Append(string.Format("{0," + (format.Alignment * format.Width).ToString() + "}", format.Description));
            }
            data.Append(System.Environment.NewLine);
            data.Append(_line);

            return data.ToString();
        }

        private string FillData<T>(List<T> sourceList, List<FlatFileDataFormat> formats, string fieldDelimiter, bool dataDoubleQuotation)
        {
            StringBuilder data = new StringBuilder();
            if (formats != null && formats.Count > 0)
            {
                for (int rowIndex = 0; rowIndex < sourceList.Count; rowIndex++)
                {
                    for (int fieldIndex = 0; fieldIndex < formats.Count; fieldIndex++)                        
                        {
                            if (formats[fieldIndex] != null)
                            {
                                if (!string.IsNullOrEmpty(fieldDelimiter) && fieldIndex != formats.Count - 1)
                                {
                                    if (fieldDelimiter.ToLower() == "tab" )
                                        data.AppendFormat("{0}\t", FillField(sourceList[rowIndex], formats[fieldIndex], dataDoubleQuotation));
                                    else if (fieldDelimiter.ToLower() == "space" )
                                        data.AppendFormat("{0} ", FillField(sourceList[rowIndex], formats[fieldIndex], dataDoubleQuotation));
                                    else
                                        data.AppendFormat("{0}{1}", FillField(sourceList[rowIndex], formats[fieldIndex], dataDoubleQuotation), fieldDelimiter);
                                }
                                else
                                    data.Append(FillField(sourceList[rowIndex], formats[fieldIndex], dataDoubleQuotation));   
                            }
                        }
                    data.Append(System.Environment.NewLine);
                }
            }

            return data.ToString();        
        }

        private string FillField<T>(T sourceRecord, FlatFileDataFormat format,bool dataDoubleQuotation)
        {
            string fielddata = string.Empty;

            if (format.Hide == true)
                return fielddata;
            
            PropertyInfo propInfo = sourceRecord.GetType().GetProperty(format.Field);

            if (propInfo == null)
            {
                fielddata = string.Format("{0," + (format.Alignment * format.Width).ToString() + "}", "").Substring(0, format.Width);
                return fielddata;
            }

            Type propType = propInfo.PropertyType;

            if (propInfo.PropertyType.IsGenericType && propInfo.PropertyType.GetGenericTypeDefinition() == typeof(Nullable<>))
            {
                propType = Nullable.GetUnderlyingType(propInfo.PropertyType);

                if (propInfo.GetValue(sourceRecord, null) == null)
                {
                    switch (propType.ToString())
                    {
                        case "System.Decimal":
                        case "System.Int32":
                        case "System.Int64":
                            if (format.Width == 0)
                                fielddata = string.Format("{0}", (string.IsNullOrEmpty(format.Format) ? "0" : 0.ToString(format.Format)));
                            else
                                fielddata = string.Format("{0," + (format.Alignment * format.Width).ToString() + "}", (string.IsNullOrEmpty(format.Format) ? "0" : 0.ToString(format.Format))).Substring(0, format.Width);
                            break;
                        default:
                            if (format.Width == 0)
                                fielddata = "";
                            else
                                fielddata = string.Format("{0," + (format.Alignment * format.Width).ToString() + "}", "").Substring(0, format.Width);
                            break;
                    }
                    return fielddata;
                }
            }

            if (!string.IsNullOrEmpty(format.Format))
            {

                switch (propType.ToString())
                {
                    case "System.String":
                        if (format.Width == 0)
                            fielddata = string.Format("{0}", propInfo.GetValue(sourceRecord, null).ToString());
                        else
                            fielddata = string.Format("{0," + (format.Alignment * format.Width).ToString() + "}", propInfo.GetValue(sourceRecord, null).ToString()).Substring(0, format.Width);
                        break;
                    case "System.Decimal":
                        if (format.Width == 0)
                            fielddata = string.Format("{0}", ((System.Decimal)propInfo.GetValue(sourceRecord, null)).ToString(format.Format));
                        else
                            fielddata = string.Format("{0," + (format.Alignment * format.Width).ToString() + "}", ((System.Decimal)propInfo.GetValue(sourceRecord, null)).ToString(format.Format)).Substring(0, format.Width);
                        break;
                    case "System.Int32":
                        if (format.Width == 0)
                            fielddata = string.Format("{0}", ((System.Int32)propInfo.GetValue(sourceRecord, null)).ToString(format.Format));
                        else
                            fielddata = string.Format("{0," + (format.Alignment * format.Width).ToString() + "}", ((System.Int32)propInfo.GetValue(sourceRecord, null)).ToString(format.Format)).Substring(0, format.Width);
                        break;
                    case "System.Int64":
                        if (format.Width == 0)
                            fielddata = string.Format("{0}", ((System.Int64)propInfo.GetValue(sourceRecord, null)).ToString(format.Format));
                        else
                            fielddata = string.Format("{0," + (format.Alignment * format.Width).ToString() + "}", ((System.Int64)propInfo.GetValue(sourceRecord, null)).ToString(format.Format)).Substring(0, format.Width);
                        break;
                    case "System.DateTime":
                        if (format.Width == 0)
                            fielddata = string.Format("{0}", ((System.DateTime)propInfo.GetValue(sourceRecord, null)).ToString(format.Format));
                        else
                            fielddata = string.Format("{0," + (format.Alignment * format.Width).ToString() + "}", ((System.DateTime)propInfo.GetValue(sourceRecord, null)).ToString(format.Format)).Substring(0, format.Width);
                        break;
                    default:
                        if (format.Width == 0)
                            fielddata = string.Format("{0}", propInfo.GetValue(sourceRecord, null).ToString());
                        else
                            fielddata = string.Format("{0," + (format.Alignment * format.Width) + "}", propInfo.GetValue(sourceRecord, null).ToString()).Substring(0, format.Width);
                        break;
                }

            }
            else
            {
                if (format.Width == 0)
                    fielddata = string.Format("{0}", Convert.ToString(propInfo.GetValue(sourceRecord, null)));
                else
                  fielddata = string.Format("{0," + (format.Alignment * format.Width) + "}", Convert.ToString(propInfo.GetValue(sourceRecord, null))).Substring(0, format.Width);

                if (dataDoubleQuotation)
                {
                    fielddata = fielddata.Replace(",", " ");
                    fielddata = fielddata.Replace("\"", "");
                    fielddata = "\"" + fielddata + "\"";
                }
            }

            if (format.PaddingChar == '0')
            {
                fielddata = fielddata.Replace(' ', format.PaddingChar);

                switch (propType.ToString())
                {
                    case "System.Decimal":
                    case "System.Int32":
                    case "System.Int64":
                    if (!string.IsNullOrEmpty(fielddata) && fielddata.IndexOf('-') >= 0)
                    {
                        fielddata = fielddata.Replace("-", "");
                        fielddata = fielddata.Insert(0, "-");
                    }
                    break;
                }
            }

            if (!string.IsNullOrEmpty(format.FieldDelimiter) )
            { 
              fielddata =  String.Format("{0}" + format.FieldDelimiter , fielddata);                
            }

            return fielddata;
        }

        private bool WriteData(string data, string filePath)
        {
            bool status = false;
            System.IO.File.WriteAllText(filePath, data);
            //System.IO.StreamWriter file = new System.IO.StreamWriter(filePath);
            //file.WriteLine(data);
            //file.Close();
            status = true;
            return status;
        }

		#endregion

		#region Methods.Public               
        
        public void Prepare<T>(string filePath, string dataFormat, bool showHeader, bool showFooter, string title, string fieldDelimiter,  List<T> sourceList, string fileName = "",bool dataDoubleQuotation =false)
        {
            try
            {
                List<FlatFileDataFormat> dataFormatList = FlatFileDataFormat.Prepare(dataFormat);

                foreach (FlatFileDataFormat dataFmt in dataFormatList)
                {
                    if (dataFmt.Hide == false)
                        _totalWidth = _totalWidth + dataFmt.Width;
                }

                _line = string.Format("{0," + _totalWidth.ToString() + "}", " ");
                _line = _line.Replace(' ', lineChar);
                _line = _line + Environment.NewLine;


                // 3. Read Data from Source
                string data = string.Empty;
                if (showHeader)
                {
                    data = data + FillHeaderWithFields(dataFormatList, title, fieldDelimiter);
                }
                data = data + PrepareData(sourceList, dataFormatList, fieldDelimiter, dataDoubleQuotation);

                if (showFooter)
                {
                    data = data + FillFooter();
                }
                filePath = ((filePath.Substring(filePath.Length - 1, 1) == @"\" || filePath.Substring(filePath.Length - 1, 1) == @"/") ? filePath : filePath + @"\");
                if (!string.IsNullOrWhiteSpace(fileName))
                    _fileName = filePath + fileName;
                else
                    _fileName = string.Format("{0}{1}_{2:yyyy-MM-dd_hh-mm-ss-tt}.txt", filePath, title.Replace('-', '_'), DateTime.Now);

                // 4. Write Data to Destination
                WriteData(data, _fileName);
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
            }
        }

        public void FtpUpload(string hostIP, string userName, string password, string remoteFile, string localFile, bool usePassive=true, bool useBinary = true, bool useSsl = false)
        {
            host = hostIP; user = userName; pass = password;
            try
            {
                /* Create an FTP Request */
                try
                {
                    ftpRequest = (FtpWebRequest)FtpWebRequest.Create(host + "/" + remoteFile);
                }
                catch
                { }
                /* Log in to the FTP Server with the User Name and Password Provided */
                ftpRequest.Credentials = new NetworkCredential(user, pass);
                /* When in doubt, use these options */
                ftpRequest.UseBinary = useBinary;
                ftpRequest.UsePassive = usePassive;
                ftpRequest.KeepAlive = true;
                if(useSsl)
                {
                    ServicePointManager.ServerCertificateValidationCallback = delegate (
              Object obj, X509Certificate certificate, X509Chain chain, SslPolicyErrors errors)
                    {
                        return (true);
                    };
                    ftpRequest.EnableSsl = true;
                    ftpRequest.Proxy = null;
                }
                /* Specify the Type of FTP Request */
                ftpRequest.Method = WebRequestMethods.Ftp.UploadFile;
                /* Establish Return Communication with the FTP Server */
                ftpStream = ftpRequest.GetRequestStream();
                /* Open a File Stream to Read the File for Upload */
                FileStream localFileStream = new FileStream(localFile, FileMode.Open);
                /* Buffer for the Downloaded Data */
                byte[] byteBuffer = new byte[bufferSize];
                int bytesSent = localFileStream.Read(byteBuffer, 0, bufferSize);
                /* Upload the File by Sending the Buffered Data Until the Transfer is Complete */
                try
                {
                    while (bytesSent != 0)
                    {
                        ftpStream.Write(byteBuffer, 0, bytesSent);
                        bytesSent = localFileStream.Read(byteBuffer, 0, bufferSize);
                    }
                }
                catch (Exception ex) { if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex)); }
                /* Resource Cleanup */
                localFileStream.Close();
                ftpStream.Close();
                ftpRequest = null;

            }
            catch (Exception ex) { if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex)); }
            return;

        }

		public void Process(string sender,string recepient,string subject,string body,string server,int  port,string username,string password)
		{
             MailMessage myMail = new MailMessage(sender, recepient, subject, body);          
             myMail.IsBodyHtml = true;
             myMail.Attachments.Add(new Attachment(_filePath));
             SmtpClient SMTPServer = new SmtpClient(server,port);
             SMTPServer.Credentials = new System.Net.NetworkCredential(username, password);
             SMTPServer.Send(myMail);
		}
        
		#endregion
	}
}
