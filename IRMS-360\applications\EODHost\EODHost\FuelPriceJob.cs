﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;

using log4net;
using Newtonsoft.Json.Linq;
using NHibernate.Criterion;
using NHibernate.SqlCommand;
using NHibernate.Transform;
using Quartz;

using Upp.Irms.Core;
using Upp.Irms.Domain;
using Upp.Shared.Utilities;
using System.Globalization;

namespace Upp.Irms.EOD.Host
{
    class FuelPriceJob : IJob
    {
        #region Fields

        ILog _logger = LogManager.GetLogger(typeof(FuelPriceJob));

        const string JParam_NumberOfDays = "NUMBEROFDAYS";
        const string JParam_nextJob = "NEXTJOB";
        const string JParam_Url = "URL";
        const string JParam_apiKey = "APIKEY";
        const string JParam_seriesId = "SERIESID";

        int numberOfDays = 0;
        string nextJob = "";
        string url = "";
        string apiKey = "";
        string seriesId = "";

        string jobname = "Fuel Price job";

        #endregion

        #region Constructor

        public FuelPriceJob()
        {
        }

        #endregion

        #region Methods.Public

        public virtual void Execute(JobExecutionContext context)
        {
            if (JobParametersAreValid(context.MergedJobDataMap) == false)
            {
                JobExecutionException jex = new JobExecutionException("Missing job parameter");
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }

            ParseJobParameters(context.MergedJobDataMap);

            GetFuelPrice();
            NextJobScheduling(jobname);
        }

        #endregion

        #region Methods.Private

        private bool JobParametersAreValid(JobDataMap jobParams)
        {
            bool validity = true;

            if (!jobParams.Contains(JParam_NumberOfDays))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_NumberOfDays);
                validity = false;
            }
            else if (jobParams.GetInt(JParam_NumberOfDays) <= 0)
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Value for '{0}' should be greater than zero", JParam_NumberOfDays);
                validity = false;
            }

            if (!jobParams.Contains(JParam_Url))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_Url);
                validity = false;
            }
            else if (string.IsNullOrEmpty(jobParams.GetString(JParam_Url)))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Value for '{0}' should not be empty", JParam_Url);
                validity = false;
            }

            if (!jobParams.Contains(JParam_apiKey))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_apiKey);
                validity = false;
            }
            else if (string.IsNullOrEmpty(jobParams.GetString(JParam_apiKey)))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Value for '{0}' should not be empty", JParam_apiKey);
                validity = false;
            }

            if (!jobParams.Contains(JParam_seriesId))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_seriesId);
                validity = false;
            }
            else if (string.IsNullOrEmpty(jobParams.GetString(JParam_seriesId)))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Value for '{0}' should not be empty", JParam_seriesId);
                validity = false;
            }


            if (!jobParams.Contains(JParam_nextJob))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_nextJob);
                validity = false;
            }

            return validity;
        }

        private void ParseJobParameters(JobDataMap jobParams)
        {
            try
            {
                //map the parameters to jobParams              
                this.numberOfDays = jobParams.GetInt(JParam_NumberOfDays);
                this.nextJob = jobParams.GetString(JParam_nextJob);
                this.url = jobParams.GetString(JParam_Url);
                this.apiKey = jobParams.GetString(JParam_apiKey);
                this.seriesId = jobParams.GetString(JParam_seriesId);
            }
            catch (Exception ex)
            {
                JobExecutionException jex = new JobExecutionException("Invalid data type in job parameters", ex);
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }
        }

        private void NextJobScheduling(string jobname)
        {
            if (!String.IsNullOrWhiteSpace(nextJob))
            {
                if (_logger.IsDebugEnabled) _logger.DebugFormat("NextJob:  {0}", nextJob);
                try
                {
                    string[] jobKeys = Service.Instance.Scheduler.GetJobNames("Manual");
                    foreach (string jobKey in jobKeys)
                    {
                        if (jobKey.Equals(nextJob))
                        {
                            SimpleTrigger trigger = new SimpleTrigger();
                            trigger.Name = nextJob + "Trigger";
                            trigger.JobName = nextJob;
                            trigger.JobGroup = "Manual";
                            trigger.Group = "Manual";
                            trigger.StartTimeUtc = DateTime.UtcNow.AddSeconds(30);
                            trigger.RepeatCount = 0;
                            trigger.RepeatInterval = TimeSpan.Zero;
                            Service.Instance.Scheduler.RescheduleJob(nextJob + "Trigger", "Manual", trigger);
                            if (_logger.IsDebugEnabled) _logger.DebugFormat("{1} - Next Job {0} is scheduled: ", nextJob, jobname);
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (_logger.IsErrorEnabled) _logger.ErrorFormat("{1} - Next Job error: {0}", ex.Message, jobname);
                }
            }
        }

        private void GetFuelPrice()
        {
            try
            {
                string response = GetResponse(this.url, this.apiKey, this.seriesId);
                //
                if (_logger.IsDebugEnabled) _logger.DebugFormat("Response:-" + response);
                //
                if (!String.IsNullOrEmpty(response))
                {
                    string effectiveDate = string.Empty;
                    string price = string.Empty;
                    //
                    try
                    {
                        JObject obj = JObject.Parse(response);
                        JArray array = (JArray)obj["series"];
                        //
                        foreach (JObject row in array)
                        {
                            JArray data = (JArray)row["data"];
                            //
                            if (data.Count > 0)
                            {
                                JArray prices = (JArray)data.First;

                                if (prices != null && prices.Count > 0)
                                {
                                    effectiveDate = prices[0].ToString();
                                    price = prices[1].ToString();
                                }
                                //
                                break;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
                    }
                    //
                    DateTime? effective = GetDateTime(effectiveDate);
                    if (effective.HasValue)
                    {
                        IList<ShipFuelSurcharge> fuelSurcharges = new List<ShipFuelSurcharge>();
                        using (UnitWrapper wrapper = new UnitWrapper())
                        {
                            wrapper.Execute(() =>
                            {
                                DateTime start = new DateTime(effective.Value.Year, effective.Value.Month, effective.Value.Day, 0, 0, 0, 0);
                                DateTime end = new DateTime(effective.Value.Year, effective.Value.Month, effective.Value.Day, 23, 59, 59, 999);
                                //
                                DetachedCriteria criteria = DetachedCriteria.For<ShipFuelSurcharge>()
                                     .Add(new SimpleExpression("Active", "A", "="))
                                     .Add(Restrictions.Between("EffectiveDate", start, end));
                                fuelSurcharges = Repositories.Get<ShipFuelSurcharge>().List(criteria);
                                //
                                if (fuelSurcharges == null || fuelSurcharges.Count == 0)
                                {
                                    try
                                    {
                                        ShipFuelSurcharge fuelSurcharge = new ShipFuelSurcharge();
                                        fuelSurcharge.DateCreated = DateTime.Now;
                                        fuelSurcharge.UserCreated = "eod_manager";
                                        fuelSurcharge.DateModified = null;
                                        fuelSurcharge.UserModified = null;
                                        fuelSurcharge.EffectiveDate = effective;
                                        fuelSurcharge.Active = "A";
                                        fuelSurcharge.FuelPrice = Converter.ToDecimal(price);
                                        //
                                        Repositories.Get<ShipFuelSurcharge>().Add(fuelSurcharge);

                                        _logger.ErrorFormat("Creation of fuel Surcharge done");
                                    }
                                    catch (Exception ex)
                                    {
                                        _logger.ErrorFormat("Error while Creation fuel Surcharge {0}", ex.Message);
                                    }
                                }
                                else if (fuelSurcharges.Count > 0)
                                {
                                    try
                                    {
                                        fuelSurcharges[0].DateModified = DateTime.Now;
                                        fuelSurcharges[0].UserModified = "eod_manager";
                                        fuelSurcharges[0].FuelPrice = Converter.ToDecimal(price);
                                        //
                                        Repositories.Get<ShipFuelSurcharge>().Update(fuelSurcharges[0]);

                                        _logger.ErrorFormat("Update fuel Surcharge done");
                                    }
                                    catch (Exception ex)
                                    {
                                        _logger.ErrorFormat("Error while updating fuel Surcharge {0}", ex.Message);
                                    }

                                }
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
            }
        }

        private DateTime? GetDateTime(string dateTime)
        {
            DateTime? date = null;
            if (!string.IsNullOrEmpty(dateTime))
            {
                try
                {
                    string orderdate = dateTime;
                    if (orderdate.Length == 8) date = DateTime.ParseExact(dateTime, "yyyyMMdd", CultureInfo.InvariantCulture);
                    else if (orderdate.Length == 14) date = DateTime.ParseExact(dateTime, "yyyyMMddHHmmss", CultureInfo.InvariantCulture);
                }
                catch (FormatException fex)
                {
                    _logger.ErrorFormat("Error parsing " + dateTime + "' field: " + fex.Message);
                }
            }
            //
            return date;
        }

        #region Methods.Private.APICall

        private string GetResponse(String url, String apiKey, String seriesId)
        {
            string content = string.Empty;
            //
            try
            {
                url += apiKey;
                url += "";
                url += "&series_id=";
                url += seriesId;
                //
                if (_logger.IsDebugEnabled) _logger.DebugFormat("Request:-" + url);
                //
                HttpWebRequest http = (HttpWebRequest)WebRequest.Create(url);
                //
                http.Accept = "application/json";
                http.ContentType = "application/json";
                http.Method = "POST";
                http.Timeout = 30000;
                //		
                Stream newStream = http.GetRequestStream();
                //newStream.Write(bytes, 0, bytes.Length);
                newStream.Close();
                //
                var webResponse = http.GetResponse();
                var stream = webResponse.GetResponseStream();
                var sr = new StreamReader(stream);
                //
                content = sr.ReadToEnd();
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
            }
            //
            return content;
        }

        #endregion

        #endregion
    }
}
