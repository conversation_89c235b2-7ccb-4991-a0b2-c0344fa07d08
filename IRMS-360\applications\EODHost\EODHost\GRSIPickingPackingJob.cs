using log4net;
using Newtonsoft.Json.Linq;
using NHibernate.Criterion;
using NHibernate.Transform;
using Quartz;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Runtime.Serialization.Json;
using System.Text;
using System.Threading.Tasks;
using Upp.Irms.Core;
using Upp.Irms.Domain;

namespace Upp.Irms.EOD.Host
{
    class GRSIPickingPackingJob : IJob
    {
        #region Fields

        ILog _logger = LogManager.GetLogger(typeof(GRSIPickingPackingJob));

        const string JParam_GRSIHostUserID = "GRSIHOSTUSERID";
        const string JParam_GRSIDownloadFileFormat = "GRSIDOWNLOADFILEFORMAT";
        const string JParam_GRSIToIRMSDownloadFolder = "GRSITOIRMSDOWNLOADFOLDER";
        const string JParam_GRSILogFileFolder = "GRSILOGFILEFOLDER";
        const string JParam_nextJob = "NEXTJOB";

        string grsiHostUserID = "";
        string grsiDownloadFileFormat = "";
        string grsiToIRMSDownloadFolder = "";
        string grsiLogFileFolder = "";
        string nextJob = "";

        string jobname = "GRSI_Interface_Manager";
        string logDateTimeFormat = "dd/MM/yy HH:mm:ss.fff";

        #endregion

        #region Constructor

        public GRSIPickingPackingJob()
        {
        }

        #endregion

        #region Methods.Public

        public virtual void Execute(JobExecutionContext context)
        {
            if (JobParametersAreValid(context.MergedJobDataMap) == false)
            {
                JobExecutionException jex = new JobExecutionException("Missing job parameter");
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }

            ParseJobParameters(context.MergedJobDataMap);

            PerformPickingPacking();

            NextJobScheduling(jobname);
        }

        #endregion

        #region Methods.Private

        private bool JobParametersAreValid(JobDataMap jobParams)
        {
            bool validity = true;

            if (!jobParams.Contains(JParam_GRSIHostUserID))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_GRSIHostUserID);
                validity = false;
            }
            if (!jobParams.Contains(JParam_GRSIDownloadFileFormat))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_GRSIDownloadFileFormat);
                validity = false;
            }
            if (!jobParams.Contains(JParam_GRSIToIRMSDownloadFolder))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_GRSIToIRMSDownloadFolder);
                validity = false;
            }
            if (!jobParams.Contains(JParam_GRSILogFileFolder))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_GRSILogFileFolder);
                validity = false;
            }
            if (!jobParams.Contains(JParam_nextJob))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_nextJob);
                validity = false;
            }

            return validity;
        }

        private void ParseJobParameters(JobDataMap jobParams)
        {
            try
            {
                //map the parameters to jobParams              
                this.grsiHostUserID = jobParams.GetString(JParam_GRSIHostUserID);
                this.grsiDownloadFileFormat = jobParams.GetString(JParam_GRSIDownloadFileFormat);
                this.grsiToIRMSDownloadFolder = jobParams.GetString(JParam_GRSIToIRMSDownloadFolder);
                this.grsiLogFileFolder = jobParams.GetString(JParam_GRSILogFileFolder);
                this.nextJob = jobParams.GetString(JParam_nextJob);
            }
            catch (Exception ex)
            {
                JobExecutionException jex = new JobExecutionException("Invalid data type in job parameters", ex);
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }
        }

        private void NextJobScheduling(string jobname)
        {
            if (!String.IsNullOrWhiteSpace(nextJob))
            {
                if (_logger.IsDebugEnabled) _logger.DebugFormat("NextJob:  {0}", nextJob);
                try
                {
                    string[] jobKeys = Service.Instance.Scheduler.GetJobNames("Manual");
                    foreach (string jobKey in jobKeys)
                    {
                        if (jobKey.Equals(nextJob))
                        {
                            SimpleTrigger trigger = new SimpleTrigger();
                            trigger.Name = nextJob + "Trigger";
                            trigger.JobName = nextJob;
                            trigger.JobGroup = "Manual";
                            trigger.Group = "Manual";
                            trigger.StartTimeUtc = DateTime.UtcNow.AddSeconds(30);
                            trigger.RepeatCount = 0;
                            trigger.RepeatInterval = TimeSpan.Zero;
                            Service.Instance.Scheduler.RescheduleJob(nextJob + "Trigger", "Manual", trigger);
                            if (_logger.IsDebugEnabled) _logger.DebugFormat("{1} - Next Job {0} is scheduled: ", nextJob, jobname);
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (_logger.IsErrorEnabled) _logger.ErrorFormat("{1} - Next Job error: {0}", ex.Message, jobname);
                }
            }
        }

        private void PerformPickingPacking()
        {
            try
            {
                if (_logger.IsDebugEnabled) _logger.DebugFormat("START - EOD Job :  {0}", jobname);

                //Check the grsiToIRMSDownloadFolder exist or not
                if (!Directory.Exists(grsiToIRMSDownloadFolder))
                {
                    _logger.ErrorFormat("Directory not Found/Access Denied to folder :" + grsiToIRMSDownloadFolder);
                    return;
                }

                //Check the grsiLogFileFolder exist or not
                if (!Directory.Exists(grsiLogFileFolder))
                {
                    _logger.ErrorFormat("Directory not Found/Access Denied to folder :" + grsiLogFileFolder);
                    return;
                }

                //Create backUp folder to move the input files after processing 
                string grsiToIRMSDownloadFolderBk = string.Empty;
                StringBuilder backupPathSb = new StringBuilder();
                if (grsiToIRMSDownloadFolder.EndsWith("\\"))
                    backupPathSb.Append(grsiToIRMSDownloadFolder);
                else
                    backupPathSb.Append(grsiToIRMSDownloadFolder + "\\");
                backupPathSb.Append("backUp\\");
                grsiToIRMSDownloadFolderBk = backupPathSb.ToString();

                if (!Directory.Exists(grsiToIRMSDownloadFolderBk))
                {
                    Directory.CreateDirectory(grsiToIRMSDownloadFolderBk);
                }

                //Get the today's LogFilePath
                string fullLogFilePath = getLogFolderFilePath(grsiLogFileFolder, "grsifrom");

                //Fetch all the Order file full paths and its content from GRSITOIRMSDOWNLOADFOLDER
                grsiDownloadFileFormat = grsiDownloadFileFormat.Replace(".", "*.");
                string[] grsiDownloadFileFormats = grsiDownloadFileFormat.Split(',');
                Dictionary<string, string> allOrdersFilePathsContents = readAllOrdersAndMoveToBackFolder(grsiToIRMSDownloadFolder, fullLogFilePath, grsiToIRMSDownloadFolderBk, grsiDownloadFileFormats, "dspsc", "dspscxxxxxxxxx");

                //Read GRSI Download File Fields name and length from DB
                Dictionary<string, int> fieldsLength = getFieldsLengthFromDB("PickingPacking");

                if(fieldsLength.Count == 0)
                {
                    _logger.ErrorFormat("No Interface Fields Found");
                    return;
                }

                //Get IntegrationApiUrl
                string integrationApiUrl = string.Empty;
                string integrationUrl = string.Empty;
                if (System.Configuration.ConfigurationManager.AppSettings["IntegrationURL"] != null)
                    integrationUrl = System.Configuration.ConfigurationManager.AppSettings["IntegrationURL"].ToString();
                integrationApiUrl = String.Format("{0}{1}", integrationUrl, "/processes/updatepickingpacking/update");

                //loop each files, read its Contents, prepare input json format, call to Integration (grsiPickingPacking endpoint)
                foreach (KeyValuePair<string, string> orderFilePathContent in allOrdersFilePathsContents)
                {
                    //Log the FileName to GRSILOGFOLDER
                    File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": GRSI Order File Found" });

                    if(string.IsNullOrEmpty(orderFilePathContent.Value))
                    {
                        File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": GRSI Order File is Empty" });
                        continue;
                    }

                    string company = string.Empty;
                    string warehouse = string.Empty;
                    string orderCode = string.Empty;
                    string orderSuffix = string.Empty;
                    string dataString = prepareInputForPickPackApi(fieldsLength, orderFilePathContent.Value, grsiHostUserID, ref company, ref warehouse, ref orderCode, ref orderSuffix);

                    JObject result = PostData(dataString, integrationApiUrl, true);
                    if (result["result_code"].ToString() == "0000")
                    {
                        File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": " + result["result_msg"].ToString() });
                        File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": GRSI order '"+ orderCode + "' Processed Successfully" });
                    }
                    else if (result["result_code"].ToString() == "9642" && result["result_msg"].ToString() == "Order not found")
                    {
                        File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": GRSI order '" + orderCode + "' not available" });
                    }
                    else if (result["result_msg"].ToString() == "Order not in distributed status")
                    {
                        File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": GRSI order '" + orderCode + "' not in distributed status" });
                    }
                    else
                    {
                        File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": "+ result["result_msg"].ToString() });
                    }
                    File.AppendAllText(fullLogFilePath,  "\n" );
                }
                if (_logger.IsDebugEnabled) _logger.DebugFormat("END - EOD Job :  {0}", jobname);
            }
            catch (Exception ex)
            {
                _logger.ErrorFormat("Error at GRSIPickingPacking::PerformPickingPacking() " + ex.Message);
            }
        }

        public string prepareInputForPickPackApi(Dictionary<string, int> fieldsLength, string orderFilePathContent, string grsiHostUserID, ref string company, ref string warehouse, ref string orderCode, ref string orderSuffix)
        {
            string pickPackApiInput = string.Empty;
            try
            {
                StringBuilder dataStrBuilder = new StringBuilder();
                dataStrBuilder.Append("[{");
                int startPosition = 0;
                foreach (KeyValuePair<string, int> fieldLength in fieldsLength)
                {
                    if ((startPosition + fieldLength.Value) <= orderFilePathContent.Length)
                    {
                        dataStrBuilder.Append("\"" + fieldLength.Key + "\"" + ":" + "\"" + orderFilePathContent.Substring(startPosition, fieldLength.Value).Trim() + "\"");

                        string value = orderFilePathContent.Substring(startPosition, fieldLength.Value).Trim();
                        if ("company".Equals(fieldLength.Key))
                            company = value;
                        else if ("warehouse".Equals(fieldLength.Key))
                            warehouse = value;
                        else if ("order_code".Equals(fieldLength.Key))
                            orderCode = value;
                        else if ("order_suffix".Equals(fieldLength.Key))
                            orderSuffix = value;

                        startPosition += fieldLength.Value;
                        dataStrBuilder.Append(",");
                    }
                    else if ("order_code".Equals(fieldLength.Key))
                    {
                        dataStrBuilder.Append("\"" + fieldLength.Key + "\"" + ":" + "\"" + orderFilePathContent.Substring(startPosition).Trim() + "\"");
                        orderCode = orderFilePathContent.Substring(startPosition).Trim();
                        startPosition += fieldLength.Value;
                        dataStrBuilder.Append(",");
                    }
                    else
                    {
                        dataStrBuilder.Append("\"" + fieldLength.Key + "\"" + ":" + "\"" + "\"");
                        dataStrBuilder.Append(",");
                        break;
                    }
                }
                dataStrBuilder.Append("\"user_id\"" + ":" + "\"" + grsiHostUserID+ "\"," + "\"result_code\"" + ":" + "\"" + "\"," + "\"result_msg\"" + ":" + "\"" + "\"");
                dataStrBuilder.Append("}]");
                pickPackApiInput = dataStrBuilder.ToString();
            }
            catch (Exception ex)
            {
                _logger.ErrorFormat("Error at GRSIPickingPacking::getDataString() " + ex.Message);
            }
            return pickPackApiInput;
        }

        public Dictionary<string, int> getFieldsLengthFromDB(string intHeaderCode)
        {
            Dictionary<string, int> fieldsLength = new Dictionary<string, int>();
            using (UnitWrapper wrapper = new UnitWrapper())
            {
                wrapper.Execute(() =>
                {
                    try
                    {
                        DetachedCriteria nhdcfieldsLength = DetachedCriteria.For<InterfaceHeader>()
                                                          .CreateAlias("InterfaceDetails", "InterfaceDetail")
                                                          .SetProjection(Projections.ProjectionList()
                                                          .Add(Projections.Property("InterfaceDetail.FieldName"), "FieldName")
                                                          .Add(Projections.Property("InterfaceDetail.DataFormat"), "DataFormat"))
                                                          .Add(Restrictions.Eq("InterfaceHeaderCode", intHeaderCode))
                                                          .Add(Restrictions.Eq("Active", "A"))
                                                          .Add(Restrictions.Eq("InterfaceDetail.Active", "A"))
                                                          .AddOrder(Order.Asc("InterfaceDetail.SortOrder"))
                                                          .SetResultTransformer(Transformers.AliasToBean<InterfaceDetail>());
                        IList<InterfaceDetail> nhfieldsLength = Repositories.Get<InterfaceDetail>().List(nhdcfieldsLength);

                        foreach (InterfaceDetail fieldLength in nhfieldsLength)
                        {
                            if (!string.IsNullOrEmpty(fieldLength.FieldName) && !string.IsNullOrEmpty(fieldLength.DataFormat))
                                fieldsLength.Add(fieldLength.FieldName, Convert.ToInt32(fieldLength.DataFormat.Substring(1)));
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.ErrorFormat("Error at GRSIPickingPacking::getFieldsLengthFromDB() " + ex.Message);
                    }
                });
            }

            return fieldsLength;
        }

        public string getLogFolderFilePath(string grsiLogFileFolder, string logPrefix)
        {
            string fullLogFilePath = string.Empty;
            try
            {
                string logFileName = logPrefix + DateTime.Now.ToString("yyyyMMdd") + ".log";

                StringBuilder logpathStrBuilder = new StringBuilder();
                if (grsiLogFileFolder.EndsWith("\\"))
                    logpathStrBuilder.Append(grsiLogFileFolder);
                else
                    logpathStrBuilder.Append(grsiLogFileFolder + "\\");
                logpathStrBuilder.Append(logFileName);

                fullLogFilePath = logpathStrBuilder.ToString();
            }
            catch (Exception ex)
            {
                _logger.ErrorFormat("Error at GRSIPickingPacking::getGRSILogFolderFilePath() " + ex.Message);
            }
            return fullLogFilePath;
        }

        public Dictionary<string, string> readAllOrdersAndMoveToBackFolder(string grsiToIRMSDownloadFolder, string fullLogFilePath, string grsiToIRMSDownloadFolderBk, string[] grsiDownloadFileFormats, string filePrefix, string fileFormat)
        {
            Dictionary<string, string> filesPathContent = new Dictionary<string, string>();
            try
            {
                DirectoryInfo dinfo = new DirectoryInfo(grsiToIRMSDownloadFolder);
                FileInfo[] Files = null;

                foreach (string fileformat in grsiDownloadFileFormats)
                {
                    if (grsiDownloadFileFormats.Count() > 0 && grsiDownloadFileFormats[0] != "" && grsiDownloadFileFormats[0] != "*")
                        Files = dinfo.GetFiles(fileformat);
                    else
                        Files = dinfo.GetFiles();
                    //
                    foreach (FileInfo file in Files)
                    {
                        if(!file.Name.StartsWith(filePrefix, StringComparison.InvariantCultureIgnoreCase))
                            File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + file.FullName + ": File name is not in format "+ fileFormat });    
                        else
                            filesPathContent.Add(file.FullName, File.ReadAllText(file.FullName));
                        file.CopyTo(Path.Combine(grsiToIRMSDownloadFolderBk, file.Name), true);
                        file.Delete();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.ErrorFormat("Error at GRSIPickingPacking::getAllOrdersFilePath() " + ex.Message);
            }
            return filesPathContent;
        }

        #region Methods.Private.APICall
        public JObject PostData(string dataString, string url, bool isArrayResult)
        {
            JObject outputData = new JObject();
            try
            {
                WebClient webClientProxy = new WebClient();

                webClientProxy.Headers["Content-type"] = "application/json";

                MemoryStream mStream = new MemoryStream();

                DataContractJsonSerializer serializerToUplaod = new DataContractJsonSerializer(typeof(string));

                serializerToUplaod.WriteObject(mStream, dataString);

                byte[] data;
                data = webClientProxy.UploadData(url, "POST", mStream.ToArray());

                MemoryStream stream = new MemoryStream(data);
                DataContractJsonSerializer obj = new DataContractJsonSerializer(typeof(string));

                string result = obj.ReadObject(stream) as string;

                if (isArrayResult)
                {
                    JArray outputDataArray = JArray.Parse(result);
                    foreach (JObject jObj in outputDataArray)
                    {
                        outputData = jObj;
                        break;
                    }
                }
                else
                {
                    outputData = JObject.Parse(result);
                }
            }
            catch (Exception ex)
            {
                _logger.ErrorFormat("Error at GRSIPickingPacking::PostData() " + ex.Message);
                outputData.Add("result_msg", ex.Message);
            }
            return outputData;
        }

        #endregion

        #endregion
    }
}
