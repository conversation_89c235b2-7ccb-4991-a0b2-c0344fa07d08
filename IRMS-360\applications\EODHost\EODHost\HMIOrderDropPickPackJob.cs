using log4net;
using Newtonsoft.Json.Linq;
using NHibernate.Criterion;
using NHibernate.Transform;
using Quartz;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Runtime.Serialization.Json;
using System.Text;
using System.Threading.Tasks;
using Upp.Irms.Core;
using Upp.Irms.Domain;

namespace Upp.Irms.EOD.Host
{
    class HMIOrderDropPickPackJob : IJob
    {
        #region Fields

        ILog _logger = LogManager.GetLogger(typeof(HMIOrderDropPickPackJob));

        const string JParam_HMIPickLocation = "HMIPICKLOCATION";
        const string JParam_HMITOIRMSDownloadFolder = "HMITOIRMSDOWNLOADFOLDER";
        const string JParam_IRMSTOHMIUploadFolder = "IRMSTOHMIUPLOADFOLDER";
        const string JParam_HMILogFileFolder = "LOGFILEFOLDER";
        const string JParam_nextJob = "NEXTJOB";

        string hmiPickLocation = string.Empty;
        string hmiToIrmsDownloadFolder = string.Empty;
        string irmsToHmiUploadFolder = string.Empty;
        string hmiLogFileFolder = string.Empty;
        string nextJob = string.Empty;

        string jobname = "HMIOrderManager";
        string logDateTimeFormat = "dd/MM/yy HH:mm:ss.fff";

        #endregion

        #region Constructor

        public HMIOrderDropPickPackJob()
        {
        }

        #endregion

        #region Methods.Public

        public virtual void Execute(JobExecutionContext context)
        {
            if (JobParametersAreValid(context.MergedJobDataMap) == false)
            {
                JobExecutionException jex = new JobExecutionException("Missing job parameter");
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }

            ParseJobParameters(context.MergedJobDataMap);

            PerformDropPickPack();

            NextJobScheduling(jobname);
        }

        #endregion

        #region Methods.Private

        private bool JobParametersAreValid(JobDataMap jobParams)
        {
            bool validity = true;

            if (!jobParams.Contains(JParam_HMIPickLocation))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_HMIPickLocation);
                validity = false;
            }
            if (!jobParams.Contains(JParam_HMITOIRMSDownloadFolder))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_HMITOIRMSDownloadFolder);
                validity = false;
            }
            if (!jobParams.Contains(JParam_IRMSTOHMIUploadFolder))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_IRMSTOHMIUploadFolder);
                validity = false;
            }
            if (!jobParams.Contains(JParam_HMILogFileFolder))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_HMILogFileFolder);
                validity = false;
            }
            if (!jobParams.Contains(JParam_nextJob))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_nextJob);
                validity = false;
            }

            return validity;
        }

        private void ParseJobParameters(JobDataMap jobParams)
        {
            try
            {
                //map the parameters to jobParams              
                this.hmiPickLocation = jobParams.GetString(JParam_HMIPickLocation);
                this.hmiToIrmsDownloadFolder = jobParams.GetString(JParam_HMITOIRMSDownloadFolder);
                this.irmsToHmiUploadFolder = jobParams.GetString(JParam_IRMSTOHMIUploadFolder);
                this.hmiLogFileFolder = jobParams.GetString(JParam_HMILogFileFolder);
                this.nextJob = jobParams.GetString(JParam_nextJob);
            }
            catch (Exception ex)
            {
                JobExecutionException jex = new JobExecutionException("Invalid data type in job parameters", ex);
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }
        }

        private void NextJobScheduling(string jobname)
        {
            if (!String.IsNullOrWhiteSpace(nextJob))
            {
                if (_logger.IsDebugEnabled) _logger.DebugFormat("NextJob:  {0}", nextJob);
                try
                {
                    string[] jobKeys = Service.Instance.Scheduler.GetJobNames("Manual");
                    foreach (string jobKey in jobKeys)
                    {
                        if (jobKey.Equals(nextJob))
                        {
                            SimpleTrigger trigger = new SimpleTrigger();
                            trigger.Name = nextJob + "Trigger";
                            trigger.JobName = nextJob;
                            trigger.JobGroup = "Manual";
                            trigger.Group = "Manual";
                            trigger.StartTimeUtc = DateTime.UtcNow.AddSeconds(30);
                            trigger.RepeatCount = 0;
                            trigger.RepeatInterval = TimeSpan.Zero;
                            Service.Instance.Scheduler.RescheduleJob(nextJob + "Trigger", "Manual", trigger);
                            if (_logger.IsDebugEnabled) _logger.DebugFormat("{1} - Next Job {0} is scheduled: ", nextJob, jobname);
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (_logger.IsErrorEnabled) _logger.ErrorFormat("{1} - Next Job error: {0}", ex.Message, jobname);
                }
            }
        }

        private void PerformDropPickPack()
        {
            try
            {
                if (_logger.IsDebugEnabled) _logger.DebugFormat("START - EOD Job :  {0}", jobname);

                //Check the hmiToIrmsDownloadFolder exist or not
                if (!Directory.Exists(hmiToIrmsDownloadFolder))
                {
                    _logger.ErrorFormat("Directory not Found/Access Denied to folder :" + hmiToIrmsDownloadFolder);
                    return;
                }

                //Check the irmsToHmiUploadFolder exist or not
                if (!Directory.Exists(irmsToHmiUploadFolder))
                {
                    _logger.ErrorFormat("Directory not Found/Access Denied to folder :" + hmiToIrmsDownloadFolder);
                    return;
                }

                //Check the hmiLogFileFolder exist or not
                if (!Directory.Exists(hmiLogFileFolder))
                {
                    _logger.ErrorFormat("Directory not Found/Access Denied to folder :" + hmiLogFileFolder);
                    return;
                }

                //Create backUp folder to move the input files after processing 
                string hmiToIrmsDownloadFolderBk = string.Empty;
                StringBuilder backupPathSb = new StringBuilder();
                if (hmiToIrmsDownloadFolder.EndsWith("\\"))
                    backupPathSb.Append(hmiToIrmsDownloadFolder);
                else
                    backupPathSb.Append(hmiToIrmsDownloadFolder + "\\");
                backupPathSb.Append("backUp\\");
                hmiToIrmsDownloadFolderBk = backupPathSb.ToString();

                if (!Directory.Exists(hmiToIrmsDownloadFolderBk))
                {
                    Directory.CreateDirectory(hmiToIrmsDownloadFolderBk);
                }

                GRSIPickingPackingJob grsiPickPack = new GRSIPickingPackingJob();

                //Get the today's LogFilePath
                string logFilePath = grsiPickPack.getLogFolderFilePath(hmiLogFileFolder, "hmicoin");
                
                //Fetch all the Order file full paths and its content from hmiToIrmsDownloadFolder
                string[] hmiDownloadFileExt = new string[] { "*" };
                Dictionary<string, string> allOrdersFilePathsContents = grsiPickPack.readAllOrdersAndMoveToBackFolder(hmiToIrmsDownloadFolder, logFilePath, hmiToIrmsDownloadFolderBk, hmiDownloadFileExt, "dcoinc", "dcoincxxxxxxxxx");

                //Read HMI Log File Fields name and length from DB
                Dictionary<string, int> LogFilefieldsLength = grsiPickPack.getFieldsLengthFromDB("HMIOrderManager_LogFile");

                //Read IRMSToHMIUpload File Fields name and length from DB
                Dictionary<string, int> irmsToHmiUploadFilefieldsLength = grsiPickPack.getFieldsLengthFromDB("HMIOrderManager_IRMSTOHMIUPLOAD");

                if (LogFilefieldsLength.Count == 0 || irmsToHmiUploadFilefieldsLength.Count == 0)
                {
                    _logger.ErrorFormat("No Interface Fields Found");
                    return;
                }

                //Get IntegrationApiUrl
                string integrationDropApiUrl = string.Empty;
                string integrationPickPackApiUrl = string.Empty;
                string integrationUrl = string.Empty;
                if (System.Configuration.ConfigurationManager.AppSettings["IntegrationURL"] != null)
                    integrationUrl = System.Configuration.ConfigurationManager.AppSettings["IntegrationURL"].ToString();

                integrationDropApiUrl = String.Format("{0}{1}", integrationUrl, "/download/irordr");
                integrationPickPackApiUrl = String.Format("{0}{1}", integrationUrl, "/processes/updatepickingpacking/update");

                //loop each files, read its Contents, prepare input json format, call to Integration apis
                foreach (KeyValuePair<string, string> orderFilePathContent in allOrdersFilePathsContents)
                {
                    //Log the FileName to HMILOGFOLDER
                    File.AppendAllLines(logFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": HMI Order File Found" });

                    if (string.IsNullOrEmpty(orderFilePathContent.Value))
                    {
                        File.AppendAllLines(logFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": HMI Order File is Empty" });
                        File.AppendAllText(logFilePath, "\n");
                        continue;
                    }

                    string company = string.Empty;
                    string warehouse = string.Empty;
                    string orderCode = string.Empty;
                    string orderSuffix = string.Empty;
                    
                    //Prepare Input for Integration calls
                    string pickPackApiInput = grsiPickPack.prepareInputForPickPackApi(LogFilefieldsLength, orderFilePathContent.Value, "IRMS-Integration", ref company, ref warehouse, ref orderCode, ref orderSuffix);
                    string dropApiInput = prepareInputForDropApi(company, warehouse, orderCode, orderSuffix, hmiPickLocation);

                    if(!orderCode.StartsWith("c", StringComparison.InvariantCultureIgnoreCase))
                    {
                        File.AppendAllLines(logFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": HMI order '" + orderCode + "' must starts with 'c'" });
                        File.AppendAllText(logFilePath, "\n");
                        continue;
                    }
                    
                    //Call to Drop Api
                    bool isOrderDropSuccess = false;
                    JObject dropApiResult = grsiPickPack.PostData(dropApiInput, integrationDropApiUrl, false);
                    if (dropApiResult["result_code"].ToString() != "0000")
                    {
                        if (dropApiResult["result_msg"].ToString().Contains("Order not in open status"))
                        {
                            File.AppendAllLines(logFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": HMI order '" + orderCode + "' is not in Open status" });
                        }
                        else if (dropApiResult["result_msg"].ToString().Contains("One or more invalid orders"))
                        {
                            File.AppendAllLines(logFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": HMI order '" + orderCode + "' is not available" });
                        }
                        else
                            File.AppendAllLines(logFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": " + dropApiResult["result_msg"].ToString() });

                        File.AppendAllText(logFilePath, "\n");
                        continue;
                    }
                    isOrderDropSuccess = true;
                    //File.AppendAllLines(logFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": HMI order '" + orderCode + "' dropped Successfully" });

                    //Call to PickPack Api
                    bool isOrderPickPackSuccess = false;
                    JObject pickPackApiResult = grsiPickPack.PostData(pickPackApiInput, integrationPickPackApiUrl, true);
                    if (pickPackApiResult["result_code"].ToString() == "0000")
                    {
                        //File.AppendAllLines(logFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": " + pickPackApiResult["result_msg"].ToString() });
                        File.AppendAllLines(logFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": HMI order '" + orderCode + "' Processed Successfully" });
                        isOrderPickPackSuccess = true;
                    }
                    else if (pickPackApiResult["result_code"].ToString() == "9642" && pickPackApiResult["result_msg"].ToString() == "Order not found")
                    {
                        File.AppendAllLines(logFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": HMI order '" + orderCode + "' not available" });
                    }
                    else if (pickPackApiResult["result_code"].ToString() == "9444" && pickPackApiResult["result_msg"].ToString() == "Carton not found")
                    {
                        File.AppendAllLines(logFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": HMI order '" + orderCode + "' not cartonized during drop" });
                    }
                    else if (pickPackApiResult["result_msg"].ToString() == "Order not in distributed status")
                    {
                        File.AppendAllLines(logFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": HMI order '" + orderCode + "' not in distributed status" });
                    }
                    else
                    {
                        File.AppendAllLines(logFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": " + pickPackApiResult["result_msg"].ToString() });
                    }

                    //Prepare output for IRMSTOHMIUPLOADFOLDER
                    if(isOrderDropSuccess && isOrderPickPackSuccess)
                    {
                        //Get the today's IrmsToHmiUploadFilePath
                        string irmsToHmiUploadFilePath = string.Empty;
                        StringBuilder irmsToHmiUploadFileSB = new StringBuilder();
                        if (irmsToHmiUploadFolder.EndsWith("\\"))
                            irmsToHmiUploadFileSB.Append(irmsToHmiUploadFolder);
                        else
                            irmsToHmiUploadFileSB.Append(irmsToHmiUploadFolder + "\\");
                        irmsToHmiUploadFileSB.Append("ucoin" + orderCode);

                        irmsToHmiUploadFilePath = irmsToHmiUploadFileSB.ToString();

                        //Get OrderDetails

                        string irmsToHmiUploadOutput = string.Empty;
                        StringBuilder irmsToHmiUploadOutputSb = new StringBuilder();
                        IList<OrderDetail> orderDetails = getOrderLineItemQuantity(orderCode, orderSuffix);
                        int i = 1;
                        foreach(OrderDetail orderdetail in orderDetails)
                        {
                            string lineNumber = orderdetail.LineNumber.ToString();
                            string itemName = orderdetail.ItemCode;
                            string quantity = Decimal.ToInt32(orderdetail.Quantity).ToString();
                            string[] fields = new string[] { company, warehouse, orderCode, orderSuffix, lineNumber, itemName, quantity };
                            
                            if(i != orderDetails.Count)
                                irmsToHmiUploadOutputSb.AppendLine(prepareIrmsToHmiUploadOutput(irmsToHmiUploadFilefieldsLength, fields));
                            else
                                irmsToHmiUploadOutputSb.Append(prepareIrmsToHmiUploadOutput(irmsToHmiUploadFilefieldsLength, fields));
                        }
                        irmsToHmiUploadOutput = irmsToHmiUploadOutputSb.ToString();
                        File.AppendAllLines(irmsToHmiUploadFilePath, new[] { irmsToHmiUploadOutput });
                        File.AppendAllLines(logFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": HMI order '" + orderCode + "' output file created" });
                        File.AppendAllLines(logFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": " + irmsToHmiUploadOutput });
                        File.AppendAllLines(logFilePath, new[] { DateTime.Now.ToString(logDateTimeFormat) + " " + orderFilePathContent.Key + ": HMI order '" + orderCode + "' file uploaded" });
                    }
                    File.AppendAllText(logFilePath, "\n");
                }
                if (_logger.IsDebugEnabled) _logger.DebugFormat("END - EOD Job :  {0}", jobname);
            }
            catch (Exception ex)
            {
                _logger.ErrorFormat("Error at HMIOrderDropPickPack::PerformPickingPacking() " + ex.Message);
            }
        }

        private string prepareIrmsToHmiUploadOutput(Dictionary<string, int> irmsToHmiUploadFilefieldsLength, string[] fields)
        {
            StringBuilder dataStrBuilder = new StringBuilder();
            int startIndex = 0;
            foreach (KeyValuePair<string, int> fieldLength in irmsToHmiUploadFilefieldsLength)
            {
                dataStrBuilder.Append(getFullFieldLengthValue(fields[startIndex], fieldLength.Value));
                startIndex++;
            }
            return dataStrBuilder.ToString(); ;
        }

        private string getFullFieldLengthValue(string company, int fieldLength)
        {
            if (company.Length > fieldLength)
                return company.Substring(0, fieldLength);
            else if (company.Length < fieldLength)
                return company.PadRight(fieldLength, ' ');
            else //if (company.Length == fieldLength)
                return company;
        }

        private IList<OrderDetail> getOrderLineItemQuantity(string orderCode, string orderSuffix)
        {
            IList<OrderDetail> orderDetails = new List<OrderDetail>();
            try
            {
                using (UnitWrapper wrapper = new UnitWrapper())
                {
                    wrapper.Execute(() =>
                    {

                        DetachedCriteria criteria = DetachedCriteria.For<OrderDetail>("_root")
                                                    .CreateAlias("OrderHeader", "OrderHeader")
                                                    .CreateAlias("Item", "Item")
                                                    .SetProjection(Projections.ProjectionList()
                                                    .Add(Projections.Property("LineNumber"), "LineNumber")
                                                    .Add(Projections.Property("Item.ItemCode"), "ItemCode")
                                                    .Add(Projections.Property("Quantity"), "Quantity"))
                                                    .Add(Restrictions.Eq("OrderHeader.OrderCode", orderCode))
                                                    .SetResultTransformer(Transformers.AliasToBean<OrderDetail>());

                        if (!string.IsNullOrWhiteSpace(orderSuffix))
                            criteria = criteria.Add(Restrictions.Eq("OrderHeader.OrderSuffix", orderSuffix));

                        orderDetails = Repositories.Get<OrderDetail>().List(criteria);
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.ErrorFormat("Error at HMIOrderDropPickPack::getOrderLineItemQuantity() " + ex.Message);
            }
            return orderDetails;
        }

        private string prepareInputForDropApi(string company, string warehouse, string orderCode, string orderSuffix, string hmiPickLocation)
        {
            string pickPackApiInput = string.Empty;
            try
            {
                StringBuilder dataStrBuilder = new StringBuilder();
                dataStrBuilder.Append("{");
                dataStrBuilder.Append("\"company\"" + ":" + "\"" + company + "\",");
                dataStrBuilder.Append("\"warehouse\"" + ":" + "\"" + warehouse + "\",");
                dataStrBuilder.Append("\"orders\"" + ":[{" + "\"order_code\"" + ":" + "\"" + orderCode + "\"," + "\"order_suffix\"" + ":" + "\"" + orderSuffix + "\"}]" + ",");   
                dataStrBuilder.Append("\"create_carton\"" + ":" + "\"Y\"" + ",");
                dataStrBuilder.Append("\"inventory_discrepancy\"" + ":" + "\"A\"" + ",");
                dataStrBuilder.Append("\"allow_thirdparty_shipping\"" + ":" + "\"N\"" + ",");
                dataStrBuilder.Append("\"skip_bulkpull\"" + ":true" + ",");
                dataStrBuilder.Append("\"hmi_drop\"" + ":true" + ",");
                dataStrBuilder.Append("\"hmi_pick_location\"" + ":" + "\"" + hmiPickLocation + "\",");
                dataStrBuilder.Append("\"result_code\"" + ":" + "\"" + "\"," + "\"result_msg\"" + ":" + "\"" + "\"");
                dataStrBuilder.Append("}");
                pickPackApiInput = dataStrBuilder.ToString();
            }
            catch (Exception ex)
            {
                _logger.ErrorFormat("Error at HMIOrderDropPickPack::getDataString() " + ex.Message);
            }
            return pickPackApiInput;
        }

        #endregion
    }
}
