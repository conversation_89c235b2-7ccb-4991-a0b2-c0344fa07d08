﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Quartz;
using log4net;
using Upp.Irms.Core;
using NHibernate.Criterion;
using Upp.Irms.Domain;
using Upp.Shared.Utilities;
using NHibernate.Transform;
using Upp.Irms.Constants;
using NHibernate.SqlCommand;

namespace Upp.Irms.EOD.Host
{
    public class InventorySnapshotJob : IJob
    {
        #region Fields

        ILog _logger = LogManager.GetLogger(typeof(WayFairInventorySnapshotJob));

        const string JParam_Title = "TITLE";
        string title = "";
        const string JParam_Format = "FORMAT";
        string format = "";
        const string JParam_ShowHeader = "SHOWHEADER";
        bool showHeader = false;
        const string JParam_ShowFooter = "SHOWFOOTER";
        bool showFooter = false;
        const string JParam_Warehouses = "WAREHOUSES";
        string warehouses = "";
        const string JParam_nextJob = "NEXTJOB";
        string nextJob = "";
        const string JParam_FieldDelimiter = "FIELDDELIMITER";
        string fieldDelimiter = ",";
        const string JParam_FlatFileDestination = "FILEDESTINATION";
        string flatFileDestination = "";
        string jobname = "Inventory Snapshot Job";
        //Customers wise filteration
        const string JParam_Customers = "CUSTOMERS";
        string customers = string.Empty;
        //

        //ftp details
        const string JParam_FtpHost = "FTPHOST";
        string ftpHost = string.Empty;
        const string JParam_FtpUser = "FTPUSERNAME";
        string ftpUser = string.Empty;
        const string JParam_FtpPassword = "FTPPASSWORD";
        string ftpPassword = string.Empty;
        const string JParam_FtpFilePath = "FTPFILEPATH";
        string ftpFilePath = string.Empty;
        const string JParam_FtpUsePassive = "FTPUSEPASSIVE";
        bool ftpUsePassive = true;
        const string JParam_FileNameFormat = "FILENAMEFORMAT";
        string fileNameFormat = string.Empty;
        const string JParam_dataDoubleQuotation = "DATADOUBLEQUOTATION";
        bool dataDoubleQuotation = false;
        //

        #endregion

        #region Constructor
       
        public InventorySnapshotJob()
        {
        }

        #endregion

        #region Methods.Public
       
        public virtual void Execute(JobExecutionContext context)
        {
            if (JobParametersAreValid(context.MergedJobDataMap) == false)
            {
                JobExecutionException jex = new JobExecutionException("Missing job parameter");
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }

            ParseJobParameters(context.MergedJobDataMap);

            GenerateInventorySnapshotReport();
            NextJobScheduling(jobname);
        }


        #endregion

        #region Methods.Private

        private bool JobParametersAreValid(JobDataMap jobParams)
        {
            bool validity = true;

            if (!jobParams.Contains(JParam_Format))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_Format);
                validity = false;
            }
            if (!jobParams.Contains(JParam_ShowHeader))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_ShowHeader);
                validity = false;
            }
            if (!jobParams.Contains(JParam_ShowFooter))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_ShowFooter);
                validity = false;
            }
            if (!jobParams.Contains(JParam_Warehouses))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_Warehouses);
                validity = false;
            }
            if (!jobParams.Contains(JParam_nextJob))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_nextJob);
                validity = false;
            }
            return validity;
        }
        // helper method to parse job parameters
        private void ParseJobParameters(JobDataMap jobParams)
        {
            //Ensure that we catch all other types of exceptions
            // and throw only a JobExecutionException
            try
            {
                //map the parameters to jobParams
                this.title = jobParams.GetString(JParam_Title);
                this.format = jobParams.GetString(JParam_Format);
                this.showHeader = jobParams.GetBoolean(JParam_ShowHeader);
                this.showFooter = jobParams.GetBoolean(JParam_ShowFooter);
                this.warehouses = jobParams.GetString(JParam_Warehouses);
                this.nextJob = jobParams.GetString(JParam_nextJob);
                if (jobParams.Contains(JParam_FieldDelimiter))
                    this.fieldDelimiter = jobParams.GetString(JParam_FieldDelimiter);
                if (jobParams.Contains(JParam_FlatFileDestination))
                    this.flatFileDestination = jobParams.GetString(JParam_FlatFileDestination);               
                //Customers wise filteration
                if (jobParams.Contains(JParam_Customers))
                    this.customers = jobParams.GetString(JParam_Customers);
                //
                //ftp details
                if (jobParams.Contains(JParam_FtpHost))
                    this.ftpHost = jobParams.GetString(JParam_FtpHost);
                if (jobParams.Contains(JParam_FtpUser))
                    this.ftpUser = jobParams.GetString(JParam_FtpUser);
                if (jobParams.Contains(JParam_FtpPassword))
                    this.ftpPassword = jobParams.GetString(JParam_FtpPassword);
                if (jobParams.Contains(JParam_FtpFilePath))
                    this.ftpFilePath = jobParams.GetString(JParam_FtpFilePath);
                if (jobParams.Contains(JParam_FtpUsePassive))
                    this.ftpUsePassive = Utilities.GetBooleanValue(jobParams.GetString(JParam_FtpUsePassive));
                if (jobParams.Contains(JParam_FileNameFormat))
                    this.fileNameFormat = jobParams.GetString(JParam_FileNameFormat);
                if (jobParams.Contains(JParam_dataDoubleQuotation))
                    this.dataDoubleQuotation = Utilities.GetBooleanValue(jobParams.GetString(JParam_dataDoubleQuotation));
                //

            }
            catch (Exception ex)
            {
                JobExecutionException jex = new JobExecutionException("Invalid data type in job parameters", ex);
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }
        }
        private void NextJobScheduling(string jobname)
        {

            if (!String.IsNullOrWhiteSpace(nextJob))
            {
                if (_logger.IsDebugEnabled) _logger.DebugFormat("         »  NextJob:  {0}", nextJob);
                try
                {
                    string[] jobKeys = Service.Instance.Scheduler.GetJobNames("Manual");
                    foreach (string jobKey in jobKeys)
                    {
                        if (jobKey.Equals(nextJob))
                        {
                            SimpleTrigger trigger = new SimpleTrigger();
                            trigger.Name = nextJob + "Trigger";
                            trigger.JobName = nextJob;
                            trigger.JobGroup = "Manual";
                            trigger.Group = "Manual";
                            trigger.StartTimeUtc = DateTime.UtcNow.AddSeconds(30);
                            trigger.RepeatCount = 0;
                            trigger.RepeatInterval = TimeSpan.Zero;
                            Service.Instance.Scheduler.RescheduleJob(nextJob + "Trigger", "Manual", trigger);
                            if (_logger.IsDebugEnabled) _logger.DebugFormat("{1} - Next Job {0} is scheduled: ", nextJob, jobname);
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (_logger.IsErrorEnabled) _logger.ErrorFormat("{1} - Next Job error: {0}", ex.Message, jobname);
                }
            }
        }

        private void GenerateInventorySnapshotReport()
        {
            try
            {
                string filePath = System.Configuration.ConfigurationManager.AppSettings["FileDestination"].ToString();
                if (!string.IsNullOrWhiteSpace(this.flatFileDestination))
                    filePath = this.flatFileDestination;

                if (!string.IsNullOrWhiteSpace(warehouses))
                {
                    char[] fieldFormatSeparator = { ';' };
                    char[] formatSeparator = { ':' };

                    warehouses = warehouses.TrimEnd(fieldFormatSeparator);
                    string[] rowFormats = warehouses.Split(fieldFormatSeparator);

                    string[] customerCodes = null;
                    if (!string.IsNullOrWhiteSpace(customers))
                        customerCodes = customers.Split(new char[] { ',' });

                    int availablestatusId = 0;
                    int distributedOrderstatusId = 0;
                    int packedOrderstatusId = 0;
                    int openOrderstatusId = 0;
                    int receiptOrderstatusId = 0;
                    int openPOstatusId = 0;
                    int activePOstatusId = 0;
                    int ShippedOrderstatusId = 0;
                    int allocatedstatusId = 0;
                    int workOrderdistributedstatusId = 0;
                    int tiedstatusId = 0;
                    using (UnitWrapper wrapper = new UnitWrapper())
                    {
                        wrapper.Execute(() =>
                        {
                            //Retrieve Status Ids
                            string[] facCodes = { "I", "ORD", "PO", "WORKORDER" };
                            string[] statusCodes = { "A", "DIST", "PG", "O", "R", "S", "SA", "DP", "Z" };
                            DetachedCriteria criteriaStatusCodes = DetachedCriteria.For<StatusCode>()
                                                                .CreateAlias("FunctionalAreaCode", "FAC")
                                                                .SetProjection(Projections.ProjectionList()
                                                                .Add(Projections.Property("Id"), "Id")
                                                                .Add(Projections.Property("Code"), "Code")
                                                                .Add(Projections.Property("FAC.Code"), "Description"))
                                                                .Add(Restrictions.Eq("Active", "A"))
                                                                .Add(Restrictions.Eq("FAC.Active", "A"))
                                                                .Add(Restrictions.In("FAC.Code", facCodes))
                                                                .Add(Restrictions.In("Code", statusCodes))
                                                                .SetResultTransformer(Transformers.AliasToBean<StatusCode>());
                            IList<StatusCode> statusCodeList = Repositories.Get<StatusCode>().List(criteriaStatusCodes);

                            availablestatusId = statusCodeList.Where(c => c.Code == "A" && c.Description == "I").Select(n => n.Id.Value).FirstOrDefault<int>();
                            distributedOrderstatusId = statusCodeList.Where(c => c.Code == "DIST" && c.Description == "ORD").Select(n => n.Id.Value).FirstOrDefault<int>();
                            packedOrderstatusId = statusCodeList.Where(c => c.Code == "PG" && c.Description == "ORD").Select(n => n.Id.Value).FirstOrDefault<int>();
                            openOrderstatusId = statusCodeList.Where(c => c.Code == "O" && c.Description == "ORD").Select(n => n.Id.Value).FirstOrDefault<int>();
                            receiptOrderstatusId = statusCodeList.Where(c => c.Code == "R" && c.Description == "ORD").Select(n => n.Id.Value).FirstOrDefault<int>();
                            openPOstatusId = statusCodeList.Where(c => c.Code == "O" && c.Description == "PO").Select(n => n.Id.Value).FirstOrDefault<int>();
                            activePOstatusId = statusCodeList.Where(c => c.Code == "A" && c.Description == "PO").Select(n => n.Id.Value).FirstOrDefault<int>();
                            ShippedOrderstatusId = statusCodeList.Where(c => c.Code == "S" && c.Description == "ORD").Select(n => n.Id.Value).FirstOrDefault<int>();
                            allocatedstatusId = statusCodeList.Where(c => c.Code == "SA" && c.Description == "I").Select(n => n.Id.Value).FirstOrDefault<int>();
                            workOrderdistributedstatusId = statusCodeList.Where(c => c.Code == "DIST" && c.Description == "WORKORDER").Select(n => n.Id.Value).FirstOrDefault<int>();
                            tiedstatusId = statusCodeList.Where(c => c.Code == "Z" && c.Description == "I").Select(n => n.Id.Value).FirstOrDefault<int>();
                            //
                        });
                    }

                    foreach (string rowFormat in rowFormats)
                    {
                        string[] fieldFormatDescriptions = rowFormat.Split(formatSeparator);

                        string companyCode = fieldFormatDescriptions[0];
                        string[] companyLocationCodes = fieldFormatDescriptions[1].Split(new char[] { ',' });

                        if (_logger.IsDebugEnabled) _logger.DebugFormat("         »  Warehouses Count is {0} for the Company : {1}", companyLocationCodes.Length.ToString(), companyCode);

                        IList<CompanyLocationType> companyLocationTypes = new List<CompanyLocationType>();
                        using (UnitWrapper wrapper = new UnitWrapper())
                        {
                            //Retrieve companyLocations Ids
                            wrapper.Execute(() =>
                            {
                                DetachedCriteria criteriaWarehouse = DetachedCriteria.For<CompanyLocationType>()
                                                        .CreateAlias("CompanyLocation", "cl")
                                                        .CreateAlias("LocationType", "LocationType", JoinType.LeftOuterJoin)
                                                        .CreateAlias("cl.Company", "c")
                                                        .SetProjection(Projections.ProjectionList()
                                                                .Add(Projections.Property("Id"), "Id")
                                                                .Add(Projections.Property("Active"), "Active")
                                                                .Add(Projections.Property("CompanyLocationCode"), "CompanyLocationCode"))
                                                        .Add(Restrictions.In("CompanyLocationCode", companyLocationCodes))
                                                        .Add(Restrictions.Eq("LocationType.LocationTypeCode", "W"))
                                                        .Add(Restrictions.Eq("c.CompanyCode", companyCode))
                                                        .SetResultTransformer(Transformers.AliasToBean<CompanyLocationType>());
                                companyLocationTypes = Repositories.Get<CompanyLocationType>().List(criteriaWarehouse);
                            });
                        }

                        foreach (string companylocCode in companyLocationCodes)
                        {
                            using (UnitWrapper wrapper = new UnitWrapper())
                            {
                                wrapper.Execute(() =>
                                {
                                    CompanyLocationType companyLocationType = companyLocationTypes.Where(com => com.CompanyLocationCode == companylocCode).FirstOrDefault();
                                    if (companyLocationType == null || companyLocationType.Id == 0)
                                    {
                                        if (_logger.IsDebugEnabled) _logger.DebugFormat("Invalid Warehouse", companylocCode);
                                        return;
                                    }
                                    int companyLocationTypeId = companyLocationType.Id.Value;
                                    DetachedCriteria criteriaItem = DetachedCriteria.For<Item>()
                                                                                    .CreateAlias("UnitOfMeasure", "UnitOfMeasure", JoinType.LeftOuterJoin)
                                                                                     .SetProjection(Projections.ProjectionList()
                                                                                    .Add(Projections.Property("Id"), "Id")
                                                                                    .Add(Projections.Property("ItemCode"), "ItemCode")
                                                                                    .Add(Projections.Property("UnitOfMeasure.Id"), "UomId"))//standard UOM Id
                                                                                    .Add(Restrictions.Eq("Active", "A"))
                                                                                    .Add(Restrictions.Eq("CompanyLocationType.Id", companyLocationType.Id))
                                                                                    .SetResultTransformer(Transformers.AliasToBean<Item>());
                                    if (customerCodes != null && customerCodes.Count() > 0)
                                    {
                                        criteriaItem = criteriaItem.CreateAlias("Customer", "customer")
                                                                    .Add(Restrictions.In("customer.CustomerCode", customerCodes));
                                    }

                                    IList<Item> items = Repositories.Get<Item>().List(criteriaItem);

                                    if (items == null || items.Count == 0)
                                    {
                                        _logger.ErrorFormat("No items found");
                                        return;
                                    }
                                    //
                                    List<int?> itemIds = items.Select(c => c.Id).ToList<int?>();

                                    List<int> availablAndAllocatedestatus = new List<int>();
                                    availablAndAllocatedestatus.Add(availablestatusId);
                                    availablAndAllocatedestatus.Add(allocatedstatusId);
                                    availablAndAllocatedestatus.Add(tiedstatusId);

                                    DetachedCriteria criteriaInventory = DetachedCriteria.For<InventoryItem>()
                                                                            .Add(Restrictions.Eq("CompanyLocationType.Id", companyLocationTypeId))
                                                                            .SetProjection(Projections.ProjectionList()
                                                                                .Add(Projections.GroupProperty("Item.Id"), "ItemsId")
                                                                                .Add(Projections.GroupProperty("StatusCode.Id"), "Id")
                                                                                .Add(Projections.Sum("Quantity"), "Quantity"))
                                                                            .SetResultTransformer(Transformers.AliasToBean<InventoryItem>());

                                    if (customerCodes != null && customerCodes.Count() > 0)
                                    {
                                        criteriaInventory = criteriaInventory.CreateAlias("Item", "Item")
                                                                    .CreateAlias("Item.Customer", "customer")
                                                                    .Add(Restrictions.In("customer.CustomerCode", customerCodes));
                                    }

                                    IList<InventoryItem> inventoryItems = Repositories.Get<InventoryItem>().List(criteriaInventory);

                                    DetachedCriteria criteriaItemFulfillment = DetachedCriteria.For<ItemFulfillment>()
                                              .CreateAlias("OrderDetail", "OrderDetail", JoinType.LeftOuterJoin)
                                              .CreateAlias("Item", "Item")
                                              .SetProjection(Projections.ProjectionList()
                                                              .Add(Projections.GroupProperty("Item.Id"), "Id")
                                                              .Add(Projections.Sum("Quantity"), "Quantity")
                                                              .Add(Projections.Sum("OrderDetail.OriginalQuantity"), "PendingQuantity")//Original quantity
                                                              .Add(Projections.GroupProperty("StatusCode.Id"), "StatusCodeId")
                                                              .Add(Projections.GroupProperty("OrderDetail.StockStatusCode.Id"), "StockStatusCodeId"))
                                          .Add(Restrictions.Eq("Item.CompanyLocationType.Id", companyLocationTypeId))
                                              .SetResultTransformer(Transformers.AliasToBean<ItemFulfillment>());

                                    if (customerCodes != null && customerCodes.Count() > 0)
                                    {
                                        criteriaItemFulfillment = criteriaItemFulfillment.CreateAlias("Item.Customer", "customer")
                                                                    .Add(Restrictions.In("customer.CustomerCode", customerCodes));
                                    }

                                    IList<ItemFulfillment> itemfulfillments = Repositories.Get<ItemFulfillment>().List(criteriaItemFulfillment);

                                    //In Transit quantity
                                    int[] statusCodeIds = { openPOstatusId, activePOstatusId };
                                    DetachedCriteria criteriaPurchaseOrders = DetachedCriteria.For<PurchaseOrderDetail>()
                                           .CreateAlias("PurchaseOrderHeader", "PurchaseOrderHeader")
                                           .Add(Restrictions.Eq("PurchaseOrderHeader.CompanyLocationType.Id", companyLocationTypeId))
                                           .Add(Restrictions.In("StatusCode.Id", statusCodeIds))
                                           .SetProjection(Projections.ProjectionList()
                                               .Add(Projections.Property("Id"), "Id")
                                               .Add(Projections.Property("Item.Id"), "ItemId")
                                               .Add(Projections.Property("UnitOfMeasure.Id"), "UnitOfMeasureId")
                                               .Add(Projections.Property("VendorItem.Id"), "VendorItemId")
                                               .Add(Projections.Property("Quantity"), "Quantity"))
                                           .SetResultTransformer(Transformers.AliasToBean<PurchaseOrderDetail>());
                                    if (customerCodes != null && customerCodes.Count() > 0)
                                    {
                                        criteriaPurchaseOrders = criteriaPurchaseOrders.CreateAlias("Item", "Item")
                                                                .CreateAlias("Item.Customer", "customer")
                                                                .Add(Restrictions.In("customer.CustomerCode", customerCodes));
                                    }

                                    IList<PurchaseOrderDetail> inTransitPO = Repositories.Get<PurchaseOrderDetail>().List(criteriaPurchaseOrders);

                                    IList<ItemUomRelationship> itemUomRelationShips = new List<ItemUomRelationship>();
                                    IList<ReceiptDetail> inTransitReceipt = new List<ReceiptDetail>();
                                    if (inTransitPO.Count > 0)
                                    {
                                        List<int> uomIds = inTransitPO.Select(i => i.UnitOfMeasureId).Distinct().ToList<int>();

                                        //Item UOM Releation Ships
                                        DetachedCriteria criteriaItemUom = DetachedCriteria.For<ItemUomRelationship>()
                                           .CreateAlias("Item", "Item")
                                           .Add(Restrictions.Eq("Item.CompanyLocationType.Id", companyLocationTypeId))
                                           .Add(Restrictions.In("UnitOfMeasure.Id", uomIds))
                                           .SetProjection(Projections.ProjectionList()
                                               .Add(Projections.Property("Factor"), "Factor")
                                               .Add(Projections.Property("Item.Id"), "ItemId")
                                               .Add(Projections.Property("UnitOfMeasure.Id"), "UomId"))
                                           .SetResultTransformer(Transformers.AliasToBean<ItemUomRelationship>());
                                        if (customerCodes != null && customerCodes.Count() > 0)
                                        {
                                            criteriaItemUom = criteriaItemUom.CreateAlias("Item.Customer", "customer")
                                                                 .Add(Restrictions.In("customer.CustomerCode", customerCodes));
                                        }
                                        itemUomRelationShips = Repositories.Get<ItemUomRelationship>().List(criteriaItemUom);

                                        //In Transit received Quantity
                                        DetachedCriteria criteriaReceipt = DetachedCriteria.For<ReceiptDetail>()
                                                .CreateAlias("PurchaseOrderDetail", "PODetail")
                                                .CreateAlias("ReceiptHeader", "ReceiptHeader")
                                                .Add(Restrictions.Eq("StatusCode.Id", receiptOrderstatusId))
                                                .Add(Restrictions.Eq("ReceiptHeader.CompanyLocationType.Id", companyLocationTypeId))
                                                .SetProjection(Projections.ProjectionList()
                                                    .Add(Projections.GroupProperty("PODetail.Id"), "Id")
                                                    .Add(Projections.Sum("Quantity"), "Quantity"))
                                                .SetResultTransformer(Transformers.AliasToBean<ReceiptDetail>());
                                        if (customerCodes != null && customerCodes.Count() > 0)
                                        {
                                            criteriaReceipt = criteriaReceipt.CreateAlias("Item", "Item")
                                                                    .CreateAlias("Item.Customer", "customer")
                                                                    .Add(Restrictions.In("customer.CustomerCode", customerCodes));
                                        }
                                        inTransitReceipt = Repositories.Get<ReceiptDetail>().List(criteriaReceipt);
                                    }

                                    //Nextavilable date and quantity.
                                    DetachedCriteria criteriaPo = PrepareQueryForItemNextAvailableDate(companyLocationTypeId, customerCodes, openPOstatusId);
                                    IList<PurchaseOrderDetail> poDetails = Repositories.Get<PurchaseOrderDetail>().List(criteriaPo);

                                    List<PurchaseOrderDetail> poDetailList = new List<PurchaseOrderDetail>();
                                    decimal quantity = 0;
                                    decimal totalInTransitQuantity = 0;
                                    decimal vendorItemQuantity = 0;
                                    foreach (Item item in items)
                                    {
                                        quantity = 0;
                                        totalInTransitQuantity = 0;
                                        vendorItemQuantity = 0;
                                        poDetailList = poDetails.Where(c => c.Id == item.Id && c.Required.HasValue && c.Required.Value.Date > DateTime.Now.Date).OrderBy(c => c.Required).ToList();
                                        if (poDetailList.Count > 0)
                                        {
                                            item.ItemNextAvailableDate = String.Format("{0:MM/dd/yyyy}", poDetailList[0].Required);
                                            item.NextReceiptQuantity = poDetailList[0].Quantity;
                                        }

                                        decimal avilableQty = inventoryItems.Where(inv => inv.Id.HasValue && availablAndAllocatedestatus.Contains(inv.Id.Value))
                                                                            .Where(po => po.ItemsId == item.Id).Sum(po => po.Quantity);

                                        decimal unavilableQty = inventoryItems.Where(inv => inv.Id.HasValue && !availablAndAllocatedestatus.Contains(inv.Id.Value))
                                                                              .Where(c => c.ItemsId == item.Id).Sum(c => c.Quantity);

                                        decimal allocatedQty = inventoryItems.Where(inv => inv.Id == allocatedstatusId)
                                                                            .Where(po => po.ItemsId == item.Id).Sum(po => po.Quantity);

                                        decimal reservedItemfulfillmentQty = itemfulfillments.Where(ful => ful.StatusCodeId == distributedOrderstatusId || ful.StatusCodeId == workOrderdistributedstatusId)
                                                                                             .Where(po => po.Id == item.Id).Sum(po => po.Quantity);

                                        decimal unAvilabelReservedItemfulfillmentQty = itemfulfillments.Where(ful => ful.StatusCodeId != openOrderstatusId && ful.StatusCodeId != ShippedOrderstatusId && ful.StockStatusCodeId.HasValue && !availablAndAllocatedestatus.Contains(ful.StockStatusCodeId.Value))
                                                                                                       .Where(fulfilment => fulfilment.Id == item.Id).Sum(po => po.Quantity);

                                        item.AvailableQuantity = avilableQty - reservedItemfulfillmentQty - allocatedQty;
                                        item.ReservedQuantity = reservedItemfulfillmentQty + unAvilabelReservedItemfulfillmentQty + allocatedQty;
                                        item.QuantityOnHand = avilableQty + unavilableQty;

                                        item.BackOrderQuantity = itemfulfillments.Where(ful => ful.StatusCodeId == packedOrderstatusId)
                                                                                .Where(fulfilment => fulfilment.Id == item.Id).Sum(fulfilment => fulfilment.PendingQuantity - fulfilment.Quantity);
                                        //item.DistributionCenter = companyLocationType.CompanyLocationCode;

                                        //In-transit quantity
                                        poDetailList = inTransitPO.Where(po => po.ItemId == item.Id).ToList();
                                        foreach (PurchaseOrderDetail purchaseOrderDetail in poDetailList)
                                        {
                                            ReceiptDetail receiptDetail = inTransitReceipt.Where(c => c.Id == purchaseOrderDetail.Id).FirstOrDefault();
                                            if (receiptDetail != null)
                                                quantity = receiptDetail.Quantity;
                                            else
                                                quantity = 0;

                                            if (item.UomId != purchaseOrderDetail.UnitOfMeasureId)
                                            {
                                                ItemUomRelationship relatedUOM = itemUomRelationShips.Where(c => c.UomId == purchaseOrderDetail.UnitOfMeasureId && c.ItemId == item.Id).FirstOrDefault();
                                                if (relatedUOM != null && relatedUOM.Factor > 0M)
                                                {
                                                    quantity = (purchaseOrderDetail.Quantity - Convert.ToDecimal(quantity));
                                                    totalInTransitQuantity += quantity * relatedUOM.Factor;
                                                    if (purchaseOrderDetail.VendorItemId.HasValue) vendorItemQuantity += quantity * relatedUOM.Factor;
                                                }
                                                else
                                                {
                                                    totalInTransitQuantity += purchaseOrderDetail.Quantity - Convert.ToDecimal(quantity);
                                                    if (purchaseOrderDetail.VendorItemId.HasValue) vendorItemQuantity += purchaseOrderDetail.Quantity - quantity;
                                                }
                                            }
                                            else
                                            {
                                                totalInTransitQuantity += purchaseOrderDetail.Quantity - Convert.ToDecimal(quantity);
                                                if (purchaseOrderDetail.VendorItemId.HasValue) vendorItemQuantity += purchaseOrderDetail.Quantity - quantity;
                                            }
                                        }
                                        item.InTransitQuantity = totalInTransitQuantity;

                                        item.PurchaseOrderQuantity = vendorItemQuantity;
                                        item.OverseasQuantity = 0;
                                        item.ShowRoomQuantity = 0;
                                    }
                                    //Remove empty records(all quantities are zero's)
                                    items = items.Where(itm => itm.NextReceiptQuantity > 0 || itm.AvailableQuantity > 0 || itm.ReservedQuantity > 0 ||
                                                           itm.QuantityOnHand > 0 || itm.BackOrderQuantity > 0 || itm.InTransitQuantity > 0 ||
                                                           itm.PurchaseOrderQuantity > 0 || itm.OverseasQuantity > 0 || itm.ShowRoomQuantity > 0).ToList();

                                    //
                                    //string fileName = string.Format("{0}_{1}_{2}{3:yyyyMMdd-HHmmss}.txt", companyCode, companylocCode, "Inventory", DateTime.Now);
                                    string fileName = "inventory.csv";
                                    if (!string.IsNullOrEmpty(fileNameFormat))
                                        fileName = Utilities.GetFileName(fileNameFormat, companyCode, companylocCode);

                                    FlatFileGenerator fg = new FlatFileGenerator();
                                    fg.Prepare(filePath, this.format, this.showHeader, this.showFooter, this.title, this.fieldDelimiter, items.ToList(), fileName, dataDoubleQuotation);

                                    //ftpupload
                                    if (!string.IsNullOrWhiteSpace(ftpHost) && !string.IsNullOrWhiteSpace(ftpUser) && !string.IsNullOrWhiteSpace(ftpPassword))
                                    {
                                        string fileDetstinationPath = string.Empty;
                                        string remoteFile;
                                        if (!string.IsNullOrWhiteSpace(ftpFilePath))
                                        {
                                            fileDetstinationPath = ftpHost + ftpFilePath + fileName;
                                            remoteFile = ftpFilePath + fileName;
                                        }
                                        else
                                        {
                                            fileDetstinationPath = ftpHost + fileName;
                                            remoteFile = fileName;
                                        }

                                        if (_logger.IsDebugEnabled) _logger.DebugFormat("         »  FtpFileSourcePath:{0} and FtpFileDestinationPath:{1} ", filePath + fileName, fileDetstinationPath);

                                        //
                                        string split = "://";
                                        int index = ftpHost.LastIndexOf(split);
                                        ftpHost = ftpHost.Substring(index + split.Length, ftpHost.Length - (index + split.Length));
                                        ftpHost = "ftp" + split + ftpHost;

                                        filePath = ((filePath.Substring(filePath.Length - 1, 1) == @"\" || filePath.Substring(filePath.Length - 1, 1) == @"/") ? filePath : filePath + @"\");
                                        fg.FtpUpload(ftpHost, ftpUser, ftpPassword, remoteFile, filePath + fileName, ftpUsePassive);
                                        if (_logger.IsDebugEnabled) _logger.DebugFormat("         » Inventory File {0} Generated at location {1}", fileName, ftpHost + ftpFilePath + fileName);
                                    }
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
            }
        }

        public static DetachedCriteria PrepareQueryForItemNextAvailableDate(int companyLocationTypeId, string[] customerCodes, int openStatusId)
        {
            DetachedCriteria detachedPOStatus = DetachedCriteria.For<PurchaseOrderStatus>("rs")
                           .CreateAlias("rs.StatusCode", "StatusCodes")
                           .SetProjection(Projections.Property("rs.Id"))
                           .Add(Expression.EqProperty("poHeader.Id", "rs.PurchaseOrderHeader.Id"))
                           .AddOrder(new Order("rs.Occurred", false))
                           .AddOrder(new Order("StatusCodes.SortOrder", false))
                           .SetMaxResults(1);

            DetachedCriteria criteriaPo = DetachedCriteria.For<PurchaseOrderDetail>()
                .CreateAlias("PurchaseOrderHeader", "poHeader")
                .CreateAlias("poHeader.PurchaseOrderStatuses", "poStatus")
                .CreateAlias("poStatus.StatusCode", "statusCodes")
                .Add(Restrictions.Eq("poStatus.StatusCode.Id", openStatusId))
                .Add(Subqueries.PropertyEq("poStatus.Id", detachedPOStatus))
                .Add(Restrictions.Eq("poHeader.CompanyLocationType.Id", companyLocationTypeId))
                .SetProjection(Projections.ProjectionList()
                   .Add(Projections.GroupProperty("Item.Id"), "Id")
                   .Add(Projections.GroupProperty("poHeader.Required"), "Required")
                   .Add(Projections.Sum("Quantity"), "Quantity"))
               .SetResultTransformer(Transformers.AliasToBean<PurchaseOrderDetail>());

            if (customerCodes != null && customerCodes.Count() > 0)
            {
                criteriaPo = criteriaPo.CreateAlias("Item", "Item")
                                    .CreateAlias("Item.Customer", "customer")
                                    .Add(Restrictions.In("customer.CustomerCode", customerCodes));
            }

            return criteriaPo;
        }

        #endregion
    }
}
