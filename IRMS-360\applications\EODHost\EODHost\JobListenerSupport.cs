using Upp.Irms.Constants;
using Upp.Irms.Core;
using Upp.Irms.Domain;
using Upp.Shared.Application;
using Upp.Shared.Utilities;

using log4net;
using Quartz;
using NHibernate.Criterion;
using NHibernate.SqlCommand;
using NHibernate.Transform;

using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;

namespace Upp.Irms.EOD.Host
{
    /// <summary>
    /// A helpful abstract base class for implementors of <see cref="IJobListener" />.
    /// </summary>
    /// <remarks>
    /// <p>
    /// The methods in this class are empty so you only need to override the  
    /// subset for the <see cref="IJobListener" /> events you care about.
    /// </p>
    /// 
    /// <p>
    /// You are required to implement <see cref="IJobListener.Name" /> 
    /// to return the unique name of your <see cref="IJobListener" />.  
    /// </p>
    /// </remarks>
    /// <seealso cref="IJobListener" />
    public class JobListenerSupport : IJobListener
    {
        ILog logger = LogManager.GetLogger(typeof(JobListenerSupport));

        /// <summary>
        /// Initializes a new instance of the <see cref="JobListenerSupport"/> class.
        /// </summary>
        public JobListenerSupport()
        {

        }

        /// <summary>
        /// Get the name of the <see cref="IJobListener"/>.
        /// </summary>
        /// <value></value>
        public string Name
        {
            get { return "JobExecutionHistoryRecorder"; }
        }

        /// <summary>
        /// Called by the <see cref="IScheduler"/> when a <see cref="JobDetail"/>
        /// is about to be executed (an associated <see cref="Trigger"/>
        /// has occured).
        /// <p>
        /// This method will not be invoked if the execution of the Job was vetoed
        /// by a <see cref="ITriggerListener"/>.
        /// </p>
        /// </summary>
        /// <param name="context"></param>
        /// <seealso cref="JobExecutionVetoed(JobExecutionContext)"/>
        public void JobToBeExecuted(JobExecutionContext context)
        {
            if (context.JobDetail.Name == "JobInitializationPlugin_xml_quartz_jobs_xml")
                return;
            if (context.JobDetail.Name == "ManualTriggerJob")
                return;
            if (context.Trigger.Group == "MANUAL_TRIGGER")
                return;
            if (logger.IsInfoEnabled) logger.InfoFormat("Job Execution Started .. - {0}", context.JobDetail.Name);
            if (logger.IsDebugEnabled) logger.Debug("=====================================================================");
            RecordJobStart(context);
        }

        /// <summary>
        /// Called by the <see cref="IScheduler"/> when a <see cref="JobDetail"/>
        /// was about to be executed (an associated <see cref="Trigger"/>
        /// has occured), but a <see cref="ITriggerListener"/> vetoed it's
        /// execution.
        /// </summary>
        /// <param name="context"></param>
        /// <seealso cref="JobToBeExecuted(JobExecutionContext)"/>
        public void JobExecutionVetoed(JobExecutionContext context)
        {
            if (context.JobDetail.Name == "JobInitializationPlugin_xml_quartz_jobs_xml")
                return;
            if (context.JobDetail.Name == "ManualTriggerJob")
                return;

            if (logger.IsInfoEnabled) logger.InfoFormat("Job Execution Started .. - {0}", context.JobDetail.Name);
        }

        /// <summary>
        /// Called by the <see cref="IScheduler"/> after a <see cref="JobDetail"/>
        /// has been executed, and be for the associated <see cref="Trigger"/>'s
        /// <see cref="Trigger.Triggered"/> method has been called.
        /// </summary>
        /// <param name="context"></param>
        /// <param name="jobException"></param>
        public void JobWasExecuted(JobExecutionContext context, JobExecutionException jobException)
        {
            if (jobException == null)
            {
                if (context.JobDetail.Name == "JobInitializationPlugin_xml_quartz_jobs_xml")
                    return;

                if (context.JobDetail.Name == "ManualTriggerJob")
                    return;
                                
                if (logger.IsDebugEnabled) logger.Debug("=====================================================================");
                if (logger.IsInfoEnabled) logger.InfoFormat("Job Execution Completed .. - {0}", context.JobDetail.Name);
                RecordJobFinish(context);
            }
            else
            {
                if (logger.IsErrorEnabled) logger.ErrorFormat("Job Execution Failed.. - {0}", context.JobDetail.Name);
                RecordJobFailure(context, jobException);
            }
        }


        public void RecordJobStart(JobExecutionContext jec)
        {
            using (UnitWrapper wrapper = new UnitWrapper())
            {
                wrapper.Execute(() =>
                {
                    JobDetailExecution jobExecution = new JobDetailExecution();

                    jobExecution.DateCreated = DateTime.Now;
                    jobExecution.UserCreated = "eod_manager";
                    jobExecution.DateModified = null;
                    jobExecution.UserModified = "";
                    jobExecution.Version = 1;

                    jobExecution.JobName = jec.JobDetail.Name;
                    jobExecution.JobGroup = jec.JobDetail.Group;
                    jobExecution.JobTriggerName = jec.Trigger.Name;
                    jobExecution.JobTriggerGroup = jec.Trigger.Group;

                    jobExecution.Started = DateTime.Now;
                    jobExecution.Completed = null;
                    jobExecution.Status = "Started";
                    jobExecution.ExecutionResult = "";

                    Repositories.Get<JobDetailExecution>().Add(jobExecution);

                    jec.Trigger.Name = jobExecution.Id.ToString();// We could not add a new property for ID hence we have removed the jec.Trigger.Name property using in the queries hence reused this for job execution detail ID to update the respective execution detail.
                });
            }            
        }

        public void RecordJobFinish(JobExecutionContext jec)
        {
            using (UnitWrapper wrapper = new UnitWrapper())
            {
                wrapper.Execute(() =>
                {

                    DetachedCriteria criteriaJobExecution = DetachedCriteria.For<JobDetailExecution>()
                                                                                           .Add(Restrictions.Eq("Id", Convert.ToInt32(jec.Trigger.Name)));

                    JobDetailExecution jobExecution = Repositories.Get<JobDetailExecution>().Retrieve(criteriaJobExecution);
                                                                   
                    jobExecution.DateModified = DateTime.Now;
                    jobExecution.UserModified = "eod_manager";                               
                    jobExecution.Completed = DateTime.Now;
                    jobExecution.Status = "Completed";
                    jobExecution.ExecutionResult = "";

                    Repositories.Get<JobDetailExecution>().Update(jobExecution);
                });
            }      
        }        

        public void RecordJobFailure(JobExecutionContext jec, JobExecutionException jex)
        {
            using (UnitWrapper wrapper = new UnitWrapper())
            {
                wrapper.Execute(() =>
                {
                    DetachedCriteria criteriaJobExecution = DetachedCriteria.For<JobDetailExecution>()
                                                                                          .Add(Restrictions.Eq("Id", Convert.ToInt32(jec.Trigger.Name)));

                    JobDetailExecution jobExecution = Repositories.Get<JobDetailExecution>().Retrieve(criteriaJobExecution);
                   
                    jobExecution.DateModified = DateTime.Now;
                    jobExecution.UserModified = "eod_manager";
                                                                            
                    jobExecution.Completed = null;
                    jobExecution.Status = "Failed";
                    jobExecution.ExecutionResult = jex.Message;

                    Repositories.Get<JobDetailExecution>().Update(jobExecution);
                });
            } 

        }
    }
}