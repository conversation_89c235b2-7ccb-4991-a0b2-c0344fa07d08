using log4net;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NHibernate.Criterion;
using NHibernate.Transform;
using Quartz;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Runtime.Serialization.Json;
using System.Text;
using System.Threading.Tasks;
using Upp.Irms.Core;
using Upp.Irms.Domain;
using Upp.Irms.Services.Integration.DataContracts;

namespace Upp.Irms.EOD.Host
{
    class MakeToOrderJob : IJob
    {
        #region Fields

        ILog _logger = LogManager.GetLogger(typeof(MakeToOrderJob));

        const string JParam_MakeToOrderHostUserID = "MAKETOORDERHOSTUSERID";
        const string JParam_MakeToOrderDownloadFileFormat = "MAKETOORDERDOWNLOADFILEFORMAT";
        const string JParam_MakeToOrderToIRMSDownloadFolder = "MAKETOORDERTOIRMSDOWNLOADFOLDER";
        const string JParam_MakeToOrderLogFileFolder = "MAKETOORDERLOGFILEFOLDER";
        const string JParam_nextJob = "NEXTJOB";

        string makeToOrderHostUserID = "";
        string makeToOrderDownloadFileFormat = "";
        string makeToOrderToIRMSDownloadFolder = "";
        string makeToOrderLogFileFolder = "";
        string nextJob = "";

        string jobname = "Make To Order";

        #endregion

        #region Constructor

        public MakeToOrderJob()
        {
        }

        #endregion


        #region Methods.Public

        public virtual void Execute(JobExecutionContext context)
        {
            if (JobParametersAreValid(context.MergedJobDataMap) == false)
            {
                JobExecutionException jex = new JobExecutionException("Missing job parameter");
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }

            ParseJobParameters(context.MergedJobDataMap);

            PerformMakeToOrder();

            NextJobScheduling(jobname);
        }

        #endregion

        #region Methods.Private

        private bool JobParametersAreValid(JobDataMap jobParams)
        {
            bool validity = true;

            if (!jobParams.Contains(JParam_MakeToOrderHostUserID))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_MakeToOrderHostUserID);
                validity = false;
            }
            if (!jobParams.Contains(JParam_MakeToOrderDownloadFileFormat))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_MakeToOrderDownloadFileFormat);
                validity = false;
            }
            if (!jobParams.Contains(JParam_MakeToOrderToIRMSDownloadFolder))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_MakeToOrderToIRMSDownloadFolder);
                validity = false;
            }
            if (!jobParams.Contains(JParam_MakeToOrderLogFileFolder))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_MakeToOrderLogFileFolder);
                validity = false;
            }
            if (!jobParams.Contains(JParam_nextJob))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_nextJob);
                validity = false;
            }

            return validity;
        }

        private void ParseJobParameters(JobDataMap jobParams)
        {
            try
            {
                //map the parameters to jobParams              
                this.makeToOrderHostUserID = jobParams.GetString(JParam_MakeToOrderHostUserID);
                this.makeToOrderDownloadFileFormat = jobParams.GetString(JParam_MakeToOrderDownloadFileFormat);
                this.makeToOrderToIRMSDownloadFolder = jobParams.GetString(JParam_MakeToOrderToIRMSDownloadFolder);
                this.makeToOrderLogFileFolder = jobParams.GetString(JParam_MakeToOrderLogFileFolder);
                this.nextJob = jobParams.GetString(JParam_nextJob);
            }
            catch (Exception ex)
            {
                JobExecutionException jex = new JobExecutionException("Invalid data type in job parameters", ex);
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }
        }

        private void NextJobScheduling(string jobname)
        {
            if (!String.IsNullOrWhiteSpace(nextJob))
            {
                if (_logger.IsDebugEnabled) _logger.DebugFormat("NextJob:  {0}", nextJob);
                try
                {
                    string[] jobKeys = Service.Instance.Scheduler.GetJobNames("Manual");
                    foreach (string jobKey in jobKeys)
                    {
                        if (jobKey.Equals(nextJob))
                        {
                            SimpleTrigger trigger = new SimpleTrigger();
                            trigger.Name = nextJob + "Trigger";
                            trigger.JobName = nextJob;
                            trigger.JobGroup = "Manual";
                            trigger.Group = "Manual";
                            trigger.StartTimeUtc = DateTime.UtcNow.AddSeconds(30);
                            trigger.RepeatCount = 0;
                            trigger.RepeatInterval = TimeSpan.Zero;
                            Service.Instance.Scheduler.RescheduleJob(nextJob + "Trigger", "Manual", trigger);
                            if (_logger.IsDebugEnabled) _logger.DebugFormat("{1} - Next Job {0} is scheduled: ", nextJob, jobname);
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (_logger.IsErrorEnabled) _logger.ErrorFormat("{1} - Next Job error: {0}", ex.Message, jobname);
                }
            }
        }



        private void PerformMakeToOrder()
        {
            try
            {
                if (_logger.IsDebugEnabled) _logger.DebugFormat("START - EOD Job :  {0}", jobname);
               
                //Check the makeToOrderToIRMSDownloadFolder exist or not
                if (!Directory.Exists(makeToOrderToIRMSDownloadFolder))
                {
                    _logger.ErrorFormat("Directory not Found/Access Denied MAKETOORDERToIRMSDownloadFolder folder :" + makeToOrderToIRMSDownloadFolder);
                    return;
                }

                //Check the makeToOrderLogFileFolder exist or not
                if (!Directory.Exists(makeToOrderLogFileFolder))
                {
                    _logger.ErrorFormat("Directory not Found/Access Denied MAKETOORDERLOGFILEFOLDER folder :" + makeToOrderLogFileFolder);
                    return;
                }

                string mtoToIRMSDownloadFolderBk = string.Empty;
                string mtoToIRMSDownloadFolderErrorFiles = string.Empty;
                string mtoToIRMSDownloadFolderEchoFiles = string.Empty;
                string mtoToIRMSDownloadFolderInProcessFiles = string.Empty;

                StringBuilder backupPathSb = new StringBuilder();
                if (makeToOrderToIRMSDownloadFolder.EndsWith("\\"))
                    backupPathSb.Append(makeToOrderToIRMSDownloadFolder);
                else
                    backupPathSb.Append(makeToOrderToIRMSDownloadFolder + "\\");
                backupPathSb.Append("bk\\");
                mtoToIRMSDownloadFolderBk = backupPathSb.ToString();

                if (!Directory.Exists(mtoToIRMSDownloadFolderBk))
                {
                    Directory.CreateDirectory(mtoToIRMSDownloadFolderBk);
                }


                StringBuilder errorPathSb = new StringBuilder();
                if (makeToOrderToIRMSDownloadFolder.EndsWith("\\"))
                    errorPathSb.Append(makeToOrderToIRMSDownloadFolder);
                else
                    errorPathSb.Append(makeToOrderToIRMSDownloadFolder + "\\");
                errorPathSb.Append("FailedOrders\\");
                mtoToIRMSDownloadFolderErrorFiles = errorPathSb.ToString();

                if (!Directory.Exists(mtoToIRMSDownloadFolderErrorFiles))
                {
                    Directory.CreateDirectory(mtoToIRMSDownloadFolderErrorFiles);
                }

                StringBuilder echoPathSb = new StringBuilder();
                if (makeToOrderToIRMSDownloadFolder.EndsWith("\\"))
                    echoPathSb.Append(makeToOrderToIRMSDownloadFolder);
                else
                    echoPathSb.Append(makeToOrderToIRMSDownloadFolder + "\\");
                echoPathSb.Append("EchoFiles\\");
                mtoToIRMSDownloadFolderEchoFiles = echoPathSb.ToString();

                if (!Directory.Exists(mtoToIRMSDownloadFolderEchoFiles))
                {
                    Directory.CreateDirectory(mtoToIRMSDownloadFolderEchoFiles);
                }

                StringBuilder inProcessPathSb = new StringBuilder();
                if (makeToOrderToIRMSDownloadFolder.EndsWith("\\"))
                    inProcessPathSb.Append(makeToOrderToIRMSDownloadFolder);
                else
                    inProcessPathSb.Append(makeToOrderToIRMSDownloadFolder + "\\");
                inProcessPathSb.Append("InProcess\\");
                mtoToIRMSDownloadFolderInProcessFiles = inProcessPathSb.ToString();

                if (!Directory.Exists(mtoToIRMSDownloadFolderInProcessFiles))
                {
                    Directory.CreateDirectory(mtoToIRMSDownloadFolderInProcessFiles);
                }



                //Get the today's LogFilePath
                string fullLogFilePath = getLogFolderFilePath(makeToOrderLogFileFolder);
                makeToOrderDownloadFileFormat = makeToOrderDownloadFileFormat.Replace(".", "*.");
                string[] makeToOrderDownloadFileFormats = makeToOrderDownloadFileFormat.Split(',');
                Dictionary<string, string> allOrdersFilePathsContents = readAllOrdersAndMoveToProcessFolder(makeToOrderToIRMSDownloadFolder, fullLogFilePath, mtoToIRMSDownloadFolderInProcessFiles, makeToOrderDownloadFileFormats, mtoToIRMSDownloadFolderErrorFiles, mtoToIRMSDownloadFolderEchoFiles);


                //Get IntegrationApiUrl
                string integrationApiUrl = string.Empty;
                //string integrationUrl = string.Empty;
                String serverName = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["ExactaServerName"]);
                integrationApiUrl = "http://" + serverName + "/exactaAPI/import/v2/container";
               // integrationApiUrl = String.Format("{0}{1}", integrationUrl, "/import/container");

                //loop each files, read its Contents, prepare input json format, call to Integration (grsiPickingPacking endpoint)
                foreach (KeyValuePair<string, string> orderFilePathContent in allOrdersFilePathsContents)
                {
                    //Log the FileName to MAKETOORDERLOGFOLDER
                    File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString("dd/MM/yy HH:mm") + " " + orderFilePathContent.Key + ": MakeToOrder File Found" });

                    if (string.IsNullOrEmpty(orderFilePathContent.Value))
                    {
                        File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString("dd/MM/yy HH:mm") + " " + orderFilePathContent.Key + ": MakeToOrder File is Empty" });
                        continue;
                    }

                    string dataString = getDataString(orderFilePathContent.Value, orderFilePathContent.Key, fullLogFilePath, mtoToIRMSDownloadFolderInProcessFiles, mtoToIRMSDownloadFolderErrorFiles, mtoToIRMSDownloadFolderEchoFiles);

                    if(!string.IsNullOrEmpty(dataString)) ExactaAPICall(dataString, integrationApiUrl, orderFilePathContent, mtoToIRMSDownloadFolderInProcessFiles, mtoToIRMSDownloadFolderErrorFiles, mtoToIRMSDownloadFolderEchoFiles, mtoToIRMSDownloadFolderBk);
                }
                if (_logger.IsDebugEnabled) _logger.DebugFormat("END - EOD Job :  {0}", jobname);
            }
            catch (Exception ex)
            {
                _logger.ErrorFormat("Error at MakeToOrder::MakeToOrder() " + ex.Message);
            }
        }


        private string getDataString(string orderFilePathContent, string fileName,string fullLogFilePath, string mtoToIRMSDownloadFolderInProcessFiles, string mtoToIRMSDownloadFolderErrorFiles, string mtoToIRMSDownloadFolderEchoFiles)
        {
            string dataString = string.Empty;
            try
            {
                List<string> lines = orderFilePathContent.Split('\n').ToList();
                lines = lines.Where(c => !string.IsNullOrEmpty(c.Trim())).Select(c => c).ToList();
                if (lines != null && lines.Count == 2)
                {
                    List<string> headers = lines[0].Split(',').ToList();
                    List<string> strRequestBody = lines[1].Split(',').ToList();

                    if (strRequestBody == null || strRequestBody.Count == 0 || strRequestBody.Count < 8)
                    {
                        _logger.ErrorFormat("Error at MakeToOrder::getDataString() " + "File has no Contents");
                        return "";
                    }

                    MakeToOrderDataContract dataContract = new MakeToOrderDataContract();
                    dataContract.records = new List<MakeToOrder>();

                    MakeToOrder record = new MakeToOrder();
                    record.cntnr_name = strRequestBody[0];
                    if (strRequestBody[1].Equals("Y", StringComparison.InvariantCultureIgnoreCase) || strRequestBody[1].Equals("true", StringComparison.InvariantCultureIgnoreCase))
                        record.qc_required = "true";
                    else
                        record.qc_required = "false";
                    if (strRequestBody[2].Equals("Y", StringComparison.InvariantCultureIgnoreCase) || strRequestBody[2].Equals("true", StringComparison.InvariantCultureIgnoreCase))
                        record.pack_qc_required = "true";
                    else
                        record.pack_qc_required = "false";
                    if (strRequestBody[3].Equals("Y", StringComparison.InvariantCultureIgnoreCase) || strRequestBody[3].Equals("true", StringComparison.InvariantCultureIgnoreCase))
                        record.ship_qc_required ="true";
                    else
                        record.ship_qc_required = "false";
                    record.qc_reason = strRequestBody[4];
                    record.ship_sort_code = strRequestBody[5];
                    record.routing_zones = new List<routing_zones>();

                    List<routing_zones> routing_zones = new List<routing_zones>();

                    List<string> routingZones = new List<string>();
                    routingZones = strRequestBody.GetRange(6, strRequestBody.Count - 6);

                    if (routingZones.Count % 2 == 0)
                    {
                        int size = 0;
                        while (size < routingZones.Count)
                        {
                            routing_zones routind_zone = new routing_zones();
                            if (!string.IsNullOrEmpty(routingZones[size]))
                                routind_zone.zone_number = routingZones[size];
                            else
                                routind_zone.zone_number = "11";
                            size++;
                            if (routingZones[size] != null && (routingZones[size] == "false" || routingZones[size] == "N")) routind_zone.is_complete = "false";
                            else routind_zone.is_complete = "true";
                            size++;
                            routing_zones.Add(routind_zone);
                            //if (size < routingZones.Count) manipulateData.Append(",");
                        }
                    }
                    if (routing_zones != null && routing_zones.Count > 0)
                        record.routing_zones.AddRange(routing_zones);
                    dataContract.records.Add(record);

                    dataString = JsonConvert.SerializeObject(dataContract);
                }
                else
                {
                    File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString("dd/MM/yy HH:mm") + " " + fileName + ": File Contents are not in Correct Format." });
                    MoveFileToBackOrErrorFolder("error", fileName, mtoToIRMSDownloadFolderInProcessFiles, mtoToIRMSDownloadFolderErrorFiles);
                    MoveFileToBackOrErrorFolder("echo", fileName, mtoToIRMSDownloadFolderInProcessFiles, mtoToIRMSDownloadFolderEchoFiles);

                }
            }            
            catch (Exception ex)
            {
                _logger.ErrorFormat("Error at MakeToOrder::getDataString() " + ex.Message);
            }
        
            return dataString;
        }


        private string getLogFolderFilePath(string makeToOrderLogFileFolder)
        {
            string fullLogFilePath = string.Empty;
            try
            {
                string logFileName = "makeToOrderfrom" + DateTime.Now.ToString("yyyyMMdd") + ".log";

                StringBuilder manipulatePath = new StringBuilder();
                if (makeToOrderLogFileFolder.EndsWith("\\"))
                    manipulatePath.Append(makeToOrderLogFileFolder);
                else
                    manipulatePath.Append(makeToOrderLogFileFolder + "\\");
                manipulatePath.Append(logFileName);

                fullLogFilePath = manipulatePath.ToString();
            }
            catch (Exception ex)
            {
                _logger.ErrorFormat("Error at MakeToOrder::getMakeToOrderLogFolderFilePath() " + ex.Message);
            }
            return fullLogFilePath;
        }


        public Dictionary<string, string> readAllOrdersAndMoveToProcessFolder(string makeToOrderToIRMSDownloadFolder, string fullLogFilePath, string makeToOrderToIRMSDownloadFolderInProgress, string[] makeToOrderDownloadFileFormats, string makeToOrderToIRMSDownloadErrorFolder, string makeToOrderToIRMSDownloadEchoFolder)
        {
            Dictionary<string, string> filesPathContent = new Dictionary<string, string>();
            try
            {
                DirectoryInfo dinfo = new DirectoryInfo(makeToOrderToIRMSDownloadFolder);
                FileInfo[] Files = null;

                foreach (string fileformat in makeToOrderDownloadFileFormats)
                {
                    if (makeToOrderDownloadFileFormats.Count() > 0 && makeToOrderDownloadFileFormats[0] != "" && makeToOrderDownloadFileFormats[0] != "*")
                        Files = dinfo.GetFiles(fileformat);
                    else
                        Files = dinfo.GetFiles();
                    //
                    foreach (FileInfo file in Files)
                    {
                        if (!file.Name.Contains("MTO") && !file.Name.Contains("TBD"))
                        {
                            File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString("dd/MM/yy HH:mm") + " " + file.FullName + ": File Name is not in Correct Format." });
                            file.CopyTo(Path.Combine(makeToOrderToIRMSDownloadErrorFolder, file.Name), true);
                            file.CopyTo(Path.Combine(makeToOrderToIRMSDownloadEchoFolder, file.Name), true);
                            File.AppendAllText(Path.Combine(makeToOrderToIRMSDownloadEchoFolder, file.Name), " File Name is not in Correct Format.");
                        }
                        else
                        {
                            filesPathContent.Add(file.Name, File.ReadAllText(file.FullName));
                            file.CopyTo(Path.Combine(makeToOrderToIRMSDownloadFolderInProgress, file.Name), true);
                        }
                        file.Delete();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.ErrorFormat("Error at MakeToOrder::getAllOrdersFilePath() " + ex.Message);
            }
            return filesPathContent;
        }

        public void MoveFileToBackOrErrorFolder(string type, string FileName, string fromFolder, string toFolder, string resultCode = null, string resultMsg = null)
        {
            DirectoryInfo dinfo = new DirectoryInfo(fromFolder);
            FileInfo[] Files = dinfo.GetFiles(FileName);
            foreach (FileInfo file in Files)
            {
                if (type.Equals("echo"))
                {
                    file.CopyTo(Path.Combine(toFolder, file.Name), true);
                    File.AppendAllText(Path.Combine(toFolder, file.Name), resultCode + " " + resultMsg);
                    file.Delete();
                }
                else if (type.Equals("bk"))
                    file.CopyTo(Path.Combine(toFolder, file.Name), true);
                else
                    file.CopyTo(Path.Combine(toFolder, file.Name), true);
            }
        }
        #endregion
        #region Methods.Private.APICall
        private JObject PostData(string dataString, string url)
        {
            JObject outputData = new JObject();
            try
            {
                WebClient webClientProxy = new WebClient();
                webClientProxy.Headers["Content-type"] = "application/json";
                if (_logger.IsDebugEnabled) _logger.Debug("URL " + url);
                if (_logger.IsDebugEnabled) _logger.Debug("Method Type POST");
                if (_logger.IsDebugEnabled) _logger.Debug("Request " + dataString);
                var result = webClientProxy.UploadString(url, "POST", dataString);
                if (_logger.IsDebugEnabled) _logger.Debug("Response " + result);
            }
            catch (Exception ex)
            {
                _logger.ErrorFormat("Error at Posting Data to Exacta API::PostData() " + ex.Message);
                outputData.Add("result_msg", ex.Message);
                outputData.Add("Code", "404");
            }
            return outputData;
        }


        private void ExactaAPICall(string exactaApiInput, string exactaApiURL, KeyValuePair<string, string> orderFilePathContent, string mtoToIRMSDownloadFolderInProcessFiles, string mtoToIRMSDownloadFolderErrorFiles, string mtoToIRMSDownloadFolderEchoFiles, string mtoToIRMSDownloadFolderBk)
        {
            string errorMessage = string.Empty;
            string fullLogFilePath = getLogFolderFilePath(makeToOrderLogFileFolder);
            if (!string.IsNullOrEmpty(exactaApiInput))
            {
                JObject result = PostData(exactaApiInput, exactaApiURL);
                // JObject result = MockResponseFromExacta();
                JToken errorContent = "";
                JToken error = "";
                if (result != null && result.ContainsKey("Code"))
                {
                    if (result["Code"].ToString() == "200")
                    {
                        if (_logger.IsDebugEnabled) _logger.DebugFormat("Exacta API Call: Success");
                        File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString("dd/MM/yy HH:mm") + " " + orderFilePathContent.Key + ": " + result["result_msg"].ToString() });
                        File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString("dd/MM/yy HH:mm") + " " + orderFilePathContent.Key + ": Make To Order Processed Successfully" });
                        MoveFileToBackOrErrorFolder("bk", orderFilePathContent.Key, mtoToIRMSDownloadFolderInProcessFiles, mtoToIRMSDownloadFolderBk);

                    }
                    else if (result["Code"].ToString() != "404" && result.ContainsKey("Content"))
                    {
                        if (_logger.IsDebugEnabled) _logger.DebugFormat("Result Code:" + result["Code"].ToString());
                        errorContent = result["Content"];
                        error = errorContent["error"];
                        File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString("dd/MM/yy HH:mm") + " " + orderFilePathContent.Key + ": " + result["result_msg"].ToString() });
                        MoveFileToBackOrErrorFolder("error", orderFilePathContent.Key, mtoToIRMSDownloadFolderInProcessFiles, mtoToIRMSDownloadFolderErrorFiles);
                    }
                    else if (result.ContainsKey("error"))
                    {
                        error = result["error"];
                        File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString("dd/MM/yy HH:mm") + " " + orderFilePathContent.Key + ": " + result["result_msg"].ToString() });
                        MoveFileToBackOrErrorFolder("error", orderFilePathContent.Key, mtoToIRMSDownloadFolderInProcessFiles, mtoToIRMSDownloadFolderErrorFiles);
                    }

                    if (result["Code"].ToString() == "400")
                    {
                        if (_logger.IsDebugEnabled) _logger.DebugFormat("Result Code:" + result["Code"].ToString());
                        if (error != null)
                        {
                            errorMessage = error.ToString();
                        }
                        else
                            errorMessage = "BAD REQUEST";
                        if (_logger.IsDebugEnabled) _logger.DebugFormat("Result Message:" + errorMessage);
                        File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString("dd/MM/yy HH:mm") + " " + orderFilePathContent.Key + ": " + result["result_msg"].ToString() });
                        MoveFileToBackOrErrorFolder("error", orderFilePathContent.Key, mtoToIRMSDownloadFolderInProcessFiles, mtoToIRMSDownloadFolderErrorFiles);
                    }
                    else if (result["Code"].ToString() == "401")
                    {
                        if (_logger.IsDebugEnabled) _logger.DebugFormat("Result Code:" + result["Code"].ToString());
                        if (error != null)
                        {
                            errorMessage = error.ToString();
                        }
                        else
                            errorMessage = "UNAUTHORIZED";
                        if (_logger.IsDebugEnabled) _logger.DebugFormat("Result Message:" + errorMessage);
                        File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString("dd/MM/yy HH:mm") + " " + orderFilePathContent.Key + ": " + result["result_msg"].ToString() });
                        MoveFileToBackOrErrorFolder("error", orderFilePathContent.Key, mtoToIRMSDownloadFolderInProcessFiles, mtoToIRMSDownloadFolderErrorFiles);
                    }
                    else if (result["Code"].ToString() == "500")
                    {
                        if (_logger.IsDebugEnabled) _logger.DebugFormat("Result Code:" + result["Code"].ToString());
                        if (error != null)
                        {
                            errorMessage = error.ToString();
                        }
                        else
                            errorMessage = "INTERNAL SERVER ERROR";
                        if (_logger.IsDebugEnabled) _logger.DebugFormat("Result Message:" + errorMessage);
                        File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString("dd/MM/yy HH:mm") + " " + orderFilePathContent.Key + ": " + result["result_msg"].ToString() });
                        MoveFileToBackOrErrorFolder("error", orderFilePathContent.Key, mtoToIRMSDownloadFolderInProcessFiles, mtoToIRMSDownloadFolderErrorFiles);
                    }
                    else if (result["Code"].ToString() == "404")
                    {
                        if (_logger.IsDebugEnabled) _logger.DebugFormat("Result Code:" + result["Code"].ToString());
                        if (result["Description"] != null)
                        {
                            errorMessage = result["Description"].ToString();
                        }
                        else
                            errorMessage = "Endpoint not found";
                        if (_logger.IsDebugEnabled) _logger.DebugFormat("Result Message:" + errorMessage);
                        File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString("dd/MM/yy HH:mm") + " " + orderFilePathContent.Key + ": " + result["result_msg"].ToString() });
                        MoveFileToBackOrErrorFolder("error", orderFilePathContent.Key, mtoToIRMSDownloadFolderInProcessFiles, mtoToIRMSDownloadFolderErrorFiles);
                    }
                    File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString("dd/MM/yy HH:mm") + " " + orderFilePathContent.Key + ": URL: " + exactaApiURL });
                    File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString("dd/MM/yy HH:mm") + " " + orderFilePathContent.Key + ": PostData: " + exactaApiInput });
                    File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString("dd/MM/yy HH:mm") + " " + orderFilePathContent.Key + ": MakeToOrder File Found" });
                    File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString("dd/MM/yy HH:mm") + " " + orderFilePathContent.Key + ": " + result["result_msg"].ToString() });
                    MoveFileToBackOrErrorFolder("echo", orderFilePathContent.Key, mtoToIRMSDownloadFolderInProcessFiles, mtoToIRMSDownloadFolderEchoFiles, result["Code"].ToString(), result["result_msg"].ToString());
                }
                else
                {
                    if (_logger.IsDebugEnabled) _logger.DebugFormat("exactaApiOutput is null");
                    MoveFileToBackOrErrorFolder("bk", orderFilePathContent.Key, mtoToIRMSDownloadFolderInProcessFiles, mtoToIRMSDownloadFolderBk);
                    File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString("dd/MM/yy HH:mm") + " " + orderFilePathContent.Key + ": URL: " + exactaApiURL });
                    File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString("dd/MM/yy HH:mm") + " " + orderFilePathContent.Key + ": PostData: " + exactaApiInput });
                    File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString("dd/MM/yy HH:mm") + " " + orderFilePathContent.Key + ": MakeToOrder File Found" });
                    File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString("dd/MM/yy HH:mm") + " " + orderFilePathContent.Key + ": " + "0000 Good Record" });
                    MoveFileToBackOrErrorFolder("echo", orderFilePathContent.Key, mtoToIRMSDownloadFolderInProcessFiles, mtoToIRMSDownloadFolderEchoFiles, "", "0000 Good Record");
                }
                
            }
            else
            {
                if (_logger.IsDebugEnabled) _logger.DebugFormat("exactaApiInput is empty");
                File.AppendAllLines(fullLogFilePath, new[] { DateTime.Now.ToString("dd/MM/yy HH:mm") + " " + orderFilePathContent.Key + ": exactaApiInput is empty" });
                MoveFileToBackOrErrorFolder("error", orderFilePathContent.Key, mtoToIRMSDownloadFolderInProcessFiles, mtoToIRMSDownloadFolderErrorFiles);
            }           

        }
        #endregion    

    }
}

