﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Quartz;
using log4net;
using Upp.Shared.Utilities;
using Upp.Irms.Domain;
using Upp.Irms.Core;
using NHibernate.Criterion;
using NHibernate.Transform;
using NHibernate.SqlCommand;

namespace Upp.Irms.EOD.Host
{
    class OrderHoldJob : IJob
    {
        #region Fields

        ILog _logger = LogManager.GetLogger(typeof(OrderHoldJob));

        const string JParam_Warehouses = "WAREHOUSES";
        string warehouses = "";
        const string JParam_NumberOfDays = "NUMBEROFDAYS";
        int numberOfDays = 0;
        const string JParam_OrderHeaderBillToCustomerCode = "BILLTOCUSTOMERCODE";
        string orderHeaderBillToCustomerCode = "";
        const string JParam_CustomerOrderHeaderStatus = "ORDERSTATUS";
        string customerOrderHeaderStatus = "";
        const string JParam_nextJob = "NEXTJOB";
        string nextJob = "";
        string jobname = "Order hold job";

        #endregion

        #region Constructor

        public OrderHoldJob()
        {
        }

        #endregion

        #region Methods.Public

        public virtual void Execute(JobExecutionContext context)
        {
            if (JobParametersAreValid(context.MergedJobDataMap) == false)
            {
                JobExecutionException jex = new JobExecutionException("Missing job parameter");
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }

            ParseJobParameters(context.MergedJobDataMap);

            GenerateOrderHold();
            NextJobScheduling(jobname);
        }

        #endregion

        #region Methods.Private

        private bool JobParametersAreValid(JobDataMap jobParams)
        {
            bool validity = true;

            if (!jobParams.Contains(JParam_Warehouses))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_Warehouses);
                validity = false;
            }
            else if (string.IsNullOrEmpty(jobParams.GetString(JParam_Warehouses)))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Value for '{0}' should not be empty", JParam_Warehouses);
                validity = false;
            }

            if (!jobParams.Contains(JParam_NumberOfDays))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_NumberOfDays);
                validity = false;
            }
            else if (jobParams.GetInt(JParam_NumberOfDays) <= 0)
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Value for '{0}' should be greater than zero", JParam_NumberOfDays);
                validity = false;
            }
            if (!jobParams.Contains(JParam_OrderHeaderBillToCustomerCode))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_OrderHeaderBillToCustomerCode);
                validity = false;
            }
            else if (string.IsNullOrEmpty(jobParams.GetString(JParam_OrderHeaderBillToCustomerCode)))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Value for '{0}' should not be empty", JParam_OrderHeaderBillToCustomerCode);
                validity = false;
            }
            if (!jobParams.Contains(JParam_CustomerOrderHeaderStatus))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_CustomerOrderHeaderStatus);
                validity = false;
            }
            else if (string.IsNullOrEmpty(jobParams.GetString(JParam_CustomerOrderHeaderStatus)))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Value for '{0}' should not be empty", JParam_CustomerOrderHeaderStatus);
                validity = false;
            }
            return validity;
        }

        private void ParseJobParameters(JobDataMap jobParams)
        {
            try
            {
                //map the parameters to jobParams
                this.warehouses = jobParams.GetString(JParam_Warehouses);
                this.numberOfDays = jobParams.GetInt(JParam_NumberOfDays);
                this.orderHeaderBillToCustomerCode = jobParams.GetString(JParam_OrderHeaderBillToCustomerCode);
                this.customerOrderHeaderStatus = jobParams.GetString(JParam_CustomerOrderHeaderStatus);
                this.nextJob = jobParams.GetString(JParam_nextJob);
            }
            catch (Exception ex)
            {
                JobExecutionException jex = new JobExecutionException("Invalid data type in job parameters", ex);
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }
        }

        private void NextJobScheduling(string jobname)
        {
            if (!String.IsNullOrWhiteSpace(nextJob))
            {
                if (_logger.IsDebugEnabled) _logger.DebugFormat("NextJob:  {0}", nextJob);
                try
                {
                    string[] jobKeys = Service.Instance.Scheduler.GetJobNames("Manual");
                    foreach (string jobKey in jobKeys)
                    {
                        if (jobKey.Equals(nextJob))
                        {
                            SimpleTrigger trigger = new SimpleTrigger();
                            trigger.Name = nextJob + "Trigger";
                            trigger.JobName = nextJob;
                            trigger.JobGroup = "Manual";
                            trigger.Group = "Manual";
                            trigger.StartTimeUtc = DateTime.UtcNow.AddSeconds(30);
                            trigger.RepeatCount = 0;
                            trigger.RepeatInterval = TimeSpan.Zero;
                            Service.Instance.Scheduler.RescheduleJob(nextJob + "Trigger", "Manual", trigger);
                            if (_logger.IsDebugEnabled) _logger.DebugFormat("{1} - Next Job {0} is scheduled: ", nextJob, jobname);
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (_logger.IsErrorEnabled) _logger.ErrorFormat("{1} - Next Job error: {0}", ex.Message, jobname);
                }
            }
        }

        private void GenerateOrderHold()
        {
            try
            {
                List<int> orderHeaderStatusIds = new List<int>();
                string[] billToCustomerCodes = orderHeaderBillToCustomerCode.Split(',');
                StatusCode holdStatus = null;
                List<int> billToLocationTypes = new List<int>();
                using (UnitWrapper wrapper = new UnitWrapper())
                {
                    wrapper.Execute(() =>
                    {
                        List<string> orderStatuses = customerOrderHeaderStatus.Split(',').ToList();
                        orderStatuses.Add("H");

                        DetachedCriteria criteriaStatusCodes = DetachedCriteria.For<StatusCode>()
                                                     .CreateAlias("FunctionalAreaCode", "FunctionalAreaCode")
                                                     .Add(Restrictions.In("Code", orderStatuses))
                                                     .Add(Restrictions.Eq("FunctionalAreaCode.Code", "ORD"));

                        IList<StatusCode> orderHeaderStatuses = Repositories.Get<StatusCode>().List(criteriaStatusCodes);
                        holdStatus = orderHeaderStatuses.Where(status => status.Code == "H").FirstOrDefault();
                        if (holdStatus == null)
                        {
                            _logger.ErrorFormat("Status Hold with functional area 'Orders' does not exist");
                            JobExecutionException jex = new JobExecutionException("Status Hold with functional area 'Orders' does not exist");
                            throw jex;
                        }
                        orderHeaderStatusIds = orderHeaderStatuses.Where(status => status.Code != "H").Select(status => status.Id.Value).Distinct().ToList();

                        DetachedCriteria criteriaLocationType = DetachedCriteria.For<LocationType>()
                                                                .CreateAlias("FunctionalAreaCode", "FunctionalAreaCode")
                                                                .SetProjection(Projections.ProjectionList()
                                                                .Add(Projections.Property("Id"), "Id"))
                                                                 .Add(Restrictions.Eq("LocationTypeCode", "B"))
                                                                 .Add(Restrictions.Eq("FunctionalAreaCode.Code", "ORG"))
                                                                .SetResultTransformer(Transformers.AliasToBean<LocationType>());
                        IList<LocationType> locationTypes = Repositories.Get<LocationType>().List(criteriaLocationType);
                        billToLocationTypes = locationTypes.Select(loc => loc.Id.Value).Distinct().ToList();

                    });
                }

                IList<OrderHeader> orderheaders = new List<OrderHeader>();
                if (!string.IsNullOrWhiteSpace(warehouses))
                {
                    warehouses = warehouses.TrimEnd(';');
                    string[] rowFormats = warehouses.Split(';');

                    foreach (string rowFormat in rowFormats)
                    {
                        string[] fieldFormatDescriptions = rowFormat.Split(':');

                        string companyCode = fieldFormatDescriptions[0];
                        string[] companyLocationCodes = fieldFormatDescriptions[1].Split(',');

                        if (_logger.IsDebugEnabled) _logger.DebugFormat("Warehouses Count is {0} for the Company : {1}", companyLocationCodes.Length.ToString(), companyCode);
                        using (UnitWrapper wrapper = new UnitWrapper())
                        {
                            wrapper.Execute(() =>
                            {
                                DetachedCriteria criteriaCompanyLocationType = DetachedCriteria.For<CompanyLocationType>()
                                                                                      .CreateAlias("CompanyLocation", "CompanyLocation")
                                                                                      .CreateAlias("CompanyLocation.Company", "Company")
                                                                                       .SetProjection(Projections.ProjectionList()
                                                                                       .Add(Projections.Property("Id"), "Id")
                                                                                       .Add(Projections.Property("Version"), "Version")
                                                                                       .Add(Projections.Property("UserCreated"), "UserCreated"))
                                                                                       .Add(Restrictions.In("CompanyLocationCode", companyLocationCodes))
                                                                                       .Add(Restrictions.Eq("Company.CompanyCode", companyCode))
                                                                                       .SetResultTransformer(Transformers.AliasToBean<CompanyLocationType>());

                                IList<CompanyLocationType> companyLocationTypes = Repositories.Get<CompanyLocationType>().List(criteriaCompanyLocationType);

                                DetachedCriteria criteriaOrderHeaders = DetachedCriteria.For<OrderHeader>("_root")
                                                                        .CreateAlias("OrderLocations", "OrderLocations")
                                                                        .CreateAlias("OrderStatuses", "OrderStatuses")
                                                                        .CreateAlias("OrderStatuses.StatusCode", "StatusCode")
                                                                        .CreateAlias("Customer", "Customer")
                                                                        .Add(Restrictions.In("CompanyLocationType.Id", companyLocationTypes.Select(com => com.Id).ToList()))
                                                                        .Add(Restrictions.IsNotNull("Ordered"))
                                                                        .Add(Restrictions.In("OrderLocations.LocationType.Id", billToLocationTypes))
                                                                        .Add(Restrictions.In("StatusCode.Id", orderHeaderStatusIds))
                                                                        .Add(Restrictions.In("OrderLocations.CustomerCode", billToCustomerCodes));
                                DetachedCriteria detachedCurrentStatus = DetachedCriteria.For<OrderStatus>("os")
                                             .CreateAlias("os.StatusCode", "StatusCodes")
                                             .SetProjection(Projections.Property("os.Id"))
                                             .Add(Expression.EqProperty("_root.Id", "os.OrderHeader.Id"))
                                             .AddOrder(new Order("os.Occurred", false))
                                             .AddOrder(new Order("StatusCodes.SortOrder", false))
                                             .SetMaxResults(1);
                                criteriaOrderHeaders.Add(Subqueries.PropertyEq("OrderStatuses.Id", detachedCurrentStatus));

                                orderheaders = Repositories.Get<OrderHeader>().List(criteriaOrderHeaders);
                            });
                        }

                        orderheaders = orderheaders.Where(order => DateTime.Now.Date.Subtract(order.Ordered.Value.Date).TotalDays >= numberOfDays).ToList();

                        if (orderheaders.Count == 0)
                        {
                            continue;
                        }
                        using (UnitWrapper wrapper = new UnitWrapper())
                        {
                            wrapper.Execute(() =>
                            {
                                foreach (OrderHeader orderHeader in orderheaders)
                                {
                                    if (CreateOrderStatuses(orderHeader, holdStatus))
                                    {
                                        orderHeader.UserModified = "eod_manager";
                                        orderHeader.DateModified = DateTime.Now;
                                        orderHeader.StatusCode = holdStatus;
                                        Repositories.Get<OrderHeader>().Update(orderHeader);
                                        if (_logger.IsDebugEnabled) _logger.DebugFormat("Modified Order Header to Hold for Order Code : {0}", orderHeader.OrderCode);
                                    }
                                }
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
            }
        }

        private Boolean CreateOrderStatuses(OrderHeader orderHeader, StatusCode holdStatus)
        {
            try
            {
                OrderStatus orderStatus = new OrderStatus();
                orderStatus.DateCreated = DateTime.Now;
                orderStatus.UserCreated = "eod_manager";
                orderStatus.DateModified = null;
                orderStatus.UserModified = null;
                orderStatus.OrderHeader = orderHeader;
                orderStatus.StatusCode = holdStatus;
                orderStatus.Occurred = DateTime.Now;

                if (orderHeader.Customer != null)
                    orderStatus.Customer = orderHeader.Customer;
                else
                    orderStatus.Customer = GetOrderStatusCustomer(orderHeader.Id);

                Repositories.Get<OrderStatus>().Add(orderStatus);
                return true;
            }
            catch (Exception ex)
            {
                _logger.ErrorFormat("Creation of order status for Order Code : {0} is failed; Exception is : {1}", orderHeader.OrderCode, ex.Message);
                return false;
            }
        }

        private Customer GetOrderStatusCustomer(int? orderId)
        {
            Customer customer = null;
            try
            {
                using (UnitWrapper wrapper = new UnitWrapper())
                {
                    wrapper.Execute(() =>
                    {
                        DetachedCriteria criteriaOrderHeaders = DetachedCriteria.For<OrderHeader>()
                                                .SetProjection(Projections.ProjectionList()
                                                    .Add(Projections.Property("CustomerCode"), "CustomerCode")
                                                    .Add(Projections.Property("Customer"), "Customer")
                                                    .Add(Projections.Property("CompanyLocationType"), "CompanyLocationType"))
                                                .Add(Restrictions.Eq("Id", orderId))
                                                .SetResultTransformer(Transformers.AliasToBean<OrderHeader>())
                                                .SetMaxResults(1);
                        IList<OrderHeader> orderHeaders = Repositories.Get<OrderHeader>().List(criteriaOrderHeaders);

                        if (orderHeaders != null && orderHeaders.Count > 0)
                        {
                            if (orderHeaders[0].Customer != null)
                                customer = orderHeaders[0].Customer;
                            else if (!string.IsNullOrEmpty(orderHeaders[0].CustomerCode))
                            {
                                DetachedCriteria criteriaCustomer = DetachedCriteria.For<Customer>()
                                                     .Add(Restrictions.Eq("CustomerCode", orderHeaders[0].CustomerCode))
                                                     .Add(Restrictions.Eq("CompanyLocationType.Id", orderHeaders[0].CompanyLocationType.Id));

                                IList<Customer> list = Repositories.Get<Customer>().List(criteriaCustomer);
                                if (list.Count > 0 && list != null)
                                {
                                    customer = list[0];
                                }
                            }
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.ErrorFormat("Exception while fetching Customer for orderId =" + Convert.ToString(orderId));
                if (!string.IsNullOrEmpty(ex.StackTrace)) _logger.ErrorFormat(ex.StackTrace);
            }
            return customer;
        }
        #endregion
    }
}
