﻿using System;
using System.Collections;
using System.ComponentModel;
using System.Configuration.Install;
using System.ServiceProcess;

namespace Upp.Irms.EOD.Host
{
	[RunInstaller(true)]
	public partial class ProjectInstaller : Installer
	{
		#region Fields

		private ServiceInstaller _installer = new ServiceInstaller();
		private ServiceProcessInstaller _processInstaller = new ServiceProcessInstaller();

		#endregion

		#region Constructor

		public ProjectInstaller()
		{
			InitializeComponent();
		}

		#endregion

		#region Overrides

		protected override void OnBeforeInstall(IDictionary savedState)
		{
			base.OnBeforeInstall(savedState);
			//
			this.ConfigureInstallers();
		}

		protected override void OnBeforeUninstall(IDictionary savedState)
		{
			base.OnBeforeUninstall(savedState);
			//
			this.ConfigureInstallers();
		}

		#endregion

		#region Methods.Private

		private void ConfigureInstallers()
		{
			String description = "IRMS 360 EOD Host";
			String name = Program.Name;
			if (this.Context.Parameters.ContainsKey("instance"))
			{
				String instance = this.Context.Parameters["instance"];
				//
				if (!String.IsNullOrEmpty(instance))
				{
                    description = String.Format("IRMS 360 EOD Host for {0}", instance);
					name = String.Format("{0}-{1}", Program.Name, instance);
				}
			}
			//
			_installer.Description = description;
			_installer.DisplayName = description;
			_installer.ServiceName = name; 
			_installer.StartType = ServiceStartMode.Automatic;
			//
			_processInstaller.Account = ServiceAccount.LocalSystem;
			//
			this.Installers.Add(_installer);
			this.Installers.Add(_processInstaller);
		}

		#endregion
	}
}
