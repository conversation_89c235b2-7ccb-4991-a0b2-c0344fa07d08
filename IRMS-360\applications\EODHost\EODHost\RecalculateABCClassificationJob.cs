﻿using Quartz;
using System;
using Upp.Irms.Domain;
using System.Collections.Generic;
using Upp.Irms.Core;
using NHibernate;
using NHibernate.Criterion;
using NHibernate.SqlCommand;
using Upp.Shared.Application;
using Upp.Shared.Utilities;
using Upp.Irms.Constants;
using NHibernate.Transform;
using System.Configuration;
using System.Collections;
using log4net;
using System.Linq;
using System.Web;
using System.Data;
using CrystalDecisions.CrystalReports.Engine;
using CrystalDecisions.Shared;
using System.IO;
using System.Net.Mail;
using System.Net;

namespace Upp.Irms.EOD.Host
{
    class RecalculateABCClassificationJob : IJob
    {
        #region Fields

        ILog _logger = LogManager.GetLogger(typeof(RecalculateABCClassificationJob));

        const string JParam_WarehouseList = "WAREHOUSELIST";
        const string JParam_Printer = "PRINTER";
        const string JParam_FolderPath = "FOLDERPATH";
        const string JParam_MailId = "MAILID";
        const string JParam_nextJob = "NEXTJOB";

        string warehouseList = "";
        string printer = "";
        string folderPath = "";
        string mailId = "";
        string nextJob = "";

        string jobname = "RecalculateABCClassification";
        string logDateTimeFormat = "dd/MM/yy HH:mm:ss.fff";

        #endregion

        #region Constructor

        public RecalculateABCClassificationJob()
        {
        }

        #endregion


        #region Methods.Public

        public virtual void Execute(JobExecutionContext context)
        {
            if (JobParametersAreValid(context.MergedJobDataMap) == false)
            {
                JobExecutionException jex = new JobExecutionException("Missing job parameter");
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }

            ParseJobParameters(context.MergedJobDataMap);

            RecalculateABCClassification();

            NextJobScheduling(jobname);
        }
        
        #endregion

        #region Methods.Private

        private bool JobParametersAreValid(JobDataMap jobParams)
        {
            bool validity = true;

            if (!jobParams.Contains(JParam_WarehouseList))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_WarehouseList);
                validity = false;
            }

            return validity;
        }

        private void ParseJobParameters(JobDataMap jobParams)
        {
            try
            {
                //map the parameters to jobParams              
                this.warehouseList = jobParams.GetString(JParam_WarehouseList);
                this.printer = jobParams.GetString(JParam_Printer);
                this.folderPath = jobParams.GetString(JParam_FolderPath);
                this.mailId = jobParams.GetString(JParam_MailId);
                this.nextJob = jobParams.GetString(JParam_nextJob);
            }
            catch (Exception ex)
            {
                JobExecutionException jex = new JobExecutionException("Invalid data type in job parameters", ex);
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }
        }

        private void NextJobScheduling(string jobname)
        {
            if (!String.IsNullOrWhiteSpace(nextJob))
            {
                if (_logger.IsDebugEnabled) _logger.DebugFormat("NextJob:  {0}", nextJob);
                try
                {
                    string[] jobKeys = Service.Instance.Scheduler.GetJobNames("Manual");
                    foreach (string jobKey in jobKeys)
                    {
                        if (jobKey.Equals(nextJob))
                        {
                            SimpleTrigger trigger = new SimpleTrigger();
                            trigger.Name = nextJob + "Trigger";
                            trigger.JobName = nextJob;
                            trigger.JobGroup = "Manual";
                            trigger.Group = "Manual";
                            trigger.StartTimeUtc = DateTime.UtcNow.AddSeconds(30);
                            trigger.RepeatCount = 0;
                            trigger.RepeatInterval = TimeSpan.Zero;
                            Service.Instance.Scheduler.RescheduleJob(nextJob + "Trigger", "Manual", trigger);
                            if (_logger.IsDebugEnabled) _logger.DebugFormat("{1} - Next Job {0} is scheduled: ", nextJob, jobname);
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (_logger.IsErrorEnabled) _logger.ErrorFormat("{1} - Next Job error: {0}", ex.Message, jobname);
                }
            }
        }

        private void RecalculateABCClassification()
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(warehouseList))
                {
                    char[] fieldFormatSeparator = { ';' };
                    char[] formatSeparator = { ':' };
                    StratificationDefinition stratificationDefinition = null;
                    DateTime currentDate = DateTime.Now;

                    warehouseList = warehouseList.TrimEnd(fieldFormatSeparator);
                    string[] rowFormats = warehouseList.Split(fieldFormatSeparator);

                    foreach (string rowFormat in rowFormats)
                    {
                        string[] fieldFormatDescriptions = rowFormat.Split(formatSeparator);

                        string companyCode = fieldFormatDescriptions[0];
                        string[] companyLocationCodes = fieldFormatDescriptions[1].Split(new char[] { ',' });

                        if (_logger.IsDebugEnabled) _logger.DebugFormat("         »  Warehouses Count is {0} for the Company : {1}", companyLocationCodes.Length.ToString(), companyCode);
                        int warehouseCount = 1;
                        foreach (string companylocCode in companyLocationCodes)
                        {
                            IList<ItemHistory> itemHistoryList = new List<ItemHistory>();

                            using (UnitWrapper wrapper = new UnitWrapper())
                            {
                                wrapper.Execute(() =>
                                {
                                    DetachedCriteria criteria_CompanyLocationType = DetachedCriteria.For<CompanyLocationType>()
                                                         .CreateAlias("CompanyLocation", "cl", JoinType.LeftOuterJoin)
                                                         .CreateAlias("cl.Company", "c", JoinType.LeftOuterJoin)
                                                         .SetProjection(Projections.ProjectionList()
                                                         .Add(Projections.Property("Id"), "Id")
                                                         .Add(Projections.Property("UserCreated"), "UserCreated")
                                                         .Add(Projections.Property("DateCreated"), "DateCreated")
                                                         .Add(Projections.Property("Active"), "Active")
                                                         .Add(Projections.Property("Version"), "Version")
                                                         .Add(Projections.Property("PrimaryAddress"), "PrimaryAddress"))
                                                         .Add(Restrictions.Eq("CompanyLocationCode", companylocCode))
                                                         .Add(Restrictions.Eq("c.CompanyCode", companyCode))
                                                         .SetResultTransformer(Transformers.AliasToBean<CompanyLocationType>())
                                                         .SetMaxResults(1);

                                    CompanyLocationType companyLocationType = Repositories.Get<CompanyLocationType>().List(criteria_CompanyLocationType).FirstOrDefault();

                                    if (companyLocationType == null)
                                    {
                                        if (_logger.IsDebugEnabled) _logger.DebugFormat("Invalid Warehouse: {0}", companylocCode);
                                        return;
                                    }
                                    if (_logger.IsDebugEnabled) _logger.DebugFormat("Processing ItemHistroy for Company: {0}", companylocCode);
                                    
                                    DetachedCriteria detachedCriteriaSD = DetachedCriteria.For<StratificationDefinition>()
                                                         .CreateAlias("CompanyLocationType", "cl", JoinType.LeftOuterJoin)
                                                         .SetProjection(Projections.ProjectionList()
                                                         .Add(Projections.Property("Id"), "Id")
                                                         .Add(Projections.Property("StratificationLastCalculation"), "StratificationLastCalculation")
                                                         .Add(Projections.Property("Frequency"), "Frequency")
                                                         .Add(Projections.Property("TimePeriodAmount"), "TimePeriodAmount")
                                                         .Add(Projections.Property("Active"), "Active")
                                                         .Add(Projections.Property("IncludeInactiveItems"), "IncludeInactiveItems")
                                                         .Add(Projections.Property("StratificationType"), "StratificationType")
                                                         .Add(Projections.Property("UpdateItemStratification"), "UpdateItemStratification")
                                                         .Add(Projections.Property("EnableStratification"), "EnableStratification"))
                                                         .Add(Restrictions.Eq("cl.Id", Converter.ToInt32(companyLocationType.Id)))
                                                         .SetResultTransformer(Transformers.AliasToBean<StratificationDefinition>())
                                                         .SetMaxResults(1);

                                    stratificationDefinition = Repositories.Get<StratificationDefinition>().List(detachedCriteriaSD).FirstOrDefault();

                                    if(stratificationDefinition == null)
                                    {
                                        if (_logger.IsDebugEnabled) _logger.DebugFormat("Invalid StratificationDefinition for Company: {0}", companylocCode);
                                        return;
                                    }
                                    if(stratificationDefinition.StratificationType == null)
                                    {
                                        if (_logger.IsDebugEnabled) _logger.DebugFormat("StratificationType is not defined for Company: {0}", companylocCode);
                                        return;
                                    }
                                    if (stratificationDefinition.EnableStratification == null || !stratificationDefinition.EnableStratification.Equals("Y", StringComparison.InvariantCultureIgnoreCase))
                                    {
                                        if (_logger.IsDebugEnabled) _logger.DebugFormat("Stratification is not Enabled for Company: {0}", companylocCode);
                                        return;
                                    }
                                    
                                    DateTime lastStratificationDate = stratificationDefinition.StratificationLastCalculation ?? new DateTime(1900, 01, 01);
                                    if (_logger.IsDebugEnabled) _logger.DebugFormat("lastStratificationDate: {0}", lastStratificationDate.ToString());
                                    if (lastStratificationDate != null && stratificationDefinition.Frequency != null && stratificationDefinition.TimePeriodAmount > 0)
                                    {
                                        if (stratificationDefinition.Frequency.FrequencyCode.Equals("D"))
                                        {
                                            lastStratificationDate.AddDays(Convert.ToDouble(stratificationDefinition.TimePeriodAmount));
                                        }
                                        else if (stratificationDefinition.Frequency.FrequencyCode.Equals("W") && lastStratificationDate.Year != 1800)
                                        {
                                            lastStratificationDate.AddDays(7 * Convert.ToDouble(stratificationDefinition.TimePeriodAmount));
                                        }
                                        else if (stratificationDefinition.Frequency.FrequencyCode.Equals("M"))
                                        {
                                            lastStratificationDate.AddMonths(Convert.ToInt32(stratificationDefinition.TimePeriodAmount));
                                        }

                                        if (lastStratificationDate >= currentDate)
                                        {
                                            if (_logger.IsDebugEnabled) _logger.DebugFormat("Stratification Recalculation internal is not matched: {0}", lastStratificationDate.ToString());
                                            return;
                                        }
                                    }
                                    
                                    DetachedCriteria criteriaItemsHistroy = DetachedCriteria.For<ItemHistory>()
                                                                .CreateAlias("Item", "Item")
                                                                .SetProjection(Projections.ProjectionList()
                                                                .Add(Projections.Property("Item"), "Item")
                                                                //.Add(Projections.Property("Item.Id"), "ItemId")
                                                                .Add(Projections.Sum("Shipments"), "Shipments")
                                                                //.Add(Projections.GroupProperty("Item.Id"))
                                                                .Add(Projections.GroupProperty("Item")))
                                                                .Add(Restrictions.Eq("Item.CompanyLocationType.Id", Converter.ToInt32(companyLocationType.Id)))
                                                                .Add(Expression.Between("AsOf", lastStratificationDate, currentDate))
                                                                .SetResultTransformer(Transformers.AliasToBean<ItemHistory>());
                                    if (!stratificationDefinition.IncludeInactiveItems.Equals("Y"))
                                        criteriaItemsHistroy = criteriaItemsHistroy.Add(Restrictions.Eq("Item.Active", "A"));
                                    IList<ItemHistory> existingItemsHistroy = Repositories.Get<ItemHistory>().List(criteriaItemsHistroy);

                                    if (stratificationDefinition.StratificationType.Equals("I"))
                                    {
                                        existingItemsHistroy = existingItemsHistroy.OrderByDescending(c => c.Shipments).ToList<ItemHistory>();
                                    }
                                    else //if(stratificationDefinition.StratificationType.Equals("D"))
                                    {
                                        foreach(ItemHistory ih in existingItemsHistroy)
                                        {
                                            ih.ShipmentsUnitCost = ih.Shipments * ih.Item.UnitCost;
                                        }
                                        existingItemsHistroy = existingItemsHistroy.OrderByDescending(c => c.ShipmentsUnitCost).ToList<ItemHistory>();
                                    }
                                    if (_logger.IsDebugEnabled) _logger.DebugFormat("(stratificationDefinition.StratificationType : {0}", stratificationDefinition.StratificationType);

                                    int totalItems = existingItemsHistroy.Count;
                                    if (_logger.IsDebugEnabled) _logger.DebugFormat("Total ItemsCounts: {0}", totalItems.ToString());
                                    DetachedCriteria criteriaCompanyStratification = DetachedCriteria.For<CompanyStratification>()
                                                                                .CreateAlias("Stratification", "Stratification")
                                                                                .CreateAlias("CompanyLocationType", "CompanyLocationType", JoinType.LeftOuterJoin)
                                                                                .Add(Restrictions.Eq("CompanyLocationType.Id", Converter.ToInt32(companyLocationType.Id)));

                                    IList<CompanyStratification> companyStratifications = Repositories.Get<CompanyStratification>().List(criteriaCompanyStratification);

                                    if (companyStratifications == null)
                                    {
                                        if (_logger.IsDebugEnabled) _logger.DebugFormat("companyStratifications not found for Company: {0}", companylocCode);
                                        return;
                                    }

                                    Stratification lastStart = companyStratifications.Where(c => c.Stratification.StratificationCode.Equals("D")).FirstOrDefault<CompanyStratification>()?.Stratification;

                                    if (stratificationDefinition.StratificationType.Equals("I"))
                                        companyStratifications = companyStratifications.Where(c => c.ItemPercentage > 0).ToList<CompanyStratification>();
                                    else
                                        companyStratifications = companyStratifications.Where(c => c.DollarPercentage > 0).ToList<CompanyStratification>();

                                    companyStratifications = companyStratifications.OrderBy(c => c.Stratification.StratificationCode).ToList<CompanyStratification>();

                                    if (_logger.IsDebugEnabled) _logger.DebugFormat("companyStratifications Count ItemPercentage/DollarPercentage > 0: {0}", companyStratifications.Count.ToString());

                                    try
                                    {

                                        int index = 0;
                                        int eachStratItemsIndex = 0;
                                        foreach (CompanyStratification cs in companyStratifications)
                                        {
                                            int percentageItems = 0;
                                            if (stratificationDefinition.StratificationType.Equals("I"))
                                                percentageItems = Convert.ToInt32(Math.Round(cs.ItemPercentage * totalItems / 100));
                                            else
                                                percentageItems = Convert.ToInt32(Math.Round(cs.DollarPercentage * totalItems / 100));

                                            if (_logger.IsDebugEnabled) _logger.DebugFormat("ItemsCount for StratificationCode {0} is {1}", cs.Stratification.StratificationCode, percentageItems.ToString());

                                            for (int i = index; i < (eachStratItemsIndex + percentageItems); i++)
                                            {
                                                if (i < totalItems)
                                                {
                                                    existingItemsHistroy[i].OldStratification = existingItemsHistroy[i].Item?.Stratification;
                                                    existingItemsHistroy[i].Item.Stratification = cs.Stratification;
                                                    index++;
                                                }
                                            }
                                            eachStratItemsIndex = index;
                                        }


                                        if (_logger.IsDebugEnabled) _logger.DebugFormat("Assigning remaining {0} Items to StratificationCode {1}", (totalItems - index).ToString(), lastStart.StratificationCode);
                                        if (index < totalItems)
                                        {
                                            for (int i = index; i < totalItems; i++)
                                            {
                                                if (i < totalItems)
                                                {
                                                    existingItemsHistroy[i].OldStratification = existingItemsHistroy[i].Item?.Stratification;
                                                    existingItemsHistroy[i].Item.Stratification = lastStart;
                                                }
                                            }
                                        }
                                    }
                                    catch(Exception e)
                                    {
                                        if (_logger.IsErrorEnabled) _logger.Error(e);
                                        warehouseCount++;
                                    }

                                    #region Generate Report

                                    ReportDocument document = GenerateABCAnalysisReport(existingItemsHistroy, lastStratificationDate, currentDate, companyCode, companylocCode, stratificationDefinition.StratificationType);

                                    #endregion

                                    #region Copy/Email/Print Report
                                    if(!string.IsNullOrWhiteSpace(folderPath))
                                        CopyReportToFolder(document);
                                    if (!string.IsNullOrWhiteSpace(mailId))
                                        EmailReport(document, "ABC_Analysis");
                                    if (!string.IsNullOrWhiteSpace(printer))
                                        PrintPDFReport(document, "ABC_Analysis", printer);
                                    #endregion

                                    if (_logger.IsDebugEnabled) _logger.DebugFormat("Done generating Report");

                                    foreach (ItemHistory itemHistory in existingItemsHistroy)
                                    {
                                        Item it = Repositories.Get<Item>().Retrieve(itemHistory.Item.Id);
                                        it.DateModified = currentDate;
                                        it.UserModified = "eod_manager";
                                        Repositories.Get<Item>().Update(it);
                                    }
                                    if (_logger.IsDebugEnabled) _logger.DebugFormat("Done Updating all Items");

                                    if (stratificationDefinition != null && stratificationDefinition.Id != null && warehouseCount == companyLocationCodes.Length)
                                    {
                                        StratificationDefinition sd = Repositories.Get<StratificationDefinition>().Retrieve(stratificationDefinition.Id);
                                        sd.StratificationLastCalculation = currentDate;
                                        sd.DateModified = currentDate;
                                        sd.UserModified = "eod_manager";
                                        if (_logger.IsDebugEnabled) _logger.DebugFormat("StratificationLastCalculation = {0}", currentDate.ToString());
                                        Repositories.Get<StratificationDefinition>().Update(sd);
                                        if (_logger.IsDebugEnabled) _logger.DebugFormat("Sucess");
                                    }
                                    if (_logger.IsDebugEnabled) _logger.DebugFormat("Done Updating StratificationLastCalculation");
                                    warehouseCount++ ;
                                });
                            }
                        }
                    }

                    if (_logger.IsDebugEnabled) _logger.DebugFormat("Updating StratificationLastCalculation");

                }
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(ex);
            }
        }

        //TODO: Move to Separate EmailPrintReport class
        public void EmailReport(ReportDocument document, String reportName)
        {
            SmtpClient smtp = null;
            try
            {
                Stream stream = document.ExportToStream(ExportFormatType.PortableDocFormat);
                String file = String.Format("{0}_{1:yyyy-MM-dd-HH.mm.ss}.pdf", reportName, DateTime.Now);

                MailMessage mail = new MailMessage(ConfigurationManager.AppSettings["SmtpFrom"], mailId);

                Attachment attachment = new Attachment(stream, file);
                mail.Attachments.Add(attachment);
                mail.Body = "Attached."; // Maybe something more verbose?
                mail.Subject = file;

                smtp = ConfigureSmtpClient();
                if (smtp != null)
                {
                    smtp.Send(mail);
                    if (_logger.IsDebugEnabled) _logger.DebugFormat("Sent Report to emailId {0} :", mailId);
                }
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(String.Format("EmailReport(): {0}", Errors.GetError(ex)));
            }
            finally
            {
                if (smtp != null) smtp.Dispose();
            }
        }

        //TODO: Move to Separate EmailPrintReport class
        public SmtpClient ConfigureSmtpClient()
        {
            SmtpClient smtp = null;
            //
            try
            {
                Boolean ssl = Converter.ToBoolean(ConfigurationManager.AppSettings["SmtpSsl"]);
                Int32 port = Converter.ToInt32(ConfigurationManager.AppSettings["SmtpPort"]);
                String host = ConfigurationManager.AppSettings["SmtpHost"];
                String password = ConfigurationManager.AppSettings["SmtpPassword"];
                String user = ConfigurationManager.AppSettings["SmtpUser"];
                //
                smtp = new SmtpClient(host, port);
                if (!String.IsNullOrEmpty(password) && !String.IsNullOrEmpty(user))
                    smtp.Credentials = new NetworkCredential(user, password);
                smtp.DeliveryMethod = SmtpDeliveryMethod.Network;
                smtp.EnableSsl = ssl;
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(String.Format("ConfigureSmtpClient(): {0}", Errors.GetError(ex)));
            }
            return smtp;
        }

        //TODO: Move to Separate EmailPrintReport class
        public void PrintPDFReport(ReportDocument document, string filename, string printer)
        {
            try
            {
                if (_logger.IsDebugEnabled) _logger.DebugFormat("Start sending Report to printer {0} :", printer);
                document.PrintOptions.PrinterName = printer;
                document.PrintToPrinter(1, false, 1, 0);
                if (_logger.IsDebugEnabled) _logger.DebugFormat("Sent Report to printer {0} :", printer);
            }
            catch(Exception ex)
            {
                if (_logger.IsDebugEnabled) _logger.DebugFormat("Invalid printer name {0} :", printer);
                if (_logger.IsErrorEnabled) _logger.Error(String.Format("PrintPDFReport(): {0}", Errors.GetError(ex)));
            }
        }

        private void CopyReportToFolder(ReportDocument document)
        {
            String file = String.Format(@"{0}\{1}_{2:yyyy-MM-dd-HH.mm.ss}.pdf", folderPath, "ABC_Analysis", DateTime.Now);
            document.ExportToDisk(ExportFormatType.PortableDocFormat, file);

            if (_logger.IsDebugEnabled) _logger.DebugFormat("Copied Report to {0} :", folderPath);
        }

        private ReportDocument GenerateABCAnalysisReport(IList<ItemHistory> existingItemsHistroy, DateTime lastStratificationDate, DateTime currentDate, string companyCode, string companylocCode, string stratificationType)
        {
            ReportDocument document = new ReportDocument();
            try
            {
                string ReportsFolder = string.Empty;
                if (System.Configuration.ConfigurationManager.AppSettings["Reports"] != null)
                    ReportsFolder = System.Configuration.ConfigurationManager.AppSettings["Reports"].ToString();

                DataSet data = new DataSet("ABCAnalysisReport");
                data.ReadXmlSchema(ReportsFolder + "Datasets\\ItemHistoryByItem.xsd");
                document.Load(ReportsFolder + "ABCAnalysis.rpt", OpenReportMethod.OpenReportByTempCopy);

                DataRow dr = data.Tables["Filters"].NewRow();
                dr["ItemCompany"] = companyCode;
                dr["ItemWarehouse"] = companylocCode;
                dr["FromDate"] = lastStratificationDate;
                dr["ToDate"] = currentDate;
                if (stratificationType.Equals("I"))
                    dr["SortedBy"] = "Quantity";
                else
                    dr["SortedBy"] = "Cost";
                data.Tables["Filters"].Rows.Add(dr);

                foreach (ItemHistory ih in existingItemsHistroy)
                {
                    DataRow drItem = data.Tables["ItemHistory"].NewRow();
                    drItem["Id"] = ih.Item.Id.Value;
                    drItem["ItemCode"] = ih.Item.ItemCode;
                    drItem["ItemDescription"] = ih.Item.Description;
                    drItem["NewStratification"] = ih.Item.Stratification?.StratificationCode;
                    drItem["OldStratification"] = ih.OldStratification?.StratificationCode;
                    if (ih.Item.Stratification?.StratificationCode == ih.OldStratification?.StratificationCode)
                        drItem["SameStratification"] = "";
                    else
                        drItem["SameStratification"] = "*";
                    drItem["Quantity"] = ih.Shipments;
                    decimal? totalSalesPrice = (ih.Item.SellingPrice == null) ? 0 : ih.Item.SellingPrice * ih.Shipments;
                    decimal? totalCostPrice = (ih.Item.UnitCost == null) ? 0 : ih.Item.UnitCost * ih.Shipments;
                    drItem["SaleCost"] = (ih.Item.SellingPrice == null) ? 0 : ih.Item.SellingPrice;
                    drItem["TotalSales"] = totalSalesPrice;
                    drItem["TotalCube"] = (ih.Item.Cube == null) ? 0 : ih.Item.Cube * ih.Shipments;
                    drItem["TotalContribution"] = totalSalesPrice - totalCostPrice;
                    data.Tables["ItemHistory"].Rows.Add(drItem);
                }

                SetDataSource(ref document, data);
                document.Refresh();
            }
            catch(Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(String.Format("GenerateABCAnalysisReport(): {0}", Errors.GetError(ex)));
            }
            return document;
        }

        private static void SetDataSource(ref ReportDocument reportDocument, DataSet rptDataSource)
        {
            SetConnectionInfo(ref reportDocument);
            //Assigning the DataSource to the Subreport(s)
            for (int i = 0; i < reportDocument.Subreports.Count; i++)
            {
                ReportDocument subreportDocument = reportDocument.Subreports[i];
                // Setting the Connection Info for Sub Reports
                SetConnectionInfo(ref subreportDocument);
                if (rptDataSource.Tables.Count > 1)
                    subreportDocument.SetDataSource(rptDataSource);
                else
                    subreportDocument.SetDataSource(rptDataSource.Tables[0]);
            }

            //Assigning the DataSource to the MainReport
            //  SetConnectionInfo(ref reportDocument);
            if (rptDataSource.Tables.Count > 1)
                reportDocument.SetDataSource(rptDataSource);
            else
                reportDocument.SetDataSource(rptDataSource.Tables[0]);
        }

        private static void SetConnectionInfo(ref ReportDocument reportDocument)
        {
            CrystalDecisions.Shared.TableLogOnInfo crLogOnInfo;
            crLogOnInfo = reportDocument.Database.Tables[0].LogOnInfo;
            crLogOnInfo.ConnectionInfo.ServerName = "";
            crLogOnInfo.ConnectionInfo.UserID = "";
            crLogOnInfo.ConnectionInfo.Password = "";
            crLogOnInfo.ConnectionInfo.DatabaseName = "";
            reportDocument.Database.Tables[0].ApplyLogOnInfo(crLogOnInfo);
        }
        #endregion
    }
}
