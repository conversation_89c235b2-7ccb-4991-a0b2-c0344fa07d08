﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Timers;

using NHibernate.Criterion;
using NHibernate.SqlCommand;
using Quartz;
using Quartz.Impl;
using Quartz.Listener;

using Upp.Irms.Core;
using Upp.Irms.Domain;
using Upp.Shared.Utilities;
using log4net;
using NHibernate.Transform;

namespace Upp.Irms.EOD.Host
{
    public sealed class Service
    {
        #region Fields

        private String _sessionId = String.Empty;
        private ISchedulerFactory _schedulerFactory = null;
        private IScheduler _scheduler = null;
        private static ILog _logger = LogManager.GetLogger(typeof(Service));

        #endregion

        #region Fields.Static

       private static readonly Lazy<Service> _lazy = new Lazy<Service>(() => new Service());

        #endregion

        #region Properties

        public String SessionId
        {
            get { return _sessionId; }
        }

        public IScheduler Scheduler
        {
            get { return _scheduler; }
        }

        #endregion

        #region Properties.Static

        public static Service Instance
        {
            get { return _lazy.Value; }
        }

        public static void EncryptConfigSection(string sectionName)
        {
            try
            {
                // Encrypt the Section in the app.config using DataProtectionConfigurationProvider
                Configuration Config = null;

#if DEBUG
            Config = ConfigurationManager.OpenExeConfiguration("Upp.Irms.EOD.Host.exe");
#else
                Config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
#endif

                ConfigurationSection Section = Config.GetSection(sectionName);

                //if (!(Section.SectionInformation.IsProtected)
                //&& !Section.SectionInformation.IsLocked
                //&& !Section.IsReadOnly())
                //{
                    if (Section != null)
                    {
                        Section.SectionInformation.ProtectSection("DataProtectionConfigurationProvider");
                        Section.SectionInformation.ForceSave = true;
                        Config.Save(ConfigurationSaveMode.Full);
                    }
                //}
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
            }
        }

        public static void DecryptConfigSection(string sectionName)
        {
            try
            {
                // Decrypt the Section in the app.config 
                Configuration Config = null;

#if DEBUG
                Config = ConfigurationManager.OpenExeConfiguration("Upp.Irms.EOD.Host.exe");
#else
                Config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
#endif

                ConfigurationSection Section = Config.GetSection(sectionName);

                //if (Section.SectionInformation.IsProtected)
                //{
                    Section.SectionInformation.UnprotectSection();
                    Section.SectionInformation.ForceSave = true;
                    Config.Save(ConfigurationSaveMode.Full);
                //}
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
            }
        }

        #endregion

        #region Constructor

        private Service()
        {
            try
            { 
                
                _schedulerFactory = new StdSchedulerFactory();
                _scheduler = _schedulerFactory.GetScheduler();
                if (_logger.IsDebugEnabled) _logger.Debug("  » Created JobScheduler");
                
                if (_logger.IsDebugEnabled) _logger.Debug("  » Added Jobs to JobScheduler");

                _scheduler.AddGlobalJobListener(new JobListenerSupport());
                if (_logger.IsDebugEnabled) _logger.Debug("  » Added JobListener to JobScheduler");                       

            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
            }
        }

        #endregion

        #region Methods.Public

        public void Start()
        {
            _scheduler.Start();
            if (_logger.IsDebugEnabled) _logger.Debug("  » Started JobScheduler");

            if (System.Configuration.ConfigurationManager.AppSettings["JobsConfiguredInDatabase"] != null && System.Configuration.ConfigurationManager.AppSettings["JobsConfiguredInDatabase"].ToString().ToLower() == "true")
            {
                RemoveOldJobs();
                ConfigureJobs();
            }
        }

        public void Stop()
        {
            _scheduler.Shutdown();
            if (_logger.IsDebugEnabled) _logger.Debug("  » Stopped JobScheduler");
        }

        public void RemoveOldJobs()
        {
            try
            {
                string[] jobKeys = _scheduler.GetJobNames("Manual");
                foreach (string jobKey in jobKeys)
                {
                    _scheduler.DeleteJob(jobKey, "Manual");
                }
            }
            catch
            {
            }
        }

        public void ConfigureJobs()
        {
            IList<Upp.Irms.Domain.JobDetail> jobList = new List<Upp.Irms.Domain.JobDetail>();
            IList<Upp.Irms.Domain.JobDetailParameter> jobParameterList = new List<Upp.Irms.Domain.JobDetailParameter>();
            // 1. Get all EOD jobs and Parameters from Database

            using (UnitWrapper wrapper = new UnitWrapper())
            {
                wrapper.Execute(() =>
                {
                    DetachedCriteria criteriaJobDetails = DetachedCriteria.For<Upp.Irms.Domain.JobDetail>()
                                                        .CreateAlias("JobDetailTriggers", "JobDetailTriggers")                                                                                                
                                                        .SetProjection(Projections.ProjectionList()
                                                        .Add(Projections.Property("Id"), "Id")
                                                        .Add(Projections.Property("JobName"), "JobName")
                                                        .Add(Projections.Property("JobGroup"), "JobGroup")
                                                        .Add(Projections.Property("JobClass"), "JobClass")
                                                        .Add(Projections.Property("Volatile"), "Volatile")
                                                        .Add(Projections.Property("Recovery"), "Recovery")
                                                        .Add(Projections.Property("Durable"), "Durable")
                                                        .Add(Projections.Property("JobDetailTriggers.CronExpression"), "CronExpression")
                                                        .Add(Projections.Property("JobDetailTriggers.TriggerName"), "TriggerName")
                                                        .Add(Projections.Property("JobDetailTriggers.TriggerGroup"), "TriggerGroup")
                                                        .Add(Projections.Property("JobDetailTriggers.MisfireInstruction"), "MisfireInstruction"))                                                                                              
                                                        .Add(Restrictions.Eq("Active", "A"))
                                                        .Add(Restrictions.Eq("JobType", "EOD"))
                                                        .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.JobDetail>());
                   
                    jobList = Repositories.Get<Upp.Irms.Domain.JobDetail>().List(criteriaJobDetails);
                    if (jobList != null)
                    {
                        if (_logger.IsDebugEnabled) _logger.DebugFormat("         »  Jobs to process :{0}", jobList.Count.ToString());
                    
                        DetachedCriteria criteriaJobDetailParameters = DetachedCriteria.For<Upp.Irms.Domain.JobDetailParameter>()
                                                        .CreateAlias("JobDetail", "JobDetail")                              
                                                        .SetProjection(Projections.ProjectionList()
                                                        .Add(Projections.Property("JobDetail.Id"), "Id")
                                                        .Add(Projections.Property("ParameterName"), "ParameterName")
                                                        .Add(Projections.Property("ParameterValue"), "ParameterValue"))                                                        
                                                        .Add(Restrictions.Eq("Active", "A"))
                                                        .Add(Restrictions.Eq("JobDetail.JobType", "EOD"))
                                                        .SetResultTransformer(Transformers.AliasToBean<Upp.Irms.Domain.JobDetailParameter>());

                        jobParameterList = Repositories.Get<Upp.Irms.Domain.JobDetailParameter>().List(criteriaJobDetailParameters);

                        if (jobParameterList != null )
                            if (_logger.IsDebugEnabled) _logger.DebugFormat("         »  job Parameters:{0}", jobParameterList.Count.ToString());
                    }
                    else
                        if (_logger.IsDebugEnabled) _logger.DebugFormat("         »  No jobs to process");
                });
            }
            // 2. Schedule all the jobs
            foreach (Upp.Irms.Domain.JobDetail jobDetail in jobList)
            {               
                bool isVolatile = false;
                bool isDurable = true;
                bool requestsRecovery = false;

                Quartz.JobDetail job = new Quartz.JobDetail(jobDetail.JobName, jobDetail.JobGroup, Type.GetType(jobDetail.JobClass), isVolatile, isDurable, requestsRecovery);
                CronTrigger trigger = new CronTrigger(jobDetail.TriggerName, jobDetail.TriggerGroup, jobDetail.CronExpression);
                
                //  Get Job Parameters
                IList<JobDetailParameter> jobParameters = new List<JobDetailParameter>();
                jobParameters = jobParameterList.Where(c => c.Id == jobDetail.Id).ToList<JobDetailParameter>();
                
                if (jobParameters != null)
                    if (_logger.IsDebugEnabled) _logger.DebugFormat("         »  job:{0} - Parameters Count:{1}", jobDetail.JobName , jobParameters.Count.ToString());
                
                JobDataMap datamap = new JobDataMap();
                foreach(JobDetailParameter jobPrameter in jobParameters)
                {
                    datamap[jobPrameter.ParameterName] = jobPrameter.ParameterValue;    
                }
                job.JobDataMap = datamap;
                           
                _scheduler.ScheduleJob(job, trigger);
            }
        }

        #endregion
    }
}
