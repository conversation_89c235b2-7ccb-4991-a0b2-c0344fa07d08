﻿using System;
using System.Collections.Generic;
using System.Collections;
using System.Configuration;
using System.Linq;
using System.Text;
using System.Threading;

using log4net;
using NHibernate;
using NHibernate.Transform;
using NHibernate.Criterion;

using Upp.Irms.Constants;
using Upp.Irms.Core;
using Upp.Irms.Domain;
using Upp.Irms.Domain.Utilities;
using Upp.Shared.Utilities;

namespace Upp.Irms.EOD.Host
{
	public class ServiceEngine
	{        
		#region Fields

        ILog _logger = LogManager.GetLogger(typeof(ServiceEngine));
		private Boolean _running = false;

		#endregion

		#region Methods

		public void Start()
		{
            if (_logger.IsInfoEnabled) _logger.InfoFormat("Starting {0}...", Program.Name);
			//
            try
            {
                //Service.DecryptConfigSection("hibernate-configuration");

                String sessionId = String.Empty;
                using (IUnitOfWork unit = UnitOfWork.Start())
                {
                    try
                    {
                        if (_logger.IsDebugEnabled) _logger.Debug("Authentication Started...");
                        //Service.DecryptConfigSection("appSettings");
                        Byte[] password = Encryption.Encrypt(ConfigurationManager.AppSettings["Password"].ToString(), "s33d");
                        String user = ConfigurationManager.AppSettings["User"].ToString();
                        //Service.EncryptConfigSection("appSettings");
                        //
                        Authentication.Login(user, password);
                        sessionId = Authentication.CreateSessionId(user);
                        //
                        unit.Commit();
                        if (_logger.IsDebugEnabled) _logger.Debug("Authentication Completed...");
                    }
                    catch (AuthenticationException aex)
                    {
                        unit.Rollback();
                        if (_logger.IsErrorEnabled) _logger.ErrorFormat("Authentication failure: {0}.", Errors.GetError(aex));
                        throw new Exception(String.Format("Authentication failure: {0}.", Errors.GetError(aex)));
                    }
                    catch (Exception ex)
                    {
                        unit.Rollback();
                        if (_logger.IsErrorEnabled) _logger.ErrorFormat("Authentication failure: {0}.", Errors.GetError(ex));
                        throw ex;
                    }
                }
#if DEBUG

                // EODJob eod = new EODJob("FourWallReportJob","FourWallReport","P$CompanyCode:W$4;P$CompanyLocationCode:W$4;P$ItemCode:W$24;P$StatusCodeCode:W$2;P$Quantity:W$9:T$0:A$Right:F$F0;",false,false);   
                // EODJob eod = new EODJob("ItemHistoryJob", "ItemHistory", "P$AsOf:W$19;P$BeginningBalance:W$9:A$Right:F$F0;P$Receipts:W$9:A$Right:F$F0;P$Returns:W$9:A$Right:F$F0;P$Adjustments:W$9:A$Right:F$F0;P$Shipments:W$9:A$Right:F$F0;P$EndingBalance:W$9:A$Right:F$F0;", false, false);
                //  EODJob eod = new EODJob("SelfShipReportJob", "SelfShip", "P$DateCreated:W$19;P$ItemCode:W$25;P$UserCreated:W$13;P$StatusCodeCode:W$7;P$CompanyCode:W$5;P$CompanyLocationCode:W$5;", false, false, "01,13,19,32,49,59,S1,S2,S3,S4,S5,S6,P1,P2,P3,P4,P5,P6");
                //  EODJob eod = new EODJob("PackedCartReportJob", "PackedCart", "P$CompanyCode:W$5;P$CompanyLocationCode:W$5;P$ItemCode:W$25;P$StatusCodeCode:W$2;P$DateCreated:W$19;", false, false);   
                //  EODJob eod = new EODJob("FourWallReportJob", "FourWall", "P$CompanyCode:W$4;P$CompanyLocationCode:W$4;P$ItemCode:W$24;P$StatusCodeCode:W$2;P$Quantity:W$9:T$0:A$Right:F$F0;", false, false);   

              //  EODJob eod = new EODJob("PreReceiptUpdateJob", "", "P$PreReceiptCode:D$PreReceiptNumber;P$ShipperCustomerName:D$shipper name;P$ShipperAddressLine1:D$shipper address line 1;P$ShipperAddressLine2:D$shipper address line 2;P$ShipperCity:D$shipper city;P$ShipperState:D$shipper state;P$ShipperZip:D$shipper zip;P$PickedUp:D$pickupdate;P$TrailerCode:D$InboundTrailerNumber;P$DeliveryDate:D$estimated delivery date:F$MM/dd/yyyy;P$DeliveryTime:D$estimated deliverytime;P$ConsigneeCustomerName:D$consignee name;P$ConsigneeAddressLine1:D$consignee address line 1;P$ConsigneeAddressLine2:D$consignee address line 2;P$ConsigneeCity:D$consignee city;P$ConsigneeState:D$consignee state;P$ConsigneeZip:D$consignee zip;P$BillOfLading:D$Bolnumber;P$PurchaseOrderCode:D$ponumber;P$Acknowledgement:D$ack number;P$ItemCode:D$item code;P$ExpectedQuantity:D$pcs;P$ItemDescription:D$description;P$HeaderStatus:D$Pre receipt header status;P$LineStatus:D$pre receipt detail line status;P$ItemStatus:D$pre receipt detailitemstatus;", true, false,"","CCS:CCS2");


#endif

                //
                Service.Instance.Start();
                _running = true;
#if DEBUG
                while (true) Thread.Sleep(100);
#endif
                if (_logger.IsInfoEnabled) _logger.InfoFormat("started {0} .", Program.Name);

            }
            catch (Exception ex)
            {
                if (_running) Service.Instance.Stop();
                if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
            }
            finally
            {
                //Service.EncryptConfigSection("hibernate-configuration");                
            }
		}
        public void Stop()
        {
            if (_running) Service.Instance.Stop();
            {
                if (_logger.IsInfoEnabled) _logger.InfoFormat("Stopping {0}...", Program.Name);
            }
        }   


		#endregion
	}
}
