﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;

using log4net;

using Quartz;

using NHibernate;
using NHibernate.Criterion;
using NHibernate.SqlCommand;
using NHibernate.Transform;

using Upp.Irms.Constants;
using Upp.Irms.Core;
using Upp.Irms.Domain;
using Upp.Shared.Application;
using Upp.Shared.Utilities;

namespace Upp.Irms.EOD.Host
{
    /// <summary>
    /// This is just a simple job  for UnavailableExpirationOrThreshold.
    /// </summary>

    public class UnavailableExpirationOrThreshold : IJob
    {
        #region Fields

        ILog _logger = LogManager.GetLogger(typeof(UnavailableExpirationOrThreshold));

        const string JParam_InvstatExp         = "INVSTATUSEXPIRED";
        const string JParam_InvStatThreshold   = "INVSTATUSTHRESHOLD";
        const string JParam_AdjCodeExp         = "ADJCODEEXPIRED";
        const string JParam_AdjCodeThreshold   = "ADJCODETHRESHOLD";
        const string JParam_InvStatToThreshold = "INVSTATUSTOTHRESHOLD";
        const string JParam_InvStatToExp       = "INVSTATUSTOEXPIRE";
        const string JParam_Customer           = "CUSTOMER";
        const string JParam_nextJob            = "NEXTJOB";
        const string JParam_Warehouses         = "WAREHOUSES";

        string invstatExp = "";
        string invStatThreshold = "";
        string adjCodeExp = "";
        string adjCodeThreshold = "";
        string invStatToThreshold = "";
        string invStatToExp = "";
        string customer = "";
        string nextJob            = "";
        string warehouses         = "";
        string jobname            = "UnavailableExpirationOrThreshold Job";
        string _userModified      = "eod_manager";

        #endregion

        #region Constructor

        /// <summary> 
        /// Empty constructor for job initilization
        /// <p>
        /// Quartz requires a public empty constructor so that the
        /// scheduler can instantiate the class whenever it needs.
        /// </p>
        /// </summary>
        public UnavailableExpirationOrThreshold()
        {
        }

        #endregion

        #region Methods.Public

        /// <summary> 
        /// Called by the <see cref="IScheduler" /> when a
        /// <see cref="Trigger" /> fires that is associated with
        /// the <see cref="IJob" />.
        /// </summary>
        public virtual void Execute(JobExecutionContext context)
        {
            if (JobParametersAreValid(context.MergedJobDataMap) == false)
            {
                JobExecutionException jex = new JobExecutionException("Missing job parameter");
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }

            ParseJobParameters(context.MergedJobDataMap);
            InventoryUpdate();
            NextJobScheduling(jobname);
        }


        #endregion

        #region Methods.Private

        // Private, helper method to validate job parameters
        // Since the parameters are passed in a hashedmap
        // as strings, there is no compile-time verification.
        // There is a high likelihood, therefore, of mistakes
        // here, specially, typing mistakes in the key name.
		
        private bool JobParametersAreValid(JobDataMap jobParams)
        {
            bool validity = true;

            if (!jobParams.Contains(JParam_InvstatExp))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_InvstatExp);
                validity = false;
            }

            if (!jobParams.Contains(JParam_InvStatThreshold))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_InvStatThreshold);
                validity = false;
            }           
           
            if (!jobParams.Contains(JParam_AdjCodeExp))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_AdjCodeExp);
                validity = false;
            }
			
			if (!jobParams.Contains(JParam_AdjCodeThreshold))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_AdjCodeThreshold);
                validity = false;
            }

            if (!jobParams.Contains(JParam_InvStatToThreshold))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_InvStatToThreshold);
                validity = false;
            }

            if (!jobParams.Contains(JParam_InvStatToExp))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_InvStatToExp);
                validity = false;
            }

            if (!jobParams.Contains(JParam_Customer))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_Customer);
                validity = false;
            }

            if (!jobParams.Contains(JParam_Warehouses))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_Warehouses);
                validity = false;
            }

            if (!jobParams.Contains(JParam_nextJob))
            {
                if (_logger.IsErrorEnabled) _logger.ErrorFormat("Missing Job parameter '{0}'", JParam_nextJob);
                validity = false;
            }

            return validity;
        }

        // helper method to parse job parameters
        private void ParseJobParameters(JobDataMap jobParams)
        {
            //Ensure that we catch all other types of exceptions
            // and throw only a JobExecutionException
            try
            {
                //map the parameters to jobParams
                if(jobParams.Contains(JParam_InvstatExp))
                    this.invstatExp = jobParams.GetString(JParam_InvstatExp);
				
				if(jobParams.Contains(JParam_InvStatThreshold))
                    this.invStatThreshold = jobParams.GetString(JParam_InvStatThreshold);
				
				if(jobParams.Contains(JParam_AdjCodeExp))
                    this.adjCodeExp = jobParams.GetString(JParam_AdjCodeExp);
				
				if(jobParams.Contains(JParam_AdjCodeThreshold))
                    this.adjCodeThreshold = jobParams.GetString(JParam_AdjCodeThreshold);
				
				if(jobParams.Contains(JParam_InvStatToThreshold))
                    this.invStatToThreshold = jobParams.GetString(JParam_InvStatToThreshold);
				
				if(jobParams.Contains(JParam_InvStatToExp))
                    this.invStatToExp = jobParams.GetString(JParam_InvStatToExp);
				
				if(jobParams.Contains(JParam_Customer))
                    this.customer = jobParams.GetString(JParam_Customer);

                if (jobParams.Contains(JParam_Warehouses))
                    this.warehouses = jobParams.GetString(JParam_Warehouses);

                if (jobParams.Contains(JParam_nextJob))
                    this.nextJob = jobParams.GetString(JParam_nextJob);


            }
            catch (Exception ex)
            {
                JobExecutionException jex = new JobExecutionException("Invalid data type in job parameters", ex);
                jex.UnscheduleAllTriggers = true;
                throw jex;
            }
        }

        private void NextJobScheduling(string jobname)
        {

            if (!String.IsNullOrWhiteSpace(nextJob))
            {
                if (_logger.IsDebugEnabled) _logger.DebugFormat("         »  NextJob:  {0}", nextJob);
                try
                {
                    string[] jobKeys = Service.Instance.Scheduler.GetJobNames("Manual");
                    foreach (string jobKey in jobKeys)
                    {
                        if (jobKey.Equals(nextJob))
                        {
                            SimpleTrigger trigger = new SimpleTrigger();
                            trigger.Name = nextJob + "Trigger";
                            trigger.JobName = nextJob;
                            trigger.JobGroup = "Manual";
                            trigger.Group = "Manual";
                            trigger.StartTimeUtc = DateTime.UtcNow.AddSeconds(30);
                            trigger.RepeatCount = 0;
                            trigger.RepeatInterval = TimeSpan.Zero;
                            Service.Instance.Scheduler.RescheduleJob(nextJob + "Trigger", "Manual", trigger);
                            if (_logger.IsDebugEnabled) _logger.DebugFormat("{1} - Next Job {0} is scheduled: ", nextJob, jobname);
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (_logger.IsErrorEnabled) _logger.ErrorFormat("{1} - Next Job error: {0}", ex.Message, jobname);
                }
            }
        }

        private void InventoryUpdate()
        {
            try
            {

                if (!string.IsNullOrWhiteSpace(warehouses))
                {
                    char[] fieldFormatSeparator = { ';' };
                    char[] formatSeparator = { ':' };

                    warehouses = warehouses.TrimEnd(fieldFormatSeparator);
                    string[] rowFormats = warehouses.Split(fieldFormatSeparator);

                    foreach (string rowFormat in rowFormats)
                    {
                        using (UnitWrapper wrapper = new UnitWrapper())
                        {
                            wrapper.Execute(() =>
                            {
                                string[] fieldFormatDescriptions = rowFormat.Split(formatSeparator);
                                string companyCode = fieldFormatDescriptions[0];
                                string[] companyLocationCodes = fieldFormatDescriptions[1].Split(new char[] { ',' });

                                // Declare arrays outside the loop to ensure consistent values
                                string[] invStatToThresholdList = !string.IsNullOrWhiteSpace(invStatToThreshold) ? invStatToThreshold.Split(',') : Array.Empty<string>();
                                string[] invStatToExpList = !string.IsNullOrWhiteSpace(invStatToExp) ? invStatToExp.Split(',') : Array.Empty<string>();
                                string[] customerList = !string.IsNullOrWhiteSpace(customer) ? customer.Split(',') : Array.Empty<string>();


                                if (_logger.IsDebugEnabled)
                                {
                                    _logger.DebugFormat("         »  Warehouses Count is {0} for the Company : {1}",
                                                        companyLocationCodes.Length.ToString(), companyCode);
                                }

                                foreach (string companylocCode in companyLocationCodes)
                                {
                                    if (invStatToThresholdList.Length > 0 && !string.IsNullOrEmpty(invStatThreshold) && !string.IsNullOrEmpty(adjCodeThreshold))
                                    {
                                        UpdateInventoryItem(companyCode, companylocCode, customerList, invStatToThresholdList, invStatThreshold, adjCodeThreshold, true, false);
                                    }
                                    else
                                    {
                                        if (_logger.IsDebugEnabled) _logger.DebugFormat("         » INVSTATUSTHRESHOLD or ADJCODETHRESHOLD or INVSTATUSTOTHRESHOLD are blank:{0}:{1}:{2}", invStatThreshold, adjCodeThreshold, invStatToThreshold);
                                    }

                                    if (invStatToExpList.Length > 0 && !string.IsNullOrEmpty(invstatExp) && !string.IsNullOrEmpty(adjCodeExp))
                                    {
                                        UpdateInventoryItem(companyCode, companylocCode, customerList, invStatToExpList, invstatExp, adjCodeExp, false, true);
                                    }
                                    else
                                    {
                                        if (_logger.IsDebugEnabled) _logger.DebugFormat("         » INVSTATUSEXPIRED or ADJCODEEXPIRED or INVSTATUSTOEXPIRE are blank:{0}:{1}:{2}", invstatExp, adjCodeExp, invStatToExp);
                                    }
                                }
                            });
                        } 
                    }
                }
                else
                {
                    using (UnitWrapper wrapper = new UnitWrapper())
                    {
                        wrapper.Execute(() =>
                        {
                            string[] invStatToThresholdList = !string.IsNullOrWhiteSpace(invStatToThreshold) ? invStatToThreshold.Split(',') : Array.Empty<string>();
                            string[] invStatToExpList = !string.IsNullOrWhiteSpace(invStatToExp) ? invStatToExp.Split(',') : Array.Empty<string>();
                            string[] customerList = !string.IsNullOrWhiteSpace(customer) ? customer.Split(',') : Array.Empty<string>();


                            if (invStatToThresholdList.Length > 0 && !string.IsNullOrEmpty(invStatThreshold) && !string.IsNullOrEmpty(adjCodeThreshold))
                            {
                                UpdateInventoryItem(null, null, customerList, invStatToThresholdList, invStatThreshold, adjCodeThreshold, true, false);
                            }
                            else
                            {
                                if (_logger.IsDebugEnabled) _logger.DebugFormat("         » INVSTATUSTHRESHOLD or ADJCODETHRESHOLD or INVSTATUSTOTHRESHOLD are blank:{0}:{1}:{2}", invStatThreshold, adjCodeThreshold, invStatToThreshold);
                            }
                            if (invStatToExpList.Length > 0 && !string.IsNullOrEmpty(invstatExp) && !string.IsNullOrEmpty(adjCodeExp))
                            {
                                UpdateInventoryItem(null, null, customerList, invStatToExpList, invstatExp, adjCodeExp, false, true);
                            }
                            else
                            {
                                if (_logger.IsDebugEnabled) _logger.DebugFormat("         » INVSTATUSEXPIRED or ADJCODEEXPIRED or INVSTATUSTOEXPIRE are blank:{0}:{1}:{2}", invstatExp, adjCodeExp, invStatToExp);
                            }
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                if (_logger.IsErrorEnabled) _logger.Error(Errors.GetError(ex));
            }
        }

        private void UpdateInventoryItem(string companyCode = null, string companylocCode = null, string[] customerList = null, string[] invStatToList = null, string invStat = null, string adjcode = null, bool threshold = false, bool expire = false)
        {
            IList<InventoryItem> items = null;
            IList<TransactionType> transactionType = null;
            InventoryAdjustmentCode adjustmentCodeForStatus = null;
            IList<StatusCode> statuses = null;
            StatusCode integrationOpenStatus = new StatusCode();
            StatusCode integrationClosedStatus = new StatusCode();
			 IList<InventoryAdjustmentCode> adjustmentCode = null;

            DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>("_root")
                                                .CreateAlias("CompanyLocationType", "lt", JoinType.LeftOuterJoin)
                                                .CreateAlias("lt.CompanyLocation", "cl", JoinType.LeftOuterJoin)
                                                .CreateAlias("cl.Company", "c", JoinType.LeftOuterJoin)
                                                .CreateAlias("Customer", "cu", JoinType.LeftOuterJoin)
                                                .CreateAlias("StatusCode", "sc", JoinType.LeftOuterJoin)
                                                .CreateAlias("Item", "it", JoinType.LeftOuterJoin)
                                                .CreateAlias("LicensePlate", "LicensePlate", JoinType.LeftOuterJoin)
                                                .CreateAlias("LicensePlate.ParentLicensePlate", "ParentLicensePlate", JoinType.LeftOuterJoin)
                                                .SetProjection(Projections.ProjectionList()
                                                .Add(Projections.Property("Id"), "Id")
                                                .Add(Projections.Property("LotExpiration"), "LotExpiration")
                                                .Add(Projections.Property("Item"), "Item")
                                                .Add(Projections.Property("StatusCode"), "StatusCode")
                                                .Add(Projections.Property("ParentLicensePlate.LicensePlateCode"), "ParentLicensePlateCode")
                                                .Add(Projections.Property("CompanyLocationType"), "CompanyLocationType"))
                                                .Add(Restrictions.In(("sc.Code"), invStatToList))
                                                .Add(Restrictions.IsNotNull("LotExpiration"))
                                               .SetResultTransformer(Transformers.AliasToBean<InventoryItem>());
            if (customerList.Length > 0)
            {
                criteria.Add(Restrictions.In("cu.CustomerCode", customerList));
            }
            if (!string.IsNullOrEmpty(companylocCode))
            {
                criteria.Add(Restrictions.Eq("lt.CompanyLocationCode", companylocCode));
            }
            if (!string.IsNullOrEmpty(companyCode))
            {
                criteria.Add(Restrictions.Eq("c.CompanyCode", companyCode));
            }
                                               
            items = Repositories.Get<InventoryItem>().List(criteria);

            DetachedCriteria criteriaTransactionType = DetachedCriteria.For<TransactionType>()
                                                                   .Add("Active", "A")
                                                                   .Add("TransactionTypeCode", "SA")
                                                                   .Add("FunctionalAreaCode.Code", "SA");
            transactionType = Repositories.Get<TransactionType>().List(criteriaTransactionType);

            DetachedCriteria criteriaadjustmentCode = DetachedCriteria.For<InventoryAdjustmentCode>()
                                                                   .Add("Active", "A")
                                                                   .Add("Code", adjcode);
            adjustmentCode = Repositories.Get<InventoryAdjustmentCode>().List(criteriaadjustmentCode);
            
            statuses = GetStatusCode(invStat);
            IList<StatusCode> integrationStatuses = Utilities.GetOpenAndClosedStatusWithFACIntegration();
            if (integrationStatuses != null && integrationStatuses.Count > 0 && statuses != null)
            {
                integrationOpenStatus = statuses.Where(c => c.Code == "O").FirstOrDefault<StatusCode>();
                integrationClosedStatus = statuses.Where(c => c.Code == "C").FirstOrDefault<StatusCode>();
            }
            if (statuses != null)
            {
                foreach (InventoryItem invItem in items)
                {
                        try
                        {
                        #region Loop InvItems
                            int ShippingThresholdDays = invItem.Item?.ShippingThreshold ?? 0;
                            foreach (InventoryAdjustmentCode adjcodeValidate in adjustmentCode)
                            {
                                if (adjcodeValidate.CompanyLocationType.Id == invItem.CompanyLocationType.Id)
                                    adjustmentCodeForStatus = adjcodeValidate;
                            }
                            StatusCode fromStatusCode = new StatusCode();
                            if (invItem.LotExpiration != null && adjustmentCodeForStatus != null)
                            {
                                DateTime expirationMinusThreshold = invItem.LotExpiration.Value.AddDays(-ShippingThresholdDays);
                                if (ShippingThresholdDays != 0 && (expirationMinusThreshold.Date == DateTime.Today) && threshold)
                                {
                                        InventoryItem inventoryItem1 = Repositories.Get<InventoryItem>().Retrieve(invItem.Id);
                                        if (inventoryItem1 != null && !inventoryItem1.StatusCode.Code.Equals(statuses[0].Code))
                                        {
                                           if (_logger.IsDebugEnabled) _logger.DebugFormat(" Before Change Inventory :ID:{0}:Item:{1}:Status:{2}:Customer:{3}:Company:{4}:WareHouse:{5}:Lot:{6}:LotExpDate:{7}:Location:{8}:Pallet:{9}", Convert.ToString(inventoryItem1.Id.Value), (inventoryItem1.Item?.ItemCode), (inventoryItem1.StatusCode?.Code), (inventoryItem1.Customer?.CustomerCode), (inventoryItem1.CompanyLocationType?.CompanyLocation?.Company?.CompanyCode), (inventoryItem1.CompanyLocationType?.CompanyLocationCode), (inventoryItem1.LotNumber), (inventoryItem1.LotExpiration), (inventoryItem1.Location?.LocationCode), (inventoryItem1.LicensePlate?.ParentLicensePlate?.LicensePlateCode));
                                            fromStatusCode = inventoryItem1.StatusCode;
                                            inventoryItem1.StatusCode = statuses[0];
                                            inventoryItem1.DateModified = DateTime.Now;
                                            inventoryItem1.UserModified = "eod_manager";
                                            Repositories.Get<InventoryItem>().Update(inventoryItem1);
                                            CreateStockAdjustmentItemTransaction(inventoryItem1, fromStatusCode, statuses[0], transactionType, adjustmentCodeForStatus, integrationOpenStatus, integrationClosedStatus);
                                        }
                                }
                                if ((invItem.LotExpiration <= DateTime.Today) && expire)
                                {
                                        InventoryItem inventoryItem1 = Repositories.Get<InventoryItem>().Retrieve(invItem.Id);
                                        if (inventoryItem1 != null && !inventoryItem1.StatusCode.Code.Equals(statuses[0].Code))
                                        {
                                            if (_logger.IsDebugEnabled) _logger.DebugFormat(" Before Change Inventory :ID:{0}:Item:{1}:Status:{2}:Customer:{3}:Company:{4}:WareHouse:{5}:Lot:{6}:LotExpDate:{7}:Location:{8}:Pallet:{9}", Convert.ToString(inventoryItem1.Id.Value), (inventoryItem1.Item?.ItemCode), (inventoryItem1.StatusCode?.Code), (inventoryItem1.Customer?.CustomerCode), (inventoryItem1.CompanyLocationType?.CompanyLocation?.Company?.CompanyCode), (inventoryItem1.CompanyLocationType?.CompanyLocationCode), (inventoryItem1.LotNumber), (inventoryItem1.LotExpiration), (inventoryItem1.Location?.LocationCode), (inventoryItem1.LicensePlate?.ParentLicensePlate?.LicensePlateCode));
                                            fromStatusCode = inventoryItem1.StatusCode;
                                            inventoryItem1.StatusCode = statuses[0];
                                            inventoryItem1.DateModified = DateTime.Now;
                                            inventoryItem1.UserModified = "eod_manager";
                                            Repositories.Get<InventoryItem>().Update(inventoryItem1);
                                            CreateStockAdjustmentItemTransaction(inventoryItem1, fromStatusCode, statuses[0], transactionType, adjustmentCodeForStatus, integrationOpenStatus, integrationClosedStatus);
                                            
                                        }
                                }
                            }
                            else
                            {
                                if (_logger.IsDebugEnabled) _logger.DebugFormat("         » Passed Adjustment code is not valid:{0} with Companycode {1}", adjcode, invItem.CompanyLocationType.CompanyLocationCode);
                            }
                        #endregion

                    }
                        catch (Exception ex)
                        {
                            if (_logger.IsErrorEnabled) _logger.ErrorFormat("Transaction failed for the inventory : {0} , Error occured : {1}.", invItem.Id, Errors.GetError(ex));
                        }
                }
            }
            else
            {
                if (_logger.IsDebugEnabled) _logger.DebugFormat("         » Passed status code is not valid:{0}", invStat);
            }
        }
        private void CreateStockAdjustmentItemTransaction(InventoryItem inventoryItem, StatusCode statusCodeFrom, StatusCode statusCodeTo, IList<TransactionType> transactionType, InventoryAdjustmentCode inventoryAdjustmentCode,StatusCode integrationOpenStatus, StatusCode integrationClosedStatus)
        {
            if (transactionType != null && transactionType.Count > 0)
            {
                ItemTransaction itemTransaction = new ItemTransaction();
                itemTransaction.TransactionType = transactionType[0];
                itemTransaction.InventoryItem = inventoryItem;
                itemTransaction.LocationTo = inventoryItem.Location;
                itemTransaction.LocationFrom = inventoryItem.Location;
                itemTransaction.Item = inventoryItem.Item;
                itemTransaction.Quantity = Converter.ToDecimal(inventoryItem.Quantity);
                itemTransaction.QuantityFrom = Converter.ToDecimal(inventoryItem.Quantity);
                itemTransaction.StatusCode = statusCodeTo;
                itemTransaction.StatusCodeFrom = statusCodeFrom;
                itemTransaction.SerialNumber = inventoryItem.SerialNumber;
                itemTransaction.LotNumber = inventoryItem.LotNumber;
                itemTransaction.Occurred = DateTime.Now;
                itemTransaction.UserCreated = "eod_manager";
                itemTransaction.DateCreated = DateTime.Now;
                itemTransaction.InventoryAdjustmentCode = inventoryAdjustmentCode;
                itemTransaction.UnitOfMeasure = inventoryItem.UnitOfMeasure;

                if (inventoryAdjustmentCode != null)
                {
                    if ("N".Equals(inventoryAdjustmentCode.SendToHost))
                        itemTransaction.IntegrationStatusCode = integrationClosedStatus;
                    else if ("Y".Equals(inventoryAdjustmentCode.SendToHost))
                        itemTransaction.IntegrationStatusCode = integrationOpenStatus;
                }
                Repositories.Get<ItemTransaction>().Add(itemTransaction);
                if (_logger.IsDebugEnabled) _logger.DebugFormat(" After Change Inventory :ID:{0}:Item:{1}:Status:{2}:AdjustmentCode:{3}", Convert.ToString(inventoryItem.Id.Value), (inventoryItem.Item?.ItemCode), (inventoryItem.StatusCode?.Code), (inventoryAdjustmentCode.Code));
            }
            else
            {
                if (_logger.IsDebugEnabled) _logger.DebugFormat("         » Tranasction Type Code of Stock Adjustment(SA) with Functional Area Code Stock Adjustment(SA) is not available to create an Item Transcation");
            }
        }
        private IList<StatusCode> GetStatusCode(string statuscode)
        {
            if (string.IsNullOrEmpty(statuscode)) return null;
            IList<StatusCode> statuscodeTo = null;

            DetachedCriteria criteriaStatusCode = DetachedCriteria.For<StatusCode>()
                .CreateAlias("FunctionalAreaCode", "fac")
                .SetProjection(Projections.ProjectionList()
                .Add(Projections.Property("Id"), "Id")
                .Add(Projections.Property("Code"), "Code")
                .Add(Projections.Property("Active"), "Active")
                .Add(Projections.Property("Version"), "Version"))
                .Add(Restrictions.Eq("fac.Code", "I"))
                .Add(Restrictions.Eq("Code", statuscode))
            .SetResultTransformer(Transformers.AliasToBean<StatusCode>());

            IList<StatusCode> inventoryStatus = Repositories.Get<StatusCode>().List(criteriaStatusCode);

            if (inventoryStatus != null && inventoryStatus.Count > 0)
                statuscodeTo = inventoryStatus;

            return statuscodeTo;
        }

        public static IList<StatusCode> GetOpenAndClosedStatusWithFACIntegration()
        {
            string[] statusList = { "O", "C" };

            DetachedCriteria criteriaStatusCode = DetachedCriteria.For<StatusCode>()
                                                                    .CreateAlias("FunctionalAreaCode", "fac")
                                                                    .SetProjection(Projections.ProjectionList()
                                                                    .Add(Projections.Property("Id"), "Id")
                                                                    .Add(Projections.Property("Code"), "Code")
                                                                    .Add(Projections.Property("Active"), "Active")
                                                                    .Add(Projections.Property("UserCreated"), "UserCreated")
                                                                    .Add(Projections.Property("Description"), "Description")
                                                                    .Add(Projections.Property("DateCreated"), "DateCreated")
                                                                    .Add(Projections.Property("Version"), "Version"))
                                                                    .Add(Restrictions.Eq("fac.Code", "IN"))
                                                                    .Add(Restrictions.In("Code", statusList))
                                                                    .SetResultTransformer(Transformers.AliasToBean<StatusCode>());

            IList<StatusCode> statuses = Repositories.Get<StatusCode>().List(criteriaStatusCode);

            return statuses;
        }
        #endregion
    }
}