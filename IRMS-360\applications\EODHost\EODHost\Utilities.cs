﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Runtime.Serialization.Json;
using System.Text;

using Newtonsoft.Json.Linq;
using NHibernate.Criterion;
using NHibernate.Transform;

using Upp.Irms.Core;
using Upp.Irms.Domain;
using Upp.Irms.Constants;
using NHibernate.SqlCommand;
using System.Reflection;
using System.Text.RegularExpressions;

namespace Upp.Irms.EOD.Host
{
    public class Utilities
    {
        [Obsolete("This method will soon be deprecated. Use 'SessionHelpers.GetBusinessRuleValue' instead.")]
        public static string GetWarehouseSpecificBusinessRuleValue(string ruleCode, string functionalAreaCode, string companyLocationTypeCode)
        {
            CompanyLocationType companyLocationType = GetCompanyLocationType(null, companyLocationTypeCode).FirstOrDefault();
            return GetBusinessRuleValue(ruleCode, functionalAreaCode, companyLocationType);
        }

        public static string GetBusinessRuleValue(string businessRuleCode, string functionalAreaCode, CompanyLocationType warehouse, int? customerId = null, string customerCode = null)
        {
            string parameterValue = null;
            //
            if (warehouse.CompanyLocation == null || warehouse.CompanyLocation.Company == null)
            {
                DetachedCriteria detachedCriteriaWarehouse = DetachedCriteria.For<CompanyLocationType>()
                    .CreateAlias("CompanyLocation", "CompanyLocation")
                    .CreateAlias("CompanyLocation.Company", "Company")
                    .CreateAlias("LocationType", "LocationType")
                    .Add(Restrictions.Eq("Id", warehouse.Id))
                    .Add(Restrictions.Eq("LocationType.LocationTypeCode", "W"))
                    .Add(Restrictions.Eq("Active", "A"))
                    .SetMaxResults(1);
                warehouse = Repositories.Get<CompanyLocationType>().List(detachedCriteriaWarehouse).FirstOrDefault();
            }
            //
            DetachedCriteria parameterValueQuery = DetachedCriteria.For<BusinessRuleDetail>()
                        .CreateAlias("BusinessRuleParameterValue", "BusinessRuleParameterValue")
                        .CreateAlias("BusinessRuleParameterValue.BusinessRuleParameter", "BusinessRuleParameter")
                        .CreateAlias("BusinessRuleParameter.BusinessRule", "BusinessRule")
                        .CreateAlias("BusinessRule.FunctionalAreaCode", "FunctionalAreaCode")
                        .CreateAlias("Customer", "Customer", JoinType.LeftOuterJoin)
                        .SetProjection(Projections.ProjectionList()
                            .Add(Projections.Property("BusinessRule.CustomerDefined"), "CustomerDefined")
                            .Add(Projections.Property("BusinessRuleParameterValue.ParameterValue"), "ParameterValue")
                            .Add(Projections.Property("Company.Id"), "CompanyId")
                            .Add(Projections.Property("CompanyLocationType.Id"), "CompanyLocationTypeId")
                            .Add(Projections.Property("Customer.Id"), "CustomerId")
                            .Add(Projections.Property("Customer.CustomerCode"), "CustomerCode"))
                        .Add(Restrictions.Eq("BusinessRule.BusinessRuleCode", businessRuleCode))
                        .Add(Restrictions.Eq("BusinessRule.Active", "A"))
                        .Add(Restrictions.Eq("BusinessRuleParameterValue.Active", "A"))
                        .Add(Restrictions.Eq("BusinessRuleParameterValue.Selected", "Y"))
                        .Add(Restrictions.Eq("BusinessRuleParameter.Active", "A"))
                        .Add(Restrictions.Eq("Active", "A"))
                        .SetResultTransformer(Transformers.AliasToBean<BusinessRuleParameterValue>());
            if (!warehouse.CustomerSpecificData)
                parameterValueQuery = parameterValueQuery.Add(Restrictions.IsNull("Customer"));
            else
            {
                var holdOr = Restrictions.Disjunction();
                if (customerId != null && customerId > 0 && warehouse.CustomerSpecificData)
                    holdOr.Add(Restrictions.Eq("Customer.Id", customerId));
                else if (!string.IsNullOrEmpty(customerCode) && warehouse.CustomerSpecificData)
                    holdOr.Add(Restrictions.Eq("Customer.CustomerCode", customerCode));
                holdOr.Add(Restrictions.IsNull("Customer"));
                parameterValueQuery.Add(holdOr);
            }
            //
            if (!string.IsNullOrEmpty(functionalAreaCode)) parameterValueQuery = parameterValueQuery.Add(Restrictions.Eq("FunctionalAreaCode.Code", functionalAreaCode));
            //
            if (warehouse != null)
            {
                //Get company level and warehouse level parameter values
                var holdAnd = Restrictions.Conjunction();
                holdAnd.Add(Restrictions.Eq("Company.Id", warehouse.CompanyLocation.Company.Id));
                holdAnd.Add(Restrictions.IsNull("CompanyLocationType"));
                var holdOr = Restrictions.Disjunction();
                holdOr.Add(Restrictions.Eq("CompanyLocationType.Id", warehouse.Id));
                holdOr.Add(holdAnd);
                parameterValueQuery = parameterValueQuery.Add(holdOr);
            }
            List<BusinessRuleParameterValue> businessRuleParameterValues = Repositories.Get<BusinessRuleParameterValue>().List(parameterValueQuery).ToList<BusinessRuleParameterValue>();
            //
            BusinessRuleParameterValue businessRuleParameterValue = new BusinessRuleParameterValue();
            if (businessRuleParameterValues != null && businessRuleParameterValues.Count > 0)
            {
                if (warehouse.CustomerSpecificData)
                    businessRuleParameterValue = businessRuleParameterValues.Where(x => "Y".Equals(x.CustomerDefined, StringComparison.InvariantCultureIgnoreCase) && x.CompanyLocationTypeId == warehouse.Id && (x.CustomerId == customerId || (customerCode != null && customerCode.Equals(x.CustomerCode, StringComparison.InvariantCultureIgnoreCase)))).FirstOrDefault();
                if ((warehouse.CustomerSpecificData && businessRuleParameterValue == null) || !warehouse.CustomerSpecificData)
                {
                    businessRuleParameterValue = businessRuleParameterValues.Where(x => x.CompanyLocationTypeId == warehouse.Id).FirstOrDefault();
                    if (businessRuleParameterValue == null)
                        businessRuleParameterValue = businessRuleParameterValues.Where(x => x.CompanyId == warehouse.CompanyLocation.Company.Id && Convert.ToInt32(x.CompanyLocationTypeId) <= 0).FirstOrDefault();
                }
                if (businessRuleParameterValue != null)
                    parameterValue = businessRuleParameterValue.ParameterValue;
            }
            //
            return parameterValue;
        }

        public static List<BusinessRuleParameterValue> GetBusinessRuleValues(List<string> businessRuleCodes,List<int?> companyTypeIds, string functionalAreaCode = null, int? customerId = null, string customerCode = null, List<int> customerIds = null, bool forAllCustomers = false)
        {
            List<CompanyLocationType> warehouses = GetCompanyLocationType(companyTypeIds);
            Boolean customerSpecificBR11136 = warehouses.Count > 0 ? warehouses[0].CustomerSpecificData : false;
            Boolean isCustomerSpecific = (customerId != null && customerId > 0) || !string.IsNullOrEmpty(customerCode) || (customerIds != null && customerIds.Count > 0);

            DetachedCriteria parameterValueQuery = DetachedCriteria.For<BusinessRuleDetail>()
                        .CreateAlias("BusinessRuleParameterValue", "BusinessRuleParameterValue")
                        .CreateAlias("BusinessRuleParameterValue.BusinessRuleParameter", "BusinessRuleParameter")
                        .CreateAlias("BusinessRuleParameter.BusinessRule", "BusinessRule")
                        .CreateAlias("BusinessRule.FunctionalAreaCode", "FunctionalAreaCode")
                        .CreateAlias("BusinessRuleParameterValue.Parameter", "Parameter")
                        .CreateAlias("Customer", "Customer", JoinType.LeftOuterJoin)
                        .SetProjection(Projections.ProjectionList()
                            .Add(Projections.Property("BusinessRule.BusinessRuleCode"), "BusinessRuleCode")
                            .Add(Projections.Property("BusinessRule.CustomerDefined"), "CustomerDefined")
                            .Add(Projections.Property("BusinessRuleParameterValue.ParameterValue"), "ParameterValue")
                            .Add(Projections.Property("Company.Id"), "CompanyId")
                            .Add(Projections.Property("CompanyLocationType.Id"), "CompanyLocationTypeId")
                            .Add(Projections.Property("Customer.Id"), "CustomerId")
                            .Add(Projections.Property("Customer.CustomerCode"), "CustomerCode")
                            .Add(Projections.Property("FunctionalAreaCode.Code"), "FunctionalAreaCodeValue")
                            .Add(Projections.Property("Parameter.ParameterCode"), "ParameterCode"))
                        .Add(Restrictions.Eq("BusinessRule.Active", "A"))
                        .Add(Restrictions.Eq("BusinessRuleParameterValue.Active", "A"))
                        .Add(Restrictions.Eq("BusinessRuleParameterValue.Selected", "Y"))
                        .Add(Restrictions.Eq("BusinessRuleParameter.Active", "A"))
                        .Add(Restrictions.Eq("Active", "A"))
                        .SetResultTransformer(Transformers.AliasToBean<BusinessRuleParameterValue>());
            //
            if (!string.IsNullOrEmpty(functionalAreaCode)) parameterValueQuery = parameterValueQuery.Add(Restrictions.Eq("FunctionalAreaCode.Code", functionalAreaCode));
            //
            if (customerIds == null || customerIds.Count == 0)
                customerIds = new List<int>();
            if (customerId != null && customerId > 0)
                customerIds.Add(Convert.ToInt32(customerId));
            if (!forAllCustomers)
            {
                if (!customerSpecificBR11136)
                    parameterValueQuery = parameterValueQuery.Add(Restrictions.IsNull("Customer"));
                else
                {
                    var holdOr = Restrictions.Disjunction();
                    if (customerIds != null && customerIds.Count > 0 && customerSpecificBR11136)
                        holdOr.Add(Restrictions.In("Customer.Id", customerIds));
                    else if (!string.IsNullOrEmpty(customerCode) && customerSpecificBR11136)
                        holdOr.Add(Restrictions.Eq("Customer.CustomerCode", customerCode));
                    holdOr.Add(Restrictions.IsNull("Customer"));
                    parameterValueQuery.Add(holdOr);
                }
            }
            //
            if (warehouses.Count > 0)
            {
                //Get company level and warehouse level parameter values
                var holdAnd = Restrictions.Conjunction();
                holdAnd.Add(Restrictions.In("Company.Id", warehouses.Select(x => x.CompanyLocation.Company.Id).ToList()));
                holdAnd.Add(Restrictions.IsNull("CompanyLocationType"));
                var holdOr = Restrictions.Disjunction();
                holdOr.Add(Restrictions.In("CompanyLocationType.Id", warehouses.Select(x => x.Id).ToList()));
                holdOr.Add(holdAnd);
                parameterValueQuery = parameterValueQuery.Add(holdOr);
            }
            //
            if (businessRuleCodes != null && businessRuleCodes.Count > 0)
            {
                parameterValueQuery = parameterValueQuery.Add(Restrictions.In("BusinessRule.BusinessRuleCode", businessRuleCodes));
            }
            IList<BusinessRuleParameterValue> businessRuleParameterValues = Repositories.Get<BusinessRuleParameterValue>().List(parameterValueQuery).ToList<BusinessRuleParameterValue>();
            //
            List<string> lstBusinessRuleCodes = lstBusinessRuleCodes = businessRuleParameterValues.Where(x => warehouses.Select(y => y.Id).ToList().Contains(x.CompanyLocationTypeId)).Select(x => x.BusinessRuleCode).Distinct().ToList();
            businessRuleParameterValues = businessRuleParameterValues.Where(x => (warehouses.Select(y => y.Id).ToList().Contains(x.CompanyLocationTypeId) && lstBusinessRuleCodes.Contains(x.BusinessRuleCode)) || (warehouses.Select(y => y.CompanyLocation.Company.Id).ToList().Contains(x.CompanyId) && !lstBusinessRuleCodes.Contains(x.BusinessRuleCode))).ToList();
            //
            if (customerSpecificBR11136 && !forAllCustomers && isCustomerSpecific)
            {
                List<string> lstCustomerSpecificBR = businessRuleParameterValues.Where(x => "Y".Equals(x.CustomerDefined, StringComparison.InvariantCultureIgnoreCase) && (customerIds.Contains(x.CustomerId.HasValue ? x.CustomerId.Value : 0)) || (customerCode != null && customerCode.Equals(x.CustomerCode, StringComparison.InvariantCultureIgnoreCase))).Select(x => x.BusinessRuleCode).Distinct().ToList();
                businessRuleParameterValues = businessRuleParameterValues.Where(x => (x.CustomerId > 0 && lstCustomerSpecificBR.Contains(x.BusinessRuleCode)) || ((x.CustomerId.HasValue ? x.CustomerId : 0) <= 0 && !lstCustomerSpecificBR.Contains(x.BusinessRuleCode))).ToList();
            }
            //
            return businessRuleParameterValues.ToList();
        }

        public static bool GetBooleanValue(string value)
        {
            bool flag = false;

            if (string.IsNullOrEmpty(value)) return flag;

            if (value.ToUpper().Equals("YES") || value.ToUpper().Equals("Y") || value.ToUpper().Equals("1") || value.ToUpper().Equals("T") || value.ToUpper().Equals("TRUE"))
                flag = true;
            else if (value.ToUpper().Equals("NO") || value.ToUpper().Equals("N") || value.ToUpper().Equals("0") || value.ToUpper().Equals("F") || value.ToUpper().Equals("FALSE"))
                flag = false;

            return flag;
        }

        public static IList<StatusCode> GetOpenAndClosedStatusWithFACIntegration()
        {
            string[] statusList = { "O", "C" };

            DetachedCriteria criteriaStatusCode = DetachedCriteria.For<StatusCode>()
                                                                    .CreateAlias("FunctionalAreaCode", "fac")
                                                                    .SetProjection(Projections.ProjectionList()
                                                                    .Add(Projections.Property("Id"), "Id")
                                                                    .Add(Projections.Property("Code"), "Code")
                                                                    .Add(Projections.Property("Active"), "Active")
                                                                    .Add(Projections.Property("UserCreated"), "UserCreated")
                                                                    .Add(Projections.Property("Description"), "Description")
                                                                    .Add(Projections.Property("DateCreated"), "DateCreated")
                                                                    .Add(Projections.Property("Version"), "Version"))
                                                                    .Add(Restrictions.Eq("fac.Code", "IN"))
                                                                    .Add(Restrictions.In("Code", statusList))
                                                                    .SetResultTransformer(Transformers.AliasToBean<StatusCode>());

            IList<StatusCode> statuses = Repositories.Get<StatusCode>().List(criteriaStatusCode);

            return statuses;
        }

        public static int Max(int x, int y, int z)
        {
            // Or inline it as x < y ? (y < z ? z : y) : (x < z ? z : x);
            // Time it before micro-optimizing though!
            return Math.Max(x, Math.Max(y, z));
        }

        public static string GetFieldValue(string property, Entity entity)
        {
            string result = string.Empty;
            try
            {
                property = property.Remove(0, 1);
                property = property.Remove(property.Length - 1, 1);

                result = Convert.ToString(Utilities.GetValueByProperty(property, entity));
                return result;
            }
            catch
            {
                return result;
            }
        }

        public static Object GetValueByProperty(String propertyName, Object fieldValue)
        {
            foreach (String part in propertyName.Split('.'))
            {
                if (fieldValue == null)
                { return null; }
                Type type = fieldValue.GetType();
                PropertyInfo info = type.GetProperty(part);
                if (info == null)
                { return null; }
                fieldValue = info.GetValue(fieldValue, null);
            }
            return fieldValue;
        }

        public static List<string> ExtractString(string data, string startTag, string endTag)
        {
            List<string> fields = new List<string>();
            string pattern = startTag + @"(.*?)" + endTag;
            Regex reg = new Regex(pattern);

            MatchCollection match = reg.Matches(data);

            for (int i = 0; i < match.Count; i++)
            {
                fields.Add(match[i].Value);
            }
            return fields;
        }

        public static string GetFileName(string fileNameFormat, string companyCode, string warehouse)
        {
            string fileName = string.Empty;
            String fieldBeginFlag = "{";
            String fieldEndFlag = "}";

            List<string> fileNameElements = Utilities.ExtractString(fileNameFormat, fieldBeginFlag, fieldEndFlag);
            if (fileNameElements != null && fileNameElements.Count > 0)
            {
                foreach (var fileNameElement in fileNameElements)
                {
                    if (fileNameElement.Equals("{Company}", StringComparison.InvariantCultureIgnoreCase))
                        fileNameFormat = fileNameFormat.Replace(fileNameElement, companyCode);

                    if (fileNameElement.Equals("{Warehouse}", StringComparison.InvariantCultureIgnoreCase))
                        fileNameFormat = fileNameFormat.Replace(fileNameElement, warehouse);

                    if (fileNameElement.ToUpper().Contains("{DATETIME"))
                    {
                        int count = fileNameElement.Count();
                        string dateTime = fileNameElement.Substring(1, count - 2);
                        string[] dateTimeFormats = dateTime.Split(':');
                        if (dateTimeFormats != null && dateTimeFormats.Count() > 1)
                        {
                            fileNameFormat = fileNameFormat.Replace(fileNameElement, DateTime.Now.ToString(dateTimeFormats[1]));
                        }
                        else
                            fileNameFormat = fileNameFormat.Replace(fileNameElement, DateTime.Now.ToString("yyyyMMdd-HHmmss"));
                    }
                }
            }
            return fileNameFormat;
        }

        public static StatusCode GetStatusCode(string statusCodeCode, string functionalAreaCode)
        {
            DetachedCriteria criteriaTransactionType = DetachedCriteria.For<StatusCode>()
           .CreateAlias("FunctionalAreaCode", "FunctionalAreaCode")
           .Add(Restrictions.Eq("FunctionalAreaCode.Code", functionalAreaCode))
           .Add(Restrictions.Eq("Code", statusCodeCode))
                                                      .SetProjection(Projections.ProjectionList()
                                                      .Add(Projections.Property("Id"), "Id")
                                                      .Add(Projections.Property("Version"), "Version"))
                                                      .SetResultTransformer(Transformers.AliasToBean<StatusCode>()).SetMaxResults(1);

            return Repositories.Get<StatusCode>().List(criteriaTransactionType).FirstOrDefault();
        }

        public static TransactionType GetTransactionType(string transactionTypeCode, string functionalAreaCode)
        {

            DetachedCriteria criteriaTransactionType = DetachedCriteria.For<TransactionType>()
                .CreateAlias("FunctionalAreaCode", "FunctionalAreaCode")
                .Add(Restrictions.Eq("FunctionalAreaCode.Code", functionalAreaCode))
                .Add(Restrictions.Eq("TransactionTypeCode", transactionTypeCode))
                                                           .SetProjection(Projections.ProjectionList()
                                                           .Add(Projections.Property("Id"), "Id")
                                                           .Add(Projections.Property("Version"), "Version"))
                                                           .SetResultTransformer(Transformers.AliasToBean<TransactionType>()).SetMaxResults(1);

            return Repositories.Get<TransactionType>().List(criteriaTransactionType).FirstOrDefault();
        }

        public static IList<ParticipantCommunication> GetAlertRecipients(List<int?> alertDefinationIds)
        {
            CommunicationRole communicationRole = GetCommunicationRole("E");
            IList<ParticipantCommunication> alertRecipients = new List<ParticipantCommunication>();

            if (communicationRole != null && alertDefinationIds != null && alertDefinationIds.Count > 0)
            {
                DetachedCriteria criteriaAlertRecipients = DetachedCriteria.For<ParticipantCommunication>()
                     .CreateAlias("AlertRecipients", "AlertRecipient")
                     .Add(Restrictions.In("AlertRecipient.AlertDefinition.Id", alertDefinationIds))
                     .Add(Restrictions.Eq("CommunicationRole.Id", communicationRole.Id))
                     .Add(Restrictions.Eq("AlertRecipient.Active", "A"))
                     .SetProjection(Projections.ProjectionList()
                     .Add(Projections.Property("CommunicationValue"), "CommunicationValue")
                     .Add(Projections.Property("PrimaryCommunication"), "PrimaryCommunication")
                     .Add(Projections.Property("AlertRecipient.AlertDefinition.Id"), "AlertDefinitionId"))
                     .SetResultTransformer(Transformers.AliasToBean<ParticipantCommunication>());

                alertRecipients = Repositories.Get<ParticipantCommunication>().List(criteriaAlertRecipients);
            }
            return alertRecipients;
        }

        public static CommunicationRole GetCommunicationRole(string communicationRoleCode)
        {
            DetachedCriteria criteriaCommunicationRole = DetachedCriteria.For<CommunicationRole>()
                     .Add(Restrictions.Eq("CommunicationRoleCode", communicationRoleCode))
                     .SetProjection(Projections.ProjectionList()
                     .Add(Projections.Property("Id"), "Id")
                     .Add(Projections.Property("Version"), "Version"))
                     .SetResultTransformer(Transformers.AliasToBean<CommunicationRole>());

            return Repositories.Get<CommunicationRole>().List(criteriaCommunicationRole).FirstOrDefault();
        }

        public static IList<AlertDefinition> GetAlertDefination(List<int?> companyLocationTypeIds, string code)
        {
            DetachedCriteria criteriaAlertDefinitions = DetachedCriteria.For<AlertDefinition>()
                    .Add(Restrictions.In("CompanyLocationType.Id", companyLocationTypeIds))
                    .Add(Restrictions.Eq("AlertDefinitionCode", code))
                    .Add(Restrictions.Eq("Active", "A"))
                    .SetProjection(Projections.ProjectionList()
                    .Add(Projections.Property("Id"), "Id")
                    .Add(Projections.Property("AlertTemplate"), "AlertTemplate")
                    .Add(Projections.Property("SubjectLine"), "SubjectLine")
                    .Add(Projections.Property("CompanyLocationType.Id"), "CompanyLocationTypeId"))
                    .SetResultTransformer(Transformers.AliasToBean<AlertDefinition>());

            return Repositories.Get<AlertDefinition>().List(criteriaAlertDefinitions);
        }

        public static String GenerateTaskCode(EntityCodes code, CompanyLocationType warehouse)
        {
            StringBuilder taskCode = new StringBuilder();
            string formatString;
            //           
            DetachedCriteria criteria = DetachedCriteria.For<EntityCode>()
                                                      .Add(new SimpleExpression("Code", CodeValue.GetCode(code), "="));
            if (warehouse != null) criteria = criteria.Add(new SimpleExpression("CompanyLocationType.Id", warehouse.Id, "="))
                                                     .SetMaxResults(1);

            IList<EntityCode> list = Repositories.Get<EntityCode>().List(criteria);

            if (list == null && list.Count == 0) throw new Exception(String.Format("Unable to find LookupPrimaryKey for '{0}'.", code));

            formatString = list[0].FormatString;

            list[0].DateModified = DateTime.Now;
            list[0].UserModified = "IRMS BL";


            String tempTaskCode = string.Empty;

            list[0].CurrentValue += list[0].IncrementValue;

            if (!string.IsNullOrEmpty(formatString)) tempTaskCode = string.Format(formatString, list[0].CurrentValue);
            else tempTaskCode = string.Format("{0}", list[0].CurrentValue);

            if (!string.IsNullOrEmpty(list[0].Prefix))
            {
                tempTaskCode = list[0].Prefix + tempTaskCode;
            }

            if (taskCode.ToString().Trim().Length > 0)
            {
                taskCode.Append("," + tempTaskCode);
            }
            else
            {
                taskCode.Append(tempTaskCode);
            }

            Repositories.Get<EntityCode>().Update(list[0]);

            return taskCode.ToString();
        }


        public static DetachedCriteria PrepareCriteriaForItems(CompanyLocationType companyLocationType, IList<string> fieldMappings, List<string> customFields = null)
        {
            DetachedCriteria query = DetachedCriteria.For<Item>().Add(Restrictions.Eq("CompanyLocationType.Id", companyLocationType.Id));

            ProjectionList projections = Projections.ProjectionList().Add(Projections.Property("Id"), "Id");
            List<String> aliases = null;
            List<String> paths = null;

            for (int count = 0; count < fieldMappings.Count; count++)
            {
                aliases = new List<String>();
                paths = new List<String>();

                string path = fieldMappings[count];
                String[] parts = path.Split('.');

                //Ignore 
                if (parts.Length == 1 && customFields.Contains(parts[0])) continue;

                for (int i = 0; i < parts.Length - 1; i++)
                {
                    if (i == 0)
                    {
                        aliases.Add(parts[0]);
                        paths.Add(parts[0]);
                    }
                    else
                    {
                        aliases.Add(String.Format("{0}_{1}", aliases[i - 1], parts[i]));
                        paths.Add(String.Format("{0}.{1}", aliases[i - 1], parts[i]));
                    }
                }

                //add to projections
                if (parts.Length == 1)
                {
                    projections.Add(Projections.Property(parts[0]), parts[0]);
                }
                else
                {
                    string field = path.Substring(path.LastIndexOf('.') + 1);
                    string property = String.Format("{0}.{1}", aliases[parts.Length - 2], field);
                    projections.Add(Projections.Property(property), field);
                }

                for (int i = 0; i < aliases.Count; i++)
                {
                    if (query.GetCriteriaByAlias(aliases[i]) != null) continue;
                    else query = query.CreateAlias(paths[i], aliases[i], NHibernate.SqlCommand.JoinType.LeftOuterJoin);
                }
            }
            query.SetProjection(projections);
            query.SetResultTransformer(Transformers.AliasToBean<Item>());

            //
            return query;
        }

        public static DetachedCriteria PrepareQueryForItemOnHandQuantity(List<int?> itemIds)
        {
            DetachedCriteria criteriaInventory = DetachedCriteria.For<InventoryItem>()
                        .CreateAlias("StatusCode", "status")
                        .CreateAlias("status.FunctionalAreaCode", "fac")
                        .Add(Restrictions.Eq("status.Code", CodeValue.GetCode(InventoryStatuses.Available)))
                        .Add(Restrictions.Eq("fac.Code", CodeValue.GetCode(FunctionalAreas.Inventory)))
                        .Add(Restrictions.In("Item.Id", itemIds))
                        .SetProjection(Projections.ProjectionList()
                            .Add(Projections.GroupProperty("Item.Id"), "Id")
                            .Add(Projections.Sum("Quantity"), "Quantity"))
                        .SetResultTransformer(Transformers.AliasToBean<InventoryItem>());

            return criteriaInventory;
        }

        public static DetachedCriteria PrepareQueryForItemDemandQuantity(List<int?> itemIds)
        {
            DetachedCriteria criteriaOrderLine = DetachedCriteria.For<OrderDetail>()
               .CreateAlias("StatusCode", "status")
               .CreateAlias("status.FunctionalAreaCode", "fac")
               .Add(Restrictions.Eq("status.Code", CodeValue.GetCode(OrderStatuses.Open)))
               .Add(Restrictions.Eq("fac.Code", CodeValue.GetCode(FunctionalAreas.Order)))
               .Add(Restrictions.In("Item.Id", itemIds))
               .SetProjection(Projections.ProjectionList()
                   .Add(Projections.GroupProperty("Item.Id"), "Id")
                   .Add(Projections.Sum("Quantity"), "Quantity"))
               .SetResultTransformer(Transformers.AliasToBean<OrderDetail>());

            return criteriaOrderLine;
        }

        public static DetachedCriteria PrepareQueryForItemNextAvailableDate(List<int?> itemIds)
        {
            DetachedCriteria detachedPOStatus = DetachedCriteria.For<PurchaseOrderStatus>("rs")
                           .CreateAlias("rs.StatusCode", "StatusCodes")
                           .SetProjection(Projections.Property("rs.Id"))
                           .Add(Expression.EqProperty("poHeader.Id", "rs.PurchaseOrderHeader.Id"))
                           .AddOrder(new Order("rs.Occurred", false))
                           .AddOrder(new Order("StatusCodes.SortOrder", false))
                           .SetMaxResults(1);

            DetachedCriteria criteriaPo = DetachedCriteria.For<PurchaseOrderDetail>()
                .CreateAlias("PurchaseOrderHeader", "poHeader")
                .CreateAlias("poHeader.PurchaseOrderStatuses", "poStatus")
                .CreateAlias("poStatus.StatusCode", "statusCodes")
                .Add(Restrictions.Eq("statusCodes.Code", CodeValue.GetCode(PurchaseStatuses.Open)))
                .Add(Subqueries.PropertyEq("poStatus.Id", detachedPOStatus))
                .Add(Restrictions.In("Item.Id", itemIds))
                .SetProjection(Projections.ProjectionList()
                   .Add(Projections.GroupProperty("Item.Id"), "Id")
                   .Add(Projections.GroupProperty("poHeader.PurchaseOrderCode"), "PurchaseOrderCode")
                   .Add(Projections.GroupProperty("poHeader.Required"), "Required"))
               .SetResultTransformer(Transformers.AliasToBean<PurchaseOrderDetail>());

            return criteriaPo;
        }

        public static DetachedCriteria PrepareQueryForInventorySnapshot(string companyLoc, string company, StatusCode availableStatus, IList<string> fieldMappings, List<string> customFields = null, string[] customerCodes = null, LocationType locationType=null)
        {
            List<String> aliases = null;
            List<String> paths = null;

            DetachedCriteria criteria = DetachedCriteria.For<Item>();

            ProjectionList projections = Projections.ProjectionList();

            projections.Add(Projections.Property("ItemCode"), "ItemCode")
                       .Add(Projections.GroupProperty("ItemCode"))
                       .Add(Projections.GroupProperty("Id"), "Id")
                       .Add(Projections.GroupProperty("c.CompanyCode"))
                       .Add(Projections.GroupProperty("lt.CompanyLocationCode"));

            criteria.CreateAlias("CompanyLocationType", "lt", JoinType.LeftOuterJoin)
                    .CreateAlias("lt.CompanyLocation", "cl", JoinType.LeftOuterJoin)
                    .CreateAlias("cl.Company", "c", JoinType.LeftOuterJoin)
                    .Add(Restrictions.Eq("Active", "A"))
                    .Add(Restrictions.Eq("lt.CompanyLocationCode", companyLoc))
                    .Add(Restrictions.Eq("c.CompanyCode", company));

            if (locationType != null)
                criteria = criteria.Add(Restrictions.Eq("lt.LocationType.Id", locationType.Id));

            if (customerCodes != null && customerCodes.Count() > 0)
            {
                criteria.CreateAlias("Customer", "customer")
                    .Add(Restrictions.In("customer.CustomerCode", customerCodes));
            }
           
            for (int count = 0; count < fieldMappings.Count; count++)
            {
                aliases = new List<String>();
                paths = new List<String>();

                string path = fieldMappings[count];
                String[] parts = path.Split('.');

                //Ignore 
                if (parts.Length == 1 && customFields.Contains(parts[0])) continue;

                for (int i = 0; i < parts.Length - 1; i++)
                {
                    if (i == 0)
                    {
                        aliases.Add(parts[0]);
                        paths.Add(parts[0]);
                    }
                    else
                    {
                        aliases.Add(String.Format("{0}_{1}", aliases[i - 1], parts[i]));
                        paths.Add(String.Format("{0}.{1}", aliases[i - 1], parts[i]));
                    }
                }

                //add to projections
                if (parts.Length == 1)
                {
                    projections.Add(Projections.Property(parts[0]), parts[0]);
                    projections.Add(Projections.GroupProperty(parts[0]));
                }
                else
                {
                    string field = path.Substring(path.LastIndexOf('.') + 1);
                    string property = String.Format("{0}.{1}", aliases[parts.Length - 2], field);
                    projections.Add(Projections.Property(property), field);
                    projections.Add(Projections.GroupProperty(property));
                }

                for (int i = 0; i < aliases.Count; i++)
                {
                    if (criteria.GetCriteriaByAlias(aliases[i]) != null) continue;
                    else criteria = criteria.CreateAlias(paths[i], aliases[i], NHibernate.SqlCommand.JoinType.LeftOuterJoin);
                }
            }
            criteria.SetProjection(projections);
            criteria.SetResultTransformer(Transformers.AliasToBean<Item>());
            //
            return criteria;

        }

        public static DetachedCriteria PrepareQueryForOrderDetailSnapshot(string companyLoc, string company, IList<string> fieldMappings, List<string> customFields = null, string[] customerCodes = null, List<int?> excludeStatusCodes = null, List<int?> includedStatusCodes = null, LocationType locationType=null)
        {
            List<String> aliases = null;
            List<String> paths = null;

            DetachedCriteria detachedShippedQuantity = DetachedCriteria.For<InventoryPick>()                           
                           .SetProjection(Projections.Sum("Quantity"))
                           .Add(Restrictions.EqProperty("OrderDetail.Id", "_root.Id"))                          
                           .SetMaxResults(1);
           
            DetachedCriteria criteria = DetachedCriteria.For<OrderDetail>("_root");
            ProjectionList projections = Projections.ProjectionList()
                                        .Add(Projections.Property("OrderHeader.Id"), "Id")
                                        .Add(Projections.Conditional(Restrictions.IsNull("OrderHeader.Ordered"), Projections.Property("OrderHeader.DateCreated"), Projections.Property("OrderHeader.Ordered")), "DateCreated")
                                        .Add(Projections.Conditional(Restrictions.IsNull("DateModified"), Projections.Property("DateCreated"), Projections.Property("DateModified")), "StatusDate")
                                        .Add(Projections.Property("StatusCode.Description"), "StatusDescription")
                                        .Add(Projections.SubQuery(detachedShippedQuantity), "ShippedQuantity");

            criteria.CreateAlias("OrderHeader", "OrderHeader")
                    .CreateAlias("OrderHeader.StatusCode", "HeaderStatus")
                    .CreateAlias("Item", "Item")
                    .CreateAlias("StatusCode", "StatusCode")
                    .CreateAlias("OrderHeader.CompanyLocationType", "lt", JoinType.LeftOuterJoin)
                    .CreateAlias("lt.CompanyLocation", "cl", JoinType.LeftOuterJoin)
                    .CreateAlias("cl.Company", "c", JoinType.LeftOuterJoin)
                    .Add(Restrictions.Eq("lt.CompanyLocationCode", companyLoc))
                    .Add(Restrictions.Eq("c.CompanyCode", company));

            if (locationType != null)
                criteria = criteria.Add(Restrictions.Eq("lt.LocationType.Id", locationType.Id));

            if (excludeStatusCodes != null && excludeStatusCodes.Count > 0)
                criteria.Add(!Restrictions.In("HeaderStatus.Id", excludeStatusCodes));

            if (includedStatusCodes != null && includedStatusCodes.Count > 0)
                criteria.Add(Restrictions.In("HeaderStatus.Id", includedStatusCodes));

            if (customerCodes != null)
            {
                criteria.CreateAlias("OrderHeader.Customer", "customer")
                  .Add(Restrictions.In("customer.CustomerCode", customerCodes));
            }

            for (int count = 0; count < fieldMappings.Count; count++)
            {
                aliases = new List<String>();
                paths = new List<String>();

                string path = fieldMappings[count];
                String[] parts = path.Split('.');

                //Ignore 
                if (parts.Length == 1 && customFields.Contains(parts[0])) continue;

                for (int i = 0; i < parts.Length - 1; i++)
                {
                    if (i == 0)
                    {
                        aliases.Add(parts[0]);
                        paths.Add(parts[0]);
                    }
                    else
                    {
                        aliases.Add(String.Format("{0}_{1}", aliases[i - 1], parts[i]));
                        paths.Add(String.Format("{0}.{1}", aliases[i - 1], parts[i]));
                    }
                }

                //add to projections
                if (parts.Length == 1)
                {
                    projections.Add(Projections.Property(parts[0]), parts[0]);
                }
                else
                {
                    string field = path.Substring(path.LastIndexOf('.') + 1);
                    string property = String.Format("{0}.{1}", aliases[parts.Length - 2], field);
                    projections.Add(Projections.Property(property), field);
                }

                for (int i = 0; i < aliases.Count; i++)
                {
                    if (criteria.GetCriteriaByAlias(aliases[i]) != null) continue;
                    else criteria = criteria.CreateAlias(paths[i], aliases[i], NHibernate.SqlCommand.JoinType.LeftOuterJoin);
                }
            }
            criteria.SetProjection(projections);
            criteria.SetResultTransformer(Transformers.AliasToBean<OrderDetail>());
            //
            return criteria;

        }

        public static JArray CommunicateRestService(string dataString, string integrationURL)
        {
            WebClient webClientProxy = new WebClient();

            webClientProxy.Headers["Content-type"] = "application/json";

            MemoryStream mStream = new MemoryStream();

            DataContractJsonSerializer serializerToUplaod = new DataContractJsonSerializer(typeof(string));

            serializerToUplaod.WriteObject(mStream, dataString);

            byte[] data = webClientProxy.DownloadData(integrationURL + dataString);

            MemoryStream stream = new MemoryStream(data);
            DataContractJsonSerializer obj = new DataContractJsonSerializer(typeof(string));

            var result = obj.ReadObject(stream) as string;            

            JArray row = JArray.Parse(result);

            return row;
          
        }

        public static List<BusinessRuleParameterValue> GetProviderSpecificBusinessRules(List<string> businessRuleCodes, List<int?> providerIds)
        {
            DetachedCriteria criteriaBusinessRuleParameterValue = DetachedCriteria.For<BusinessRuleParameterValue>()
                                        .CreateAlias("BusinessRuleDetails", "BusinessRuleDetails")
                                        .CreateAlias("BusinessRuleDetails.BusinessRuleParameterValue", "Detail_ParamValue")
                                        .CreateAlias("BusinessRuleParameter", "BusinessRuleParameter")
                                        .CreateAlias("BusinessRuleParameter.BusinessRule", "BusinessRule")
                                        .CreateAlias("BusinessRule.FunctionalAreaCode", "FunctionalAreaCode")
                                        .CreateAlias("Parameter", "Parameter")
                                        .SetProjection(Projections.ProjectionList()
                                        .Add(Projections.Property("BusinessRule.BusinessRuleCode"), "BusinessRuleCode")
                                        .Add(Projections.Property("BusinessRuleDetails.Provider.Id"), "ProviderId")
                                        .Add(Projections.Property("Parameter.ParameterCode"), "ParameterCode")
                                        .Add(Projections.Property("ParameterValue"), "ParameterValue"))
                                        .Add(Restrictions.In("BusinessRuleDetails.Provider.Id", providerIds))
                                        .Add(Restrictions.IsNotNull("BusinessRuleDetails.Provider"))
                                        .Add(Restrictions.Eq("BusinessRuleParameter.Active", "A"))
                                        .Add(Restrictions.Eq("BusinessRule.Active", "A"))
                                        .Add(Restrictions.Eq("Active", "A"))
                                        .Add(Restrictions.Eq("Selected", "Y"))
                                        .Add(Restrictions.Eq("BusinessRuleDetails.Active", "A"))
                                        .Add(Restrictions.EqProperty("Id", "Detail_ParamValue.Id"))
                                        .SetResultTransformer(Transformers.AliasToBean<BusinessRuleParameterValue>());

            if (businessRuleCodes != null && businessRuleCodes.Count > 0)
            {
                criteriaBusinessRuleParameterValue.Add(Restrictions.In("BusinessRule.BusinessRuleCode", businessRuleCodes));
            }
            List<BusinessRuleParameterValue> businessRuleParamValues = Repositories.Get<BusinessRuleParameterValue>().List(criteriaBusinessRuleParameterValue).ToList<BusinessRuleParameterValue>();

            return businessRuleParamValues;
        }

        public static string GetParameterValue(string businessRuleCode, string functionalAreaCode, int providerId = 0)
        {
            string parameterValue = null;
            DetachedCriteria detachedBRParamValues = null;

            detachedBRParamValues = PrepareDetachedForBRParamValue(businessRuleCode, providerId);

            DetachedCriteria criteriaBusinessRuleParameterValue = DetachedCriteria.For<BusinessRuleParameterValue>()
                       .CreateAlias("BusinessRuleParameter", "BusinessRuleParameter")
                        .CreateAlias("BusinessRuleParameter.BusinessRule", "BusinessRule")
                        .CreateAlias("BusinessRule.FunctionalAreaCode", "FunctionalAreaCode")
                        .SetProjection(Projections.ProjectionList().Add(Projections.Property("ParameterValue"), "ParameterValue"))
                        .Add(new SimpleExpression("BusinessRule.BusinessRuleCode", businessRuleCode, "="))
                        .Add(new SimpleExpression("BusinessRule.Active", "A", "="))
                        .Add(new SimpleExpression("Active", "A", "="))
                        .Add(new SimpleExpression("Selected", "Y", "="))
                        .Add(Restrictions.Eq("BusinessRuleParameter.Active", "A"))
                        .SetResultTransformer(Transformers.AliasToBean<BusinessRuleParameterValue>());

            if (!string.IsNullOrEmpty(functionalAreaCode)) criteriaBusinessRuleParameterValue = criteriaBusinessRuleParameterValue.Add(new SimpleExpression("FunctionalAreaCode.Code", functionalAreaCode, "="));

            if (detachedBRParamValues != null) criteriaBusinessRuleParameterValue = criteriaBusinessRuleParameterValue.Add(Subqueries.PropertyIn("Id", detachedBRParamValues));

            IList<BusinessRuleParameterValue> businessRuleParameterValues = Repositories.Get<BusinessRuleParameterValue>().List(criteriaBusinessRuleParameterValue).ToList<BusinessRuleParameterValue>();
            if (businessRuleParameterValues != null && businessRuleParameterValues.Count > 0)
            {
                parameterValue = businessRuleParameterValues[0].ParameterValue;
            }
            return parameterValue;
        }
        private static DetachedCriteria PrepareDetachedForBRParamValue(string businessRuleCode, int providerId = 0)
        {
            DetachedCriteria detachedBRParamValues = DetachedCriteria.For<BusinessRuleDetail>("details")
                                        .CreateCriteria("BusinessRule", "BusinessRule")
                                        .Add(Restrictions.Eq("BusinessRule.BusinessRuleCode", businessRuleCode))
                                        .Add(Restrictions.Eq("details.Active", "A"))
                                        .SetProjection(Projections.Property("BusinessRuleParameterValue.Id"));
            if (providerId != 0) detachedBRParamValues.CreateAlias("details.Provider", "Provider").Add(Restrictions.Eq("Provider.Id", providerId));

            return detachedBRParamValues;
        }

        public static List<CompanyLocationType> GetCompanyLocationType(List<int?> companyLocationIds, string companyLocationCode = null)
        {
            DetachedCriteria detachedCriteriaWarehouse = null;
            if (companyLocationIds != null && companyLocationIds.Count > 0)
            {
                detachedCriteriaWarehouse = DetachedCriteria.For<CompanyLocationType>()
                    .CreateAlias("CompanyLocation", "CompanyLocation")
                    .CreateAlias("CompanyLocation.Company", "Company")
                    .CreateAlias("LocationType", "LocationType")
                    .Add(Restrictions.In("Id", companyLocationIds))
                    .Add(Restrictions.Eq("LocationType.LocationTypeCode", "W"))
                    .Add(Restrictions.Eq("Active", "A"));
            }
            else
            {
                detachedCriteriaWarehouse = DetachedCriteria.For<CompanyLocationType>()
                    .CreateAlias("CompanyLocation", "CompanyLocation")
                    .CreateAlias("CompanyLocation.Company", "Company")
                    .CreateAlias("LocationType", "LocationType")
                    .Add(Restrictions.Eq("CompanyLocationCode", companyLocationCode))
                    .Add(Restrictions.Eq("LocationType.LocationTypeCode", "W"))
                    .Add(Restrictions.Eq("Active", "A"))
                    .SetMaxResults(1);
            }

            List<CompanyLocationType> companyLocationType = Repositories.Get<CompanyLocationType>().List(detachedCriteriaWarehouse).ToList<CompanyLocationType>();
            if (companyLocationType.Count > 0)
            {
                //Customer Specific Data
                DetachedCriteria detachedBRParamValuescds = DetachedCriteria.For<BusinessRuleDetail>("detail")
                                         .CreateCriteria("detail.BusinessRule", "BusinessRule")
                                         .Add(Restrictions.Eq("BusinessRule.BusinessRuleCode", "11136"))
                                         .Add(Restrictions.Eq("detail.Active", "A"))
                                         .Add(Restrictions.In("detail.CompanyLocationType.Id", companyLocationType.Select(x => x.Id).ToList()))
                                         .SetProjection(Projections.Property("detail.BusinessRuleParameterValue.Id"));

                DetachedCriteria detachedCriteriaBRValuesCSD = DetachedCriteria.For<BusinessRuleParameterValue>()
                    .CreateAlias("BusinessRuleParameter", "BusinessRuleParameter")
                    .CreateAlias("BusinessRuleParameter.BusinessRule", "BusinessRule")
                    .SetProjection(Projections.ProjectionList().Add(Projections.Property("ParameterValue"), "ParameterValue"))
                    .Add(Restrictions.Eq("BusinessRule.BusinessRuleCode", "11136"))
                    .Add(Restrictions.Eq("BusinessRule.Active", "A"))
                    .Add(Restrictions.Eq("Active", "A"))
                    .Add(Restrictions.Eq("Selected", "Y"))
                    .Add(Restrictions.Eq("BusinessRuleParameter.Active", "A"))
                    .Add(Subqueries.PropertyIn("Id", detachedBRParamValuescds))
                    .SetResultTransformer(Transformers.AliasToBean<BusinessRuleParameterValue>());

                IList<BusinessRuleParameterValue> businessRuleParameterValuesCSD = Repositories.Get<BusinessRuleParameterValue>().List(detachedCriteriaBRValuesCSD).ToList<BusinessRuleParameterValue>();

                if (businessRuleParameterValuesCSD != null && businessRuleParameterValuesCSD.Count > 0)
                {
                    string parameterValue = businessRuleParameterValuesCSD[0].ParameterValue;
                    companyLocationType.ForEach(x => x.CustomerSpecificData = GetBooleanValue(parameterValue));
                }
            }
            return companyLocationType;
        }
    }
}
