<?xml version="1.0" encoding="UTF-8"?>
<quartz xmlns="http://quartznet.sourceforge.net/JobSchedulingData" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
 				version="1.0"	overwrite-existing-jobs="true">

	<job>
		<job-detail>
			<name>CycleCountSetupJob</name>
			<group>Manual</group>
			<description>Job to setup cyclecount</description>
			<job-type>Upp.Irms.EOD.Host.CycleCountSetupJob,Upp.Irms.EOD.Host</job-type>
			<volatile>false</volatile>
			<durable>true</durable>
			<recover>false</recover>
			<job-data-map>
				<entry>
					<key>WAREHOUSES</key>
					<value>888:wh28;upp:upp27,wh28,02;</value>
				</entry>
        <entry>
          <key>NEXTJOB</key>
          <value>PasswordExpiration</value>
        </entry>        
      </job-data-map>
		</job-detail>
		<trigger>
			<cron>
				<name>CycleCountSetupJobTrigger</name>
				<group>Manual</group>
				<description>To run CycleCountSetup Job on manual trigger</description>
				<misfire-instruction>SmartPolicy</misfire-instruction>
				<volatile>false</volatile>
				<job-name>CycleCountSetupJob</job-name>
				<job-group>Manual</job-group>
				<cron-expression>0 10 17 ? * *</cron-expression>
			</cron>
		</trigger>
	</job>
	<job>
		<job-detail>
			<name>ShippingThresholdJob</name>
			<group>Manual</group>
			<description>Job to send expired items to Quality Control</description>
			<job-type>Upp.Irms.EOD.Host.ShippingThresholdJob,Upp.Irms.EOD.Host</job-type>
			<volatile>false</volatile>
			<durable>true</durable>
			<recover>false</recover>
			<job-data-map>
				<entry>
					<key>WAREHOUSES</key>
					<value>888:wh28;upp:upp27,wh28,02;</value>
				</entry>
        <entry>
          <key>NEXTJOB</key>
          <value>ShelfShipReportJob</value>
        </entry>
				<entry>
					<key>TITLE</key>
					<value>ShippingThreshold</value>
				</entry>
				<entry>
					<key>FORMAT</key>
					<value>P$CompanyLocationCode:W$20;P$ItemCode:W$20;P$AssetCode:W$20;P$LotNumber:W$10;P$LotExpiration:W$20;</value>
				</entry>
				<entry>
					<key>SHOWHEADER</key>
					<value>false</value>
				</entry>
				<entry>
					<key>SHOWFOOTER</key>
					<value>false</value>
				</entry>				
			</job-data-map>
		</job-detail>
		<trigger>
			<cron>
				<name>ShippingThresholdJobTrigger</name>
				<group>Manual</group>
				<description>To run ShippingThreshold job on manual trigger</description>
				<misfire-instruction>SmartPolicy</misfire-instruction>
				<volatile>false</volatile>
				<job-name>ShippingThresholdJob</job-name>
				<job-group>Manual</job-group>
				<cron-expression>0 15 17 ? * *</cron-expression>
			</cron>
		</trigger>
	</job>  
	<job>
		<job-detail>
			<name>FourWallReportJob</name>
			<group>Manual</group>
			<description>FourWallReport job</description>
			<job-type>Upp.Irms.EOD.Host.FourWallReportJob,Upp.Irms.EOD.Host</job-type>
			<volatile>false</volatile>
			<durable>true</durable>
			<recover>false</recover>
			<job-data-map>
				<entry>
					<key>WAREHOUSES</key>
					<value>888:wh28;upp:upp27,wh28,02;</value>
				</entry>
        <entry>
          <key>NEXTJOB</key>
          <value>ItemHistoryReportJob</value>
        </entry>
				<entry>
					<key>TITLE</key>
					<value>FourWall</value>
				</entry>
				<entry>
					<key>FORMAT</key>
					<value>P$CompanyCode:W$4;P$CompanyLocationCode:W$4;P$ItemCode:W$24;P$StatusCodeCode:W$1;P$Quantity:W$14:T$0:A$Right:F$F0;</value>
				</entry>
				<entry>
					<key>SHOWHEADER</key>
					<value>false</value>
				</entry>
				<entry>
					<key>SHOWFOOTER</key>
					<value>false</value>
				</entry>				
			</job-data-map>
		</job-detail>
		<trigger>
			<cron>
				<name>FourWallReportJobTrigger</name>
				<group>Manual</group>
				<description>To run FourWallReport job on manual trigger</description>
				<misfire-instruction>SmartPolicy</misfire-instruction>
				<volatile>false</volatile>
				<job-name>FourWallReportJob</job-name>
				<job-group>Manual</job-group>
				<cron-expression>0 20 17 ? * *</cron-expression>
			</cron>
		</trigger>
	</job>

	<job>
		<job-detail>
			<name>PackedCartReportJob</name>
			<group>Manual</group>
			<description>PackedCartReport job</description>
			<job-type>Upp.Irms.EOD.Host.PackedCardReportJob,Upp.Irms.EOD.Host</job-type>
			<volatile>false</volatile>
			<durable>true</durable>
			<recover>false</recover>
			<job-data-map>
				<entry>
					<key>WAREHOUSES</key>
					<value>888:wh28;upp:upp27,wh28,02;</value>
				</entry>
        <entry>
          <key>NEXTJOB</key>
          <value>ShippingThresholdJob</value>
        </entry>
				<entry>
					<key>TITLE</key>
					<value>PackedCart</value>
				</entry>
				<entry>
					<key>FORMAT</key>
					<value>P$CompanyCode:W$20;P$CompanyLocationCode:W$20;P$ItemCode:W$20;P$StatusCodeCode:W$10;P$DateCreated:W$20;</value>
				</entry>
				<entry>
					<key>SHOWHEADER</key>
					<value>false</value>
				</entry>
				<entry>
					<key>SHOWFOOTER</key>
					<value>false</value>
				</entry>				
			</job-data-map>
		</job-detail>
		<trigger>
			<cron>
				<name>PackedCartReportJobTrigger</name>
				<group>Manual</group>
				<description>To run PackedCartReport job on manual trigger</description>
				<misfire-instruction>SmartPolicy</misfire-instruction>
				<volatile>false</volatile>
				<job-name>PackedCartReportJob</job-name>
				<job-group>Manual</job-group>
				<cron-expression>0 25 17 ? * *</cron-expression>
			</cron>
		</trigger>
	</job>

	<job>
		<job-detail>
			<name>SelfShipReportJob</name>
			<group>Manual</group>
			<description>SelfShipReport job</description>
			<job-type>Upp.Irms.EOD.Host.ShelfShipReportJob,Upp.Irms.EOD.Host</job-type>
			<volatile>false</volatile>
			<durable>true</durable>
			<recover>false</recover>
			<job-data-map>
				<entry>
					<key>WAREHOUSES</key>
					<value>888:wh28;upp:upp27,wh28,02;</value>
				</entry>
        <entry>
          <key>NEXTJOB</key>
          <value>CycleCountSetupJob</value>
        </entry>
				<entry>
					<key>TITLE</key>
					<value>SelfShip</value>
				</entry>
				<entry>
					<key>FORMAT</key>
					<value>P$DateCreated:W$19;P$ItemCode:W$25;P$UserCreated:W$13;P$StatusCodeCode:W$7;P$CompanyCode:W$5;P$CompanyLocationCode:W$5;</value>
				</entry>
				<entry>
					<key>SHOWHEADER</key>
					<value>false</value>
				</entry>
				<entry>
					<key>SHOWFOOTER</key>
					<value>false</value>
				</entry>
				<entry>
					<key>ZONES</key>
					<value>01,02,03,04,05,06,07,13,19,32,49,59,S1,S2,S3,S4,S5,S6,P1,P2,P3,P4,P5,P6</value>
				</entry>				
			</job-data-map>
		</job-detail>
		<trigger>
			<cron>
				<name>SelfShipReportJobTrigger</name>
				<group>Manual</group>
				<description>To run SelfShipReport job on manual trigger</description>
				<misfire-instruction>SmartPolicy</misfire-instruction>
				<volatile>false</volatile>
				<job-name>SelfShipReportJob</job-name>
				<job-group>Manual</job-group>
				<cron-expression>0 30 17 ? * *</cron-expression>
			</cron>
		</trigger>
	</job>

	<job>
		<job-detail>
			<name>ItemHistoryReportJob</name>
			<group>Manual</group>
			<description>ItemHistoryReport job</description>
			<job-type>Upp.Irms.EOD.Host.ItemHistoryReportJob,Upp.Irms.EOD.Host</job-type>
			<volatile>false</volatile>
			<durable>true</durable>
			<recover>false</recover>
			<job-data-map>
				<entry>
					<key>WAREHOUSES</key>
					<value>888:wh28;upp:upp27,wh28,02;</value>
				</entry>
        <entry>
          <key>NEXTJOB</key>
          <value>PackedCardReportJob</value>
        </entry>
				<entry>
					<key>TITLE</key>
					<value>ItemHistory</value>
				</entry>
				<entry>
					<key>FORMAT</key>
					<value>P$AsOf:W$19;P$BeginningBalance:W$9:A$Right:F$F0;P$Receipts:W$9:A$Right:F$F0;P$Returns:W$9:A$Right:F$F0;P$Adjustments:W$9:A$Right:F$F0;P$Shipments:W$9:A$Right:F$F0;P$EndingBalance:W$9:A$Right:F$F0;</value>
				</entry>
				<entry>
					<key>SHOWHEADER</key>
					<value>false</value>
				</entry>
				<entry>
					<key>SHOWFOOTER</key>
					<value>false</value>
				</entry>				
			</job-data-map>
		</job-detail>
		<trigger>
			<cron>
				<name>ItemHistoryReportJobTrigger</name>
				<group>Manual</group>
				<description>To run ItemHistoryReport job on manual trigger</description>
				<misfire-instruction>SmartPolicy</misfire-instruction>
				<volatile>false</volatile>
				<job-name>ItemHistoryReportJob</job-name>
				<job-group>Manual</job-group>
				<cron-expression>0 35 17 ? * *</cron-expression>
			</cron>
		</trigger>
	</job>

	<job>
		<job-detail>
			<name>InventoryHistoryJob</name>
			<group>Manual</group>
			<description>InventoryHistory job</description>
			<job-type>Upp.Irms.EOD.Host.InventoryHistoryJob,Upp.Irms.EOD.Host</job-type>
			<volatile>false</volatile>
			<durable>true</durable>
			<recover>false</recover>
			<job-data-map>
			<entry>
			<key>WAREHOUSES</key>
      <value>888:wh28;upp:upp27,wh28,02;</value>			
			</entry>
			<entry>
			<key>NEXTJOB</key>
			<value>FourWallReportJob</value>
			</entry>
			</job-data-map>
		</job-detail>
		<trigger>
			<cron>
				<name>InventoryHistoryJobTrigger</name>
				<group>Manual</group>
				<description>To run InventoryHistory job on manual trigger</description>
				<misfire-instruction>SmartPolicy</misfire-instruction>
				<volatile>false</volatile>
				<job-name>InventoryHistoryJob</job-name>
				<job-group>Manual</job-group>
				<cron-expression>0 40 17 ? * *</cron-expression>
			</cron>
		</trigger>
	</job>
  
  <job>
    <job-detail>
      <name>PasswordExpirationJob</name>
      <group>Manual</group>
      <description>Job to list Users Password Expiration</description>
      <job-type>Upp.Irms.EOD.Host.PasswordExpiration,Upp.Irms.EOD.Host</job-type>
      <volatile>false</volatile>
      <durable>true</durable>
      <recover>false</recover>
      <job-data-map>
        <entry>
          <key>WAREHOUSES</key>
          <value>888:wh28;upp:upp27,wh28,02;</value>
        </entry>
        <entry>
          <key>NEXTJOB</key>
          <value></value>
        </entry>
      </job-data-map>
    </job-detail>
    <trigger>
      <cron>
        <name>PasswordExpirationJobTrigger</name>
        <group>Manual</group>
        <description>To run PasswordExpiration job on manual trigger</description>
        <misfire-instruction>SmartPolicy</misfire-instruction>
        <volatile>false</volatile>
        <job-name>PasswordExpirationJob</job-name>
        <job-group>Manual</job-group>
        <cron-expression>0 05 17 ? * *</cron-expression>
      </cron>
    </trigger>
  </job>

	<job>
		<job-detail>
			<name>MonthlyReconciliationJob</name>
			<group>Manual</group>
			<description>Monthly Reconciliation job</description>
			<job-type>Upp.Irms.EOD.Host.MonthlyReconciliationJob,Upp.Irms.EOD.Host</job-type>
			<volatile>false</volatile>
			<durable>true</durable>
			<recover>false</recover>
			<job-data-map>
				<entry>
					<key>WAREHOUSES</key>
					<value>WI:02;</value>
				</entry>
				<entry>
					<key>NEXTJOB</key>
					<value></value>
				</entry>				
				<entry>
					<key>FILEPATH</key>
					<value></value>
				</entry>
			</job-data-map>
		</job-detail>
		<trigger>
			<cron>
				<name>MonthlyReconciliationJobTrigger</name>
				<group>Manual</group>
				<description>To run MonthlyReconciliationJob job on manual trigger</description>
				<misfire-instruction>SmartPolicy</misfire-instruction>
				<volatile>false</volatile>
				<job-name>MonthlyReconciliationJob</job-name>
				<job-group>Manual</job-group>
				<cron-expression>0 30 00 1 * ?</cron-expression>
			</cron>
		</trigger>
	</job>

	<job>
		<job-detail>
			<name>IncubationHoldJob</name>
			<group>Manual</group>
			<description>Incubation Hold job</description>
			<job-type>Upp.Irms.EOD.Host.IncubationHoldJob,Upp.Irms.EOD.Host</job-type>
			<volatile>false</volatile>
			<durable>true</durable>
			<recover>false</recover>
			<job-data-map>
				<entry>
					<key>WAREHOUSES</key>
					<value>888:wh28;upp:upp27,wh28,02;</value>
				</entry>
				<entry>
					<key>NEXTJOB</key>
					<value></value>
				</entry>				
			</job-data-map>
		</job-detail>
		<trigger>
			<cron>
				<name>IncubationHoldJobTrigger</name>
				<group>Manual</group>
				<description>To run IncubationHold job on manual trigger</description>
				<misfire-instruction>SmartPolicy</misfire-instruction>
				<volatile>false</volatile>
				<job-name>IncubationHoldJob</job-name>
				<job-group>Manual</job-group>
				<cron-expression>0 45 23 * * ?</cron-expression>
			</cron>
		</trigger>
	</job>

	<job>
		<job-detail>
			<name>MaintainInventoryStatusJob</name>
			<group>Manual</group>
			<description>Maintain Inventory Status job</description>
			<job-type>Upp.Irms.EOD.Host.MaintainInventoryStatusJob,Upp.Irms.EOD.Host</job-type>
			<volatile>false</volatile>
			<durable>true</durable>
			<recover>false</recover>
			<job-data-map>
				<entry>
					<key>WAREHOUSES</key>
					<value>WI:02;</value>
				</entry>
				<entry>
					<key>NEXTJOB</key>
					<value></value>
				</entry>
			</job-data-map>
		</job-detail>
		<trigger>
			<cron>
				<name>MaintainInventoryStatusJobTrigger</name>
				<group>Manual</group>
				<description>To run Maintain Inventory Status job on manual trigger</description>
				<misfire-instruction>SmartPolicy</misfire-instruction>
				<volatile>false</volatile>
				<job-name>MaintainInventoryStatusJob</job-name>
				<job-group>Manual</job-group>
				<cron-expression>0 50 23 * * ?</cron-expression>
			</cron>
		</trigger>
	</job>

</quartz>

