<?xml version="1.0"?>
<configuration>

	<configSections>
		<section name="castle" type="Castle.Windsor.Configuration.AppDomain.CastleSectionHandler, Castle.Windsor"/>
		<section name="hibernate-configuration" type="NHibernate.Cfg.ConfigurationS<PERSON><PERSON><PERSON><PERSON><PERSON>, NHibernate"/>
	</configSections>

	<appSettings>
		<add key="ServiceLog" value="c:\upp\irms360\log\mobile.txt"/>
		<add key="UseProfiler" value="false"/>
	</appSettings>

	<castle>
		<components>
			<component
				id="managers"
				lifestyle="transient"
				service="Upp.Irms.Proxy.IManager`1, Upp.Irms.Proxy"
				type="Upp.Irms.Proxy.Manager`1, Upp.Irms.Proxy"/>
			<component
				id="repositories"
				lifestyle="transient"
				service="Upp.Irms.Core.IRepository`1, Upp.Irms.Core"
				type="Upp.Irms.Core.Repository`1, Upp.Irms.Core"/>
		</components>
	</castle>

	<hibernate-configuration xmlns="urn:nhibernate-configuration-2.2">
		<session-factory name="Services">
			<property name="connection.driver_class">NHibernate.Driver.SqlClientDriver</property>
			<property name="current_session_context_class">NHibernate.Context.ThreadStaticSessionContext</property>
			<property name="dialect">NHibernate.Dialect.MsSql2008Dialect</property>
			<property name="proxyfactory.factory_class">NHibernate.ByteCode.Castle.ProxyFactoryFactory, NHibernate.ByteCode.Castle</property>
			<!--Second Level Caching-->
		    <property name="cache.provider_class">NHibernate.Caches.RtMemoryCache.RtMemoryCacheProvider, NHibernate.Caches.RtMemoryCache</property>
		    <property name="cache.use_second_level_cache">true</property>
		    <property name="cache.use_query_cache">true</property>
		    <property name="cache.default_expiration">600</property><!-- 10 mins, if required can be increased-->
		</session-factory>
	</hibernate-configuration>

	<system.serviceModel>

		<services>
			<service behaviorConfiguration="ServiceBehavior" name="Upp.Irms.Services.Service">
				<endpoint address="MainService" 
						  binding="basicHttpBinding" 
						  bindingConfiguration="ServiceBinding" 
						  contract="Upp.Irms.Services.IMainService" 
						  name="MainService" />
				<endpoint address="LoginService" 
						  binding="basicHttpBinding" 
						  bindingConfiguration="ServiceBinding" 
						  contract="Upp.Irms.Services.ILoginService" 
						  name="LoginService" />
				<endpoint address="mex" binding="mexHttpBinding" contract="IMetadataExchange"/>
				<host>
					<baseAddresses>
						<add baseAddress="http://localhost/irms360/mobile/services/"/>
					</baseAddresses>
				</host>
			</service>
		</services>

		<behaviors>
			<serviceBehaviors>
				<behavior name="ServiceBehavior">
					<serviceMetadata httpGetEnabled="True"/>
					<serviceDebug includeExceptionDetailInFaults="True"/>
				</behavior>
			</serviceBehaviors>
		</behaviors>

		<bindings>
			<basicHttpBinding>
				<binding name="ServiceBinding" 
						 maxBufferSize="2147483647" 
						 maxReceivedMessageSize="2147483647" 
						 receiveTimeout="01:00:00" 
						 sendTimeout="01:00:00">
					<readerQuotas maxArrayLength="2147483647" 
								  maxBytesPerRead="2147483647" 
								  maxDepth="2147483647"/>
				</binding>
			</basicHttpBinding>
		</bindings>

	</system.serviceModel>

</configuration>
