﻿using System;
using System.ServiceModel;
using System.Threading;

using Upp.Irms.Services;
using Upp.Shared.Utilities;

namespace Upp.Irms.Services.Host
{
	public class ServiceEngine
	{
		#region Fields

		private ServiceHost _host = null;

		#endregion

		#region Methods

		public void Start()
		{
			Log.Instance.WriteEntry(String.Format("Starting {0}...", Program.Name));
			//
			try
			{
				_host = new ServiceHost(typeof(Service));
				_host.Open();
#if DEBUG
				while (true) Thread.Sleep(100);
#else
				Log.Instance.WriteEntry(String.Format("{0} has started successfully.", Program.Name));
#endif
			}
			catch (Exception ex)
			{
				if (_host != null && _host.State == CommunicationState.Faulted) _host.Abort();
				Log.Instance.WriteError(Errors.GetError(ex));
			}
		}

		public void Stop()
		{
			Log.Instance.WriteEntry(String.Format("Stopping {0}...", Program.Name));
			if (_host != null) _host.Close();
		}

		#endregion
	}
}
