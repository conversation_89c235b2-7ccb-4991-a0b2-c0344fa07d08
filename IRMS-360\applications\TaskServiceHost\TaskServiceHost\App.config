﻿<?xml version="1.0"?>
<configuration>

	<configSections>
		<section name="castle" type="Castle.Windsor.Configuration.AppDomain.CastleSectionHandler, Castle.Windsor"/>
		<section name="hibernate-configuration" type="NHibernate.Cfg.Configuration<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NHibernate"/>
	</configSections>

	<appSettings>
		<add key="CrystalPrinting" value="false"/>
		<add key="Interval" value="30"/>
		<add key="Log" value="c:\upp\irms360\log\"/>
		<add key="Name" value="TaskService"/>
		<add key="Reports" value="C:\IRMS360\Source\irms-360-1.0\_reports\Templates\"/>
		<add key="CustomReports" value = "C:\IRMS360\Source\irms-360-1.0.0\IRMS-360-Web\IRMS-360\applications\WebClient\Core\Reports\Custom"/>
		<add key="TaskBatch" value="10"/>
		<add key="Ucc128Prefix" value=""/>
		<add key="UseProfiler" value="false"/>
		<add key="Verbosity" value="Error"/>
		<add key="ReportAPIBaseAddress" value="http://localhost:60413" />
		<add key="IntegrationURL" value="http://localhost/irms360/rest/integration"/>
		<add key="BarcodeFontURL" value="http://dga1app01irmsdv/IDAutomation/IDAutomationStreamingLinear.aspx"/>
	</appSettings>

	<castle>
		<components>
			<component
				id="managers"
				lifestyle="transient"
				service="Upp.Irms.Proxy.IManager`1, Upp.Irms.Proxy.Reports"
				type="Upp.Irms.Proxy.Manager`1, Upp.Irms.Proxy.Reports" />
			<component
				id="repositories"
				lifestyle="transient"
				service="Upp.Irms.Core.IRepository`1, Upp.Irms.Core"
				type="Upp.Irms.Core.Repository`1, Upp.Irms.Core" />
		</components>
	</castle>

	<hibernate-configuration xmlns="urn:nhibernate-configuration-2.2">
		<session-factory name="Services">
			<property name="connection.driver_class">NHibernate.Driver.SqlClientDriver</property>
      <property name="connection.connection_string">Data Source=dga1app24irms;Initial Catalog=irms360_29sp1;Integrated Security=False;User ID=sa;Password=********;</property>
			<property name="current_session_context_class">NHibernate.Context.ThreadStaticSessionContext</property>
			<property name="dialect">NHibernate.Dialect.MsSql2008Dialect</property>
  </session-factory>
	</hibernate-configuration>

	<startup useLegacyV2RuntimeActivationPolicy="true">
		  <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2"/>
	</startup>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-11.0.0.0" newVersion="11.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="NHibernate" publicKeyToken="aa95f207798dfdb4" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.1.0.0" newVersion="5.1.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
