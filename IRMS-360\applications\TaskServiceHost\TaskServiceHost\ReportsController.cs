﻿using System;
using System.Threading;
using System.Web.Http;
using Upp.Irms.TaskService.Workers;

namespace Upp.Irms.TaskService.Host
{
    public class ReportsController : ApiController
    {
        [HttpPost]
        public String GetReport(Domain.Task task)
        {
            return ProcessReport(task);
        }

        private string ProcessReport(Domain.Task task)
        {
            using (ReportWorker worker = new ReportWorker(task))
            {
                worker.RunWorkerAsync();
                while (worker.IsBusy) Thread.Sleep(100);
                return worker._base64Report;
            }

        }
    }
}
