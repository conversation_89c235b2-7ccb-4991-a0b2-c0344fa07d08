﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using NHibernate.Criterion;
using NHibernate.Transform;

using Upp.Irms.Core;
using Upp.Irms.Domain;
using Upp.Irms.Domain.Utilities;
using Upp.Shared.Application;
using Upp.Shared.Utilities;
using System.Web.Http.SelfHost;
using System.Web.Http;

namespace Upp.Irms.TaskService.Host
{
	public class ServiceEngine
	{
		#region Fields

		private Boolean _running = false;
		private String _sessionId = String.Empty;

		#endregion

		#region Methods.Private

		private void Login()
		{
			// TODO: move 'User' and 'Password' to an encrypted file.
			Byte[] password = Encryption.Encrypt(Configuration.Settings["Password"], "s33d");
			String agencyCode = Configuration.Settings["Agency"];
			String companyCode = Configuration.Settings["Company"];
			String unitCode = Configuration.Settings["Unit"];
			String userName = Configuration.Settings["User"];
			String warehouseCode = Configuration.Settings["Warehouse"];
            String providerCode = Configuration.Settings["PROVIDERS"];
			//
			UserAccount user = Authentication.Login(userName, password);
			_sessionId = Authentication.CreateSessionId(user.Id.ToString());
			//
			Registry.InitializeKey(_sessionId);
			if (!String.IsNullOrEmpty(agencyCode))
			{
				if (String.IsNullOrEmpty(unitCode))
				{
					DetachedCriteria criteria = DetachedCriteria.For<Agency>()
						.Add("Active", "A")
						.Add("AgencyCode", agencyCode)
						.SetMaxResults(2);
					Agency agency = Repositories.Get<Agency>().Retrieve(criteria);
					if (agency == null) throw new Exception("Invalid agency code.");
					else Registry.Add<Agency>(agency);
				}
				else
				{
					DetachedCriteria criteria = DetachedCriteria.For<AgencyOrganizationalUnit>()
						.Add("Active", "A")
						.Add("OrganizationCode", unitCode)
						.AddOr("Agency.AgencyCode", agencyCode, "AgencyLocation.Agency.AgencyCode", agencyCode)
						.SetMaxResults(2);
					AgencyOrganizationalUnit unit = Repositories.Get<AgencyOrganizationalUnit>().Retrieve(criteria);
					if (unit == null) throw new Exception("Invalid agency or unit code.");
					else
					{
						if (unit.Agency != null) Registry.Add<Agency>(unit.Agency);
						else Registry.Add<Agency>(unit.AgencyLocation.Agency);
						Registry.Add<AgencyOrganizationalUnit>(unit);
					}
				}
			}
			else if (!String.IsNullOrEmpty(companyCode))
			{
				if (String.IsNullOrEmpty(warehouseCode))
				{
					DetachedCriteria criteria = DetachedCriteria.For<Company>()
						.Add("Active", "A")
						.Add("CompanyCode", companyCode)
						.SetMaxResults(2);
					Company company = Repositories.Get<Company>().Retrieve(criteria);
					if (company == null) throw new Exception("Invalid company code.");
					else Registry.Add<Company>(company);
				}
				else
				{
					DetachedCriteria criteria = DetachedCriteria.For<CompanyLocationType>()
						.Add("Active", "A")
						.Add("CompanyLocation.Company.CompanyCode", companyCode)
						.Add("CompanyLocationCode", warehouseCode)
						.SetMaxResults(2);
					CompanyLocationType warehouse = Repositories.Get<CompanyLocationType>().Retrieve(criteria);
					if (warehouse == null) throw new Exception("Invalid company or warehouse code.");
					else
					{
						Registry.Add<Company>(warehouse.CompanyLocation.Company);
						Registry.Add<CompanyLocationType>(warehouse);
					}
				}
			}
            else if (!String.IsNullOrEmpty(providerCode))
            {
                string[] providerCodes = providerCode.Split(',');

                DetachedCriteria criteria = DetachedCriteria.For<Provider>()
                    .Add("Active", "A")
                    .Add(Restrictions.In("ProviderCode", providerCodes))
                    .SetProjection(Projections.ProjectionList()
                    .Add(Projections.Property("ProviderCode"), "ProviderCode"))
                    .SetResultTransformer(Transformers.AliasToBean<Provider>()); 
                    
                IList<Provider> providers = Repositories.Get<Provider>().List(criteria);
                if (providers == null || providers.Count == 0) throw new Exception("Invalid provider code.");
                else
                {
                    List<string> existingProviderCodes = providers.GroupBy(e => e.ProviderCode).Select(c => c.Key).ToList<string>();
                    string invalidproviderCodes = string.Empty;
                    if (providerCodes.Length != existingProviderCodes.Count)
                    {
                        foreach (string providercode in providerCodes)
                        {
                            if (!existingProviderCodes.Contains(providercode))
                            {
                                if (string.IsNullOrEmpty(invalidproviderCodes)) invalidproviderCodes = providercode;
                                else invalidproviderCodes = invalidproviderCodes + "," + providercode;
                            }
                        }
                        if (!string.IsNullOrEmpty(invalidproviderCodes)) throw new Exception("Invalid provider codes " + invalidproviderCodes);
                    }
                }
            }
            else throw new Exception("All login parameters are mandatory.");
		}

		#endregion

		#region Methods.Public

		public void Start()
		{
			Log.Instance.WriteEntry(String.Format("Starting {0}...", Program.Name));
			//
			try
			{
                // Self Hosting Reports Web API
                var config = new HttpSelfHostConfiguration(Configuration.Settings["ReportAPIBaseAddress"]);
                config.Routes.MapHttpRoute(
                    name: "API",
                    routeTemplate: "{controller}/{action}/{id}",
                    defaults: new { id = RouteParameter.Optional }
                );
                config.MaxBufferSize = 250000000;
                config.MaxReceivedMessageSize = 250000000;
                HttpSelfHostServer server = new HttpSelfHostServer(config);
                server.OpenAsync().Wait();
                //
                using (UnitWrapper wrapper = new UnitWrapper())
				{
					wrapper.Execute(() => this.Login());
					if (!String.IsNullOrEmpty(wrapper.Error)) throw new Exception(wrapper.Error);
				}
				//
				Service.Instance.Start(_sessionId);
				_running = true;
#if DEBUG
				while (true) Thread.Sleep(100);
#else
				Log.Instance.WriteEntry(String.Format("{0} has started successfully.", Program.Name));
#endif
			}
			catch (Exception ex)
			{
				if (_running) Service.Instance.Stop();
				Log.Instance.WriteError(Errors.GetError(ex));
			}
		}

		public void Stop()
		{
			if (_running) Service.Instance.Stop();
			Log.Instance.WriteEntry(String.Format("Stopping {0}...", Program.Name));
		}

		#endregion
	}
}
