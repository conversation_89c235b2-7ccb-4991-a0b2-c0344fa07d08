﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x86</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{26AF379C-636F-43F7-9471-C53DDA20A5BA}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Upp.Irms.TaskService.Host</RootNamespace>
    <AssemblyName>Upp.Irms.TaskService.Host</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <TargetFrameworkProfile>
    </TargetFrameworkProfile>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisIgnoreBuiltInRuleSets>false</CodeAnalysisIgnoreBuiltInRuleSets>
    <CodeAnalysisIgnoreBuiltInRules>false</CodeAnalysisIgnoreBuiltInRules>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|AnyCPU'">
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="HibernatingRhinos.Profiler.Appender">
      <HintPath>..\..\..\libraries\_bin\HibernatingRhinos.Profiler.Appender.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
	  <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\libraries\_bin\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NHibernate">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\libraries\_bin\NHibernate.dll</HintPath>
    </Reference>
    <Reference Include="Remotion.Linq">
      <HintPath>..\..\..\libraries\_bin\Remotion.Linq.dll</HintPath>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="Remotion.Linq.EagerFetching">
      <HintPath>..\..\..\libraries\_bin\Remotion.Linq.EagerFetching.dll</HintPath>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Configuration.Install" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Net.Http.Formatting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.4.0.20710.0\lib\net40\System.Net.Http.Formatting.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net.Http.WebRequest" />
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.4.0.20710.0\lib\net40\System.Web.Http.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Http.SelfHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.SelfHost.4.0.20710.0\lib\net40\System.Web.Http.SelfHost.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="Upp.Shared">
      <HintPath>..\..\..\..\_libraries\_bin\Upp.Shared.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="HostService.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="HostService.Designer.cs">
      <DependentUpon>HostService.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="ProjectInstaller.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProjectInstaller.Designer.cs">
      <DependentUpon>ProjectInstaller.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ReportsController.cs" />
    <Compile Include="ServiceEngine.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="..\..\_bin\TaskServiceHost\Config.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\libraries\Constants\Constants\Constants.csproj">
      <Project>{34CD49DA-7DBB-4A30-82F9-314BC22D14BF}</Project>
      <Name>Constants</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\libraries\Core\Core\Core.csproj">
      <Project>{6911B114-73DA-4E9E-B86B-4E5558149ECA}</Project>
      <Name>Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\libraries\Domain\Domain\Domain.csproj">
      <Project>{8302CA83-B340-4F77-8F98-FE0A60EB9C5C}</Project>
      <Name>Domain</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\libraries\TaskService\TaskService\TaskService.csproj">
      <Project>{FD2DDB27-78C3-4A24-9955-3FA930FE78A2}</Project>
      <Name>TaskService</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PostBuildEvent>if "$(ConfigurationName)" == "Release" copy "$(TargetDir)$(TargetFileName)" "$(ProjectDir)..\..\_bin\$(ProjectName)\"
if "$(ConfigurationName)" == "Release" copy "$(TargetDir)$(TargetFileName).config" "$(ProjectDir)..\..\_bin\$(ProjectName)\"
if "$(ConfigurationName)" == "Release" copy "$(TargetDir)*.dll" "$(ProjectDir)..\..\_bin\$(ProjectName)\"</PostBuildEvent>
  </PropertyGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>