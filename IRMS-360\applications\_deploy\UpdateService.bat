@echo off

set source=\\tsclient\c\upp\irms360\1.0\_source\irms-360\applications\_bin\ServicesHost\
set target=c:\upp\irms360\mobile\

if not exist "%target%\old\" goto fail

copy "%target%Upp.*.dll" "%target%old\"
copy "%target%*.exe" "%target%old\"
echo Finished backup.

copy "%source%Upp.*.dll" %target%
copy "%source%*.exe" %target%
echo Finished update.
goto end

:fail
echo Unable to find the backup folder (.\old\).

:end
pause