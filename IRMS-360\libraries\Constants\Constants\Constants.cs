﻿using System;

namespace Upp.Irms.Constants
{
    #region InventoryAdjustmentCodes Enumeration
    public enum InventoryAdjustmentCodes
    {
        [CodeValue("RB")]
        RepackBox,
    }
    #endregion       

    #region FunctionalAreas Enumeration
    public enum FunctionalAreas
	{
		[CodeValue("A")]
		Alert,

        [CodeValue("status")]
        ClaimStatus,

        [CodeValue("HT")]
		Height,

		[CodeValue("IN")]
		Integration,

		[CodeValue("I")]
		Inventory,

		[CodeValue("LPN")]
		LicensePlate,

		None,

		[CodeValue("ORD")]
		Order,

		[CodeValue("ORG")]
		Organization,

		[CodeValue("PACK")]
		Packing,

		[CodeValue("PO")]
		Purchase,

		[CodeValue("RE")]
		Receiving,

		[CodeValue("REP")]
		Report,

		[CodeValue("REQS")]
		Requsition,

		[CodeValue("RET")]
		Return,             

        [CodeValue("SH")]
		Shipping,

		[CodeValue("SA")]
		StockAdjustment,

		[CodeValue("T")]
		Task,

		[CodeValue("WT")]
		Weight
	}
	#endregion

	#region Applications Enumeration
	public enum Applications
	{
		[CodeValue("IRMS-AM-M")]
		Am,

		[CodeValue("IRMS-WM-M")]
		Wm,
	}
	#endregion

    #region BillingCodes Enumeration
    public enum BillingCodes
    {
        [CodeValue("CC")]
        CustomerCharges,

        [CodeValue("CH")]
        Charges,

        [CodeValue("D")]
        Discount,

        [CodeValue("E")]
        ExpectedFreight,

        [CodeValue("F")]
        Freight,

        [CodeValue("T")]
        Tax,
    }
    #endregion

	#region CarrierCodes Enumeration
	public enum CarrierCodes
	{
		[CodeValue("CAL")]
		California,

		[CodeValue("FDX")]
		FedEx,

		[CodeValue("SPD")]
		SpeeDee,

		[CodeValue("SSCC")]
		SSCC,

		[CodeValue("UPS")]
		Ups,
	}
	#endregion

    #region CartonTypes Enumeration
    public enum CartonTypes
    {
        [CodeValue("OP")]
        OriginalPackaging,

        [CodeValue("RB")]
        RepackBox,
        
    }
    #endregion

	#region CommunicationRoles Enumeration
	public enum CommunicationRoles
	{
		[CodeValue("E")]
		Email,

		[CodeValue("PR")]
		Print,
	}
	#endregion

	#region EntityCodes Enumeration
	public enum EntityCodes
	{
		[CodeValue("BOL_NUMBER")]
		Bol,

		[CodeValue("CRMA")]
		CustomerReturn,

        [CodeValue("INSPECTION")]
        Inspection,

		[CodeValue("KITS")]
		Kit,

		[CodeValue("RECEIPT_HEADERS")]
		ReceiptHeader,

		[CodeValue("TASKS")]
		Task,

        [CodeValue("UCC128")]
        UCC128,

		[CodeValue("WAVES")]
		Wave,

		[CodeValue("ZONES")]
		Zone,
	}
	#endregion

	#region OrderClasses Enumeration
	public enum OrderClasses
	{
		[CodeValue("WT")]
		Transfer,
	}
	#endregion

    #region LookupCodes Enumeration

    public enum LookupCodes
    {
        [CodeValue("VISIT")]
        VISIT,
    }

    #endregion

	#region PickTypes Enumeration
	public enum PickTypes
	{
		[CodeValue("CS")]
		FullCase,

		[CodeValue("PAL")]
		Pallet,

		[CodeValue("SPLITCS")]
		SplitCase,
	}
	#endregion

	#region PrinterTypes Enumeration
	public enum PrinterTypes
	{
		[CodeValue("L")]
		Label,

		None,

		[CodeValue("P")]
		PickTicket,

		[CodeValue("R")]
		Report,
	}
	#endregion

	#region ReceiptTypes Enumeration
	public enum ReceiptTypes
	{
		[CodeValue("ASN")]
		Asn,

		[CodeValue("X")]
		CrossDock,

		[CodeValue("PR")]
		Planned,

		[CodeValue("PB")]
		PlannedBulk,

		[CodeValue("P")]
		Po,

		[CodeValue("U")]
		Unplanned,

		[CodeValue("UB")]
		UnplannedBulk,
	}
	#endregion

	#region Reports Enumeration
	public enum Reports
	{
		[CodeValue("RPTCCDISC")]
		CycleCountDiscrepancies,

		[CodeValue("REPITLBL12")]
		ItemLabel1x2,

		[CodeValue("REPITLBL24")]
		ItemLabel2x4,

		[CodeValue("RPTSHPLBL")]
		LicensePlateLabel,

		[CodeValue("RPTPCKLST")]
		PackingList,

		[CodeValue("RPTSCMLBL")]
		SCMLabel,
	}
	#endregion

	#region ReportTypes Enumeration
	public enum ReportTypes
	{
		[CodeValue("L")]
		Label,

		[CodeValue("PL")]
		PackingList,
	}
	#endregion

	#region ZoneTypes Enumeration
	public enum ZoneTypes
	{
		[CodeValue("CAR")]
		Carousel,

		[CodeValue("CNTRSL")]
		CounterSale,

		[CodeValue("DG")]
		DamagedGoods,

		[CodeValue("DIS")]
		Discrepancy,

		[CodeValue("PK")]
		Packaging,

		[CodeValue("REG")]
		Regular
	}
	#endregion
}
