﻿using System;

namespace Upp.Irms.Constants
{
	#region InventoryLicenseTypes Enumeration
	[AreaValue(FunctionalAreas.Inventory)]
	public enum InventoryLicenseTypes
	{
		[CodeValue("C")]
		<PERSON><PERSON>,

		[CodeValue("V")]
		InventoryItem,

		[CodeValue("I")]
		Item,

		[CodeValue("L")]
		Location,

		[CodeValue("P")]
		Pallet,

		[CodeValue("S")]
		Shipping,

		[CodeValue("T")]
		Tote
	}
	#endregion

	#region OrderLicenseTypes Enumeration
	[AreaValue(FunctionalAreas.Order)]
	public enum OrderLicenseTypes
	{
		[CodeValue("CON")]
		Container,
	}
	#endregion
}
