﻿using System;

namespace Upp.Irms.Constants
{
	#region InventoryLocationTypes Enumeration
	[AreaValue(FunctionalAreas.Inventory)]
	public enum InventoryLocationTypes
	{
		[CodeValue("B")]
		Bulk,

		[CodeValue("M")]
		Mobile,

		[CodeValue("PK")]
		Packaging,

		[CodeValue("P")]
		Pallet,

		[CodeValue("RT")]
		Receiving,

		[CodeValue("RJ")]
		Reject,

		[CodeValue("RET")]
		Returns,

		[CodeValue("S")]
		Shelf,

		[CodeValue("SH")]
		Shipping,

		[CodeValue("T")]
		Staging,

		[CodeValue("WT")]
		Transfer,

		[CodeValue("ZS")]
		ZoneStage,
	}
	#endregion

	#region OrganizationLocationTypes Enumeration
	[AreaValue(FunctionalAreas.Organization)]
	public enum OrganizationLocationTypes
	{
		[CodeValue("B")]
		BillTo,

		[CodeValue("R")]
		RemitTo,

		[CodeValue("S")]
		ShipTo,

		[CodeValue("W")]
		Warehouse,

        [CodeValue("COD")]
        COD,
    }
	#endregion

	#region RequisitionLocationTypes Enumeration
	[AreaValue(FunctionalAreas.Requsition)]
	public enum RequisitionLocationTypes
	{
		[CodeValue("B")]
		BillTo,

		[CodeValue("R")]
		RemitTo,
	}
	#endregion
}
