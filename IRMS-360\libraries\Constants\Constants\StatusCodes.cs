﻿using System;

namespace Upp.Irms.Constants
{
	#region AltertStatuses Enumeration
	[AreaValue(FunctionalAreas.Alert)]
	public enum AlertStatuses
	{
		[CodeValue("A")]
		Active,

		[CodeValue("C")]
		Complete,

		[CodeValue("E")]
		Error,

		[CodeValue("O")]
		Open,
	}
	#endregion

	#region IntegrationStatuses Enumeration
	[AreaValue(FunctionalAreas.Integration)]
	public enum IntegrationStatuses
	{
		[CodeValue("A")]
		Active,

		[CodeValue("C")]
		Complete,

		[CodeValue("O")]
		Open,
	}
	#endregion

	#region InventoryStatuses Enumeration
	[AreaValue(FunctionalAreas.Inventory)]
	public enum InventoryStatuses
	{
		[CodeValue("A")]
		Available,

		[CodeValue("IH")]
		IncubationHold,

		[CodeValue("ISS")]
		Issued,

		[CodeValue("K")]
		Kit,

		[CodeValue("P")]
		Pending,

		[CodeValue("Q")]
		QaHold,

		[CodeValue("ST")]
		Staged,

		[CodeValue("U")]
		Unavailable,

		[CodeValue("W")]
		WorkInProgress,

        [CodeValue("SA")]
        Allocated,

        [CodeValue("INB")]
        InboundInspection
	}
	#endregion

	#region LicensePlateStatuses Enumeration
	[AreaValue(FunctionalAreas.LicensePlate)]
	public enum LicensePlateStatuses
	{
		[CodeValue("A")]
		Associated,

		[CodeValue("C")]
		Complete,

        [CodeValue("CA")]
        Cancelled,

		[CodeValue("FT")]
		FullTote,

		[CodeValue("I")]
		Inducted,

		[CodeValue("O")]
		Open,

        [CodeValue("L")]
        Loaded,

        [CodeValue("P")]
		Packed,

		[CodeValue("PZ")]
		PutawayZoneComplete,

		[CodeValue("S")]
		Shipped,

		[CodeValue("ST")]
		Staged,

		[CodeValue("UP")]
		UndoPacking,

		[CodeValue("V")]
		Verified,

		[CodeValue("VD")]
		Void,

		[CodeValue("ZC")]
		ZoneComplete,
	}
	#endregion

	#region OrderStatuses Enumeration
	[AreaValue(FunctionalAreas.Order)]
	public enum OrderStatuses
	{
		[CodeValue("A")]
		Active,

		[CodeValue("CA")]
		Cancelled,

		[CodeValue("C")]
		Complete,

		[CodeValue("D")]
		Denied,

		[CodeValue("DIST")]
		Distributed,

		[CodeValue("ID")]
		InDrop,

		[CodeValue("IP")]
		InPick,

		[CodeValue("O")]
		Open,

		[CodeValue("PG")]
		Packed,

		[CodeValue("P")]
		Pending,

		[CodeValue("PK")]
		Picked,

		[CodeValue("R")]
		Received,

		[CodeValue("S")]
		Shipped,

		[CodeValue("CV")]
		Verify,

		[CodeValue("XX")]
		XX,
	}
	#endregion

	#region PurchaseStatuses Enumeration
	[AreaValue(FunctionalAreas.Purchase)]
	public enum PurchaseStatuses
	{
		[CodeValue("A")]
		Active,

		[CodeValue("APP")]
		Approved,

		[CodeValue("C")]
		Complete,

		[CodeValue("DEC")]
		Declined,

		[CodeValue("O")]
		Open,

		[CodeValue("P")]
		Pending,

		[CodeValue("PA")]
		PendingApproval,

		[CodeValue("RT")]
		Received,
	}
	#endregion

	#region ReportStatuses Enumeration
	[AreaValue(FunctionalAreas.Report)]
	public enum ReportStatuses
	{
		[CodeValue("A")]
		Active,

		[CodeValue("C")]
		Complete,

		[CodeValue("E")]
		Error,

		[CodeValue("O")]
		Open,
	}
	#endregion

	#region RequisitionStatuses Enumeration
	[AreaValue(FunctionalAreas.Requsition)]
	public enum RequisitionStatuses
	{
		[CodeValue("APP")]
		Approved,

		[CodeValue("C")]
		Complete,

		[CodeValue("DEC")]
		Declined,

		[CodeValue("DIST")]
		Distributed,

		[CodeValue("IP")]
		InPick,

		[CodeValue("PG")]
		Packed,

		[CodeValue("PK")]
		Picked,

		[CodeValue("ST")]
		Staged,
	}
	#endregion

	#region ReturnStatuses Enumeration
	[AreaValue(FunctionalAreas.Return)]
	public enum ReturnStatuses
	{
		[CodeValue("A")]
		Active,

		[CodeValue("C")]
		Complete,

		[CodeValue("O")]
		Open
	}
	#endregion

	#region ShippingStatuses Enumeration
	[AreaValue(FunctionalAreas.Shipping)]
	public enum ShippingStatuses
	{
		[CodeValue("C")]
		Closed,

		[CodeValue("O")]
		Open,

		[CodeValue("V")]
		Void
	}
	#endregion

	#region TaskStatuses Enumeration
	[AreaValue(FunctionalAreas.Task)]
	public enum TaskStatuses
	{
		[CodeValue("A")]
		Active,

		[CodeValue("C")]
		Complete,

		[CodeValue("E")]
		Error,

		[CodeValue("O")]
		Open,
	}
	#endregion
}