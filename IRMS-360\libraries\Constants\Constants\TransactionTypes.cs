﻿using System;

namespace Upp.Irms.Constants
{
	#region AlertTransactions Enumeration
	[AreaValue(FunctionalAreas.Alert)]
	public enum AlertTransactions
	{
		[CodeValue("AR")]
		AlertRequest
	}
	#endregion

	#region InventoryTransactions Enumeration
	[AreaValue(FunctionalAreas.Inventory)]
	public enum InventoryTransactions
	{
		[CodeValue("CKIN")]
		CheckIn,

		[CodeValue("CKOUT")]
		CheckOut,

		[CodeValue("XS")]
		CrossDock,

		[CodeValue("CRET")]
		CustomerReturn,

		[CodeValue("CC")]
		CycleCount,

		[CodeValue("CCE")]
		CycleCountEmployee,

		[CodeValue("CCI")]
		CycleCountItem,

		[CodeValue("CCL")]
		CycleCountLocation,

		[CodeValue("CCP")]
		CycleCountParent,

		[CodeValue("DR")]
		Dimensions,

		[CodeValue("DIST")]
		Distribution,

		[CodeValue("FI")]
		FoundItem,

		[CodeValue("KA")]
		KitAssignment,

		[CodeValue("KB")]
		KitBuild,

		[CodeValue("MI")]
		MissingItem,

        [CodeValue("PF")]
		PartFlip,

		[CodeValue("PI")]
		Physical,

        [CodeValue("YC")]
        PhysicalCount,

		[CodeValue("P")]
		Pick,

		[CodeValue("IC")]
		PrimaryUpdate,

		[CodeValue("PA")]
		Putaway,

		[CodeValue("RT")]
		Receipt,

		[CodeValue("RE")]
		ReceiptEntered,

		[CodeValue("RS")]
		ReceiptSend,

		[CodeValue("RM")]
		ReleaseReplenishment,

		[CodeValue("RP")]
		Repack,

		[CodeValue("MR")]
		Replenishment,

		[CodeValue("TN")]
		Return,

		[CodeValue("SH")]
		Shipment,

		[CodeValue("ST")]
		Staging,

		[CodeValue("XFER")]
		Transfer,

		[CodeValue("MU")]
		UnplannedReplenishment,

		[CodeValue("VRET")]
		VendorReturn
	}
	#endregion

	#region LicensePlateTransactions
	[AreaValue(FunctionalAreas.LicensePlate)]
	public enum LicensePlateTransactions
	{
        [CodeValue("CS")]
        CartonSorted,
		[CodeValue("KA")]
		ItemVerified,
	}
	#endregion

	#region OrderTransactions Enumeration
	[AreaValue(FunctionalAreas.Order)]
	public enum OrderTransactions
	{
		[CodeValue("D")]
		Drop,

		[CodeValue("ID")]
		InDrop,

		[CodeValue("KG")]
		Packed,

		[CodeValue("IG")]
		Picked,

		[CodeValue("R")]
		Received,

		[CodeValue("PR")]
		Returned,

		[CodeValue("UD")]
		UnDrop,

        [CodeValue("SDORDDP")]
        ScheduledOrderDrop
    }
	#endregion

	#region ReportTransactions Enumeration
	[AreaValue(FunctionalAreas.Report)]
	public enum ReportTransactions
	{
		[CodeValue("RR")]
		ReportRequest
    }
	#endregion

	#region RequisitionTransactions Enumeration
	[AreaValue(FunctionalAreas.Requsition)]
	public enum RequisitionTransactions
	{
		[CodeValue("IG")]
		Picked,

		[CodeValue("ST")]
		Staged,
	}
	#endregion

	#region StockAdjustmentTransactions Enumeration
	[AreaValue(FunctionalAreas.StockAdjustment)]
	public enum StockAdjustmentTransactions
	{
		[CodeValue("SA")]
		StockAdjustment,

		[CodeValue("SM")]
		StockMovement,
	}
	#endregion
}