﻿using System;

namespace Upp.Irms.Constants
{
	#region HeightUoms Enumeration
	[AreaValue(FunctionalAreas.Height)]
	public enum HeightUoms
	{
		[CodeValue("FT")]
		Foot,

		[CodeValue("IN")]
		Inch,
	}
	#endregion

	#region InventoryUoms Enumeration
	[AreaValue(FunctionalAreas.Inventory)]
	public enum InventoryUoms
	{
		[CodeValue("B")]
		Box,

		[CodeValue("C")]
		Case,

		[CodeValue("EA")]
		Each,

		[CodeValue("P")]
		Pallet,
	}
	#endregion

	#region WeightUoms Enumeration
	[AreaValue(FunctionalAreas.Weight)]
	public enum WeightUoms
	{

		[CodeValue("OZ")]
		Ounce,

		[CodeValue("LB")]
		Pound,
	}
	#endregion
}