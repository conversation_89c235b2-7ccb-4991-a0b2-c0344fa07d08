﻿using System;
using System.Linq;

namespace Upp.Irms.Constants
{
	public class AreaValue : Attribute
	{
		#region Fields

		private FunctionalAreas _area = FunctionalAreas.None;

		#endregion

		#region Properties

		public FunctionalAreas Area
		{
			get { return _area; }
		}

		#endregion

		#region Constructor

		public AreaValue(FunctionalAreas area)
		{
			_area = area;
		}

		#endregion

		#region Methods.Public.Static

		public static String GetArea(Enum value)
		{
			Object[] attributes = value.GetType().GetCustomAttributes(typeof(AreaValue), false);
			if (attributes == null || attributes.Length != 1) return String.Empty;
			else return CodeValue.GetCode(((AreaValue)attributes[0]).Area);
		}

		#endregion
	}
}
