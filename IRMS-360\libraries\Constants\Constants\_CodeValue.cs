﻿using System;

namespace Upp.Irms.Constants
{
	public class CodeValue : Attribute
	{
		#region Fields

		private String _code = String.Empty;

		#endregion

		#region Properties

		public String Code
		{
			get { return _code; }
		}

		#endregion

		#region Constructor

		public CodeValue(string code)
		{
			_code = code;
		}

		#endregion

		#region Methods.Public.Static

		public static String GetCode(Enum value)
		{
			Object[] attributes = value.GetType()
				.GetField(value.ToString())
				.GetCustomAttributes(typeof(CodeValue), false);
			if (attributes == null || attributes.Length != 1) return String.Empty;
			else return ((CodeValue)attributes[0]).Code;
		}

		#endregion
	}
}
