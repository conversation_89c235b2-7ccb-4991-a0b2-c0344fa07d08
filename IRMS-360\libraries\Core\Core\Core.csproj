﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{6911B114-73DA-4E9E-B86B-4E5558149ECA}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Upp.Irms.Core</RootNamespace>
    <AssemblyName>Upp.Irms.Core</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>TRACE;DEBUG;NHPROF</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Castle.Windsor">
      <HintPath>..\..\_bin\Castle.Windsor.dll</HintPath>
    </Reference>
    <Reference Include="NHibernate">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\_bin\NHibernate.dll</HintPath>
    </Reference>
    <Reference Include="Remotion.Linq">
      <HintPath>..\..\_bin\Remotion.Linq.dll</HintPath>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="Remotion.Linq.EagerFetching">
      <HintPath>..\..\_bin\Remotion.Linq.EagerFetching.dll</HintPath>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="Upp.Shared">
      <HintPath>..\..\..\..\_libraries\_bin\Upp.Shared.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Ioc.cs" />
    <Compile Include="IRepository.cs" />
    <Compile Include="IUnitOfWork.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Repositories.cs" />
    <Compile Include="Repository.cs" />
    <Compile Include="SessionManager.cs" />
    <Compile Include="SessionProvider.cs" />
    <Compile Include="UnitOfWork.cs" />
    <Compile Include="UnitWrapper.cs" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PostBuildEvent>copy "$(TargetPath)" "$(ProjectDir)..\..\_bin\"</PostBuildEvent>
  </PropertyGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>