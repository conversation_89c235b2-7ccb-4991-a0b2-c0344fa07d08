﻿using System;
using System.Collections.Generic;
using System.Linq;

using NHibernate;
using NHibernate.Criterion;

namespace Upp.Irms.Core
{
	public interface IRepository
	{
		//
	}

	public interface IRepository<TEntity> : IRepository
	{
		#region Methods

		void Add(TEntity entity);
		Int32 Count(DetachedCriteria criteria);
		void Evict(TEntity entity);
		T Function<T>(DetachedCriteria criteria);
		IList<TEntity> List(DetachedCriteria criteria);
		TEntity Merge(TEntity entity);
		IQueryable<TEntity> Query();
		IQuery Query(String query);
        ISQLQuery SQLQuery(String query);
		void Refresh(TEntity entity);
		void Remove(TEntity entity);
		TEntity Retrieve(DetachedCriteria criteria);
		TEntity Retrieve(Int32? id);
		void Update(TEntity entity);

		#endregion
	}
}
