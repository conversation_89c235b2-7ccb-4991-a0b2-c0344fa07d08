﻿using System;

using Castle.Windsor;
using Castle.MicroKernel.Registration;
using Castle.Windsor.Configuration.Interpreters;

namespace Upp.Irms.Core
{
	public sealed class Ioc
	{
		#region Fields

		static readonly WindsorContainer _container = null;

		#endregion

		#region Properties

		public static WindsorContainer Container
		{
			get { return _container; }
		}

		#endregion

		#region Constructor

		static Ioc()
		{
            _container = new WindsorContainer();
            _container.Install(Castle.Windsor.Installer.Configuration.FromAppConfig());
		}

		#endregion
	}
}
