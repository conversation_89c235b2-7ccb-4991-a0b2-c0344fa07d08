﻿using System;
using System.Collections.Generic;

namespace Upp.Irms.Core
{
	public sealed class Repositories
	{
		#region Fields

		static readonly Dictionary<String, IRepository> _repositories = null;

		#endregion

		#region Constructor

		static Repositories()
		{
			_repositories = new Dictionary<String, IRepository>();
		}

		#endregion

		#region Methods.Public

		public static IRepository<TEntity> Get<TEntity>()
		{
			String name = typeof(TEntity).Name;
			//
			if (_repositories.ContainsKey(name)) return _repositories[name] as IRepository<TEntity>;
			//
			if (Ioc.Container == null) throw new Exception("Uninitialized Ioc Container.");
			//
			IRepository<TEntity> result = Ioc.Container.Resolve<IRepository<TEntity>>();
			if (result == null) throw new Exception(String.Format("Unable to resolve IRepository<{0}>.", name));
			else if (!_repositories.Contains<PERSON>ey(name)) _repositories.Add(name, result); // <-- Not 100% sure why.
			//
			return result;
		}

		#endregion
	}
}
