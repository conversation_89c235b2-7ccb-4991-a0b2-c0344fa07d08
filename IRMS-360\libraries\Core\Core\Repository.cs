﻿using System;
using System.Collections.Generic;
using System.Linq;

using NHibernate;
using NHibernate.Criterion;
using NHibernate.Linq;

namespace Upp.Irms.Core
{
	public class Repository<TEntity> : IRepository<TEntity>
	{
		#region Constructor

		public Repository()
		{
			//
		}

		#endregion

		#region Methods.IRepository<TEntity>

		public void Add(TEntity entity)
		{
			if (entity != null) this.Operation(() => SessionProvider.CurrentSession.Save(entity));
		}

		public Int32 Count(DetachedCriteria criteria)
		{
			ICriteria clone = criteria.GetExecutableCriteria(SessionProvider.CurrentSession).Clone() as ICriteria;
			return this.Operation(() => clone.SetProjection(Projections.Count("Id")).UniqueResult<Int32>());
		}

		public void Evict(TEntity entity)
		{
			if (entity != null) this.Operation(() => SessionProvider.CurrentSession.Evict(entity));
		}

		public T Function<T>(DetachedCriteria criteria)
		{
			return this.Operation(() => criteria.GetExecutableCriteria(SessionProvider.CurrentSession).UniqueResult<T>());
		}

		public IList<TEntity> List(DetachedCriteria criteria)
		{
			return this.Operation(() => criteria.GetExecutableCriteria(SessionProvider.CurrentSession).List<TEntity>());
		}

		public TEntity Merge(TEntity entity)
		{
            if (entity != null)
            {
                object obj = (object)entity;
                return (TEntity)this.Operation(() => SessionProvider.CurrentSession.Merge(obj));
            }

            else return default(TEntity);
            //return entity;
		}

		public IQueryable<TEntity> Query()
		{
			return SessionProvider.CurrentSession.Query<TEntity>();
		}

		public IQuery Query(String query)
		{
			return SessionProvider.CurrentSession.CreateQuery(query);
		}

        public ISQLQuery SQLQuery(String query)
        {
            return SessionProvider.CurrentSession.CreateSQLQuery(query);
        }

		public void Refresh(TEntity entity)
		{
			if (entity != null) this.Operation(() => SessionProvider.CurrentSession.Refresh(entity));
		}

		public void Remove(TEntity entity)
		{
			if (entity != null) this.Operation(() => SessionProvider.CurrentSession.Delete(entity));
		}

		public TEntity Retrieve(DetachedCriteria criteria)
		{
			return this.Operation(() => criteria.GetExecutableCriteria(SessionProvider.CurrentSession).UniqueResult<TEntity>());
		}

		public TEntity Retrieve(Int32? id)
		{
			return this.Operation(() => SessionProvider.CurrentSession.Get<TEntity>(id));
		}

		public void Update(TEntity entity)
		{
			if (entity != null) this.Operation(() => SessionProvider.CurrentSession.Update(entity));
		}

		#endregion

		#region Methods.Private

		private TResult Operation<TResult>(Func<TResult> function)
		{
			if (SessionProvider.CurrentSession.Transaction.IsActive) return function.Invoke();
			//
			TResult result;
			using (ITransaction tranaction = SessionProvider.CurrentSession.BeginTransaction())
			{
				result = function.Invoke();
				tranaction.Commit();
			}
			//
			return result;
		}

		private void Operation(Action action)
		{
			this.Operation<Boolean>(() =>
				{
					action.Invoke();
					return true;
				});
		}

		#endregion
	}
}
