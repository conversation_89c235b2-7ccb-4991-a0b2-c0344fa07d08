﻿using System;
using System.Data;

using NHibernate;
using NHibernate.Context;

namespace Upp.Irms.Core
{
	public class SessionManager : IUnitOfWork
	{
		#region Fields

		private readonly ISession _session = null;
		private readonly ITransaction _transaction = null;

		#endregion

		#region Constructor

		public SessionManager()
		{
			_session = SessionProvider.OpenSession();
			//
			if (_session != null)
			{
				_transaction = _session.BeginTransaction(IsolationLevel.ReadCommitted);
				CurrentSessionContext.Bind(_session);
			}
		}

		#endregion

		#region Methods.IDisposable

		public void Dispose()
		{
			if (_transaction != null) _transaction.Dispose();
			//
			if (_session != null)
			{
				_session.Close();
				_session.Dispose();
				CurrentSessionContext.Unbind(SessionProvider.SessionFactory);
			}
		}

		#endregion

		#region Methods.IUnitOfWork

		public void Commit()
		{
			if (_transaction != null) _transaction.Commit();
		}

		public void Rollback()
		{
			if (_transaction != null) _transaction.Rollback();
		}

		#endregion
	}
}
