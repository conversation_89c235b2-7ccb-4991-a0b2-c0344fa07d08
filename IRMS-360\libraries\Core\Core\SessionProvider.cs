﻿using System;

using NHibernate;
using NHibernate.Cfg;
using NHibernate.Context;

using Upp.Shared.Utilities;

namespace Upp.Irms.Core
{
	public sealed class SessionProvider
	{
		#region Fields

		static readonly ISessionFactory _factory = null;
		static readonly String _error = String.Empty;

		#endregion

		#region Properties

		internal static ISession CurrentSession
		{
			get
			{
				if (_factory != null) return _factory.GetCurrentSession();
				else throw new Exception(_error);
			}
		}

		internal static ISessionFactory SessionFactory
		{
			get { return _factory; }
		}

		#endregion

		#region Constructor

		static SessionProvider()
		{
			try
			{
				if (_factory != null) throw new Exception("Only one instance of session factory is allowed.");
				//
				Upp.Shared.Application.Configuration.Load();
				Configuration configuration = new Configuration().AddAssembly("Upp.Irms.Domain");
				if (configuration == null) throw new Exception("Unable to configure NHibernate.");
				if (!configuration.Properties.ContainsKey("connection.connection_string"))
					configuration.Properties.Add("connection.connection_string",
						Upp.Shared.Application.Configuration.Settings["Connection"]);
				//
				_factory = configuration.BuildSessionFactory();
				if (_factory == null) throw new Exception("Unable to build NHibernate session factory.");
			}
			catch (Exception ex)
			{
				_error = String.Format("SessionProvider(): {0}", Errors.GetError(ex));
			}
		}

		#endregion

		#region Methods.Internal

		internal static ISession OpenSession()
		{
			if (_factory != null)
			{
				if (!CurrentSessionContext.HasBind(_factory)) CurrentSessionContext.Bind(_factory.OpenSession());
				return _factory.GetCurrentSession();
				//return _factory.OpenSession();
			}
			else throw new Exception(_error);
		}

		#endregion

		#region Methods.Public

		public static void Initialize()
		{
			// Do nothing.
		}

		#endregion
	}
}
