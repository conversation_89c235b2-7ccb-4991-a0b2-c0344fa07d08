﻿using System;

using Upp.Shared.Utilities;

namespace Upp.Irms.Core
{
	public class UnitWrapper : IDisposable
	{
		#region Fields

		private IUnitOfWork _unit = null;
		private String _error = String.Empty;

		#endregion

		#region Properties

		public String Error
		{
			get { return _error; }
		}

		#endregion

		#region Constructor

		public UnitWrapper()
		{
			try { _unit = UnitOfWork.Start(); }
			catch (Exception ex) { _error = Errors.GetError(ex); }
		}

		#endregion

		#region Methods.IDisposable

		public void Dispose()
		{
			if (_unit != null) _unit.Dispose();
		}

		#endregion

		#region Methods.Public

		public void Execute(Action action)
		{
			if (_unit == null) return;
			//
			try
			{
				action.Invoke();
				//
				_unit.Commit();
			}
			catch (Exception ex)
			{
				_unit.Rollback();
				//
				_error = Errors.GetError(ex);
				if (Log.Instance != null)
				{
					String log = String.Format("{0}{1}{1}{2}", _error, Environment.NewLine, ex.StackTrace);
					Log.Instance.WriteError(log);
				}
				else _error = String.Format("{0}{1}{1}{2}", _error, Environment.NewLine, ex.StackTrace);
			}
		}

		#endregion
	}
}
