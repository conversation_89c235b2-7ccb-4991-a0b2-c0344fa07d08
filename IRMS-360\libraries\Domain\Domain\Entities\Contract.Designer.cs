using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Contract : Entity
	{
		#region Fields

		private Agency _agency;
		private ContractType _contractType;
		private Customer _customer;
		private DateTime _contractStart;
		private DateTime? _contractExpiration;
		private Decimal? _contractAmount;
		private Int32 _duration;
		private ICollection<ContractLine> _contractLines = new HashSet<ContractLine>();
		private ICollection<InventoryItem> _inventoryItems = new HashSet<InventoryItem>();
		private ICollection<PurchaseOrderHeader> _purchaseOrderHeaders = new HashSet<PurchaseOrderHeader>();
		private ICollection<RequisitionHeader> _requisitionHeaders = new HashSet<RequisitionHeader>();
		private OrganizationParticipant _eContact;
		private OrganizationParticipant _iContact;
		private String _contractCode;
		private String _description;
		private UnitOfMeasure _unitOfMeasure;
		private Vendor _vendor;

		#endregion

		#region Properties

		[DataMember]
		public virtual Agency Agency
		{
			get { return _agency; }
			set { _agency = value; }
		}

		[DataMember]
		public virtual ContractType ContractType
		{
			get { return _contractType; }
			set { _contractType = value; }
		}

		[DataMember]
		public virtual Customer Customer
		{
			get { return _customer; }
			set { _customer = value; }
		}

		[DataMember]
		public virtual DateTime ContractStart
		{
			get { return _contractStart; }
			set { _contractStart = value; }
		}

		[DataMember]
		public virtual DateTime? ContractExpiration
		{
			get { return _contractExpiration; }
			set { _contractExpiration = value; }
		}

		[DataMember]
		public virtual Decimal? ContractAmount
		{
			get { return _contractAmount; }
			set { _contractAmount = value; }
		}

		[DataMember]
		public virtual Int32 Duration
		{
			get { return _duration; }
			set { _duration = value; }
		}

		[DataMember]
		public virtual ICollection<ContractLine> ContractLines
		{
			get { return _contractLines; }
			set { _contractLines = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryItem> InventoryItems
		{
			get { return _inventoryItems; }
			set { _inventoryItems = value; }
		}

		[DataMember]
		public virtual ICollection<PurchaseOrderHeader> PurchaseOrderHeaders
		{
			get { return _purchaseOrderHeaders; }
			set { _purchaseOrderHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<RequisitionHeader> RequisitionHeaders
		{
			get { return _requisitionHeaders; }
			set { _requisitionHeaders = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant EContact
		{
			get { return _eContact; }
			set { _eContact = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant IContact
		{
			get { return _iContact; }
			set { _iContact = value; }
		}

		[DataMember]
		public virtual String ContractCode
		{
			get { return _contractCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ContractCode must not be blank or null.");
				else _contractCode = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual UnitOfMeasure UnitOfMeasure
		{
			get { return _unitOfMeasure; }
			set { _unitOfMeasure = value; }
		}

		[DataMember]
		public virtual Vendor Vendor
		{
			get { return _vendor; }
			set { _vendor = value; }
		}


		#endregion
	}
}
