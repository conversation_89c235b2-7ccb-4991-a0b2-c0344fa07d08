using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ContractLine : Entity
	{
		#region Fields

		private Contract _contract;
		private Decimal _amount;
		private ICollection<ContractSubLine> _contractSubLines = new HashSet<ContractSubLine>();
		private ICollection<InventoryItem> _inventoryItems = new HashSet<InventoryItem>();
		private String _active;
		private String _description;
		private String _lineCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual Contract Contract
		{
			get { return _contract; }
			set { _contract = value; }
		}

		[DataMember]
		public virtual Decimal Amount
		{
			get { return _amount; }
			set { _amount = value; }
		}

		[DataMember]
		public virtual ICollection<ContractSubLine> ContractSubLines
		{
			get { return _contractSubLines; }
			set { _contractSubLines = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryItem> InventoryItems
		{
			get { return _inventoryItems; }
			set { _inventoryItems = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String LineCode
		{
			get { return _lineCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("LineCode must not be blank or null.");
				else _lineCode = value;
			}
		}


		#endregion
	}
}
