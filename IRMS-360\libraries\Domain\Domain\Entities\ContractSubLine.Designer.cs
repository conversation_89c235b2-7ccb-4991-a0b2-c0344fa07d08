using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ContractSubLine : Entity
	{
		#region Fields

		private ContractLine _contractLine;
		private Decimal _amount;
		private ICollection<InventoryItem> _inventoryItems = new HashSet<InventoryItem>();
		private String _active;
		private String _description;
		private String _subLineCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ContractLine ContractLine
		{
			get { return _contractLine; }
			set { _contractLine = value; }
		}

		[DataMember]
		public virtual Decimal Amount
		{
			get { return _amount; }
			set { _amount = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryItem> InventoryItems
		{
			get { return _inventoryItems; }
			set { _inventoryItems = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String SubLineCode
		{
			get { return _subLineCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("SubLineCode must not be blank or null.");
				else _subLineCode = value;
			}
		}


		#endregion
	}
}
