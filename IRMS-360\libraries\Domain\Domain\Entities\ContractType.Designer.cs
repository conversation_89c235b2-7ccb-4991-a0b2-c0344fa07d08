using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ContractType : Entity
	{
		#region Fields

		private ICollection<Contract> _contracts = new HashSet<Contract>();
		private String _active;
		private String _contractTypeCode;
		private String _description;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<Contract> Contracts
		{
			get { return _contracts; }
			set { _contracts = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String ContractTypeCode
		{
			get { return _contractTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ContractTypeCode must not be blank or null.");
				else _contractTypeCode = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}


		#endregion
	}
}
