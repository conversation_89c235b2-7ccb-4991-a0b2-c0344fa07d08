using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class CountryCode : Entity
	{
		#region Fields

		private ICollection<Item> _items = new HashSet<Item>();
		private ICollection<ShipmentLocation> _shipmentLocations = new HashSet<ShipmentLocation>();
		private ICollection<StateCode> _stateCodes = new HashSet<StateCode>();
		private String _active;
		private String _description;
		private String _fips104Code;
		private String _isoA2Code;
		private String _isoA3Code;
		private String _isoCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<Item> Items
		{
			get { return _items; }
			set { _items = value; }
		}

		[DataMember]
		public virtual ICollection<ShipmentLocation> ShipmentLocations
		{
			get { return _shipmentLocations; }
			set { _shipmentLocations = value; }
		}

		[DataMember]
		public virtual ICollection<StateCode> StateCodes
		{
			get { return _stateCodes; }
			set { _stateCodes = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String Fips104Code
		{
			get { return _fips104Code; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Fips104Code must not be blank or null.");
				else _fips104Code = value;
			}
		}

		[DataMember]
		public virtual String IsoA2Code
		{
			get { return _isoA2Code; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("IsoA2Code must not be blank or null.");
				else _isoA2Code = value;
			}
		}

		[DataMember]
		public virtual String IsoA3Code
		{
			get { return _isoA3Code; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("IsoA3Code must not be blank or null.");
				else _isoA3Code = value;
			}
		}

		[DataMember]
		public virtual String IsoCode
		{
			get { return _isoCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("IsoCode must not be blank or null.");
				else _isoCode = value;
			}
		}


		#endregion
	}
}
