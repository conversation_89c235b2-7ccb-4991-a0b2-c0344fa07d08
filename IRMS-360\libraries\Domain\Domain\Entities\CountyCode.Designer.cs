using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class CountyCode : Entity
	{
		#region Fields

		private ICollection<ZipCode> _zipCodes = new HashSet<ZipCode>();
		private StateCode _stateCode;
		private String _active;
		private String _code;
		private String _description;
		private String _fipsCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<ZipCode> ZipCodes
		{
			get { return _zipCodes; }
			set { _zipCodes = value; }
		}

		[DataMember]
		public virtual StateCode StateCode
		{
			get { return _stateCode; }
			set { _stateCode = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Code
		{
			get { return _code; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Code must not be blank or null.");
				else _code = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String FipsCode
		{
			get { return _fipsCode; }
			set { _fipsCode = value; }
		}


		#endregion
	}
}
