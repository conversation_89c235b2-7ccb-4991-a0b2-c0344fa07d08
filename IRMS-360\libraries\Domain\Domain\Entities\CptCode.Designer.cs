using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class CptCode : Entity
	{
		#region Fields

		private DateTime? _effectiveDate;
		private DateTime? _terminationDate;
		private ICollection<InterfaceCpt> _interfaceCpts = new HashSet<InterfaceCpt>();
		private ICollection<ServiceDetail> _serviceDetails = new HashSet<ServiceDetail>();
		private String _active;
		private String _alternateCptCode;
		private String _code;
		private String _description;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime? EffectiveDate
		{
			get { return _effectiveDate; }
			set { _effectiveDate = value; }
		}

		[DataMember]
		public virtual DateTime? TerminationDate
		{
			get { return _terminationDate; }
			set { _terminationDate = value; }
		}

		[DataMember]
		public virtual ICollection<InterfaceCpt> InterfaceCpts
		{
			get { return _interfaceCpts; }
			set { _interfaceCpts = value; }
		}

		[DataMember]
		public virtual ICollection<ServiceDetail> ServiceDetails
		{
			get { return _serviceDetails; }
			set { _serviceDetails = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String AlternateCptCode
		{
			get { return _alternateCptCode; }
			set { _alternateCptCode = value; }
		}

		[DataMember]
		public virtual String Code
		{
			get { return _code; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Code must not be blank or null.");
				else _code = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}


		#endregion
	}
}
