using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class CrossReferenceType : Entity
	{
		#region Fields

		private ICollection<ItemCrossReference> _itemCrossReferences = new HashSet<ItemCrossReference>();
		private String _active;
		private String _crossReferenceCode;
		private String _description;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<ItemCrossReference> ItemCrossReferences
		{
			get { return _itemCrossReferences; }
			set { _itemCrossReferences = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String CrossReferenceCode
		{
			get { return _crossReferenceCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("CrossReferenceCode must not be blank or null.");
				else _crossReferenceCode = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}


		#endregion
	}
}
