using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Customer : Entity
	{
		#region Fields

		private Agency _agency;
        private Aisle _aisle;
        private Byte[] _customerLogo;
		private CompanyLocationType _companyLocationType;
		private Customer _parentCustomer;
		private CustomerType _customerType;
		private Int32? _currentProNumber;
		private ICollection<BillingRate> _billingRates = new HashSet<BillingRate>();
		private ICollection<BusinessRuleDetail> _businessRuleDetails = new HashSet<BusinessRuleDetail>();
		private ICollection<Comment> _comments = new HashSet<Comment>();
		private ICollection<Contract> _contracts = new HashSet<Contract>();
		private ICollection<Customer> _childCustomers = new HashSet<Customer>();
		private ICollection<CustomerBillingPeriod> _customerBillingPeriods = new HashSet<CustomerBillingPeriod>();
		private ICollection<CustomerBillingTerm> _customerBillingTerms = new HashSet<CustomerBillingTerm>();
		private ICollection<CustomerBroker> _customerBrokers = new HashSet<CustomerBroker>();
		private ICollection<CustomerCommunication> _customerCommunications = new HashSet<CustomerCommunication>();
		private ICollection<CustomerCurrencyCode> _customerCurrencyCodes = new HashSet<CustomerCurrencyCode>();
		private ICollection<CustomerItem> _customerItems = new HashSet<CustomerItem>();
		private ICollection<CustomerLocation> _customerLocations = new HashSet<CustomerLocation>();
		private ICollection<CustomerPaymentMethod> _customerPaymentMethods = new HashSet<CustomerPaymentMethod>();
		private ICollection<CustomerShipmentReference> _customerShipmentReferences = new HashSet<CustomerShipmentReference>();
		private ICollection<InventoryItem> _inventoryItems = new HashSet<InventoryItem>();
		private ICollection<InvoiceHeader> _invoiceHeaders = new HashSet<InvoiceHeader>();
		private ICollection<Item> _items = new HashSet<Item>();
		private ICollection<ItemTransaction> _itemTransactions = new HashSet<ItemTransaction>();
		private ICollection<LocationCustomer> _locationCustomers = new HashSet<LocationCustomer>();
		private ICollection<OrderHeader> _orderHeaders = new HashSet<OrderHeader>();
		private ICollection<OrganizationParticipant> _organizationParticipants = new HashSet<OrganizationParticipant>();
		private ICollection<PurchaseOrderHeader> _purchaseOrderHeaders = new HashSet<PurchaseOrderHeader>();
		private ICollection<ReportFormat> _reportFormats = new HashSet<ReportFormat>();
		private ICollection<ReturnHeader> _returnHeaders = new HashSet<ReturnHeader>();
		private ICollection<UserAccountCustomer> _userAccountCustomers = new HashSet<UserAccountCustomer>();
		private ICollection<Vendor> _vendors = new HashSet<Vendor>();
		private String _active;
		private String _alternateCustomerCode;
		private String _cartonVerification;
		private String _customerCode;
		private String _customerPackingList;
		private String _dbaName;
		private String _edi;
		private String _excludeBillTo;
		private String _fein;
		private String _hidePrice;
		private String _name;
		private String _notes;
		private String _shipComplete;
		private String _uccDesignation;
		private String _url;
		private TimeZone _timeZone;

		#endregion

		#region Properties

		[DataMember]
		public virtual Agency Agency
		{
			get { return _agency; }
			set { _agency = value; }
		}

        [DataMember]
        public virtual Aisle Aisle
        {
            get { return _aisle; }
            set { _aisle = value; }
        }

        [DataMember]
        public virtual Byte[] CustomerLogo
        {
            get { return _customerLogo; }
            set { _customerLogo = value; }
        }

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual Customer ParentCustomer
		{
			get { return _parentCustomer; }
			set { _parentCustomer = value; }
		}

		[DataMember]
		public virtual CustomerType CustomerType
		{
			get { return _customerType; }
			set { _customerType = value; }
		}

		[DataMember]
		public virtual Int32? CurrentProNumber
		{
			get { return _currentProNumber; }
			set { _currentProNumber = value; }
		}

		[DataMember]
		public virtual ICollection<BillingRate> BillingRates
		{
			get { return _billingRates; }
			set { _billingRates = value; }
		}

		[DataMember]
		public virtual ICollection<BusinessRuleDetail> BusinessRuleDetails
		{
			get { return _businessRuleDetails; }
			set { _businessRuleDetails = value; }
		}

		[DataMember]
		public virtual ICollection<Comment> Comments
		{
			get { return _comments; }
			set { _comments = value; }
		}

		[DataMember]
		public virtual ICollection<Contract> Contracts
		{
			get { return _contracts; }
			set { _contracts = value; }
		}

		[DataMember]
		public virtual ICollection<Customer> ChildCustomers
		{
			get { return _childCustomers; }
			set { _childCustomers = value; }
		}

		[DataMember]
		public virtual ICollection<CustomerBillingPeriod> CustomerBillingPeriods
		{
			get { return _customerBillingPeriods; }
			set { _customerBillingPeriods = value; }
		}

		[DataMember]
		public virtual ICollection<CustomerBillingTerm> CustomerBillingTerms
		{
			get { return _customerBillingTerms; }
			set { _customerBillingTerms = value; }
		}

		[DataMember]
		public virtual ICollection<CustomerBroker> CustomerBrokers
		{
			get { return _customerBrokers; }
			set { _customerBrokers = value; }
		}

		[DataMember]
		public virtual ICollection<CustomerCommunication> CustomerCommunications
		{
			get { return _customerCommunications; }
			set { _customerCommunications = value; }
		}

		[DataMember]
		public virtual ICollection<CustomerCurrencyCode> CustomerCurrencyCodes
		{
			get { return _customerCurrencyCodes; }
			set { _customerCurrencyCodes = value; }
		}

		[DataMember]
		public virtual ICollection<CustomerItem> CustomerItems
		{
			get { return _customerItems; }
			set { _customerItems = value; }
		}

		[DataMember]
		public virtual ICollection<CustomerLocation> CustomerLocations
		{
			get { return _customerLocations; }
			set { _customerLocations = value; }
		}

		[DataMember]
		public virtual ICollection<CustomerPaymentMethod> CustomerPaymentMethods
		{
			get { return _customerPaymentMethods; }
			set { _customerPaymentMethods = value; }
		}

		[DataMember]
		public virtual ICollection<CustomerShipmentReference> CustomerShipmentReferences
		{
			get { return _customerShipmentReferences; }
			set { _customerShipmentReferences = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryItem> InventoryItems
		{
			get { return _inventoryItems; }
			set { _inventoryItems = value; }
		}

		[DataMember]
		public virtual ICollection<InvoiceHeader> InvoiceHeaders
		{
			get { return _invoiceHeaders; }
			set { _invoiceHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<Item> Items
		{
			get { return _items; }
			set { _items = value; }
		}

		[DataMember]
		public virtual ICollection<ItemTransaction> ItemTransactions
		{
			get { return _itemTransactions; }
			set { _itemTransactions = value; }
		}

		[DataMember]
		public virtual ICollection<LocationCustomer> LocationCustomers
		{
			get { return _locationCustomers; }
			set { _locationCustomers = value; }
		}

		[DataMember]
		public virtual ICollection<OrderHeader> OrderHeaders
		{
			get { return _orderHeaders; }
			set { _orderHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<OrganizationParticipant> OrganizationParticipants
		{
			get { return _organizationParticipants; }
			set { _organizationParticipants = value; }
		}

		[DataMember]
		public virtual ICollection<PurchaseOrderHeader> PurchaseOrderHeaders
		{
			get { return _purchaseOrderHeaders; }
			set { _purchaseOrderHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<ReportFormat> ReportFormats
		{
			get { return _reportFormats; }
			set { _reportFormats = value; }
		}

		[DataMember]
		public virtual ICollection<ReturnHeader> ReturnHeaders
		{
			get { return _returnHeaders; }
			set { _returnHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<UserAccountCustomer> UserAccountCustomers
		{
			get { return _userAccountCustomers; }
			set { _userAccountCustomers = value; }
		}

		[DataMember]
		public virtual ICollection<Vendor> Vendors
		{
			get { return _vendors; }
			set { _vendors = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String AlternateCustomerCode
		{
			get { return _alternateCustomerCode; }
			set { _alternateCustomerCode = value; }
		}

		[DataMember]
		public virtual String CartonVerification
		{
			get { return _cartonVerification; }
			set { _cartonVerification = value; }
		}

		[DataMember]
		public virtual String CustomerCode
		{
			get { return _customerCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("CustomerCode must not be blank or null.");
				else _customerCode = value;
			}
		}
		[DataMember]
		public virtual String CustomerPackingList
		{
			get { return _customerPackingList; }
			set { _customerPackingList = value; }
		}

		[DataMember]
		public virtual String DbaName
		{
			get { return _dbaName; }
			set { _dbaName = value; }
		}

		[DataMember]
		public virtual String Edi
		{
			get { return _edi; }
			set { _edi = value; }
		}

		[DataMember]
		public virtual String ExcludeBillTo
		{
			get { return _excludeBillTo; }
			set { _excludeBillTo = value; }
		}

		[DataMember]
		public virtual String Fein
		{
			get { return _fein; }
			set { _fein = value; }
		}

		[DataMember]
		public virtual String HidePrice
		{
		    get { return _hidePrice; }
		    set { _hidePrice = value; }
		}

		[DataMember]
		public virtual String Name
		{
			get { return _name; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Name must not be blank or null.");
				else _name = value;
			}
		}

		[DataMember]
		public virtual String Notes
		{
			get { return _notes; }
			set { _notes = value; }
		}

		[DataMember]
		public virtual String ShipComplete
		{
			get { return _shipComplete; }
			set { _shipComplete = value; }
		}

		[DataMember]
		public virtual String UccDesignation
		{
			get { return _uccDesignation; }
			set { _uccDesignation = value; }
		}

		[DataMember]
		public virtual String Url
		{
			get { return _url; }
			set { _url = value; }
		}

		[DataMember]
		public virtual TimeZone TimeZone
		{
			get { return _timeZone; }
			set { _timeZone = value; }
		}


		#endregion
	}
}
