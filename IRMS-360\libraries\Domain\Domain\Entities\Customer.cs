using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class Customer : Entity
	{
		#region Constructor

		public Customer()
		{
			//
		}

		#endregion

        #region Report Properties

        public virtual Int32? CustomerLocationTypeId { get; set; }
        public virtual String AddressLine1 { get; set; }
        public virtual String AddressLine2 { get; set; }
        public virtual String AddressLine3 { get; set; }
        public virtual String AddressSuffix { get; set; }
        public virtual String AddressNumber { get; set; }
        public virtual String City { get; set; }
        public virtual String CustomerLocationTypeCode { get; set; }
        public virtual String CustomerLocationTypeFAC { get; set; }
        public virtual String State { get; set; }
        public virtual String ZipCodeCode { get; set; }

        #endregion Report Properties
    }
}
