using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class CustomerBillingPeriod : Entity
	{
		#region Fields

		private BillingPeriod _billingPeriod;
		private Customer _customer;
		private DateTime _effective;
		private DateTime? _expiration;
		private ICollection<InvoiceHeader> _invoiceHeaders = new HashSet<InvoiceHeader>();

		#endregion

		#region Properties

		[DataMember]
		public virtual BillingPeriod BillingPeriod
		{
			get { return _billingPeriod; }
			set { _billingPeriod = value; }
		}

		[DataMember]
		public virtual Customer Customer
		{
			get { return _customer; }
			set { _customer = value; }
		}

		[DataMember]
		public virtual DateTime Effective
		{
			get { return _effective; }
			set { _effective = value; }
		}

		[DataMember]
		public virtual DateTime? Expiration
		{
			get { return _expiration; }
			set { _expiration = value; }
		}

		[DataMember]
		public virtual ICollection<InvoiceHeader> InvoiceHeaders
		{
			get { return _invoiceHeaders; }
			set { _invoiceHeaders = value; }
		}


		#endregion
	}
}
