using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class CustomerBillingTerm : Entity
	{
		#region Fields

		private BillingTerm _billingTerm;
		private Customer _customer;
		private String _active;

		#endregion

		#region Properties

		[DataMember]
		public virtual BillingTerm BillingTerm
		{
			get { return _billingTerm; }
			set { _billingTerm = value; }
		}

		[DataMember]
		public virtual Customer Customer
		{
			get { return _customer; }
			set { _customer = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}


		#endregion
	}
}
