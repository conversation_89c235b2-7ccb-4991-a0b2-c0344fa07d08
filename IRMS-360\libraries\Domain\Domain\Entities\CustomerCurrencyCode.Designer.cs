using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class CustomerCurrencyCode : Entity
	{
		#region Fields

		private CurrencyCode _currencyCode;
		private Customer _customer;
		private CustomerLocation _customerLocation;
		private DateTime _effective;
		private ICollection<OrderHeader> _orderHeaders = new HashSet<OrderHeader>();
		private String _primaryCurrency;

		#endregion

		#region Properties

		[DataMember]
		public virtual CurrencyCode CurrencyCode
		{
			get { return _currencyCode; }
			set { _currencyCode = value; }
		}

		[DataMember]
		public virtual Customer Customer
		{
			get { return _customer; }
			set { _customer = value; }
		}

		[DataMember]
		public virtual CustomerLocation CustomerLocation
		{
			get { return _customerLocation; }
			set { _customerLocation = value; }
		}

		[DataMember]
		public virtual DateTime Effective
		{
			get { return _effective; }
			set { _effective = value; }
		}

		[DataMember]
		public virtual ICollection<OrderHeader> OrderHeaders
		{
			get { return _orderHeaders; }
			set { _orderHeaders = value; }
		}

		[DataMember]
		public virtual String PrimaryCurrency
		{
			get { return _primaryCurrency; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("PrimaryCurrency must not be blank or null.");
				else _primaryCurrency = value;
			}
		}


		#endregion
	}
}
