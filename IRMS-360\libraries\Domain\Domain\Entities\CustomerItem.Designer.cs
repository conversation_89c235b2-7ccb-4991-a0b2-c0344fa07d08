using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class CustomerItem : Entity
	{
		#region Fields

		private Customer _customer;
		private CustomerItem _parentCustomerItem;
		private CustomerLocation _customerLocation;
		private DateTime _effective;
		private DateTime? _expiration;
		private Decimal? _salesPrice;
		private Item _item;
		private ICollection<CustomerItem> _childCustomerItems = new HashSet<CustomerItem>();
		private String _customerItemCode;
		private String _description;

		#endregion

		#region Properties

		[DataMember]
		public virtual Customer Customer
		{
			get { return _customer; }
			set { _customer = value; }
		}

		[DataMember]
		public virtual CustomerItem ParentCustomerItem
		{
			get { return _parentCustomerItem; }
			set { _parentCustomerItem = value; }
		}

		[DataMember]
		public virtual CustomerLocation CustomerLocation
		{
			get { return _customerLocation; }
			set { _customerLocation = value; }
		}

		[DataMember]
		public virtual DateTime Effective
		{
			get { return _effective; }
			set { _effective = value; }
		}

		[DataMember]
		public virtual DateTime? Expiration
		{
			get { return _expiration; }
			set { _expiration = value; }
		}

		[DataMember]
		public virtual Decimal? SalesPrice
		{
			get { return _salesPrice; }
			set { _salesPrice = value; }
		}

		[DataMember]
		public virtual Item Item
		{
			get { return _item; }
			set { _item = value; }
		}

		[DataMember]
		public virtual ICollection<CustomerItem> ChildCustomerItems
		{
			get { return _childCustomerItems; }
			set { _childCustomerItems = value; }
		}

		[DataMember]
		public virtual String CustomerItemCode
		{
			get { return _customerItemCode; }
			set { _customerItemCode = value; }
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set { _description = value; }
		}


		#endregion
	}
}
