using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class CustomerItem : Entity
	{
		#region Properties.Reports

        public virtual String CustomerCode { get; set; }
        public virtual int? CustomerId { get; set; }
		public virtual String ItemCode { get; set; }
        public virtual int? ItemId { get; set; }
		#endregion

		#region Constructor

		public CustomerItem()
		{
			//
		}

		#endregion
	}
}
