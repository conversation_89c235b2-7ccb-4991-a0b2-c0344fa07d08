using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class CustomerLocation : Entity
	{
		#region Fields

		private Customer _customer;
		private ICollection<CustomerCommunication> _customerCommunications = new HashSet<CustomerCommunication>();
		private ICollection<CustomerCurrencyCode> _customerCurrencyCodes = new HashSet<CustomerCurrencyCode>();
		private ICollection<CustomerItem> _customerItems = new HashSet<CustomerItem>();
		private ICollection<CustomerLocationType> _customerLocationTypes = new HashSet<CustomerLocationType>();
		private ICollection<Location> _locations = new HashSet<Location>();
		private String _active;
		private String _addressDirection;
		private String _addressLine1;
		private String _addressLine2;
		private String _addressLine3;
		private String _addressNumber;
		private String _addressSuffix;
		private String _city;
		private String _country;
		private String _stateCode;
		private String _zip;
		private TimeZone _timeZone;
		private ZipCode _zipCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual Customer Customer
		{
			get { return _customer; }
			set { _customer = value; }
		}

		[DataMember]
		public virtual ICollection<CustomerCommunication> CustomerCommunications
		{
			get { return _customerCommunications; }
			set { _customerCommunications = value; }
		}

		[DataMember]
		public virtual ICollection<CustomerCurrencyCode> CustomerCurrencyCodes
		{
			get { return _customerCurrencyCodes; }
			set { _customerCurrencyCodes = value; }
		}

		[DataMember]
		public virtual ICollection<CustomerItem> CustomerItems
		{
			get { return _customerItems; }
			set { _customerItems = value; }
		}

		[DataMember]
		public virtual ICollection<CustomerLocationType> CustomerLocationTypes
		{
			get { return _customerLocationTypes; }
			set { _customerLocationTypes = value; }
		}

		[DataMember]
		public virtual ICollection<Location> Locations
		{
			get { return _locations; }
			set { _locations = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String AddressDirection
		{
			get { return _addressDirection; }
			set { _addressDirection = value; }
		}

		[DataMember]
		public virtual String AddressLine1
		{
			get { return _addressLine1; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("AddressLine1 must not be blank or null.");
				else _addressLine1 = value;
			}
		}

		[DataMember]
		public virtual String AddressLine2
		{
			get { return _addressLine2; }
			set { _addressLine2 = value; }
		}

		[DataMember]
		public virtual String AddressLine3
		{
			get { return _addressLine3; }
			set { _addressLine3 = value; }
		}

		[DataMember]
		public virtual String AddressNumber
		{
			get { return _addressNumber; }
			set { _addressNumber = value; }
		}

		[DataMember]
		public virtual String AddressSuffix
		{
			get { return _addressSuffix; }
			set { _addressSuffix = value; }
		}

		[DataMember]
		public virtual String City
		{
			get { return _city; }
			set { _city = value; }
		}

		[DataMember]
		public virtual String Country
		{
			get { return _country; }
			set { _country = value; }
		}

		[DataMember]
		public virtual String StateCode
		{
			get { return _stateCode; }
			set { _stateCode = value; }
		}

		[DataMember]
		public virtual String Zip
		{
			get { return _zip; }
			set { _zip = value; }
		}

		[DataMember]
		public virtual TimeZone TimeZone
		{
			get { return _timeZone; }
			set { _timeZone = value; }
		}

		[DataMember]
		public virtual ZipCode ZipCode
		{
			get { return _zipCode; }
			set { _zipCode = value; }
		}


		#endregion
	}
}
