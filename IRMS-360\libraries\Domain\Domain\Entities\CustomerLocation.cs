using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class CustomerLocation : Entity
	{
		#region Constructor

		public CustomerLocation()
		{
			//
		}

        #endregion

        #region Report Properties

        [DataMember]
        public virtual Int32? ZipCodeId { get; set; }
        [DataMember]
        public virtual Int32? CustomerId { get; set; }
        public virtual Int32? CompanyLocationTypeId { get; set; }
        [DataMember]
        public virtual string CustomerCode { get; set; }
        [DataMember]
        public virtual string LocationTypeDescription { get; set; }
        public virtual string LocationTypeCode { get; set; }
        [DataMember]
        public virtual string Name { get; set; }
        [DataMember]
        public virtual string ZipCodeCity { get; set; }
        [DataMember]
        public virtual string ZipCodeCountry { get; set; }      
        [DataMember]
        public virtual string ZipCodeState { get; set; }
        [DataMember]
        public virtual string ZipCodeValue { get; set; }
        [DataMember]
        public virtual string Email { get; set; }
        [DataMember]
        public virtual string Phone { get; set; }
        [DataMember]
        public virtual string Website { get; set; }
        [DataMember]
        public virtual string Fax { get; set; }
        [DataMember]
        public virtual string PackingListMessage { get; set; }

        #endregion Report Properties
    }
}
