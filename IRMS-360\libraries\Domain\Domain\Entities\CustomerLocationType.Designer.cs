using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class CustomerLocationType : Entity
	{
		#region Fields

		private CustomerLocation _customerLocation;
		private ICollection<LicensePlate> _licensePlates = new HashSet<LicensePlate>();
		private ICollection<OrderHeader> _orderHeaders = new HashSet<OrderHeader>();
		private ICollection<OrganizationParticipant> _organizationParticipants = new HashSet<OrganizationParticipant>();
		private ICollection<Pool> _pools = new HashSet<Pool>();
		private ICollection<RouteDetail> _routeDetails = new HashSet<RouteDetail>();
		private LocationType _locationType;
		private RouteHeader _routeHeader;
		private String _active;
		private String _customerLocationCode;
		private String _description;

		#endregion

		#region Properties

		[DataMember]
		public virtual CustomerLocation CustomerLocation
		{
			get { return _customerLocation; }
			set { _customerLocation = value; }
		}

		[DataMember]
		public virtual ICollection<LicensePlate> LicensePlates
		{
			get { return _licensePlates; }
			set { _licensePlates = value; }
		}

		[DataMember]
		public virtual ICollection<OrderHeader> OrderHeaders
		{
			get { return _orderHeaders; }
			set { _orderHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<OrganizationParticipant> OrganizationParticipants
		{
			get { return _organizationParticipants; }
			set { _organizationParticipants = value; }
		}

		[DataMember]
		public virtual ICollection<Pool> Pools
		{
			get { return _pools; }
			set { _pools = value; }
		}

		[DataMember]
		public virtual ICollection<RouteDetail> RouteDetails
		{
			get { return _routeDetails; }
			set { _routeDetails = value; }
		}

		[DataMember]
		public virtual LocationType LocationType
		{
			get { return _locationType; }
			set { _locationType = value; }
		}

		[DataMember]
		public virtual RouteHeader RouteHeader
		{
			get { return _routeHeader; }
			set { _routeHeader = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String CustomerLocationCode
		{
			get { return _customerLocationCode; }
			set { _customerLocationCode = value; }
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set { _description = value; }
		}


		#endregion
	}
}
