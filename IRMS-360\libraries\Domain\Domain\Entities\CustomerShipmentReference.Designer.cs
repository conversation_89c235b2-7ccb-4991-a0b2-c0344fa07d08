using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class CustomerShipmentReference : Entity
	{
		#region Fields

		private Customer _customer;
		private ICollection<OrderReferenceCode> _orderReferenceCodes = new HashSet<OrderReferenceCode>();
		private ReferenceCode _referenceCode;
		private String _active;
		private String _required;

		#endregion

		#region Properties

		[DataMember]
		public virtual Customer Customer
		{
			get { return _customer; }
			set { _customer = value; }
		}

		[DataMember]
		public virtual ICollection<OrderReferenceCode> OrderReferenceCodes
		{
			get { return _orderReferenceCodes; }
			set { _orderReferenceCodes = value; }
		}

		[DataMember]
		public virtual ReferenceCode ReferenceCode
		{
			get { return _referenceCode; }
			set { _referenceCode = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Required
		{
			get { return _required; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Required must not be blank or null.");
				else _required = value;
			}
		}


		#endregion
	}
}
