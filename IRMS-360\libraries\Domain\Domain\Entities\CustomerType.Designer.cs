using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class CustomerType : Entity
	{
		#region Fields

		private ICollection<Customer> _customers = new HashSet<Customer>();
		private String _active;
		private String _customerTypeCode;
		private String _description;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<Customer> Customers
		{
			get { return _customers; }
			set { _customers = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String CustomerTypeCode
		{
			get { return _customerTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("CustomerTypeCode must not be blank or null.");
				else _customerTypeCode = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}


		#endregion
	}
}
