using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class DamageDescription : Entity
	{
		#region Fields

		private ICollection<ClaimDetail> _claimDetails = new HashSet<ClaimDetail>();
		private String _active;
		private String _damageDescriptionCode;
		private String _description;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<ClaimDetail> ClaimDetails
		{
			get { return _claimDetails; }
			set { _claimDetails = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String DamageDescriptionCode
		{
			get { return _damageDescriptionCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("DamageDescriptionCode must not be blank or null.");
				else _damageDescriptionCode = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}


		#endregion
	}
}
