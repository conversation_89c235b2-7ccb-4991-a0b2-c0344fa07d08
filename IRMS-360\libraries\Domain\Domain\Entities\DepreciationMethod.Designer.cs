using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class DepreciationMethod : Entity
	{
		#region Fields

		private ICollection<InventoryFinancialSchedule> _inventoryFinancialSchedules = new HashSet<InventoryFinancialSchedule>();
		private ICollection<Item> _items = new HashSet<Item>();
		private String _active;
		private String _depreciationMethodCode;
		private String _description;
		private String _formula;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<InventoryFinancialSchedule> InventoryFinancialSchedules
		{
			get { return _inventoryFinancialSchedules; }
			set { _inventoryFinancialSchedules = value; }
		}

		[DataMember]
		public virtual ICollection<Item> Items
		{
			get { return _items; }
			set { _items = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String DepreciationMethodCode
		{
			get { return _depreciationMethodCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("DepreciationMethodCode must not be blank or null.");
				else _depreciationMethodCode = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String Formula
		{
			get { return _formula; }
			set { _formula = value; }
		}


		#endregion
	}
}
