using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Disorder : Entity
	{
		#region Fields

		private DisorderType _disorderType;
		private String _active;
		private String _description;
		private String _disorderCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual DisorderType DisorderType
		{
			get { return _disorderType; }
			set { _disorderType = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String DisorderCode
		{
			get { return _disorderCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("DisorderCode must not be blank or null.");
				else _disorderCode = value;
			}
		}


		#endregion
	}
}
