using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class DisorderType : Entity
	{
		#region Fields

		private ICollection<Disorder> _disorders = new HashSet<Disorder>();
		private String _active;
		private String _description;
		private String _disorderTypeCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<Disorder> Disorders
		{
			get { return _disorders; }
			set { _disorders = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String DisorderTypeCode
		{
			get { return _disorderTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("DisorderTypeCode must not be blank or null.");
				else _disorderTypeCode = value;
			}
		}


		#endregion
	}
}
