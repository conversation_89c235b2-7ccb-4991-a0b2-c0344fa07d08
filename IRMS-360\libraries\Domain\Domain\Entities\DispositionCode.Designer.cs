using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class DispositionCode : Entity
	{
		#region Fields

		private ICollection<InspectionDetail> _inspectionDetails = new HashSet<InspectionDetail>();
		private ICollection<InspectionProcedure> _inspectionProcedures = new HashSet<InspectionProcedure>();
		private ICollection<InventoryTask> _inventoryTasks = new HashSet<InventoryTask>();
		private ICollection<ReturnDetail> _returnDetails = new HashSet<ReturnDetail>();
		private String _active;
		private String _code;
		private String _description;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<InspectionDetail> InspectionDetails
		{
			get { return _inspectionDetails; }
			set { _inspectionDetails = value; }
		}

		[DataMember]
		public virtual ICollection<InspectionProcedure> InspectionProcedures
		{
			get { return _inspectionProcedures; }
			set { _inspectionProcedures = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryTask> InventoryTasks
		{
			get { return _inventoryTasks; }
			set { _inventoryTasks = value; }
		}

		[DataMember]
		public virtual ICollection<ReturnDetail> ReturnDetails
		{
			get { return _returnDetails; }
			set { _returnDetails = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Code
		{
			get { return _code; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Code must not be blank or null.");
				else _code = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}


		#endregion
	}
}
