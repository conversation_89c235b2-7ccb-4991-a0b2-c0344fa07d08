using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Dock : Entity
	{
		#region Fields

		private Carrier _carrier;
		private CarrierService _carrierService;
		private CompanyLocationType _companyLocationType;
		private ICollection<CarrierAppointment> _carrierAppointments = new HashSet<CarrierAppointment>();
		private ICollection<DockPrinter> _dockPrinters = new HashSet<DockPrinter>();
		private ICollection<Location> _locations = new HashSet<Location>();
		private ICollection<PreReceiptHeader> _preReceiptHeaders = new HashSet<PreReceiptHeader>();
		private ICollection<ShipmentHeader> _shipmentHeaders = new HashSet<ShipmentHeader>();
		private Location _location;
		private RouteHeader _routeHeader;
		private String _active;
		private String _description;
		private String _dockCode;
		private String _inbound;
		private String _outbound;
		private String _primaryDock;

		#endregion

		#region Properties

		[DataMember]
		public virtual Carrier Carrier
		{
			get { return _carrier; }
			set { _carrier = value; }
		}

		[DataMember]
		public virtual CarrierService CarrierService
		{
			get { return _carrierService; }
			set { _carrierService = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual ICollection<CarrierAppointment> CarrierAppointments
		{
			get { return _carrierAppointments; }
			set { _carrierAppointments = value; }
		}

		[DataMember]
		public virtual ICollection<DockPrinter> DockPrinters
		{
			get { return _dockPrinters; }
			set { _dockPrinters = value; }
		}

		[DataMember]
		public virtual ICollection<Location> Locations
		{
			get { return _locations; }
			set { _locations = value; }
		}

		[DataMember]
		public virtual ICollection<PreReceiptHeader> PreReceiptHeaders
		{
			get { return _preReceiptHeaders; }
			set { _preReceiptHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<ShipmentHeader> ShipmentHeaders
		{
			get { return _shipmentHeaders; }
			set { _shipmentHeaders = value; }
		}

		[DataMember]
		public virtual Location Location
		{
			get { return _location; }
			set { _location = value; }
		}

		[DataMember]
		public virtual RouteHeader RouteHeader
		{
			get { return _routeHeader; }
			set { _routeHeader = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String DockCode
		{
			get { return _dockCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("DockCode must not be blank or null.");
				else _dockCode = value;
			}
		}

		[DataMember]
		public virtual String Inbound
		{
			get { return _inbound; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Inbound must not be blank or null.");
				else _inbound = value;
			}
		}

		[DataMember]
		public virtual String Outbound
		{
			get { return _outbound; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Outbound must not be blank or null.");
				else _outbound = value;
			}
		}

		[DataMember]
		public virtual String PrimaryDock
		{
			get { return _primaryDock; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("PrimaryDock must not be blank or null.");
				else _primaryDock = value;
			}
		}


		#endregion
	}
}
