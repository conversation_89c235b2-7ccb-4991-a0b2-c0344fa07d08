using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using NHibernate.Criterion;

using Upp.Irms.Constants;
using Upp.Irms.Core;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class Dock : Entity
	{
		#region Properties

        [DataMember]
        public virtual Boolean AutoGenerateBol { get; set; }
        [DataMember]
        public virtual Boolean GenerateBolByItem { get; set; }
		[DataMember]
		public virtual Boolean PopulateBol { get; set; }
		[DataMember]
		public virtual Boolean PrintManifest { get; set; }
		[DataMember]
		public virtual Location DefaultLocation { get; set; }
		[DataMember]
		public virtual PrinterTypePrinter PrinterTypePrinter { get; set; }
		[DataMember]
		public virtual ShipmentHeader ShipmentHeader { get; set; }
		[DataMember]
		public virtual String BillOfLading { get; set; }
		[DataMember]
		public virtual String CarrierCode { get; set; }
		[DataMember]
		public virtual String DockStatus { get; set; }
        [DataMember]
        public virtual String SealNumber { get; set; }
		[DataMember]
		public virtual String TrailerCode { get; set; }

		#endregion

		#region Properties.Reports

        public virtual int? CarrierId { get; set; }

		#endregion Properties.Reports

		#region Constructor

		public Dock()
		{
			//
		}

		#endregion

		#region Methods.Public

		public virtual void FindDefaultLocation(InventoryLocationTypes type)
		{
			DetachedCriteria criteria = DetachedCriteria.For<Location>()
				.Add("Active", "A")
				.Add("Dock", this)
				.SetMaxResults(2);
			if (type == InventoryLocationTypes.Returns)
				criteria = criteria.Add("LocationType", Entity.Retrieve<LocationType>(type));
			//
			IList<Location> locations = Repositories.Get<Location>().List(criteria);
			if (locations.Count == 1)
			{
				this.DefaultLocation = locations[0];
				this.DefaultLocation.CheckIsPallet();
			}
		}

		public virtual void FindShipmentHeader()
		{
			String report = BusinessRule.RetrieveString("24");
			if (!String.IsNullOrEmpty(report))
			{
				Boolean? generate = BusinessRule.RetrieveBoolean("11042");
				if (generate.HasValue && !generate.Value) this.PopulateBol = true;
                this.AutoGenerateBol = generate.HasValue ? generate.Value : false;
			}
            //
            Boolean? generateBolByItem = BusinessRule.RetrieveBoolean("11043");
            this.GenerateBolByItem = generateBolByItem.HasValue ? generateBolByItem.Value : false;
			//
			CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
			StatusCode open = Entity.Retrieve<StatusCode>(ShippingStatuses.Open);
			DetachedCriteria criteria = DetachedCriteria.For<ShipmentStatus>()
				.Add("ShipmentHeader.CompanyLocationType", warehouse)
				.Add("ShipmentHeader.Dock", this)
				.AddOrder("Occurred", false)
				.AddOrder("StatusCode.SortOrder", false)
				.SetMaxResults(1);
			ShipmentStatus status = Repositories.Get<ShipmentStatus>().Retrieve(criteria);
			if (status == null)
			{
				this.DockStatus = "Empty";
				this.ShipmentHeader = null;
			}
			else if (!status.StatusCode.SameAs(open))
			{
				this.DockStatus = "Empty";
				this.ShipmentHeader = null;
			}
			else
			{
				ShipmentHeader header = status.ShipmentHeader;
				//
				this.Carrier = header.Carrier;
				this.CarrierCode = (this.Carrier == null) ? null : this.Carrier.CarrierCode;
				this.DockStatus = "Occupied";
				this.PopulateBol = String.IsNullOrEmpty(header.BillOfLading) && this.PopulateBol;
                if (this.GenerateBolByItem && this.PopulateBol)
                {
                    this.PopulateBol = false;
                    if (!header.ValidateShipment()) this.PopulateBol = true;
                }
                this.SealNumber = header.SealNumber;
				this.ShipmentHeader = header;
				this.TrailerCode = header.TrailerCode;
			}
		}

		public virtual void OpenOrClose()
		{
			if ("Empty".Equals(this.DockStatus))
			{
				_carrier = Repositories.Get<Carrier>().Retrieve(_carrier.Id);
				this.DockStatus = "Occupied";
				//
				String report = BusinessRule.RetrieveString("24");
				if (!String.IsNullOrEmpty(report))
				{
					Boolean? generate = BusinessRule.RetrieveBoolean("11042");
                    if (generate.HasValue && generate.Value)
                    {
                        Boolean? generateBOLByItem = BusinessRule.RetrieveBoolean("11043");
                        if (generateBOLByItem.HasValue && generateBOLByItem.Value) this.BillOfLading = string.Empty;
                        else this.BillOfLading = EntityCode.GetCurrentValue(EntityCodes.Bol);
                    }
				}
				//
				this.ShipmentHeader = ShipmentHeader.Create(this);
				this.ShipmentHeader.BillOfLading = this.BillOfLading;
				Repositories.Get<ShipmentHeader>().Add(this.ShipmentHeader);
				//
				StatusCode open = Entity.Retrieve<StatusCode>(ShippingStatuses.Open);
				this.ShipmentHeader.ChangeStatus(open);
			}
			else if ("Occupied".Equals(this.DockStatus))
			{
				_carrier = null;
				this.DockStatus = "Empty";
				//
				this.FindShipmentHeader();
				if (this.ShipmentHeader == null) return;
				//
                if (String.IsNullOrEmpty(this.BillOfLading) && this.GenerateBolByItem) this.ShipmentHeader.GenerateBOLByItem();
                else
                {
                    if (String.IsNullOrEmpty(this.BillOfLading) && this.AutoGenerateBol) this.BillOfLading = EntityCode.GetCurrentValue(EntityCodes.Bol);
                    this.ShipmentHeader.BillOfLading = this.BillOfLading;
                    this.ShipmentHeader.DateModified = DateTime.Now;
                    this.ShipmentHeader.UserModified = Registry.Find<UserAccount>().UserName;
                    //
                    Repositories.Get<ShipmentHeader>().Update(this.ShipmentHeader);
                }
                //
				StatusCode closed = Entity.Retrieve<StatusCode>(ShippingStatuses.Closed);
				this.ShipmentHeader.ChangeStatus(closed);
				//
				if (this.PrinterTypePrinter != null && this.PrintManifest)
				{
					String report = BusinessRule.RetrieveString("11018");
					if (String.IsNullOrEmpty(report)) return;
					//
					Dictionary<String, String> parameters = new Dictionary<String, String>();
					parameters.Add("ShipmentHeader", this.ShipmentHeader.Id.Value.ToString());
					//
					ReportRequestHeader request = ReportRequestHeader.Create(report, parameters, this.PrinterTypePrinter, 1);
					Repositories.Get<Task>().Add(Task.Create(request));
				}
			}
		}

		#endregion
	}
}
