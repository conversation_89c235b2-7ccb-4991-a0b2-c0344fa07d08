using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class DockPrinter : Entity
	{
		#region Fields

		private Dock _dock;
		private Printer _printer;
		private Report _report;
		private String _active;

		#endregion

		#region Properties

		[DataMember]
		public virtual Dock Dock
		{
			get { return _dock; }
			set { _dock = value; }
		}

		[DataMember]
		public virtual Printer Printer
		{
			get { return _printer; }
			set { _printer = value; }
		}

		[DataMember]
		public virtual Report Report
		{
			get { return _report; }
			set { _report = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}


		#endregion
	}
}
