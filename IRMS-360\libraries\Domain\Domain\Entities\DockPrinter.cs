using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class DockPrinter : Entity
	{
		#region Constructor

		public DockPrinter()
		{
			//
		}

		#endregion

        #region Properties.Reports

        public virtual int? DockId { get; set; }
        public virtual int? PrinterId { get; set; }
        public virtual int? ReportId { get; set; }
        public virtual string PrinterCode { get; set; }
        public virtual string QueueName { get; set; }

        #endregion Properties.Reports


    }
}
