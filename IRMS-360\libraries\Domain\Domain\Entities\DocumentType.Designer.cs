using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class DocumentType : Entity
	{
		#region Fields

		private ICollection<InspectionDocument> _inspectionDocuments = new HashSet<InspectionDocument>();
		private ICollection<InventoryDocument> _inventoryDocuments = new HashSet<InventoryDocument>();
		private ICollection<ItemDocument> _itemDocuments = new HashSet<ItemDocument>();
		private ICollection<RecallDocument> _recallDocuments = new HashSet<RecallDocument>();
		private ICollection<WorkOrderDocument> _workOrderDocuments = new HashSet<WorkOrderDocument>();
		private String _active;
		private String _description;
		private String _documentTypeCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<InspectionDocument> InspectionDocuments
		{
			get { return _inspectionDocuments; }
			set { _inspectionDocuments = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryDocument> InventoryDocuments
		{
			get { return _inventoryDocuments; }
			set { _inventoryDocuments = value; }
		}

		[DataMember]
		public virtual ICollection<ItemDocument> ItemDocuments
		{
			get { return _itemDocuments; }
			set { _itemDocuments = value; }
		}

		[DataMember]
		public virtual ICollection<RecallDocument> RecallDocuments
		{
			get { return _recallDocuments; }
			set { _recallDocuments = value; }
		}

		[DataMember]
		public virtual ICollection<WorkOrderDocument> WorkOrderDocuments
		{
			get { return _workOrderDocuments; }
			set { _workOrderDocuments = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String DocumentTypeCode
		{
			get { return _documentTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("DocumentTypeCode must not be blank or null.");
				else _documentTypeCode = value;
			}
		}


		#endregion
	}
}
