using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class EdiBatch : Entity
	{
		#region Fields

		private DateTime _occurred;
		private DateTime? _accepted;
		private DateTime? _acknowledged;
		private DateTime? _submitted;
		private Decimal? _totalCharges;
		private EdiFile _ediFile;
		private Int32? _gsCount;
		private Int32? _stCount;
		private Int32? _totalClaims;
		private ICollection<EdiBatchDetail> _ediBatchDetails = new HashSet<EdiBatchDetail>();
		private ICollection<EdiResponseMessage> _ediResponseMessages = new HashSet<EdiResponseMessage>();
		private String _batchCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime Occurred
		{
			get { return _occurred; }
			set { _occurred = value; }
		}

		[DataMember]
		public virtual DateTime? Accepted
		{
			get { return _accepted; }
			set { _accepted = value; }
		}

		[DataMember]
		public virtual DateTime? Acknowledged
		{
			get { return _acknowledged; }
			set { _acknowledged = value; }
		}

		[DataMember]
		public virtual DateTime? Submitted
		{
			get { return _submitted; }
			set { _submitted = value; }
		}

		[DataMember]
		public virtual Decimal? TotalCharges
		{
			get { return _totalCharges; }
			set { _totalCharges = value; }
		}

		[DataMember]
		public virtual EdiFile EdiFile
		{
			get { return _ediFile; }
			set { _ediFile = value; }
		}

		[DataMember]
		public virtual Int32? GsCount
		{
			get { return _gsCount; }
			set { _gsCount = value; }
		}

		[DataMember]
		public virtual Int32? StCount
		{
			get { return _stCount; }
			set { _stCount = value; }
		}

		[DataMember]
		public virtual Int32? TotalClaims
		{
			get { return _totalClaims; }
			set { _totalClaims = value; }
		}

		[DataMember]
		public virtual ICollection<EdiBatchDetail> EdiBatchDetails
		{
			get { return _ediBatchDetails; }
			set { _ediBatchDetails = value; }
		}

		[DataMember]
		public virtual ICollection<EdiResponseMessage> EdiResponseMessages
		{
			get { return _ediResponseMessages; }
			set { _ediResponseMessages = value; }
		}

		[DataMember]
		public virtual String BatchCode
		{
			get { return _batchCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("BatchCode must not be blank or null.");
				else _batchCode = value;
			}
		}


		#endregion
	}
}
