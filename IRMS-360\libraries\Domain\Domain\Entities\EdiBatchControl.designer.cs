using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class EdiBatchControl : Entity
	{
		#region Fields

		private DateTime? _accepted;
		private DateTime? _acknowledged;
		private DateTime? _interchangeDatetime;
		private DateTime? _submittedDatetime;
		private Decimal? _totalCharges;
		private EdiType _ediType;
		private Int32 _interchangeControlNumber;
		private Int32? _groupControlNumber;
		private Int32? _gsCount;
		private Int32? _stCount;
		private Int32? _totalClaims;
		private ICollection<EdiError> _ediErrors = new HashSet<EdiError>();
		private ICollection<EraVoucher> _eraVouchers = new HashSet<EraVoucher>();
		private OrganizationParticipant _organizationParticipant;
		private ProviderLocation _providerLocation;
		private ProviderOrganizationalUnit _providerOrganizationalUnit;
		private String _batchStatusCode;
		private String _batchStatusReasonCode;
		private String _fileNumber;
		private String _interchangeAcknowlegeCode;
		private String _interchangeNoteCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime? Accepted
		{
			get { return _accepted; }
			set { _accepted = value; }
		}

		[DataMember]
		public virtual DateTime? Acknowledged
		{
			get { return _acknowledged; }
			set { _acknowledged = value; }
		}

		[DataMember]
		public virtual DateTime? InterchangeDatetime
		{
			get { return _interchangeDatetime; }
			set { _interchangeDatetime = value; }
		}

		[DataMember]
		public virtual DateTime? SubmittedDatetime
		{
			get { return _submittedDatetime; }
			set { _submittedDatetime = value; }
		}

		[DataMember]
		public virtual Decimal? TotalCharges
		{
			get { return _totalCharges; }
			set { _totalCharges = value; }
		}

		[DataMember]
		public virtual EdiType EdiType
		{
			get { return _ediType; }
			set { _ediType = value; }
		}

		[DataMember]
		public virtual Int32 InterchangeControlNumber
		{
			get { return _interchangeControlNumber; }
			set { _interchangeControlNumber = value; }
		}

		[DataMember]
		public virtual Int32? GroupControlNumber
		{
			get { return _groupControlNumber; }
			set { _groupControlNumber = value; }
		}

		[DataMember]
		public virtual Int32? GsCount
		{
			get { return _gsCount; }
			set { _gsCount = value; }
		}

		[DataMember]
		public virtual Int32? StCount
		{
			get { return _stCount; }
			set { _stCount = value; }
		}

		[DataMember]
		public virtual Int32? TotalClaims
		{
			get { return _totalClaims; }
			set { _totalClaims = value; }
		}

		[DataMember]
		public virtual ICollection<EdiError> EdiErrors
		{
			get { return _ediErrors; }
			set { _ediErrors = value; }
		}

		[DataMember]
		public virtual ICollection<EraVoucher> EraVouchers
		{
			get { return _eraVouchers; }
			set { _eraVouchers = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant OrganizationParticipant
		{
			get { return _organizationParticipant; }
			set { _organizationParticipant = value; }
		}

		[DataMember]
		public virtual ProviderLocation ProviderLocation
		{
			get { return _providerLocation; }
			set { _providerLocation = value; }
		}

		[DataMember]
		public virtual ProviderOrganizationalUnit ProviderOrganizationalUnit
		{
			get { return _providerOrganizationalUnit; }
			set { _providerOrganizationalUnit = value; }
		}

		[DataMember]
		public virtual String BatchStatusCode
		{
			get { return _batchStatusCode; }
			set { _batchStatusCode = value; }
		}

		[DataMember]
		public virtual String BatchStatusReasonCode
		{
			get { return _batchStatusReasonCode; }
			set { _batchStatusReasonCode = value; }
		}

		[DataMember]
		public virtual String FileNumber
		{
			get { return _fileNumber; }
			set { _fileNumber = value; }
		}

		[DataMember]
		public virtual String InterchangeAcknowlegeCode
		{
			get { return _interchangeAcknowlegeCode; }
			set { _interchangeAcknowlegeCode = value; }
		}

		[DataMember]
		public virtual String InterchangeNoteCode
		{
			get { return _interchangeNoteCode; }
			set { _interchangeNoteCode = value; }
		}


		#endregion
	}
}
