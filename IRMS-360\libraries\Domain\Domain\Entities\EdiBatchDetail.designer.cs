using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class EdiBatchDetail : Entity
	{
		#region Fields

		private EdiBatch _ediBatch;
		private ICollection<EdiResponseMessage> _ediResponseMessages = new HashSet<EdiResponseMessage>();
		private ParticipantEncounter _participantEncounter;
		private String _active;

		#endregion

		#region Properties

		[DataMember]
		public virtual EdiBatch EdiBatch
		{
			get { return _ediBatch; }
			set { _ediBatch = value; }
		}

		[DataMember]
		public virtual ICollection<EdiResponseMessage> EdiResponseMessages
		{
			get { return _ediResponseMessages; }
			set { _ediResponseMessages = value; }
		}

		[DataMember]
		public virtual ParticipantEncounter ParticipantEncounter
		{
			get { return _participantEncounter; }
			set { _participantEncounter = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}


		#endregion
	}
}
