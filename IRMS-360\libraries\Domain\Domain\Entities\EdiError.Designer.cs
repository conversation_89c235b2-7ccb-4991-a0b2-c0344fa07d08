using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class EdiError : Entity
	{
		#region Fields

		private EdiBatchControl _ediBatchControl;
		private String _errorCode;
		private String _errorReasonCode;
		private String _errorSource;
		private String _segmentCode;
		private String _severity;

		#endregion

		#region Properties

		[DataMember]
		public virtual EdiBatchControl EdiBatchControl
		{
			get { return _ediBatchControl; }
			set { _ediBatchControl = value; }
		}

		[DataMember]
		public virtual String ErrorCode
		{
			get { return _errorCode; }
			set { _errorCode = value; }
		}

		[DataMember]
		public virtual String ErrorReasonCode
		{
			get { return _errorReasonCode; }
			set { _errorReasonCode = value; }
		}

		[DataMember]
		public virtual String ErrorSource
		{
			get { return _errorSource; }
			set { _errorSource = value; }
		}

		[DataMember]
		public virtual String SegmentCode
		{
			get { return _segmentCode; }
			set { _segmentCode = value; }
		}

		[DataMember]
		public virtual String Severity
		{
			get { return _severity; }
			set { _severity = value; }
		}


		#endregion
	}
}
