using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class EdiFile : Entity
	{
		#region Fields

		private Byte[] _ediFileResponse;
		private DateTime _interchangeOccurred;
		private Int32 _interchangeControl;
		private ICollection<EdiBatch> _ediBatches = new HashSet<EdiBatch>();
		private String _interchangeReceiver;
		private String _interchangeSender;

		#endregion

		#region Properties

		[DataMember]
		public virtual Byte[] EdiFileResponse
		{
			get { return _ediFileResponse; }
			set { _ediFileResponse = value; }
		}

		[DataMember]
		public virtual DateTime InterchangeOccurred
		{
			get { return _interchangeOccurred; }
			set { _interchangeOccurred = value; }
		}

		[DataMember]
		public virtual Int32 InterchangeControl
		{
			get { return _interchangeControl; }
			set { _interchangeControl = value; }
		}

		[DataMember]
		public virtual ICollection<EdiBatch> EdiBatches
		{
			get { return _ediBatches; }
			set { _ediBatches = value; }
		}

		[DataMember]
		public virtual String InterchangeReceiver
		{
			get { return _interchangeReceiver; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("InterchangeReceiver must not be blank or null.");
				else _interchangeReceiver = value;
			}
		}

		[DataMember]
		public virtual String InterchangeSender
		{
			get { return _interchangeSender; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("InterchangeSender must not be blank or null.");
				else _interchangeSender = value;
			}
		}


		#endregion
	}
}
