using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class EdiResponseMessage : Entity
	{
		#region Fields

		private EdiBatch _ediBatch;
		private EdiBatchDetail _ediBatchDetail;
		private Int32 _segmentIdCode;
		private Int32 _segmentPosition;
		private Int32? _syntaxError;
		private String _acknowledgementCode;
		private String _segmentErrorCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual EdiBatch EdiBatch
		{
			get { return _ediBatch; }
			set { _ediBatch = value; }
		}

		[DataMember]
		public virtual EdiBatchDetail EdiBatchDetail
		{
			get { return _ediBatchDetail; }
			set { _ediBatchDetail = value; }
		}

		[DataMember]
		public virtual Int32 SegmentIdCode
		{
			get { return _segmentIdCode; }
			set { _segmentIdCode = value; }
		}

		[DataMember]
		public virtual Int32 SegmentPosition
		{
			get { return _segmentPosition; }
			set { _segmentPosition = value; }
		}

		[DataMember]
		public virtual Int32? SyntaxError
		{
			get { return _syntaxError; }
			set { _syntaxError = value; }
		}

		[DataMember]
		public virtual String AcknowledgementCode
		{
			get { return _acknowledgementCode; }
			set { _acknowledgementCode = value; }
		}

		[DataMember]
		public virtual String SegmentErrorCode
		{
			get { return _segmentErrorCode; }
			set { _segmentErrorCode = value; }
		}


		#endregion
	}
}
