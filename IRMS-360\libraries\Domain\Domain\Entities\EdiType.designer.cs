using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class EdiType : Entity
	{
		#region Fields

		private ICollection<EdiBatchControl> _ediBatchControls = new HashSet<EdiBatchControl>();
		private String _active;
		private String _description;
		private String _industryIdentifierCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<EdiBatchControl> EdiBatchControls
		{
			get { return _ediBatchControls; }
			set { _ediBatchControls = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String IndustryIdentifierCode
		{
			get { return _industryIdentifierCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("IndustryIdentifierCode must not be blank or null.");
				else _industryIdentifierCode = value;
			}
		}


		#endregion
	}
}
