using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Eligibility : Entity
	{
		#region Fields

		private DateTime? _dob;
		private DateTime? _eligibilityDate;
		private String _claimNumber;
		private String _gender;
		private String _insuranceStatus;
		private String _patientFirstName;
		private String _patientLastName;
		private String _payerName;
		private String _program;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime? Dob
		{
			get { return _dob; }
			set { _dob = value; }
		}

		[DataMember]
		public virtual DateTime? EligibilityDate
		{
			get { return _eligibilityDate; }
			set { _eligibilityDate = value; }
		}

		[DataMember]
		public virtual String ClaimNumber
		{
			get { return _claimNumber; }
			set { _claimNumber = value; }
		}

		[DataMember]
		public virtual String Gender
		{
			get { return _gender; }
			set { _gender = value; }
		}

		[DataMember]
		public virtual String InsuranceStatus
		{
			get { return _insuranceStatus; }
			set { _insuranceStatus = value; }
		}

		[DataMember]
		public virtual String PatientFirstName
		{
			get { return _patientFirstName; }
			set { _patientFirstName = value; }
		}

		[DataMember]
		public virtual String PatientLastName
		{
			get { return _patientLastName; }
			set { _patientLastName = value; }
		}

		[DataMember]
		public virtual String PayerName
		{
			get { return _payerName; }
			set { _payerName = value; }
		}

		[DataMember]
		public virtual String Program
		{
			get { return _program; }
			set { _program = value; }
		}


		#endregion
	}
}
