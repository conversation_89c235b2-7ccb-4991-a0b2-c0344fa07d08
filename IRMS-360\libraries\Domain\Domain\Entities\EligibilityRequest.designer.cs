using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class EligibilityRequest : Entity
	{
		#region Fields

		private Byte[] _ssn;
		private DateTime? _dateOfBirth;
		private DateTime? _serviceFrom;
		private DateTime? _serviceTo;
		private StatusCode _statusCode;
		private String _alternateProviderNumber;
		private String _firstName;
		private String _gender;
		private String _groupNumber;
		private String _lastName;
		private String _memberCode;
		private String _payer;
		private String _providerName;
		private String _providerNpi;
		private String _providerNumber;
		private String _providerTaxid;
		private String _relationshipCode;
		private String _rinNumber;
		private String _scanCode;
		private String _serviceType;
		private String _stateCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual Byte[] Ssn
		{
			get { return _ssn; }
			set { _ssn = value; }
		}

		[DataMember]
		public virtual DateTime? DateOfBirth
		{
			get { return _dateOfBirth; }
			set { _dateOfBirth = value; }
		}

		[DataMember]
		public virtual DateTime? ServiceFrom
		{
			get { return _serviceFrom; }
			set { _serviceFrom = value; }
		}

		[DataMember]
		public virtual DateTime? ServiceTo
		{
			get { return _serviceTo; }
			set { _serviceTo = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode != null ? _statusCode : StatusCode.GetOpenIntegrationStatusCode(); }
			set { _statusCode = value != null ? value : StatusCode.GetOpenIntegrationStatusCode(); }
		}

		[DataMember]
		public virtual String AlternateProviderNumber
		{
			get { return _alternateProviderNumber; }
			set { _alternateProviderNumber = value; }
		}

		[DataMember]
		public virtual String FirstName
		{
			get { return _firstName; }
			set { _firstName = value; }
		}

		[DataMember]
		public virtual String Gender
		{
			get { return _gender; }
			set { _gender = value; }
		}

		[DataMember]
		public virtual String GroupNumber
		{
			get { return _groupNumber; }
			set { _groupNumber = value; }
		}

		[DataMember]
		public virtual String LastName
		{
			get { return _lastName; }
			set { _lastName = value; }
		}

		[DataMember]
		public virtual String MemberCode
		{
			get { return _memberCode; }
			set { _memberCode = value; }
		}

		[DataMember]
		public virtual String Payer
		{
			get { return _payer; }
			set { _payer = value; }
		}

		[DataMember]
		public virtual String ProviderName
		{
			get { return _providerName; }
			set { _providerName = value; }
		}

		[DataMember]
		public virtual String ProviderNpi
		{
			get { return _providerNpi; }
			set { _providerNpi = value; }
		}

		[DataMember]
		public virtual String ProviderNumber
		{
			get { return _providerNumber; }
			set { _providerNumber = value; }
		}

		[DataMember]
		public virtual String ProviderTaxid
		{
			get { return _providerTaxid; }
			set { _providerTaxid = value; }
		}

		[DataMember]
		public virtual String RelationshipCode
		{
			get { return _relationshipCode; }
			set { _relationshipCode = value; }
		}

		[DataMember]
		public virtual String RinNumber
		{
			get { return _rinNumber; }
			set { _rinNumber = value; }
		}

		[DataMember]
		public virtual String ScanCode
		{
			get { return _scanCode; }
			set { _scanCode = value; }
		}

		[DataMember]
		public virtual String ServiceType
		{
			get { return _serviceType; }
			set { _serviceType = value; }
		}

		[DataMember]
		public virtual String StateCode
		{
			get { return _stateCode; }
			set { _stateCode = value; }
		}


		#endregion
	}
}
