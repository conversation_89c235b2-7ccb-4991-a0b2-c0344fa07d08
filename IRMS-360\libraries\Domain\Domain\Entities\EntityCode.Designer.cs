using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class EntityCode : Entity
	{
		#region Fields

		private Agency _agency;
		private CompanyLocationType _companyLocationType;
		private Int32 _currentValue;
		private Int32 _incrementValue;
		private Int32 _initialValue;
		private Provider _provider;
		private String _code;
		private String _formatString;
		private String _prefix;

		#endregion

		#region Properties

		[DataMember]
		public virtual Agency Agency
		{
			get { return _agency; }
			set { _agency = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual Int32 CurrentValue
		{
			get { return _currentValue; }
			set { _currentValue = value; }
		}

		[DataMember]
		public virtual Int32 IncrementValue
		{
			get { return _incrementValue; }
			set { _incrementValue = value; }
		}

		[DataMember]
		public virtual Int32 InitialValue
		{
			get { return _initialValue; }
			set { _initialValue = value; }
		}

		[DataMember]
		public virtual Provider Provider
		{
			get { return _provider; }
			set { _provider = value; }
		}

		[DataMember]
		public virtual String Code
		{
			get { return _code; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Code must not be blank or null.");
				else _code = value;
			}
		}

		[DataMember]
		public virtual String FormatString
		{
			get { return _formatString; }
			set { _formatString = value; }
		}

		[DataMember]
		public virtual String Prefix
		{
			get { return _prefix; }
			set { _prefix = value; }
		}


		#endregion
	}
}
