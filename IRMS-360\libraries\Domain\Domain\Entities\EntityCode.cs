using System;
using System.Runtime.Serialization;

using NHibernate.Criterion;

using Upp.Irms.Constants;
using Upp.Irms.Core;
using Upp.Shared.Utilities;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class EntityCode : Entity
	{
		#region Constructor

		public EntityCode()
		{
			//
		}

		#endregion

		#region Methods.Static

		public static String GetCurrentValue(EntityCodes code)
		{
			CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
			DetachedCriteria criteria = DetachedCriteria.For<EntityCode>()
				.Add("Code", CodeValue.GetCode(code));
			if (warehouse != null) criteria = criteria.Add("CompanyLocationType", warehouse);
			else
			{
				Agency agency = Registry.Find<Agency>();
				if (agency == null) agency = Registry.Find<AgencyOrganizationalUnit>().Agency;
				if (agency == null)
				{
					AgencyOrganizationalUnit unit = Registry.Find<AgencyOrganizationalUnit>();
					if (unit.AgencyLocation != null) agency = unit.AgencyLocation.Agency;
				}
				//
				if (agency == null) throw new Exception(String.Format("Unable to find Entity Code for '{0}'.", code));
				else criteria = criteria.Add("Agency", agency);
			}
			//
			EntityCode key = Repositories.Get<EntityCode>().Retrieve(criteria);
			if (key == null) throw new Exception(String.Format("Unable to find EntityCode for '{0}'.", code));
			//
			++key.CurrentValue;
			key.DateModified = DateTime.Now;
			key.UserModified = "IRMS BL";
			Repositories.Get<EntityCode>().Update(key);
			//
			if (String.IsNullOrEmpty(key.FormatString)) return String.Format("{0}{1}", key.Prefix, key.CurrentValue);
			else return String.Format(key.Prefix + key.FormatString, key.CurrentValue);
		}

		#endregion
	}
}
