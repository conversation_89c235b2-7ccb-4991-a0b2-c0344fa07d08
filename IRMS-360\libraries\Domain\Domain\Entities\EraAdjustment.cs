using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class EraAdjustment : Entity
	{
		#region Constructor

		public EraAdjustment()
		{
			//
		}

		#endregion

        #region Properties

        [DataMember]
        public virtual Decimal? Adjustment_amount6 { get; set; }

        [DataMember]
        public virtual String AdjustmentStatusCode { get; set; }

        [DataMember]
        public virtual String EraProviderAdjustmentStatusCode { get; set; }

        [DataMember]
        public virtual String EraReasonDescription { get; set; }

        [DataMember]
        public virtual String ERAReasoncode1 { get; set; }

        [DataMember]
        public virtual Int32 Adjustmentgroupid { get; set; }              

        [DataMember]
        public virtual Int32 EraServiceId { get; set; }               

        [DataMember]
        public virtual Int32 Referenceid4 { get; set; }

        #endregion
    }
}
