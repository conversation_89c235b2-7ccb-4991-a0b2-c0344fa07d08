using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class EraAdjustment : Entity
	{
		#region Fields

		private AdjustmentGroup _adjustmentGroup;
		private Decimal? _adjustmentAmount;
		private EraClaim _eraClaim;
		private EraReasonCode _eraReasonCode;
		private EraService _eraService;
		private EraVoucher _eraVoucher;
		private Int32? _adjustmentQuantity;
		private String _referenceid;

		#endregion

		#region Properties

		[DataMember]
		public virtual AdjustmentGroup AdjustmentGroup
		{
			get { return _adjustmentGroup; }
			set { _adjustmentGroup = value; }
		}

		[DataMember]
		public virtual Decimal? AdjustmentAmount
		{
			get { return _adjustmentAmount; }
			set { _adjustmentAmount = value; }
		}

		[DataMember]
		public virtual EraClaim EraClaim
		{
			get { return _eraClaim; }
			set { _eraClaim = value; }
		}

		[DataMember]
		public virtual EraReasonCode EraReasonCode
		{
			get { return _eraReasonCode; }
			set { _eraReasonCode = value; }
		}

		[DataMember]
		public virtual EraService EraService
		{
			get { return _eraService; }
			set { _eraService = value; }
		}

		[DataMember]
		public virtual EraVoucher EraVoucher
		{
			get { return _eraVoucher; }
			set { _eraVoucher = value; }
		}

		[DataMember]
		public virtual Int32? AdjustmentQuantity
		{
			get { return _adjustmentQuantity; }
			set { _adjustmentQuantity = value; }
		}

		[DataMember]
		public virtual String Referenceid
		{
			get { return _referenceid; }
			set { _referenceid = value; }
		}


		#endregion
	}
}
