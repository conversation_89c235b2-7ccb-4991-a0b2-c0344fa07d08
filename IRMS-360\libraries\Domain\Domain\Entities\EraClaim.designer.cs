using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class EraClaim : Entity
	{
		#region Fields

		private DateTime? _claimServiceDate;
		private Decimal? _claimCharges;
		private Decimal? _claimPatientAmount;
		private Decimal? _claimPayments;
		private Decimal? _drgAmount;
		private Decimal? _dshAmount;
		private Decimal? _hcpcsPaymentAmount;
		private Decimal? _mspAmount;
		private Decimal? _ppsCapAmount;
		private Decimal? _ppsOutlierAmount;
		private Decimal? _reimbursementRate;
		private EraVoucher _eraVoucher;
		private Int32? _claimInsuredid;
		private Int32? _coveredDays;
		private Int32? _lifetimePsychDays;
		private ICollection<EraAdjustment> _eraAdjustments = new HashSet<EraAdjustment>();
		private ICollection<EraClaimPosting> _eraClaimPostings = new HashSet<EraClaimPosting>();
		private ICollection<EraClaimRemark> _eraClaimRemarks = new HashSet<EraClaimRemark>();
		private ICollection<EraError> _eraErrors = new HashSet<EraError>();
		private ICollection<EraService> _eraServices = new HashSet<EraService>();
		private String _claimControlNumber;
		private String _claimFilingIndicator;
		private String _claimInsuredFirstName;
		private String _claimInsuredLastName;
		private String _claimPatientFirstName;
		private String _claimPatientLastName;
		private String _eraClaimStatusCode;
		private String _medicalRecordNumber;
		private String _payerControlNumber;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime? ClaimServiceDate
		{
			get { return _claimServiceDate; }
			set { _claimServiceDate = value; }
		}

		[DataMember]
		public virtual Decimal? ClaimCharges
		{
			get { return _claimCharges; }
			set { _claimCharges = value; }
		}

		[DataMember]
		public virtual Decimal? ClaimPatientAmount
		{
			get { return _claimPatientAmount; }
			set { _claimPatientAmount = value; }
		}

		[DataMember]
		public virtual Decimal? ClaimPayments
		{
			get { return _claimPayments; }
			set { _claimPayments = value; }
		}

		[DataMember]
		public virtual Decimal? DrgAmount
		{
			get { return _drgAmount; }
			set { _drgAmount = value; }
		}

		[DataMember]
		public virtual Decimal? DshAmount
		{
			get { return _dshAmount; }
			set { _dshAmount = value; }
		}

		[DataMember]
		public virtual Decimal? HcpcsPaymentAmount
		{
			get { return _hcpcsPaymentAmount; }
			set { _hcpcsPaymentAmount = value; }
		}

		[DataMember]
		public virtual Decimal? MspAmount
		{
			get { return _mspAmount; }
			set { _mspAmount = value; }
		}

		[DataMember]
		public virtual Decimal? PpsCapAmount
		{
			get { return _ppsCapAmount; }
			set { _ppsCapAmount = value; }
		}

		[DataMember]
		public virtual Decimal? PpsOutlierAmount
		{
			get { return _ppsOutlierAmount; }
			set { _ppsOutlierAmount = value; }
		}

		[DataMember]
		public virtual Decimal? ReimbursementRate
		{
			get { return _reimbursementRate; }
			set { _reimbursementRate = value; }
		}

		[DataMember]
		public virtual EraVoucher EraVoucher
		{
			get { return _eraVoucher; }
			set { _eraVoucher = value; }
		}

		[DataMember]
		public virtual Int32? ClaimInsuredid
		{
			get { return _claimInsuredid; }
			set { _claimInsuredid = value; }
		}

		[DataMember]
		public virtual Int32? CoveredDays
		{
			get { return _coveredDays; }
			set { _coveredDays = value; }
		}

		[DataMember]
		public virtual Int32? LifetimePsychDays
		{
			get { return _lifetimePsychDays; }
			set { _lifetimePsychDays = value; }
		}

		[DataMember]
		public virtual ICollection<EraAdjustment> EraAdjustments
		{
			get { return _eraAdjustments; }
			set { _eraAdjustments = value; }
		}

		[DataMember]
		public virtual ICollection<EraClaimPosting> EraClaimPostings
		{
			get { return _eraClaimPostings; }
			set { _eraClaimPostings = value; }
		}

		[DataMember]
		public virtual ICollection<EraClaimRemark> EraClaimRemarks
		{
			get { return _eraClaimRemarks; }
			set { _eraClaimRemarks = value; }
		}

		[DataMember]
		public virtual ICollection<EraError> EraErrors
		{
			get { return _eraErrors; }
			set { _eraErrors = value; }
		}

		[DataMember]
		public virtual ICollection<EraService> EraServices
		{
			get { return _eraServices; }
			set { _eraServices = value; }
		}

		[DataMember]
		public virtual String ClaimControlNumber
		{
			get { return _claimControlNumber; }
			set { _claimControlNumber = value; }
		}

		[DataMember]
		public virtual String ClaimFilingIndicator
		{
			get { return _claimFilingIndicator; }
			set { _claimFilingIndicator = value; }
		}

		[DataMember]
		public virtual String ClaimInsuredFirstName
		{
			get { return _claimInsuredFirstName; }
			set { _claimInsuredFirstName = value; }
		}

		[DataMember]
		public virtual String ClaimInsuredLastName
		{
			get { return _claimInsuredLastName; }
			set { _claimInsuredLastName = value; }
		}

		[DataMember]
		public virtual String ClaimPatientFirstName
		{
			get { return _claimPatientFirstName; }
			set { _claimPatientFirstName = value; }
		}

		[DataMember]
		public virtual String ClaimPatientLastName
		{
			get { return _claimPatientLastName; }
			set { _claimPatientLastName = value; }
		}

		[DataMember]
		public virtual String EraClaimStatusCode
		{
			get { return _eraClaimStatusCode; }
			set { _eraClaimStatusCode = value; }
		}

		[DataMember]
		public virtual String MedicalRecordNumber
		{
			get { return _medicalRecordNumber; }
			set { _medicalRecordNumber = value; }
		}

		[DataMember]
		public virtual String PayerControlNumber
		{
			get { return _payerControlNumber; }
			set { _payerControlNumber = value; }
		}


		#endregion
	}
}
