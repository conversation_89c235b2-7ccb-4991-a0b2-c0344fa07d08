using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class EraClaimPosting : Entity
	{
		#region Fields

		private DateTime? _eraPosted;
		private EraClaim _eraClaim;
		private EraPosting _eraPosting;
		private ParticipantEncounter _participantEncounter;
		private String _eraPostingType;
		private String _eraPostStatus;
		private Transaction _transaction;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime? EraPosted
		{
			get { return _eraPosted; }
			set { _eraPosted = value; }
		}

		[DataMember]
		public virtual EraClaim EraClaim
		{
			get { return _eraClaim; }
			set { _eraClaim = value; }
		}

		[DataMember]
		public virtual EraPosting EraPosting
		{
			get { return _eraPosting; }
			set { _eraPosting = value; }
		}

		[DataMember]
		public virtual ParticipantEncounter ParticipantEncounter
		{
			get { return _participantEncounter; }
			set { _participantEncounter = value; }
		}

		[DataMember]
		public virtual String EraPostingType
		{
			get { return _eraPostingType; }
			set { _eraPostingType = value; }
		}

		[DataMember]
		public virtual String EraPostStatus
		{
			get { return _eraPostStatus; }
			set { _eraPostStatus = value; }
		}

		[DataMember]
		public virtual Transaction Transaction
		{
			get { return _transaction; }
			set { _transaction = value; }
		}


		#endregion
	}
}
