using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class EraClaimRemark : Entity
	{
		#region Fields

		private EraClaim _eraClaim;
		private EraRemark _eraRemark;
		private String _active;

		#endregion

		#region Properties

		[DataMember]
		public virtual EraClaim EraClaim
		{
			get { return _eraClaim; }
			set { _eraClaim = value; }
		}

		[DataMember]
		public virtual EraRemark EraRemark
		{
			get { return _eraRemark; }
			set { _eraRemark = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}


		#endregion
	}
}
