using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class EraClaimStatus : Entity
	{
		#region Fields

		private String _active;
		private String _description;
		private String _statusCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set { _description = value; }
		}

		[DataMember]
		public virtual String StatusCode
		{
			get { return _statusCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("StatusCode must not be blank or null.");
				else _statusCode = value;
			}
		}


		#endregion
	}
}
