using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class EraError : Entity
	{
		#region Fields

		private Decimal? _varianceAmount;
		private EraClaim _eraClaim;
		private EraService _eraService;
		private EraVoucher _eraVoucher;
		private String _errorCode;
		private String _errorReasonCode;
		private String _errorSource;
		private String _segmentCode;
		private String _severity;

		#endregion

		#region Properties

		[DataMember]
		public virtual Decimal? VarianceAmount
		{
			get { return _varianceAmount; }
			set { _varianceAmount = value; }
		}

		[DataMember]
		public virtual EraClaim EraClaim
		{
			get { return _eraClaim; }
			set { _eraClaim = value; }
		}

		[DataMember]
		public virtual EraService EraService
		{
			get { return _eraService; }
			set { _eraService = value; }
		}

		[DataMember]
		public virtual EraVoucher EraVoucher
		{
			get { return _eraVoucher; }
			set { _eraVoucher = value; }
		}

		[DataMember]
		public virtual String ErrorCode
		{
			get { return _errorCode; }
			set { _errorCode = value; }
		}

		[DataMember]
		public virtual String ErrorReasonCode
		{
			get { return _errorReasonCode; }
			set { _errorReasonCode = value; }
		}

		[DataMember]
		public virtual String ErrorSource
		{
			get { return _errorSource; }
			set { _errorSource = value; }
		}

		[DataMember]
		public virtual String SegmentCode
		{
			get { return _segmentCode; }
			set { _segmentCode = value; }
		}

		[DataMember]
		public virtual String Severity
		{
			get { return _severity; }
			set { _severity = value; }
		}


		#endregion
	}
}
