using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class EraPosting : Entity
	{
		#region Fields

		private DateTime? _eraPosted;
		private Decimal? _eraBatchTotal;
		private Decimal? _eraPostTotal;
		private EraVoucher _eraVoucher;
		private Int32? _eraBatchCount;
		private Int32? _eraPostCount;
		private Int32? _eraPostErrors;
		private ICollection<EraClaimPosting> _eraClaimPostings = new HashSet<EraClaimPosting>();
		private String _eraBatchCode;
		private String _eraPostStatus;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime? EraPosted
		{
			get { return _eraPosted; }
			set { _eraPosted = value; }
		}

		[DataMember]
		public virtual Decimal? EraBatchTotal
		{
			get { return _eraBatchTotal; }
			set { _eraBatchTotal = value; }
		}

		[DataMember]
		public virtual Decimal? EraPostTotal
		{
			get { return _eraPostTotal; }
			set { _eraPostTotal = value; }
		}

		[DataMember]
		public virtual EraVoucher EraVoucher
		{
			get { return _eraVoucher; }
			set { _eraVoucher = value; }
		}

		[DataMember]
		public virtual Int32? EraBatchCount
		{
			get { return _eraBatchCount; }
			set { _eraBatchCount = value; }
		}

		[DataMember]
		public virtual Int32? EraPostCount
		{
			get { return _eraPostCount; }
			set { _eraPostCount = value; }
		}

		[DataMember]
		public virtual Int32? EraPostErrors
		{
			get { return _eraPostErrors; }
			set { _eraPostErrors = value; }
		}

		[DataMember]
		public virtual ICollection<EraClaimPosting> EraClaimPostings
		{
			get { return _eraClaimPostings; }
			set { _eraClaimPostings = value; }
		}

		[DataMember]
		public virtual String EraBatchCode
		{
			get { return _eraBatchCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("EraBatchCode must not be blank or null.");
				else _eraBatchCode = value;
			}
		}

		[DataMember]
		public virtual String EraPostStatus
		{
			get { return _eraPostStatus; }
			set { _eraPostStatus = value; }
		}


		#endregion
	}
}
