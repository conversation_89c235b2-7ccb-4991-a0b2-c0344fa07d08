using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class EraProviderReasonCode : Entity
	{
		#region Fields

		private EraReasonCode _eraReasonCode;
		private Provider _provider;
		private StatusCode _statusCode;
		private String _active;

		#endregion

		#region Properties

		[DataMember]
		public virtual EraReasonCode EraReasonCode
		{
			get { return _eraReasonCode; }
			set { _eraReasonCode = value; }
		}

		[DataMember]
		public virtual Provider Provider
		{
			get { return _provider; }
			set { _provider = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}


		#endregion
	}
}
