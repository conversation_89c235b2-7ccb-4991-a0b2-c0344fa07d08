using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class EraReasonCode : Entity
	{
		#region Fields

		private ICollection<EraAdjustment> _eraAdjustments = new HashSet<EraAdjustment>();
		private ICollection<EraProviderReasonCode> _eraProviderReasonCodes = new HashSet<EraProviderReasonCode>();
		private ICollection<Transaction> _transactions = new HashSet<Transaction>();
		private StatusCode _statusCode;
		private String _active;
		private String _code;
		private String _description;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<EraAdjustment> EraAdjustments
		{
			get { return _eraAdjustments; }
			set { _eraAdjustments = value; }
		}

		[DataMember]
		public virtual ICollection<EraProviderReasonCode> EraProviderReasonCodes
		{
			get { return _eraProviderReasonCodes; }
			set { _eraProviderReasonCodes = value; }
		}

		[DataMember]
		public virtual ICollection<Transaction> Transactions
		{
			get { return _transactions; }
			set { _transactions = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Code
		{
			get { return _code; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Code must not be blank or null.");
				else _code = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set { _description = value; }
		}


		#endregion
	}
}
