using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class EraRemark : Entity
	{
		#region Fields

		private ICollection<EraClaimRemark> _eraClaimRemarks = new HashSet<EraClaimRemark>();
		private String _active;
		private String _description;
		private String _eraRemarkCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<EraClaimRemark> EraClaimRemarks
		{
			get { return _eraClaimRemarks; }
			set { _eraClaimRemarks = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String EraRemarkCode
		{
			get { return _eraRemarkCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("EraRemarkCode must not be blank or null.");
				else _eraRemarkCode = value;
			}
		}


		#endregion
	}
}
