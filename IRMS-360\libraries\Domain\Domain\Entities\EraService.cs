using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class EraService : Entity
	{
		#region Constructor

		public EraService()
		{
			//
		}

		#endregion

        #region Properities

        [DataMember]
        public virtual decimal? exisitingTranTotalServiceAmount { get; set; }

        [DataMember]
        public virtual decimal? servicelevelPostedPaymentAmount { get; set; }

        [DataMember]
        public virtual decimal? servicelevelPostedAdjustmentAmount { get; set; }

        [DataMember]
        public virtual Int32 EncounterId { get; set; }

        [DataMember]
        public virtual Int32 ServiceDetailId { get; set; }

        [DataMember]
        public virtual Int32 ParticipantImmunizationId { get; set; }

        #endregion
    }
}
