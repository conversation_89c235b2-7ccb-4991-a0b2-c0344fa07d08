using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class EraService : Entity
	{
		#region Fields

		private DateTime? _eraServiceDate;
		private Decimal? _eraAllowedAmount;
		private Decimal? _eraPaymentAmount;
		private Decimal? _eraServiceAmount;
		private EraClaim _eraClaim;
		private Int32? _eraServiceUnits;
		private ICollection<EraAdjustment> _eraAdjustments = new HashSet<EraAdjustment>();
		private ICollection<EraError> _eraErrors = new HashSet<EraError>();
		private Provider _provider;
		private String _eraCptCode;
		private String _eraModifier1;
		private String _eraModifier2;
		private String _eraModifier3;
		private String _eraModifier4;
		private String _eraRevenueCode;
		private String _eraServiceLine;
		private String _eraServiceType;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime? EraServiceDate
		{
			get { return _eraServiceDate; }
			set { _eraServiceDate = value; }
		}

		[DataMember]
		public virtual Decimal? EraAllowedAmount
		{
			get { return _eraAllowedAmount; }
			set { _eraAllowedAmount = value; }
		}

		[DataMember]
		public virtual Decimal? EraPaymentAmount
		{
			get { return _eraPaymentAmount; }
			set { _eraPaymentAmount = value; }
		}

		[DataMember]
		public virtual Decimal? EraServiceAmount
		{
			get { return _eraServiceAmount; }
			set { _eraServiceAmount = value; }
		}

		[DataMember]
		public virtual EraClaim EraClaim
		{
			get { return _eraClaim; }
			set { _eraClaim = value; }
		}

		[DataMember]
		public virtual Int32? EraServiceUnits
		{
			get { return _eraServiceUnits; }
			set { _eraServiceUnits = value; }
		}

		[DataMember]
		public virtual ICollection<EraAdjustment> EraAdjustments
		{
			get { return _eraAdjustments; }
			set { _eraAdjustments = value; }
		}

		[DataMember]
		public virtual ICollection<EraError> EraErrors
		{
			get { return _eraErrors; }
			set { _eraErrors = value; }
		}

		[DataMember]
		public virtual Provider Provider
		{
			get { return _provider; }
			set { _provider = value; }
		}

		[DataMember]
		public virtual String EraCptCode
		{
			get { return _eraCptCode; }
			set { _eraCptCode = value; }
		}

		[DataMember]
		public virtual String EraModifier1
		{
			get { return _eraModifier1; }
			set { _eraModifier1 = value; }
		}

		[DataMember]
		public virtual String EraModifier2
		{
			get { return _eraModifier2; }
			set { _eraModifier2 = value; }
		}

		[DataMember]
		public virtual String EraModifier3
		{
			get { return _eraModifier3; }
			set { _eraModifier3 = value; }
		}

		[DataMember]
		public virtual String EraModifier4
		{
			get { return _eraModifier4; }
			set { _eraModifier4 = value; }
		}

		[DataMember]
		public virtual String EraRevenueCode
		{
			get { return _eraRevenueCode; }
			set { _eraRevenueCode = value; }
		}

		[DataMember]
		public virtual String EraServiceLine
		{
			get { return _eraServiceLine; }
			set { _eraServiceLine = value; }
		}

		[DataMember]
		public virtual String EraServiceType
		{
			get { return _eraServiceType; }
			set { _eraServiceType = value; }
		}


		#endregion
	}
}
