using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class EraVoucher : Entity
	{
		#region Fields

		private DateTime? _paid;
		private Decimal? _totalCharged;
		private Decimal? _totalPaid;
		private EdiBatchControl _ediBatchControl;
		private InsurancePayer _insurancePayer;
		private Int32? _groups;
		private Int32? _segments;
		private Int32? _totalClaims;
		private Int32? _transactions;
		private ICollection<EraAdjustment> _eraAdjustments = new HashSet<EraAdjustment>();
		private ICollection<EraClaim> _eraClaims = new HashSet<EraClaim>();
		private ICollection<EraError> _eraErrors = new HashSet<EraError>();
		private ICollection<EraPosting> _eraPostings = new HashSet<EraPosting>();
        private Provider _childProvider;
		private Provider _provider;		
		private ProviderInsurancePayer _providerInsurancePayer;
		private String _nationalProviderIdentifier;
		private String _payeeName;
		private String _payerIdCode;
		private String _payerName;
		private String _taxid;
		private String _voucherCode;
		private String _voucherTypeCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime? Paid
		{
			get { return _paid; }
			set { _paid = value; }
		}

		[DataMember]
		public virtual Decimal? TotalCharged
		{
			get { return _totalCharged; }
			set { _totalCharged = value; }
		}

		[DataMember]
		public virtual Decimal? TotalPaid
		{
			get { return _totalPaid; }
			set { _totalPaid = value; }
		}

		[DataMember]
		public virtual EdiBatchControl EdiBatchControl
		{
			get { return _ediBatchControl; }
			set { _ediBatchControl = value; }
		}

		[DataMember]
		public virtual InsurancePayer InsurancePayer
		{
			get { return _insurancePayer; }
			set { _insurancePayer = value; }
		}

		[DataMember]
		public virtual Int32? Groups
		{
			get { return _groups; }
			set { _groups = value; }
		}

		[DataMember]
		public virtual Int32? Segments
		{
			get { return _segments; }
			set { _segments = value; }
		}

		[DataMember]
		public virtual Int32? TotalClaims
		{
			get { return _totalClaims; }
			set { _totalClaims = value; }
		}

		[DataMember]
		public virtual Int32? Transactions
		{
			get { return _transactions; }
			set { _transactions = value; }
		}

		[DataMember]
		public virtual ICollection<EraAdjustment> EraAdjustments
		{
			get { return _eraAdjustments; }
			set { _eraAdjustments = value; }
		}

		[DataMember]
		public virtual ICollection<EraClaim> EraClaims
		{
			get { return _eraClaims; }
			set { _eraClaims = value; }
		}

		[DataMember]
		public virtual ICollection<EraError> EraErrors
		{
			get { return _eraErrors; }
			set { _eraErrors = value; }
		}

		[DataMember]
		public virtual ICollection<EraPosting> EraPostings
		{
			get { return _eraPostings; }
			set { _eraPostings = value; }
		}

        [DataMember]
        public virtual Provider ChildProvider
        {
            get { return _childProvider; }
            set { _childProvider = value; }
        }

		[DataMember]
		public virtual Provider Provider
		{
			get { return _provider; }
			set { _provider = value; }
		}		

		[DataMember]
		public virtual ProviderInsurancePayer ProviderInsurancePayer
		{
			get { return _providerInsurancePayer; }
			set { _providerInsurancePayer = value; }
		}

		[DataMember]
		public virtual String NationalProviderIdentifier
		{
			get { return _nationalProviderIdentifier; }
			set { _nationalProviderIdentifier = value; }
		}

		[DataMember]
		public virtual String PayeeName
		{
			get { return _payeeName; }
			set { _payeeName = value; }
		}

		[DataMember]
		public virtual String PayerIdCode
		{
			get { return _payerIdCode; }
			set { _payerIdCode = value; }
		}

		[DataMember]
		public virtual String PayerName
		{
			get { return _payerName; }
			set { _payerName = value; }
		}

		[DataMember]
		public virtual String Taxid
		{
			get { return _taxid; }
			set { _taxid = value; }
		}

		[DataMember]
		public virtual String VoucherCode
		{
			get { return _voucherCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("VoucherCode must not be blank or null.");
				else _voucherCode = value;
			}
		}

		[DataMember]
		public virtual String VoucherTypeCode
		{
			get { return _voucherTypeCode; }
			set { _voucherTypeCode = value; }
		}


		#endregion
	}
}
