using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class FavouriteQuery : Entity
	{
		#region Fields

		private CompanyLocationType _companyLocationType;
		private String _controlParameters;
		private String _favouriteQueryName;
		private String _filterCategory;
		private String _filterParameters;
		private String _orderParameters;

		#endregion

		#region Properties

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual String ControlParameters
		{
			get { return _controlParameters; }
			set { _controlParameters = value; }
		}

		[DataMember]
		public virtual String FavouriteQueryName
		{
			get { return _favouriteQueryName; }
			set { _favouriteQueryName = value; }
		}

		[DataMember]
		public virtual String FilterCategory
		{
			get { return _filterCategory; }
			set { _filterCategory = value; }
		}

		[DataMember]
		public virtual String FilterParameters
		{
			get { return _filterParameters; }
			set { _filterParameters = value; }
		}

		[DataMember]
		public virtual String OrderParameters
		{
			get { return _orderParameters; }
			set { _orderParameters = value; }
		}


		#endregion
	}
}
