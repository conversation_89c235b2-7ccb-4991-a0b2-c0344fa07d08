using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class FemaRegion : Entity
	{
		#region Fields

		private Agency _agency;
		private ICollection<FemaRegionState> _femaRegionStates = new HashSet<FemaRegionState>();
		private String _active;
		private String _description;
		private String _femaRegionCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual Agency Agency
		{
			get { return _agency; }
			set { _agency = value; }
		}

		[DataMember]
		public virtual ICollection<FemaRegionState> FemaRegionStates
		{
			get { return _femaRegionStates; }
			set { _femaRegionStates = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String FemaRegionCode
		{
			get { return _femaRegionCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("FemaRegionCode must not be blank or null.");
				else _femaRegionCode = value;
			}
		}


		#endregion
	}
}
