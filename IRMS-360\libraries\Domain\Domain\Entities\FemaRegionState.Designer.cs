using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class FemaRegionState : Entity
	{
		#region Fields

		private DateTime _effective;
		private FemaRegion _femaRegion;
		private StateCode _stateCode;
		private String _active;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime Effective
		{
			get { return _effective; }
			set { _effective = value; }
		}

		[DataMember]
		public virtual FemaRegion FemaRegion
		{
			get { return _femaRegion; }
			set { _femaRegion = value; }
		}

		[DataMember]
		public virtual StateCode StateCode
		{
			get { return _stateCode; }
			set { _stateCode = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}


		#endregion
	}
}
