using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class FinancialClass : Entity
	{
		#region Fields

		private ICollection<ProviderFinancialClass> _providerFinancialClasses = new HashSet<ProviderFinancialClass>();
		private String _active;
		private String _description;
		private String _financialClassCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<ProviderFinancialClass> ProviderFinancialClasses
		{
			get { return _providerFinancialClasses; }
			set { _providerFinancialClasses = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String FinancialClassCode
		{
			get { return _financialClassCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("FinancialClassCode must not be blank or null.");
				else _financialClassCode = value;
			}
		}


		#endregion
	}
}
