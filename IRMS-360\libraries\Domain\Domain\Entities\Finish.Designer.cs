using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Finish : Entity
	{
		#region Fields

		private ICollection<Item> _items = new HashSet<Item>();
		private String _active;
		private String _description;
		private String _finishCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<Item> Items
		{
			get { return _items; }
			set { _items = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String FinishCode
		{
			get { return _finishCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("FinishCode must not be blank or null.");
				else _finishCode = value;
			}
		}


		#endregion
	}
}
