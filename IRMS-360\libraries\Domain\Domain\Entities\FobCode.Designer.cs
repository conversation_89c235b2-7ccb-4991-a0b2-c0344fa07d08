using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class FobCode : Entity
	{
		#region Fields

		private ICollection<OrderHeader> _orderHeaders = new HashSet<OrderHeader>();
		private String _active;
		private String _bolAddress;
		private String _code;
		private String _description;
		private String _internationalApplicability;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<OrderHeader> OrderHeaders
		{
			get { return _orderHeaders; }
			set { _orderHeaders = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String BolAddress
		{
			get { return _bolAddress; }
			set { _bolAddress = value; }
		}

		[DataMember]
		public virtual String Code
		{
			get { return _code; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Code must not be blank or null.");
				else _code = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String InternationalApplicability
		{
			get { return _internationalApplicability; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("InternationalApplicability must not be blank or null.");
				else _internationalApplicability = value;
			}
		}


		#endregion
	}
}
