using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class FreightClass : Entity
	{
		#region Fields

		private Decimal? _maxWeight;
		private Decimal? _minWeight;
		private ICollection<Item> _items = new HashSet<Item>();
		private ICollection<OrderHeader> _orderHeaders = new HashSet<OrderHeader>();
		private String _active;
		private String _description;
		private String _freightClassCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual Decimal? MaxWeight
		{
			get { return _maxWeight; }
			set { _maxWeight = value; }
		}

		[DataMember]
		public virtual Decimal? MinWeight
		{
			get { return _minWeight; }
			set { _minWeight = value; }
		}

		[DataMember]
		public virtual ICollection<Item> Items
		{
			get { return _items; }
			set { _items = value; }
		}

		[DataMember]
		public virtual ICollection<OrderHeader> OrderHeaders
		{
			get { return _orderHeaders; }
			set { _orderHeaders = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String FreightClassCode
		{
			get { return _freightClassCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("FreightClassCode must not be blank or null.");
				else _freightClassCode = value;
			}
		}


		#endregion
	}
}
