using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Frequency : Entity
	{
		#region Fields

		private Int32? _days;
		private ICollection<BillingPeriod> _billingPeriods = new HashSet<BillingPeriod>();
		private ICollection<JobSchedule> _jobSchedules = new HashSet<JobSchedule>();
		private ICollection<ReportSchedule> _reportSchedules = new HashSet<ReportSchedule>();
		private ICollection<StratificationDefinition> _stratificationDefinitions = new HashSet<StratificationDefinition>();
		private String _active;
		private String _description;
		private String _frequencyCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual Int32? Days
		{
			get { return _days; }
			set { _days = value; }
		}

		[DataMember]
		public virtual ICollection<BillingPeriod> BillingPeriods
		{
			get { return _billingPeriods; }
			set { _billingPeriods = value; }
		}

		[DataMember]
		public virtual ICollection<JobSchedule> JobSchedules
		{
			get { return _jobSchedules; }
			set { _jobSchedules = value; }
		}

		[DataMember]
		public virtual ICollection<ReportSchedule> ReportSchedules
		{
			get { return _reportSchedules; }
			set { _reportSchedules = value; }
		}

		[DataMember]
		public virtual ICollection<StratificationDefinition> StratificationDefinitions
		{
			get { return _stratificationDefinitions; }
			set { _stratificationDefinitions = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String FrequencyCode
		{
			get { return _frequencyCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("FrequencyCode must not be blank or null.");
				else _frequencyCode = value;
			}
		}


		#endregion
	}
}
