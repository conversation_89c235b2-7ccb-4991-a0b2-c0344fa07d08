using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class FunctionalAreaCode : Entity
	{
		#region Fields

		private ICollection<ActionCode> _actionCodes = new HashSet<ActionCode>();
		private ICollection<BusinessRule> _businessRules = new HashSet<BusinessRule>();
		private ICollection<Identifier> _identifiers = new HashSet<Identifier>();
		private ICollection<LicenseType> _licenseTypes = new HashSet<LicenseType>();
		private ICollection<LocationType> _locationTypes = new HashSet<LocationType>();
		private ICollection<OrderType> _orderTypes = new HashSet<OrderType>();
		private ICollection<Parameter> _parameters = new HashSet<Parameter>();
		private ICollection<Priority> _priorities = new HashSet<Priority>();
		private ICollection<ProgramType> _programTypes = new HashSet<ProgramType>();
		private ICollection<Question> _questions = new HashSet<Question>();
		private ICollection<Questionnaire> _questionnaires = new HashSet<Questionnaire>();
		private ICollection<ReferenceCode> _referenceCodes = new HashSet<ReferenceCode>();
		private ICollection<StatusCode> _statusCodes = new HashSet<StatusCode>();
		private ICollection<TransactionType> _transactionTypes = new HashSet<TransactionType>();
		private ICollection<UnitOfMeasure> _unitOfMeasures = new HashSet<UnitOfMeasure>();
		private ICollection<Zone> _zones = new HashSet<Zone>();
		private String _active;
		private String _code;
		private String _description;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<ActionCode> ActionCodes
		{
			get { return _actionCodes; }
			set { _actionCodes = value; }
		}

		[DataMember]
		public virtual ICollection<BusinessRule> BusinessRules
		{
			get { return _businessRules; }
			set { _businessRules = value; }
		}

		[DataMember]
		public virtual ICollection<Identifier> Identifiers
		{
			get { return _identifiers; }
			set { _identifiers = value; }
		}

		[DataMember]
		public virtual ICollection<LicenseType> LicenseTypes
		{
			get { return _licenseTypes; }
			set { _licenseTypes = value; }
		}

		[DataMember]
		public virtual ICollection<LocationType> LocationTypes
		{
			get { return _locationTypes; }
			set { _locationTypes = value; }
		}

		[DataMember]
		public virtual ICollection<OrderType> OrderTypes
		{
			get { return _orderTypes; }
			set { _orderTypes = value; }
		}

		[DataMember]
		public virtual ICollection<Parameter> Parameters
		{
			get { return _parameters; }
			set { _parameters = value; }
		}

		[DataMember]
		public virtual ICollection<Priority> Priorities
		{
			get { return _priorities; }
			set { _priorities = value; }
		}

		[DataMember]
		public virtual ICollection<ProgramType> ProgramTypes
		{
			get { return _programTypes; }
			set { _programTypes = value; }
		}

		[DataMember]
		public virtual ICollection<Question> Questions
		{
			get { return _questions; }
			set { _questions = value; }
		}

		[DataMember]
		public virtual ICollection<Questionnaire> Questionnaires
		{
			get { return _questionnaires; }
			set { _questionnaires = value; }
		}

		[DataMember]
		public virtual ICollection<ReferenceCode> ReferenceCodes
		{
			get { return _referenceCodes; }
			set { _referenceCodes = value; }
		}

		[DataMember]
		public virtual ICollection<StatusCode> StatusCodes
		{
			get { return _statusCodes; }
			set { _statusCodes = value; }
		}

		[DataMember]
		public virtual ICollection<TransactionType> TransactionTypes
		{
			get { return _transactionTypes; }
			set { _transactionTypes = value; }
		}

		[DataMember]
		public virtual ICollection<UnitOfMeasure> UnitOfMeasures
		{
			get { return _unitOfMeasures; }
			set { _unitOfMeasures = value; }
		}

		[DataMember]
		public virtual ICollection<Zone> Zones
		{
			get { return _zones; }
			set { _zones = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Code
		{
			get { return _code; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Code must not be blank or null.");
				else _code = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}


		#endregion
	}
}
