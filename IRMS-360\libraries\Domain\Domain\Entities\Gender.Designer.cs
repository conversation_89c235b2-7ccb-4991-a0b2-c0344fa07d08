using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Gender : Entity
	{
		#region Fields

		private ICollection<Participant> _participants = new HashSet<Participant>();
		private String _active;
		private String _description;
		private String _genderCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<Participant> Participants
		{
			get { return _participants; }
			set { _participants = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String GenderCode
		{
			get { return _genderCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("GenderCode must not be blank or null.");
				else _genderCode = value;
			}
		}


		#endregion
	}
}
