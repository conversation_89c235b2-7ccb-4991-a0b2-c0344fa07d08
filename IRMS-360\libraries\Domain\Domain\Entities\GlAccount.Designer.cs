using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class GlAccount : Entity
	{
		#region Fields

		private String _account;
		private String _active;
		private String _description;

		#endregion

		#region Properties

		[DataMember]
		public virtual String Account
		{
			get { return _account; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Account must not be blank or null.");
				else _account = value;
			}
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}


		#endregion
	}
}
