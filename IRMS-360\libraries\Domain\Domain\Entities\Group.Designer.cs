using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Group : Entity
	{
		#region Fields

		private ICollection<AlertGroup> _alertGroups = new HashSet<AlertGroup>();
		private String _active;
		private String _description;
		private String _groupCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<AlertGroup> AlertGroups
		{
			get { return _alertGroups; }
			set { _alertGroups = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String GroupCode
		{
			get { return _groupCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("GroupCode must not be blank or null.");
				else _groupCode = value;
			}
		}


		#endregion
	}
}
