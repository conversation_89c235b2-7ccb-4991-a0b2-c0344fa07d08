using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Holiday : Entity
	{
		#region Fields

		private ICollection<HolidaySchedule> _holidaySchedules = new HashSet<HolidaySchedule>();
		private String _active;
		private String _description;
		private String _holidayCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<HolidaySchedule> HolidaySchedules
		{
			get { return _holidaySchedules; }
			set { _holidaySchedules = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String HolidayCode
		{
			get { return _holidayCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("HolidayCode must not be blank or null.");
				else _holidayCode = value;
			}
		}


		#endregion
	}
}
