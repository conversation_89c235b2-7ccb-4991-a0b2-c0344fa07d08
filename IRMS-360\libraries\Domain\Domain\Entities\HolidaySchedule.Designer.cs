using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class HolidaySchedule : Entity
	{
		#region Fields

		private Agency _agency;
		private CompanyLocationType _companyLocationType;
		private DateTime _observed;
		private Holiday _holiday;
		private Int32 _year;

		#endregion

		#region Properties

		[DataMember]
		public virtual Agency Agency
		{
			get { return _agency; }
			set { _agency = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual DateTime Observed
		{
			get { return _observed; }
			set { _observed = value; }
		}

		[DataMember]
		public virtual Holiday Holiday
		{
			get { return _holiday; }
			set { _holiday = value; }
		}

		[DataMember]
		public virtual Int32 Year
		{
			get { return _year; }
			set { _year = value; }
		}


		#endregion
	}
}
