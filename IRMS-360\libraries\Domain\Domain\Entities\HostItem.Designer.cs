using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
    [Serializable]
    partial class HostItem : Entity
    {
        #region Fields

        private DateTime _imported;
        private Decimal _quantity;
        private Decimal _quantityAvailable;
        private Decimal _quantityDemand;
        private Decimal _quantityIntransfer;
        private Decimal _quantityIntransit;
        private Decimal _quantityOnhand;
        private Decimal _quantityOuttransfer;
        private Decimal _quantityReserved;
        private Decimal _quantityUnavailable;
        private Decimal _wmQuantity;
        private Decimal _wmQuantityAvailable;
        private Decimal _wmQuantityDemand;
        private Decimal _wmQuantityDifference;
        private Decimal _wmQuantityIntransfer;
        private Decimal _wmQuantityIntransit;
        private Decimal _wmQuantityOnhand;
        private Decimal _wmQuantityOuttransfer;
        private Decimal _wmQuantityReserved;
        private Decimal _wmQuantityUnavailable;
        private Decimal _wmPrimarySplitCaseLocationQty;
        private Decimal _wmPrimaryFullCaseLocationQty;
        private Decimal _wmPrimaryPalletLocationQty;
        private String _active;
        private String _companyCode;
        private String _itemCode;
        private String _saleUomCode;
        private String _warehouseCode;
        private String _wmSaleUomCode;
        private String _wmPrimarySplitCaseLocation;
        private String _wmPrimaryFullCaseLocation;
        private String _wmPrimaryPalletLocation;

        #endregion

        #region Properties

        [DataMember]
        public virtual DateTime Imported
        {
            get { return _imported; }
            set { _imported = value; }
        }

        [DataMember]
        public virtual Decimal Quantity
        {
            get { return _quantity; }
            set { _quantity = value; }
        }

        [DataMember]
        public virtual Decimal QuantityAvailable
        {
            get { return _quantityAvailable; }
            set { _quantityAvailable = value; }
        }

        [DataMember]
        public virtual Decimal QuantityDemand
        {
            get { return _quantityDemand; }
            set { _quantityDemand = value; }
        }

        [DataMember]
        public virtual Decimal QuantityIntransfer
        {
            get { return _quantityIntransfer; }
            set { _quantityIntransfer = value; }
        }

        [DataMember]
        public virtual Decimal QuantityIntransit
        {
            get { return _quantityIntransit; }
            set { _quantityIntransit = value; }
        }

        [DataMember]
        public virtual Decimal QuantityOnhand
        {
            get { return _quantityOnhand; }
            set { _quantityOnhand = value; }
        }

        [DataMember]
        public virtual Decimal QuantityOuttransfer
        {
            get { return _quantityOuttransfer; }
            set { _quantityOuttransfer = value; }
        }

        [DataMember]
        public virtual Decimal QuantityReserved
        {
            get { return _quantityReserved; }
            set { _quantityReserved = value; }
        }

        [DataMember]
        public virtual Decimal QuantityUnavailable
        {
            get { return _quantityUnavailable; }
            set { _quantityUnavailable = value; }
        }

        [DataMember]
        public virtual Decimal WmQuantity
        {
            get { return _wmQuantity; }
            set { _wmQuantity = value; }
        }

        [DataMember]
        public virtual Decimal WmQuantityAvailable
        {
            get { return _wmQuantityAvailable; }
            set { _wmQuantityAvailable = value; }
        }

        [DataMember]
        public virtual Decimal WmQuantityDemand
        {
            get { return _wmQuantityDemand; }
            set { _wmQuantityDemand = value; }
        }

        [DataMember]
        public virtual Decimal WmQuantityDifference
        {
            get { return _wmQuantityDifference; }
            set { _wmQuantityDifference = value; }
        }

        [DataMember]
        public virtual Decimal WmQuantityIntransfer
        {
            get { return _wmQuantityIntransfer; }
            set { _wmQuantityIntransfer = value; }
        }

        [DataMember]
        public virtual Decimal WmQuantityIntransit
        {
            get { return _wmQuantityIntransit; }
            set { _wmQuantityIntransit = value; }
        }

        [DataMember]
        public virtual Decimal WmQuantityOnhand
        {
            get { return _wmQuantityOnhand; }
            set { _wmQuantityOnhand = value; }
        }

        [DataMember]
        public virtual Decimal WmQuantityOuttransfer
        {
            get { return _wmQuantityOuttransfer; }
            set { _wmQuantityOuttransfer = value; }
        }

        [DataMember]
        public virtual Decimal WmQuantityReserved
        {
            get { return _wmQuantityReserved; }
            set { _wmQuantityReserved = value; }
        }

        [DataMember]
        public virtual Decimal WmQuantityUnavailable
        {
            get { return _wmQuantityUnavailable; }
            set { _wmQuantityUnavailable = value; }
        }

        [DataMember]
        public virtual Decimal WmPrimarySplitCaseLocationQty
        {
            get { return _wmPrimarySplitCaseLocationQty; }
            set { _wmPrimarySplitCaseLocationQty = value; }
        }

        [DataMember]
        public virtual Decimal WmPrimaryFullCaseLocationQty
        {
            get { return _wmPrimaryFullCaseLocationQty; }
            set { _wmPrimaryFullCaseLocationQty = value; }
        }

        [DataMember]
        public virtual Decimal WmPrimaryPalletLocationQty
        {
            get { return _wmPrimaryPalletLocationQty; }
            set { _wmPrimaryPalletLocationQty = value; }
        }

        [DataMember]
        public virtual String Active
        {
            get { return _active; }
            set
            {
                if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
                else _active = value;
            }
        }

        [DataMember]
        public virtual String CompanyCode
        {
            get { return _companyCode; }
            set
            {
                if (String.IsNullOrEmpty(value)) throw new NullReferenceException("CompanyCode must not be blank or null.");
                else _companyCode = value;
            }
        }

        [DataMember]
        public virtual String ItemCode
        {
            get { return _itemCode; }
            set
            {
                if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ItemCode must not be blank or null.");
                else _itemCode = value;
            }
        }

        [DataMember]
        public virtual String SaleUomCode
        {
            get { return _saleUomCode; }
            set
            {
                if (String.IsNullOrEmpty(value)) throw new NullReferenceException("SaleUomCode must not be blank or null.");
                else _saleUomCode = value;
            }
        }

        [DataMember]
        public virtual String WarehouseCode
        {
            get { return _warehouseCode; }
            set
            {
                if (String.IsNullOrEmpty(value)) throw new NullReferenceException("WarehouseCode must not be blank or null.");
                else _warehouseCode = value;
            }
        }

        [DataMember]
        public virtual String WmSaleUomCode
        {
            get { return _wmSaleUomCode; }
            set
            {
                if (String.IsNullOrEmpty(value)) throw new NullReferenceException("WmSaleUomCode must not be blank or null.");
                else _wmSaleUomCode = value;
            }
        }

        [DataMember]
        public virtual String WmPrimarySplitCaseLocation
        {
            get { return _wmPrimarySplitCaseLocation; }
            set { _wmPrimarySplitCaseLocation = value; }
            
        }

        [DataMember]
        public virtual String WmPrimaryFullCaseLocation
        {
            get { return _wmPrimaryFullCaseLocation; }
            set { _wmPrimaryFullCaseLocation = value; }          
            
        }

        [DataMember]
        public virtual String WmPrimaryPalletLocation
        {
            get { return _wmPrimaryPalletLocation; }
            set { _wmPrimaryPalletLocation = value; }
            
        }

        #endregion
    }
}
