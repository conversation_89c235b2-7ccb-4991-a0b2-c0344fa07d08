using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class IcdCode : Entity
	{
		#region Fields

		private DateTime? _effective;
		private DateTime? _expiration;
		private IcdType _icdType;
		private ICollection<ParticipantImmunization> _participantImmunizations = new HashSet<ParticipantImmunization>();
		private ICollection<ServiceDetail> _serviceDetails = new HashSet<ServiceDetail>();
		private String _active;
		private String _code;
		private String _description;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime? Effective
		{
			get { return _effective; }
			set { _effective = value; }
		}

		[DataMember]
		public virtual DateTime? Expiration
		{
			get { return _expiration; }
			set { _expiration = value; }
		}

		[DataMember]
		public virtual IcdType IcdType
		{
			get { return _icdType; }
			set { _icdType = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantImmunization> ParticipantImmunizations
		{
			get { return _participantImmunizations; }
			set { _participantImmunizations = value; }
		}

		[DataMember]
		public virtual ICollection<ServiceDetail> ServiceDetails
		{
			get { return _serviceDetails; }
			set { _serviceDetails = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Code
		{
			get { return _code; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Code must not be blank or null.");
				else _code = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set { _description = value; }
		}


		#endregion
	}
}
