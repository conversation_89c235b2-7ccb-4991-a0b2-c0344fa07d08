using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class IcdType : Entity
	{
		#region Fields

		private ICollection<IcdCode> _icdCodes = new HashSet<IcdCode>();
		private String _active;
		private String _description;
		private String _icdTypeCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<IcdCode> IcdCodes
		{
			get { return _icdCodes; }
			set { _icdCodes = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String IcdTypeCode
		{
			get { return _icdTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("IcdTypeCode must not be blank or null.");
				else _icdTypeCode = value;
			}
		}


		#endregion
	}
}
