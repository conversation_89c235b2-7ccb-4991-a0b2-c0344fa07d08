using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Identifier : Entity
	{
		#region Fields

		private FunctionalAreaCode _functionalAreaCode;
		private Int32? _sortOrder;
		private String _active;
		private String _description;
		private String _identifierCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual FunctionalAreaCode FunctionalAreaCode
		{
			get { return _functionalAreaCode; }
			set { _functionalAreaCode = value; }
		}

		[DataMember]
		public virtual Int32? SortOrder
		{
			get { return _sortOrder; }
			set { _sortOrder = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String IdentifierCode
		{
			get { return _identifierCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("IdentifierCode must not be blank or null.");
				else _identifierCode = value;
			}
		}


		#endregion
	}
}
