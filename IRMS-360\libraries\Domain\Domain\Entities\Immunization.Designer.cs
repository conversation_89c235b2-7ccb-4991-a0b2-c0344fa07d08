using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Immunization : Entity
	{
		#region Fields

		private ICollection<ParticipantImmunization> _participantImmunizations = new HashSet<ParticipantImmunization>();
		private Provider _provider;
		private ProviderLocation _providerLocation;
		private ProviderOrganizationalUnit _providerOrganizationalUnit;
		private String _active;
		private String _description;
		private String _epsdtIndicator;
		private String _famplanIndicator;
		private String _immunizationCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<ParticipantImmunization> ParticipantImmunizations
		{
			get { return _participantImmunizations; }
			set { _participantImmunizations = value; }
		}

		[DataMember]
		public virtual Provider Provider
		{
			get { return _provider; }
			set { _provider = value; }
		}

		[DataMember]
		public virtual ProviderLocation ProviderLocation
		{
			get { return _providerLocation; }
			set { _providerLocation = value; }
		}

		[DataMember]
		public virtual ProviderOrganizationalUnit ProviderOrganizationalUnit
		{
			get { return _providerOrganizationalUnit; }
			set { _providerOrganizationalUnit = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String EpsdtIndicator
		{
			get { return _epsdtIndicator; }
			set { _epsdtIndicator = value; }
		}

		[DataMember]
		public virtual String FamplanIndicator
		{
			get { return _famplanIndicator; }
			set { _famplanIndicator = value; }
		}

		[DataMember]
		public virtual String ImmunizationCode
		{
			get { return _immunizationCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ImmunizationCode must not be blank or null.");
				else _immunizationCode = value;
			}
		}


		#endregion
	}
}
