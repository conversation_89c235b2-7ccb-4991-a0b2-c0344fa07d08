using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class IngestionLocation : Entity
	{
		#region Fields

		private IngestionMethod _ingestionMethod;
		private String _active;
		private String _description;
		private String _ingestionLocationCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual IngestionMethod IngestionMethod
		{
			get { return _ingestionMethod; }
			set { _ingestionMethod = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String IngestionLocationCode
		{
			get { return _ingestionLocationCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("IngestionLocationCode must not be blank or null.");
				else _ingestionLocationCode = value;
			}
		}


		#endregion
	}
}
