using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class IngestionMethod : Entity
	{
		#region Fields

		private ICollection<IngestionLocation> _ingestionLocations = new HashSet<IngestionLocation>();
		private String _active;
		private String _description;
		private String _ingestionMethodCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<IngestionLocation> IngestionLocations
		{
			get { return _ingestionLocations; }
			set { _ingestionLocations = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String IngestionMethodCode
		{
			get { return _ingestionMethodCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("IngestionMethodCode must not be blank or null.");
				else _ingestionMethodCode = value;
			}
		}


		#endregion
	}
}
