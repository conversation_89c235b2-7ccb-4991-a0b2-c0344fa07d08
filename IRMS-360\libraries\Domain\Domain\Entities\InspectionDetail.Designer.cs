using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InspectionDetail : Entity
	{
		#region Fields

		private DispositionCode _dispositionCode;
		private InspectionHeader _inspectionHeader;
		private InspectionProcedure _inspectionProcedure;
		private InventoryItem _inventoryItem;
		private ICollection<InspectionDocument> _inspectionDocuments = new HashSet<InspectionDocument>();
		private ICollection<InspectionResult> _inspectionResults = new HashSet<InspectionResult>();
		private ParticipantInventoryItem _participantInventoryItem;
		private String _notes;

		#endregion

		#region Properties

		[DataMember]
		public virtual DispositionCode DispositionCode
		{
			get { return _dispositionCode; }
			set { _dispositionCode = value; }
		}

		[DataMember]
		public virtual InspectionHeader InspectionHeader
		{
			get { return _inspectionHeader; }
			set { _inspectionHeader = value; }
		}

		[DataMember]
		public virtual InspectionProcedure InspectionProcedure
		{
			get { return _inspectionProcedure; }
			set { _inspectionProcedure = value; }
		}

		[DataMember]
		public virtual InventoryItem InventoryItem
		{
			get { return _inventoryItem; }
			set { _inventoryItem = value; }
		}

		[DataMember]
		public virtual ICollection<InspectionDocument> InspectionDocuments
		{
			get { return _inspectionDocuments; }
			set { _inspectionDocuments = value; }
		}

		[DataMember]
		public virtual ICollection<InspectionResult> InspectionResults
		{
			get { return _inspectionResults; }
			set { _inspectionResults = value; }
		}

		[DataMember]
		public virtual ParticipantInventoryItem ParticipantInventoryItem
		{
			get { return _participantInventoryItem; }
			set { _participantInventoryItem = value; }
		}

		[DataMember]
		public virtual String Notes
		{
			get { return _notes; }
			set { _notes = value; }
		}


		#endregion
	}
}
