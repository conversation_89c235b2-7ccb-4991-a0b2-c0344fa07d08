using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InspectionDocument : Entity
	{
		#region Fields

		private Byte[] _document;
		private DocumentType _documentType;
		private InspectionDetail _inspectionDetail;
		private InspectionHeader _inspectionHeader;
		private String _documentName;

		#endregion

		#region Properties

		[DataMember]
		public virtual Byte[] Document
		{
			get { return _document; }
			set { _document = value; }
		}

		[DataMember]
		public virtual DocumentType DocumentType
		{
			get { return _documentType; }
			set { _documentType = value; }
		}

		[DataMember]
		public virtual InspectionDetail InspectionDetail
		{
			get { return _inspectionDetail; }
			set { _inspectionDetail = value; }
		}

		[DataMember]
		public virtual InspectionHeader InspectionHeader
		{
			get { return _inspectionHeader; }
			set { _inspectionHeader = value; }
		}

		[DataMember]
		public virtual String DocumentName
		{
			get { return _documentName; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("DocumentName must not be blank or null.");
				else _documentName = value;
			}
		}


		#endregion
	}
}
