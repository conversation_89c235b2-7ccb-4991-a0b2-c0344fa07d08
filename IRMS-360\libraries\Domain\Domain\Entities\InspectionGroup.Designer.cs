using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InspectionGroup : Entity
	{
		#region Fields

		private ICollection<InspectionTemplate> _inspectionTemplates = new HashSet<InspectionTemplate>();
		private String _active;
		private String _description;
		private String _inspectionGroupCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<InspectionTemplate> InspectionTemplates
		{
			get { return _inspectionTemplates; }
			set { _inspectionTemplates = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String InspectionGroupCode
		{
			get { return _inspectionGroupCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("InspectionGroupCode must not be blank or null.");
				else _inspectionGroupCode = value;
			}
		}


		#endregion
	}
}
