using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InspectionHeader : Entity
	{
		#region Fields

		private CompanyLocationType _companyLocationType;
		private DateTime _inspectionBegin;
		private DateTime? _inspectionEnd;
		private InspectionHeader _parentInspectionHeader;
		private InspectionTemplate _inspectionTemplate;
		private InventoryItem _inventoryItem;
		private ICollection<InspectionDetail> _inspectionDetails = new HashSet<InspectionDetail>();
		private ICollection<InspectionDocument> _inspectionDocuments = new HashSet<InspectionDocument>();
		private ICollection<InspectionHeader> _childInspectionHeaders = new HashSet<InspectionHeader>();
		private OrganizationParticipant _inspector;
		private OrganizationParticipant _signoff;
		private StatusCode _statusCode;
		private String _inspectionCode;
		private String _inspectionHeaderCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual DateTime InspectionBegin
		{
			get { return _inspectionBegin; }
			set { _inspectionBegin = value; }
		}

		[DataMember]
		public virtual DateTime? InspectionEnd
		{
			get { return _inspectionEnd; }
			set { _inspectionEnd = value; }
		}

		[DataMember]
		public virtual InspectionHeader ParentInspectionHeader
		{
			get { return _parentInspectionHeader; }
			set { _parentInspectionHeader = value; }
		}

		[DataMember]
		public virtual InspectionTemplate InspectionTemplate
		{
			get { return _inspectionTemplate; }
			set { _inspectionTemplate = value; }
		}

		[DataMember]
		public virtual InventoryItem InventoryItem
		{
			get { return _inventoryItem; }
			set { _inventoryItem = value; }
		}

		[DataMember]
		public virtual ICollection<InspectionDetail> InspectionDetails
		{
			get { return _inspectionDetails; }
			set { _inspectionDetails = value; }
		}

		[DataMember]
		public virtual ICollection<InspectionDocument> InspectionDocuments
		{
			get { return _inspectionDocuments; }
			set { _inspectionDocuments = value; }
		}

		[DataMember]
		public virtual ICollection<InspectionHeader> ChildInspectionHeaders
		{
			get { return _childInspectionHeaders; }
			set { _childInspectionHeaders = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant Inspector
		{
			get { return _inspector; }
			set { _inspector = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant Signoff
		{
			get { return _signoff; }
			set { _signoff = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual String InspectionCode
		{
			get { return _inspectionCode; }
			set { _inspectionCode = value; }
		}

		[DataMember]
		public virtual String InspectionHeaderCode
		{
			get { return _inspectionHeaderCode; }
			set { _inspectionHeaderCode = value; }
		}


		#endregion
	}
}
