using System;
using System.Runtime.Serialization;

using Upp.Irms.Constants;
using Upp.Irms.Core;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class InspectionHeader : Entity
    {
        #region Properties

        [DataMember]
		public virtual String SerialNumber { get; set; }

		#endregion

		#region Constructor

		public InspectionHeader()
		{
			//
		}

		#endregion

        #region Methods.Public

        public virtual void RecordInspection(InventoryItem inventoryItem)
        {
            InspectionHeader header = Entity.Activate<InspectionHeader>();
            header.CompanyLocationType = Registry.Find<CompanyLocationType>();
            header.InspectionBegin = this.InspectionBegin;
            header.InspectionCode = EntityCode.GetCurrentValue(EntityCodes.Inspection);
            header.InspectionEnd = this.InspectionEnd;
            header.InspectionTemplate = this.InspectionTemplate;
            header.InventoryItem = inventoryItem;
            header.Inspector = Registry.Find<UserAccount>().OrganizationParticipant;
            header.StatusCode = Entity.Retrieve<StatusCode>(TaskStatuses.Active);
            Repositories.Get<InspectionHeader>().Add(header);
            //
            foreach (InspectionDetail inspectionDetail in this.InspectionDetails)
            {
                InspectionDetail detail = Entity.Activate<InspectionDetail>();
                detail.InspectionProcedure = inspectionDetail.InspectionProcedure;
                detail.InspectionHeader = header;
                detail.InventoryItem = inventoryItem;
                Repositories.Get<InspectionDetail>().Add(detail);
                //
                foreach (InspectionResult inspectionResult in inspectionDetail.InspectionResults)
                {
                    InspectionResult result = Entity.Activate<InspectionResult>();
                    result.Active = inspectionResult.Active;
                    result.InspectionOption = inspectionResult.InspectionOption;
                    result.InspectionDetail = detail;
                    result.InspectionResults = inspectionResult.InspectionResults;
                    Repositories.Get<InspectionResult>().Add(result);
                }
            }
        }

        #endregion
	}
}
