using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InspectionOption : Entity
	{
		#region Fields

		private InspectionProcedure _inspectionProcedure;
		private Int32 _sortSequence;
		private ICollection<InspectionResult> _inspectionResults = new HashSet<InspectionResult>();
		private String _active;
		private String _openText;
		private String _optionText;

		#endregion

		#region Properties

		[DataMember]
		public virtual InspectionProcedure InspectionProcedure
		{
			get { return _inspectionProcedure; }
			set { _inspectionProcedure = value; }
		}

		[DataMember]
		public virtual Int32 SortSequence
		{
			get { return _sortSequence; }
			set { _sortSequence = value; }
		}

		[DataMember]
		public virtual ICollection<InspectionResult> InspectionResults
		{
			get { return _inspectionResults; }
			set { _inspectionResults = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String OpenText
		{
			get { return _openText; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("OpenText must not be blank or null.");
				else _openText = value;
			}
		}

		[DataMember]
		public virtual String OptionText
		{
			get { return _optionText; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("OptionText must not be blank or null.");
				else _optionText = value;
			}
		}


		#endregion
	}
}
