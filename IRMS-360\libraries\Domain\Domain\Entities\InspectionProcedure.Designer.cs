using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InspectionProcedure : Entity
	{
		#region Fields

		private DispositionCode _dispositionCode;
		private InspectionTemplate _inspectionTemplate;
		private Int32 _inspectionSequence;
		private ICollection<InspectionDetail> _inspectionDetails = new HashSet<InspectionDetail>();
		private ICollection<InspectionOption> _inspectionOptions = new HashSet<InspectionOption>();
		private Questionnaire _questionnaire;
		private String _active;
		private String _inspectionNarrative;
		private String _multipleOptions;

		#endregion

		#region Properties

		[DataMember]
		public virtual DispositionCode DispositionCode
		{
			get { return _dispositionCode; }
			set { _dispositionCode = value; }
		}

		[DataMember]
		public virtual InspectionTemplate InspectionTemplate
		{
			get { return _inspectionTemplate; }
			set { _inspectionTemplate = value; }
		}

		[DataMember]
		public virtual Int32 InspectionSequence
		{
			get { return _inspectionSequence; }
			set { _inspectionSequence = value; }
		}

		[DataMember]
		public virtual ICollection<InspectionDetail> InspectionDetails
		{
			get { return _inspectionDetails; }
			set { _inspectionDetails = value; }
		}

		[DataMember]
		public virtual ICollection<InspectionOption> InspectionOptions
		{
			get { return _inspectionOptions; }
			set { _inspectionOptions = value; }
		}

		[DataMember]
		public virtual Questionnaire Questionnaire
		{
			get { return _questionnaire; }
			set { _questionnaire = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String InspectionNarrative
		{
			get { return _inspectionNarrative; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("InspectionNarrative must not be blank or null.");
				else _inspectionNarrative = value;
			}
		}

		[DataMember]
		public virtual String MultipleOptions
		{
			get { return _multipleOptions; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("MultipleOptions must not be blank or null.");
				else _multipleOptions = value;
			}
		}


		#endregion
	}
}
