using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InspectionResult : Entity
	{
		#region Fields

		private InspectionDetail _inspectionDetail;
		private InspectionOption _inspectionOption;
		private String _active;
		private String _inspectionResults;

		#endregion

		#region Properties

		[DataMember]
		public virtual InspectionDetail InspectionDetail
		{
			get { return _inspectionDetail; }
			set { _inspectionDetail = value; }
		}

		[DataMember]
		public virtual InspectionOption InspectionOption
		{
			get { return _inspectionOption; }
			set { _inspectionOption = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String InspectionResults
		{
			get { return _inspectionResults; }
			set { _inspectionResults = value; }
		}


		#endregion
	}
}
