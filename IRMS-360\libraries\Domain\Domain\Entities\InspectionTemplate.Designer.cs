using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InspectionTemplate : Entity
	{
		#region Fields

		private CompanyLocationType _companyLocationType;
		private InspectionGroup _inspectionGroup;
		private InspectionType _inspectionType;
		private Item _item;
		private ItemGroup _itemGroup;
		private ItemGroupLine _itemGroupLine;
		private ItemGroupSubLine _itemGroupSubLine;
		private ICollection<InspectionHeader> _inspectionHeaders = new HashSet<InspectionHeader>();
		private ICollection<InspectionProcedure> _inspectionProcedures = new HashSet<InspectionProcedure>();
		private String _active;
		private String _description;
		private String _name;

		#endregion

		#region Properties

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual InspectionGroup InspectionGroup
		{
			get { return _inspectionGroup; }
			set { _inspectionGroup = value; }
		}

		[DataMember]
		public virtual InspectionType InspectionType
		{
			get { return _inspectionType; }
			set { _inspectionType = value; }
		}

		[DataMember]
		public virtual Item Item
		{
			get { return _item; }
			set { _item = value; }
		}

		[DataMember]
		public virtual ItemGroup ItemGroup
		{
			get { return _itemGroup; }
			set { _itemGroup = value; }
		}

		[DataMember]
		public virtual ItemGroupLine ItemGroupLine
		{
			get { return _itemGroupLine; }
			set { _itemGroupLine = value; }
		}

		[DataMember]
		public virtual ItemGroupSubLine ItemGroupSubLine
		{
			get { return _itemGroupSubLine; }
			set { _itemGroupSubLine = value; }
		}

		[DataMember]
		public virtual ICollection<InspectionHeader> InspectionHeaders
		{
			get { return _inspectionHeaders; }
			set { _inspectionHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<InspectionProcedure> InspectionProcedures
		{
			get { return _inspectionProcedures; }
			set { _inspectionProcedures = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String Name
		{
			get { return _name; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Name must not be blank or null.");
				else _name = value;
			}
		}


		#endregion
	}
}
