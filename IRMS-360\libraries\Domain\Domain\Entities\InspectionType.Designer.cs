using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InspectionType : Entity
	{
		#region Fields

		private ICollection<InspectionTemplate> _inspectionTemplates = new HashSet<InspectionTemplate>();
		private String _active;
		private String _description;
		private String _inbound;
		private String _inspectionTypeCode;
		private String _outbound;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<InspectionTemplate> InspectionTemplates
		{
			get { return _inspectionTemplates; }
			set { _inspectionTemplates = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String Inbound
		{
			get { return _inbound; }
			set { _inbound = value; }
		}

		[DataMember]
		public virtual String InspectionTypeCode
		{
			get { return _inspectionTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("InspectionTypeCode must not be blank or null.");
				else _inspectionTypeCode = value;
			}
		}

		[DataMember]
		public virtual String Outbound
		{
			get { return _outbound; }
			set { _outbound = value; }
		}


		#endregion
	}
}
