using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InsuranceLocationType : Entity
	{
		#region Fields

		private InsurancePayerLocation _insurancePayerLocation;
		private LocationType _locationType;
		private String _active;
		private String _insuranceLocationCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual InsurancePayerLocation InsurancePayerLocation
		{
			get { return _insurancePayerLocation; }
			set { _insurancePayerLocation = value; }
		}

		[DataMember]
		public virtual LocationType LocationType
		{
			get { return _locationType; }
			set { _locationType = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String InsuranceLocationCode
		{
			get { return _insuranceLocationCode; }
			set { _insuranceLocationCode = value; }
		}


		#endregion
	}
}
