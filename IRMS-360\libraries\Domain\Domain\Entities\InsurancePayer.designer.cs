using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InsurancePayer : Entity
	{
		#region Fields

		private InsuranceType _insuranceType;
		private ICollection<EraVoucher> _eraVouchers = new HashSet<EraVoucher>();
		private ICollection<InsurancePayerLocation> _insurancePayerLocations = new HashSet<InsurancePayerLocation>();
		private ICollection<InsurancePayerService> _insurancePayerServices = new HashSet<InsurancePayerService>();
		private ICollection<ParticipantInsurance> _participantInsurances = new HashSet<ParticipantInsurance>();
		private ICollection<ProviderInsurancePayer> _providerInsurancePayers = new HashSet<ProviderInsurancePayer>();
		private String _active;
		private String _alternateProviderNumber;
		private String _dualChAllowed;
		private String _eligibilityAllowed;
		private String _enrollRequired;
		private String _isDobRequired;
		private String _isFirstNameRequired;
		private String _isGenderRequired;
		private String _isLastNameRequired;
		private String _isMemberIdRequired;
		private String _isSsnRequired;
		private String _isStateRequired;
		private String _name;
		private String _tmpInsurancePayerCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual InsuranceType InsuranceType
		{
			get { return _insuranceType; }
			set { _insuranceType = value; }
		}

		[DataMember]
		public virtual ICollection<EraVoucher> EraVouchers
		{
			get { return _eraVouchers; }
			set { _eraVouchers = value; }
		}

		[DataMember]
		public virtual ICollection<InsurancePayerLocation> InsurancePayerLocations
		{
			get { return _insurancePayerLocations; }
			set { _insurancePayerLocations = value; }
		}

		[DataMember]
		public virtual ICollection<InsurancePayerService> InsurancePayerServices
		{
			get { return _insurancePayerServices; }
			set { _insurancePayerServices = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantInsurance> ParticipantInsurances
		{
			get { return _participantInsurances; }
			set { _participantInsurances = value; }
		}

		[DataMember]
		public virtual ICollection<ProviderInsurancePayer> ProviderInsurancePayers
		{
			get { return _providerInsurancePayers; }
			set { _providerInsurancePayers = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String AlternateProviderNumber
		{
			get { return _alternateProviderNumber; }
			set { _alternateProviderNumber = value; }
		}

		[DataMember]
		public virtual String DualChAllowed
		{
			get { return _dualChAllowed; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("DualChAllowed must not be blank or null.");
				else _dualChAllowed = value;
			}
		}

		[DataMember]
		public virtual String EligibilityAllowed
		{
			get { return _eligibilityAllowed; }
			set { _eligibilityAllowed = value; }
		}

		[DataMember]
		public virtual String EnrollRequired
		{
			get { return _enrollRequired; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("EnrollRequired must not be blank or null.");
				else _enrollRequired = value;
			}
		}

		[DataMember]
		public virtual String IsDobRequired
		{
			get { return _isDobRequired; }
			set { _isDobRequired = value; }
		}

		[DataMember]
		public virtual String IsFirstNameRequired
		{
			get { return _isFirstNameRequired; }
			set { _isFirstNameRequired = value; }
		}

		[DataMember]
		public virtual String IsGenderRequired
		{
			get { return _isGenderRequired; }
			set { _isGenderRequired = value; }
		}

		[DataMember]
		public virtual String IsLastNameRequired
		{
			get { return _isLastNameRequired; }
			set { _isLastNameRequired = value; }
		}

		[DataMember]
		public virtual String IsMemberIdRequired
		{
			get { return _isMemberIdRequired; }
			set { _isMemberIdRequired = value; }
		}

		[DataMember]
		public virtual String IsSsnRequired
		{
			get { return _isSsnRequired; }
			set { _isSsnRequired = value; }
		}

		[DataMember]
		public virtual String IsStateRequired
		{
			get { return _isStateRequired; }
			set { _isStateRequired = value; }
		}

		[DataMember]
		public virtual String Name
		{
			get { return _name; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Name must not be blank or null.");
				else _name = value;
			}
		}

		[DataMember]
		public virtual String TmpInsurancePayerCode
		{
			get { return _tmpInsurancePayerCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("TmpInsurancePayerCode must not be blank or null.");
				else _tmpInsurancePayerCode = value;
			}
		}


		#endregion
	}
}
