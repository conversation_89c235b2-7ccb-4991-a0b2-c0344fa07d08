using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InsurancePayerLocation : Entity
	{
		#region Fields

		private InsurancePayer _insurancePayer;
		private ICollection<InsuranceLocationType> _insuranceLocationTypes = new HashSet<InsuranceLocationType>();
		private String _active;
		private String _addressLine1;
		private String _addressLine2;
		private String _addressLine3;
		private String _contactEmail;
		private String _contactFirstName;
		private String _contactLastName;
		private String _contactPhone;
		private ZipCode _zipCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual InsurancePayer InsurancePayer
		{
			get { return _insurancePayer; }
			set { _insurancePayer = value; }
		}

		[DataMember]
		public virtual ICollection<InsuranceLocationType> InsuranceLocationTypes
		{
			get { return _insuranceLocationTypes; }
			set { _insuranceLocationTypes = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String AddressLine1
		{
			get { return _addressLine1; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("AddressLine1 must not be blank or null.");
				else _addressLine1 = value;
			}
		}

		[DataMember]
		public virtual String AddressLine2
		{
			get { return _addressLine2; }
			set { _addressLine2 = value; }
		}

		[DataMember]
		public virtual String AddressLine3
		{
			get { return _addressLine3; }
			set { _addressLine3 = value; }
		}

		[DataMember]
		public virtual String ContactEmail
		{
			get { return _contactEmail; }
			set { _contactEmail = value; }
		}

		[DataMember]
		public virtual String ContactFirstName
		{
			get { return _contactFirstName; }
			set { _contactFirstName = value; }
		}

		[DataMember]
		public virtual String ContactLastName
		{
			get { return _contactLastName; }
			set { _contactLastName = value; }
		}

		[DataMember]
		public virtual String ContactPhone
		{
			get { return _contactPhone; }
			set { _contactPhone = value; }
		}

		[DataMember]
		public virtual ZipCode ZipCode
		{
			get { return _zipCode; }
			set { _zipCode = value; }
		}


		#endregion
	}
}
