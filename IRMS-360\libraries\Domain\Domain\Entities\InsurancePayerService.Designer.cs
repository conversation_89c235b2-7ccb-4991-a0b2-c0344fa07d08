using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InsurancePayerService : Entity
	{
		#region Fields

		private InsurancePayer _insurancePayer;
		private Service _service;
		private String _active;

		#endregion

		#region Properties

		[DataMember]
		public virtual InsurancePayer InsurancePayer
		{
			get { return _insurancePayer; }
			set { _insurancePayer = value; }
		}

		[DataMember]
		public virtual Service Service
		{
			get { return _service; }
			set { _service = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}


		#endregion
	}
}
