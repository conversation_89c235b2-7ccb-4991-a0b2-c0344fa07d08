using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InsuranceServiceType : Entity
	{
		#region Fields

		private ICollection<Provider> _providers = new HashSet<Provider>();
		private String _active;
		private String _name;
		private String _tmpInsuranceServiceTypeCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<Provider> Providers
		{
			get { return _providers; }
			set { _providers = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Name
		{
			get { return _name; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Name must not be blank or null.");
				else _name = value;
			}
		}

		[DataMember]
		public virtual String TmpInsuranceServiceTypeCode
		{
			get { return _tmpInsuranceServiceTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("TmpInsuranceServiceTypeCode must not be blank or null.");
				else _tmpInsuranceServiceTypeCode = value;
			}
		}


		#endregion
	}
}
