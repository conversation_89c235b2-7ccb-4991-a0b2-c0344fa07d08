using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InsuranceType : Entity
	{
		#region Fields

		private ICollection<InsurancePayer> _insurancePayers = new HashSet<InsurancePayer>();
		private ICollection<ParticipantInsurance> _participantInsurances = new HashSet<ParticipantInsurance>();
		private Provider _provider;
		private ProviderLocation _providerLocation;
		private ProviderOrganizationalUnit _providerOrganizationalUnit;
		private String _active;
		private String _description;
		private String _insuranceTypeCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<InsurancePayer> InsurancePayers
		{
			get { return _insurancePayers; }
			set { _insurancePayers = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantInsurance> ParticipantInsurances
		{
			get { return _participantInsurances; }
			set { _participantInsurances = value; }
		}

		[DataMember]
		public virtual Provider Provider
		{
			get { return _provider; }
			set { _provider = value; }
		}

		[DataMember]
		public virtual ProviderLocation ProviderLocation
		{
			get { return _providerLocation; }
			set { _providerLocation = value; }
		}

		[DataMember]
		public virtual ProviderOrganizationalUnit ProviderOrganizationalUnit
		{
			get { return _providerOrganizationalUnit; }
			set { _providerOrganizationalUnit = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String InsuranceTypeCode
		{
			get { return _insuranceTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("InsuranceTypeCode must not be blank or null.");
				else _insuranceTypeCode = value;
			}
		}


		#endregion
	}
}
