using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InterfaceCommunication : Entity
	{
		#region Fields

		private Agency _agency;
		private AgencyLocation _agencyLocation;
		private Company _company;
		private CompanyLocationType _companyLocationType;
		private Int32? _portNumber;
		private InterfaceHeader _interfaceHeader;
		private ProviderLocation _providerLocation;
		private ProviderOrganizationalUnit _providerOrganizationalUnit;
		private StateCode _stateCode;
		private String _active;
		private String _communicationPassword;
		private String _communicationType;
		private String _communicationUrl;
		private String _communicationUserName;
		private String _inboundFolder;
		private String _outboundFolder;

		#endregion

		#region Properties

		[DataMember]
		public virtual Agency Agency
		{
			get { return _agency; }
			set { _agency = value; }
		}

		[DataMember]
		public virtual AgencyLocation AgencyLocation
		{
			get { return _agencyLocation; }
			set { _agencyLocation = value; }
		}

		[DataMember]
		public virtual Company Company
		{
			get { return _company; }
			set { _company = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual Int32? PortNumber
		{
			get { return _portNumber; }
			set { _portNumber = value; }
		}

		[DataMember]
		public virtual InterfaceHeader InterfaceHeader
		{
			get { return _interfaceHeader; }
			set { _interfaceHeader = value; }
		}

		[DataMember]
		public virtual ProviderLocation ProviderLocation
		{
			get { return _providerLocation; }
			set { _providerLocation = value; }
		}

		[DataMember]
		public virtual ProviderOrganizationalUnit ProviderOrganizationalUnit
		{
			get { return _providerOrganizationalUnit; }
			set { _providerOrganizationalUnit = value; }
		}

		[DataMember]
		public virtual StateCode StateCode
		{
			get { return _stateCode; }
			set { _stateCode = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String CommunicationPassword
		{
			get { return _communicationPassword; }
			set { _communicationPassword = value; }
		}

		[DataMember]
		public virtual String CommunicationType
		{
			get { return _communicationType; }
			set { _communicationType = value; }
		}

		[DataMember]
		public virtual String CommunicationUrl
		{
			get { return _communicationUrl; }
			set { _communicationUrl = value; }
		}

		[DataMember]
		public virtual String CommunicationUserName
		{
			get { return _communicationUserName; }
			set { _communicationUserName = value; }
		}

		[DataMember]
		public virtual String InboundFolder
		{
			get { return _inboundFolder; }
			set { _inboundFolder = value; }
		}

		[DataMember]
		public virtual String OutboundFolder
		{
			get { return _outboundFolder; }
			set { _outboundFolder = value; }
		}


		#endregion
	}
}
