using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InterfaceCpt : Entity
	{
		#region Fields

		private CptCode _cptCode;
		private InterfaceHeader _interfaceHeader;
		private String _active;

		#endregion

		#region Properties

		[DataMember]
		public virtual CptCode CptCode
		{
			get { return _cptCode; }
			set { _cptCode = value; }
		}

		[DataMember]
		public virtual InterfaceHeader InterfaceHeader
		{
			get { return _interfaceHeader; }
			set { _interfaceHeader = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}


		#endregion
	}
}
