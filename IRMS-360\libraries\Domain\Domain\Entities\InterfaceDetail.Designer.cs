using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InterfaceDetail : Entity
	{
		#region Fields

		private Int32? _sortOrder;
		private InterfaceDetail _parentInterfaceDetail;
		private InterfaceHeader _interfaceHeader;
		private ICollection<InterfaceDetail> _childInterfaceDetails = new HashSet<InterfaceDetail>();
		private ICollection<InterfaceDetailDefault> _interfaceDetailDefaults = new HashSet<InterfaceDetailDefault>();
		private String _active;
		private String _dataFormat;
		private String _dataType;
		private String _fieldDescription;
		private String _fieldMapping;
		private String _fieldName;
		private String _jsonTagName;
		private String _required;

		#endregion

		#region Properties

		[DataMember]
		public virtual Int32? SortOrder
		{
			get { return _sortOrder; }
			set { _sortOrder = value; }
		}

		[DataMember]
		public virtual InterfaceDetail ParentInterfaceDetail
		{
			get { return _parentInterfaceDetail; }
			set { _parentInterfaceDetail = value; }
		}

		[DataMember]
		public virtual InterfaceHeader InterfaceHeader
		{
			get { return _interfaceHeader; }
			set { _interfaceHeader = value; }
		}

		[DataMember]
		public virtual ICollection<InterfaceDetail> ChildInterfaceDetails
		{
			get { return _childInterfaceDetails; }
			set { _childInterfaceDetails = value; }
		}

		[DataMember]
		public virtual ICollection<InterfaceDetailDefault> InterfaceDetailDefaults
		{
			get { return _interfaceDetailDefaults; }
			set { _interfaceDetailDefaults = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set { _active = value; }
		}

		[DataMember]
		public virtual String DataFormat
		{
			get { return _dataFormat; }
			set { _dataFormat = value; }
		}

		[DataMember]
		public virtual String DataType
		{
			get { return _dataType; }
			set { _dataType = value; }
		}

		[DataMember]
		public virtual String FieldDescription
		{
			get { return _fieldDescription; }
			set { _fieldDescription = value; }
		}

		[DataMember]
		public virtual String FieldMapping
		{
			get { return _fieldMapping; }
			set { _fieldMapping = value; }
		}

		[DataMember]
		public virtual String FieldName
		{
			get { return _fieldName; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("FieldName must not be blank or null.");
				else _fieldName = value;
			}
		}

		[DataMember]
		public virtual String JsonTagName
		{
			get { return _jsonTagName; }
			set { _jsonTagName = value; }
		}

		[DataMember]
		public virtual String Required
		{
			get { return _required; }
			set { _required = value; }
		}


		#endregion
	}
}
