using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InterfaceDetailDefault : Entity
	{
		#region Fields

		private Agency _agency;
		private AgencyLocation _agencyLocation;
		private Company _company;
		private CompanyLocationType _companyLocationType;
		private InterfaceDetail _interfaceDetail;
		private ProviderLocation _providerLocation;
		private ProviderOrganizationalUnit _providerOrganizationalUnit;
		private StateCode _stateCode;
		private String _active;
		private String _defaultValue;

		#endregion

		#region Properties

		[DataMember]
		public virtual Agency Agency
		{
			get { return _agency; }
			set { _agency = value; }
		}

		[DataMember]
		public virtual AgencyLocation AgencyLocation
		{
			get { return _agencyLocation; }
			set { _agencyLocation = value; }
		}

		[DataMember]
		public virtual Company Company
		{
			get { return _company; }
			set { _company = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual InterfaceDetail InterfaceDetail
		{
			get { return _interfaceDetail; }
			set { _interfaceDetail = value; }
		}

		[DataMember]
		public virtual ProviderLocation ProviderLocation
		{
			get { return _providerLocation; }
			set { _providerLocation = value; }
		}

		[DataMember]
		public virtual ProviderOrganizationalUnit ProviderOrganizationalUnit
		{
			get { return _providerOrganizationalUnit; }
			set { _providerOrganizationalUnit = value; }
		}

		[DataMember]
		public virtual StateCode StateCode
		{
			get { return _stateCode; }
			set { _stateCode = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String DefaultValue
		{
			get { return _defaultValue; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("DefaultValue must not be blank or null.");
				else _defaultValue = value;
			}
		}


		#endregion
	}
}
