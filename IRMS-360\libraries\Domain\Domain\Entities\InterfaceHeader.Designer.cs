using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InterfaceHeader : Entity
	{
		#region Fields

		private ICollection<Carrier3pService> _carrier3pServices = new HashSet<Carrier3pService>();
		private ICollection<InterfaceCommunication> _interfaceCommunications = new HashSet<InterfaceCommunication>();
		private ICollection<InterfaceCpt> _interfaceCpts = new HashSet<InterfaceCpt>();
		private ICollection<InterfaceDetail> _interfaceDetails = new HashSet<InterfaceDetail>();
		private Provider _provider;
		private StatusCode _statusCode;
		private String _active;
		private String _description;
		private String _interfaceHeaderCode;
		private String _interfaceName;
		private String _interfaceType;
		private String _jsonTagName;
		private String _seperator;
		private String _serviceurl;
		private String _systemNotes;
		private String _userNotes;
		private TransactionType _transactionType;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<Carrier3pService> Carrier3pServices
		{
			get { return _carrier3pServices; }
			set { _carrier3pServices = value; }
		}

		[DataMember]
		public virtual ICollection<InterfaceCommunication> InterfaceCommunications
		{
			get { return _interfaceCommunications; }
			set { _interfaceCommunications = value; }
		}

		[DataMember]
		public virtual ICollection<InterfaceCpt> InterfaceCpts
		{
			get { return _interfaceCpts; }
			set { _interfaceCpts = value; }
		}

		[DataMember]
		public virtual ICollection<InterfaceDetail> InterfaceDetails
		{
			get { return _interfaceDetails; }
			set { _interfaceDetails = value; }
		}

		[DataMember]
		public virtual Provider Provider
		{
			get { return _provider; }
			set { _provider = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set { _description = value; }
		}

		[DataMember]
		public virtual String InterfaceHeaderCode
		{
			get { return _interfaceHeaderCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("InterfaceHeaderCode must not be blank or null.");
				else _interfaceHeaderCode = value;
			}
		}

		[DataMember]
		public virtual String InterfaceName
		{
			get { return _interfaceName; }
			set { _interfaceName = value; }
		}

		[DataMember]
		public virtual String InterfaceType
		{
			get { return _interfaceType; }
			set { _interfaceType = value; }
		}

		[DataMember]
		public virtual String JsonTagName
		{
			get { return _jsonTagName; }
			set { _jsonTagName = value; }
		}

		[DataMember]
		public virtual String Seperator
		{
			get { return _seperator; }
			set { _seperator = value; }
		}

		[DataMember]
		public virtual String Serviceurl
		{
			get { return _serviceurl; }
			set { _serviceurl = value; }
		}

		[DataMember]
		public virtual String SystemNotes
		{
			get { return _systemNotes; }
			set { _systemNotes = value; }
		}

		[DataMember]
		public virtual String UserNotes
		{
			get { return _userNotes; }
			set { _userNotes = value; }
		}

		[DataMember]
		public virtual TransactionType TransactionType
		{
			get { return _transactionType; }
			set { _transactionType = value; }
		}


		#endregion
	}
}
