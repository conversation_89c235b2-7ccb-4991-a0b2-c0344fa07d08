using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InventoryAdjustmentCode : Entity
	{
		#region Fields

		private Agency _agency;
		private CompanyLocationType _companyLocationType;
		private ICollection<AdjustmentCodeTransactionType> _adjustmentCodeTransactionTypes = new HashSet<AdjustmentCodeTransactionType>();
		private ICollection<BillingCodeTask> _billingCodeTasks = new HashSet<BillingCodeTask>();
		private ICollection<ItemTransaction> _itemTransactions = new HashSet<ItemTransaction>();
		private StatusCode _statusCodeFrom;
		private StatusCode _statusCodeTo;
		private String _active;
		private String _code;
		private String _description;
		private String _memoRequired;
		private String _orderRequired;
		private String _sendToHost;

		#endregion

		#region Properties

		[DataMember]
		public virtual Agency Agency
		{
			get { return _agency; }
			set { _agency = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual ICollection<AdjustmentCodeTransactionType> AdjustmentCodeTransactionTypes
		{
			get { return _adjustmentCodeTransactionTypes; }
			set { _adjustmentCodeTransactionTypes = value; }
		}

		[DataMember]
		public virtual ICollection<BillingCodeTask> BillingCodeTasks
		{
			get { return _billingCodeTasks; }
			set { _billingCodeTasks = value; }
		}

		[DataMember]
		public virtual ICollection<ItemTransaction> ItemTransactions
		{
			get { return _itemTransactions; }
			set { _itemTransactions = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCodeFrom
		{
			get { return _statusCodeFrom; }
			set { _statusCodeFrom = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCodeTo
		{
			get { return _statusCodeTo; }
			set { _statusCodeTo = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Code
		{
			get { return _code; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Code must not be blank or null.");
				else _code = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String MemoRequired
		{
			get { return _memoRequired; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("MemoRequired must not be blank or null.");
				else _memoRequired = value;
			}
		}

		[DataMember]
		public virtual String OrderRequired
		{
			get { return _orderRequired; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("OrderRequired must not be blank or null.");
				else _orderRequired = value;
			}
		}

		[DataMember]
		public virtual String SendToHost
		{
			get { return _sendToHost; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("SendToHost must not be blank or null.");
				else _sendToHost = value;
			}
		}


		#endregion
	}
}
