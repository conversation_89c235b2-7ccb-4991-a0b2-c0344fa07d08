using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InventoryAttribute : Entity
	{
		#region Fields

		private DateTime? _lotExpiration;
		private DateTime? _shelfExpiration;
		private LicensePlate _licensePlate;
		private String _assetCode;
		private String _lotNumber;
		private String _serialNumber;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime? LotExpiration
		{
			get { return _lotExpiration; }
			set { _lotExpiration = value; }
		}

		[DataMember]
		public virtual DateTime? ShelfExpiration
		{
			get { return _shelfExpiration; }
			set { _shelfExpiration = value; }
		}

		[DataMember]
		public virtual LicensePlate LicensePlate
		{
			get { return _licensePlate; }
			set { _licensePlate = value; }
		}

		[DataMember]
		public virtual String AssetCode
		{
			get { return _assetCode; }
			set { _assetCode = value; }
		}

		[DataMember]
		public virtual String LotNumber
		{
			get { return _lotNumber; }
			set { _lotNumber = value; }
		}

		[DataMember]
		public virtual String SerialNumber
		{
			get { return _serialNumber; }
			set { _serialNumber = value; }
		}

		#endregion
	}
}
