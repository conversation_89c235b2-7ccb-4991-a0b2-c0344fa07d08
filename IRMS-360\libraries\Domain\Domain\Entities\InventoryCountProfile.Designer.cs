using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InventoryCountProfile : Entity
	{
		#region Fields

		private CompanyLocationType _companyLocationType;
		private Int32 _abcADays;
		private Int32 _abcBDays;
		private Int32 _abcCDays;
		private Int32 _abcDDays;
		private Int32 _maxACounts;
		private Int32 _maxBCounts;
		private Int32 _maxCCounts;
		private Int32 _maxDCounts;
		private String _ccAdjustOnCount;
		private String _ccDecreaseAdjustmentCode;
		private String _ccIncreaseAdjustmentCode;
		private String _ccShowQuantity;
		private String _dailyCycleCountType;
		private String _pcAdjustOnCount;
		private String _pcDecreaseAdjustmentCode;
		private String _pcIncreaseAdjustmentCode;
		private String _pcShowQuantity;
		private String _rcAdjustOnCount;
		private String _rcDecreaseAdjCode;
		private String _rcIncreaseAdjCode;
		private String _rcShowQuantity;

		#endregion

		#region Properties

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual Int32 AbcADays
		{
			get { return _abcADays; }
			set { _abcADays = value; }
		}

		[DataMember]
		public virtual Int32 AbcBDays
		{
			get { return _abcBDays; }
			set { _abcBDays = value; }
		}

		[DataMember]
		public virtual Int32 AbcCDays
		{
			get { return _abcCDays; }
			set { _abcCDays = value; }
		}

		[DataMember]
		public virtual Int32 AbcDDays
		{
			get { return _abcDDays; }
			set { _abcDDays = value; }
		}

		[DataMember]
		public virtual Int32 MaxACounts
		{
			get { return _maxACounts; }
			set { _maxACounts = value; }
		}

		[DataMember]
		public virtual Int32 MaxBCounts
		{
			get { return _maxBCounts; }
			set { _maxBCounts = value; }
		}

		[DataMember]
		public virtual Int32 MaxCCounts
		{
			get { return _maxCCounts; }
			set { _maxCCounts = value; }
		}

		[DataMember]
		public virtual Int32 MaxDCounts
		{
			get { return _maxDCounts; }
			set { _maxDCounts = value; }
		}

		[DataMember]
		public virtual String CcAdjustOnCount
		{
			get { return _ccAdjustOnCount; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("CcAdjustOnCount must not be blank or null.");
				else _ccAdjustOnCount = value;
			}
		}

		[DataMember]
		public virtual String CcDecreaseAdjustmentCode
		{
			get { return _ccDecreaseAdjustmentCode; }
			set { _ccDecreaseAdjustmentCode = value; }
		}

		[DataMember]
		public virtual String CcIncreaseAdjustmentCode
		{
			get { return _ccIncreaseAdjustmentCode; }
			set { _ccIncreaseAdjustmentCode = value; }
		}

		[DataMember]
		public virtual String CcShowQuantity
		{
			get { return _ccShowQuantity; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("CcShowQuantity must not be blank or null.");
				else _ccShowQuantity = value;
			}
		}

		[DataMember]
		public virtual String DailyCycleCountType
		{
			get { return _dailyCycleCountType; }
			set { _dailyCycleCountType = value; }
		}

		[DataMember]
		public virtual String PcAdjustOnCount
		{
			get { return _pcAdjustOnCount; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("PcAdjustOnCount must not be blank or null.");
				else _pcAdjustOnCount = value;
			}
		}

		[DataMember]
		public virtual String PcDecreaseAdjustmentCode
		{
			get { return _pcDecreaseAdjustmentCode; }
			set { _pcDecreaseAdjustmentCode = value; }
		}

		[DataMember]
		public virtual String PcIncreaseAdjustmentCode
		{
			get { return _pcIncreaseAdjustmentCode; }
			set { _pcIncreaseAdjustmentCode = value; }
		}

		[DataMember]
		public virtual String PcShowQuantity
		{
			get { return _pcShowQuantity; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("PcShowQuantity must not be blank or null.");
				else _pcShowQuantity = value;
			}
		}

		[DataMember]
		public virtual String RcAdjustOnCount
		{
			get { return _rcAdjustOnCount; }
			set { _rcAdjustOnCount = value; }
		}

		[DataMember]
		public virtual String RcDecreaseAdjCode
		{
			get { return _rcDecreaseAdjCode; }
			set { _rcDecreaseAdjCode = value; }
		}

		[DataMember]
		public virtual String RcIncreaseAdjCode
		{
			get { return _rcIncreaseAdjCode; }
			set { _rcIncreaseAdjCode = value; }
		}

		[DataMember]
		public virtual String RcShowQuantity
		{
			get { return _rcShowQuantity; }
			set { _rcShowQuantity = value; }
		}


		#endregion
	}
}
