using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InventoryDiscrepancy : Entity
	{
		#region Fields

		private DateTime _processed;
		private Decimal _actualQuantity;
		private Decimal _expectedQuantity;
		private ItemTransaction _itemTransaction;
		private String _actualLocationCode;
		private String _actualPalletLpn;
		private String _companyCode;
		private String _employeeCode;
		private String _expectedLocationCode;
		private String _expectedPalletLpn;
		private String _itemCode;
		private String _problem;
		private String _transactionTypeCode;
		private String _warehouseCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime Processed
		{
			get { return _processed; }
			set { _processed = value; }
		}

		[DataMember]
		public virtual Decimal ActualQuantity
		{
			get { return _actualQuantity; }
			set { _actualQuantity = value; }
		}

		[DataMember]
		public virtual Decimal ExpectedQuantity
		{
			get { return _expectedQuantity; }
			set { _expectedQuantity = value; }
		}

		[DataMember]
		public virtual ItemTransaction ItemTransaction
		{
			get { return _itemTransaction; }
			set { _itemTransaction = value; }
		}

		[DataMember]
		public virtual String ActualLocationCode
		{
			get { return _actualLocationCode; }
			set { _actualLocationCode = value; }
		}

		[DataMember]
		public virtual String ActualPalletLpn
		{
			get { return _actualPalletLpn; }
			set { _actualPalletLpn = value; }
		}

		[DataMember]
		public virtual String CompanyCode
		{
			get { return _companyCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("CompanyCode must not be blank or null.");
				else _companyCode = value;
			}
		}

		[DataMember]
		public virtual String EmployeeCode
		{
			get { return _employeeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("EmployeeCode must not be blank or null.");
				else _employeeCode = value;
			}
		}

		[DataMember]
		public virtual String ExpectedLocationCode
		{
			get { return _expectedLocationCode; }
			set { _expectedLocationCode = value; }
		}

		[DataMember]
		public virtual String ExpectedPalletLpn
		{
			get { return _expectedPalletLpn; }
			set { _expectedPalletLpn = value; }
		}

		[DataMember]
		public virtual String ItemCode
		{
			get { return _itemCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ItemCode must not be blank or null.");
				else _itemCode = value;
			}
		}

		[DataMember]
		public virtual String Problem
		{
			get { return _problem; }
			set { _problem = value; }
		}

		[DataMember]
		public virtual String TransactionTypeCode
		{
			get { return _transactionTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("TransactionTypeCode must not be blank or null.");
				else _transactionTypeCode = value;
			}
		}

		[DataMember]
		public virtual String WarehouseCode
		{
			get { return _warehouseCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("WarehouseCode must not be blank or null.");
				else _warehouseCode = value;
			}
		}


		#endregion
	}
}
