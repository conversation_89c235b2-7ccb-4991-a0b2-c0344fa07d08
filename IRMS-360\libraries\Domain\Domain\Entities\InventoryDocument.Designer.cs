using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InventoryDocument : Entity
	{
		#region Fields

		private Byte[] _inventoryItemDocument;
		private DateTime? _effective;
		private DocumentType _documentType;
		private InventoryItem _inventoryItem;
		private String _alternateLocation;
		private String _documentCode;
		private String _fileName;
		private String _notes;

		#endregion

		#region Properties

		[DataMember]
		public virtual Byte[] InventoryItemDocument
		{
			get { return _inventoryItemDocument; }
			set { _inventoryItemDocument = value; }
		}

		[DataMember]
		public virtual DateTime? Effective
		{
			get { return _effective; }
			set { _effective = value; }
		}

		[DataMember]
		public virtual DocumentType DocumentType
		{
			get { return _documentType; }
			set { _documentType = value; }
		}

		[DataMember]
		public virtual InventoryItem InventoryItem
		{
			get { return _inventoryItem; }
			set { _inventoryItem = value; }
		}

		[DataMember]
		public virtual String AlternateLocation
		{
			get { return _alternateLocation; }
			set { _alternateLocation = value; }
		}

		[DataMember]
		public virtual String DocumentCode
		{
			get { return _documentCode; }
			set { _documentCode = value; }
		}

		[DataMember]
		public virtual String FileName
		{
			get { return _fileName; }
			set { _fileName = value; }
		}

		[DataMember]
		public virtual String Notes
		{
			get { return _notes; }
			set { _notes = value; }
		}


		#endregion
	}
}
