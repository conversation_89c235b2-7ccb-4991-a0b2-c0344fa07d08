using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InventoryFinancialSchedule : Entity
	{
		#region Fields

		private DateTime _realized;
		private Decimal _amount;
		private Decimal? _bookValueBegin;
		private Decimal? _bookValueEnd;
		private Decimal? _depreciationAccum;
		private Decimal? _depreciationExpense;
		private Decimal? _depreciationRate;
		private DepreciationMethod _depreciationMethod;
		private InventoryItem _inventoryItem;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime Realized
		{
			get { return _realized; }
			set { _realized = value; }
		}

		[DataMember]
		public virtual Decimal Amount
		{
			get { return _amount; }
			set { _amount = value; }
		}

		[DataMember]
		public virtual Decimal? BookValueBegin
		{
			get { return _bookValueBegin; }
			set { _bookValueBegin = value; }
		}

		[DataMember]
		public virtual Decimal? BookValueEnd
		{
			get { return _bookValueEnd; }
			set { _bookValueEnd = value; }
		}

		[DataMember]
		public virtual Decimal? DepreciationAccum
		{
			get { return _depreciationAccum; }
			set { _depreciationAccum = value; }
		}

		[DataMember]
		public virtual Decimal? DepreciationExpense
		{
			get { return _depreciationExpense; }
			set { _depreciationExpense = value; }
		}

		[DataMember]
		public virtual Decimal? DepreciationRate
		{
			get { return _depreciationRate; }
			set { _depreciationRate = value; }
		}

		[DataMember]
		public virtual DepreciationMethod DepreciationMethod
		{
			get { return _depreciationMethod; }
			set { _depreciationMethod = value; }
		}

		[DataMember]
		public virtual InventoryItem InventoryItem
		{
			get { return _inventoryItem; }
			set { _inventoryItem = value; }
		}


		#endregion
	}
}
