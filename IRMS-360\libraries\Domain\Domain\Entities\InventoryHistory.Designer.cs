using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InventoryHistory : Entity
	{
		#region Fields

		private AgencyOrganizationalUnit _agencyOrganizationalUnit;
		private CompanyLocationType _companyLocationType;
        private Customer _customer;
        private DateTime _processed;
		private DateTime? _fromDatetime;
		private DateTime? _toDatetime;
		private Decimal _quantity;
		private InventoryItem _inventoryItem;
		private ICollection<InvoiceDetail> _invoiceDetails = new HashSet<InvoiceDetail>();
		private String _assetCode;
		private String _inventoryLicensePlateCode;
		private String _itemCode;
		private String _locationCode;
		private String _lotNumber;
		private String _serialNumber;
        private String _isClosed;

        #endregion

        #region Properties

        [DataMember]
		public virtual AgencyOrganizationalUnit AgencyOrganizationalUnit
		{
			get { return _agencyOrganizationalUnit; }
			set { _agencyOrganizationalUnit = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}
        [DataMember]
        public virtual Customer Customer
        {
            get { return _customer; }
            set { _customer = value; }
        }

        [DataMember]
		public virtual DateTime Processed
		{
			get { return _processed; }
			set { _processed = value; }
		}

		[DataMember]
		public virtual DateTime? FromDatetime
		{
			get { return _fromDatetime; }
			set { _fromDatetime = value; }
		}

		[DataMember]
		public virtual DateTime? ToDatetime
		{
			get { return _toDatetime; }
			set { _toDatetime = value; }
		}

		[DataMember]
		public virtual Decimal Quantity
		{
			get { return _quantity; }
			set { _quantity = value; }
		}

		[DataMember]
		public virtual InventoryItem InventoryItem
		{
			get { return _inventoryItem; }
			set { _inventoryItem = value; }
		}

		[DataMember]
		public virtual ICollection<InvoiceDetail> InvoiceDetails
		{
			get { return _invoiceDetails; }
			set { _invoiceDetails = value; }
		}

		[DataMember]
		public virtual String AssetCode
		{
			get { return _assetCode; }
			set { _assetCode = value; }
		}

		[DataMember]
		public virtual String InventoryLicensePlateCode
		{
			get { return _inventoryLicensePlateCode; }
			set { _inventoryLicensePlateCode = value; }
		}

		[DataMember]
		public virtual String ItemCode
		{
			get { return _itemCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ItemCode must not be blank or null.");
				else _itemCode = value;
			}
		}

		[DataMember]
		public virtual String LocationCode
		{
			get { return _locationCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("LocationCode must not be blank or null.");
				else _locationCode = value;
			}
		}

		[DataMember]
		public virtual String LotNumber
		{
			get { return _lotNumber; }
			set { _lotNumber = value; }
		}

		[DataMember]
		public virtual String SerialNumber
		{
			get { return _serialNumber; }
			set { _serialNumber = value; }
		}

        [DataMember]
        public virtual String IsClosed
        {
            get { return _isClosed; }
            set { _isClosed = value; }
        }


        #endregion
    }
}
