using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class InventoryHistory : Entity
	{
		#region Constructor

		public InventoryHistory()
		{
			//
		}
        public virtual String CustomerCode { get; set; }
        public virtual Decimal? CatchWeight { get; set; }
        public virtual Decimal? ItemWeight { get; set; }

        #endregion
    }
}
