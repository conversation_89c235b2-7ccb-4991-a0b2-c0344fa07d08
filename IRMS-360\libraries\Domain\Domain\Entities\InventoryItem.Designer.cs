using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InventoryItem : Entity
	{
		#region Fields

		private AgencyOrganizationalUnit _agencyOrganizationalUnit;
		private CompanyLocationType _companyLocationType;
		private Contract _contract;
		private ContractLine _contractLine;
		private ContractSubLine _contractSubLine;
		private Customer _customer;
		private DateTime? _depreciationStartDate;
		private DateTime? _lastCycleCount;
		private DateTime? _lotExpiration;
		private DateTime? _manufactureDate;
		private DateTime? _received;
		private DateTime? _shelfExpiration;
		private DateTime? _stored;
		private DateTime? _warrantyExpiration;
		private Decimal _quantity;
		private Decimal? _averageCost;
		private Decimal? _catchWeight;
		private Decimal? _salePrice;
		private Decimal? _salvageValue;
		private Decimal? _unitCost;
		private DisposalCode _disposalCode;
		private Decimal? _uomQuantity;
		private Int32? _usefulLife;
		private Int32 _itemId;
		private Int32 _statusCodeId;
		private Int32? _companyLocationZoneId;
		private InventoryItem _parentInventoryItem;
		private Item _item;
		private KitHeader _kitHeader;
		private LicensePlate _licensePlate;
		private ICollection<Alert> _alerts = new HashSet<Alert>();
		private ICollection<AlertDetail> _alertDetails = new HashSet<AlertDetail>();
		private ICollection<InspectionDetail> _inspectionDetails = new HashSet<InspectionDetail>();
		private ICollection<InspectionHeader> _inspectionHeaders = new HashSet<InspectionHeader>();
		private ICollection<InventoryDocument> _inventoryDocuments = new HashSet<InventoryDocument>();
		private ICollection<InventoryFinancialSchedule> _inventoryFinancialSchedules = new HashSet<InventoryFinancialSchedule>();
        private ICollection<InventoryItemDetail> _inventoryItemDetails = new HashSet<InventoryItemDetail>();
        private ICollection<InventoryHistory> _inventoryHistories = new HashSet<InventoryHistory>();
		private ICollection<InventoryItem> _childInventoryItems = new HashSet<InventoryItem>();
		private ICollection<InventoryShadow> _inventoryShadows = new HashSet<InventoryShadow>();
		private ICollection<InventoryTask> _inventoryTasks = new HashSet<InventoryTask>();
		private ICollection<ItemFulfillment> _itemFulfillments = new HashSet<ItemFulfillment>();
		private ICollection<MaintenanceRequest> _maintenanceRequests = new HashSet<MaintenanceRequest>();
		private ICollection<ParticipantAsset> _participantAssets = new HashSet<ParticipantAsset>();
		private ICollection<ParticipantImmunization> _participantImmunizations = new HashSet<ParticipantImmunization>();
		private ICollection<RecallItem> _recallItems = new HashSet<RecallItem>();
		private ICollection<RequisitionDetail> _requisitionDetails = new HashSet<RequisitionDetail>();
		private ICollection<ReturnDetail> _returnDetails = new HashSet<ReturnDetail>();
		private ICollection<ShipmentDetail> _shipmentDetails = new HashSet<ShipmentDetail>();
		private ICollection<UdfInventoryValue> _udfInventoryValues = new HashSet<UdfInventoryValue>();
		private ICollection<WorkOrderDetail> _workOrderDetails = new HashSet<WorkOrderDetail>();
		private ICollection<WorkOrderHeader> _workOrderHeaders = new HashSet<WorkOrderHeader>();
		private Location _homeLocation;
		private Location _location;
		private PoolItem _poolItem;
		private ProviderOrganizationalUnit _providerOrganizationalUnit;
		private ReceiptDetail _receiptDetail;
		private StatusCode _statusCode;
		private StatusCode _stockStatusCode;
		private String _assetCode;
		private String _caliber;
		private String _comments;
		private String _cycleCount;
		private String _disposalNotes;
		private String _leased;
		private String _locationPickType;
		private String _lotNumber;
		private String _nonItemCode;
		private String _nonItemDescription;
		private String _paplItem;
		private String _serialNumber;
		private UnitOfMeasure _unitOfMeasure;
        private String _primaryPick;

        #endregion

        #region Properties

        [DataMember]
		public virtual AgencyOrganizationalUnit AgencyOrganizationalUnit
		{
			get { return _agencyOrganizationalUnit; }
			set { _agencyOrganizationalUnit = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual Contract Contract
		{
			get { return _contract; }
			set { _contract = value; }
		}

		[DataMember]
		public virtual ContractLine ContractLine
		{
			get { return _contractLine; }
			set { _contractLine = value; }
		}

		[DataMember]
		public virtual ContractSubLine ContractSubLine
		{
			get { return _contractSubLine; }
			set { _contractSubLine = value; }
		}

		[DataMember]
		public virtual Customer Customer
		{
			get { return _customer; }
			set { _customer = value; }
		}

		[DataMember]
		public virtual DateTime? DepreciationStartDate
		{
			get { return _depreciationStartDate; }
			set { _depreciationStartDate = value; }
		}

		[DataMember]
		public virtual DateTime? LastCycleCount
		{
			get { return _lastCycleCount; }
			set { _lastCycleCount = value; }
		}

		[DataMember]
		public virtual DateTime? LotExpiration
		{
			get { return _lotExpiration; }
			set { _lotExpiration = value; }
		}

		[DataMember]
		public virtual DateTime? ManufactureDate
		{
			get { return _manufactureDate; }
			set { _manufactureDate = value; }
		}

		[DataMember]
		public virtual DateTime? Received
		{
			get { return _received; }
			set { _received = value; }
		}

		[DataMember]
		public virtual DateTime? ShelfExpiration
		{
			get { return _shelfExpiration; }
			set { _shelfExpiration = value; }
		}

		[DataMember]
		public virtual DateTime? Stored
		{
			get { return _stored; }
			set { _stored = value; }
		}

		[DataMember]
		public virtual DateTime? WarrantyExpiration
		{
			get { return _warrantyExpiration; }
			set { _warrantyExpiration = value; }
		}

		[DataMember]
		public virtual Decimal Quantity
		{
			get { return _quantity; }
			set { _quantity = value; }
		}

		[DataMember]
		public virtual Decimal? AverageCost
		{
			get { return _averageCost; }
			set { _averageCost = value; }
		}

		[DataMember]
		public virtual Decimal? CatchWeight
		{
			get { return _catchWeight; }
			set { _catchWeight = value; }
		}

		[DataMember]
		public virtual Decimal? SalePrice
		{
			get { return _salePrice; }
			set { _salePrice = value; }
		}

		[DataMember]
		public virtual Decimal? SalvageValue
		{
			get { return _salvageValue; }
			set { _salvageValue = value; }
		}

		[DataMember]
		public virtual Decimal? UnitCost
		{
			get { return _unitCost; }
			set { _unitCost = value; }
		}

		[DataMember]
		public virtual DisposalCode DisposalCode
		{
			get { return _disposalCode; }
			set { _disposalCode = value; }
		}

		[DataMember]
		public virtual Decimal? UomQuantity
		{
			get { return _uomQuantity; }
			set { _uomQuantity = value; }
		}

		[DataMember]
		public virtual Int32? UsefulLife
		{
			get { return _usefulLife; }
			set { _usefulLife = value; }
		}

		[DataMember]
		public virtual Int32 ItemId
		{
			get { return _itemId; }
			set { _itemId = value; }
		}

		[DataMember]
		public virtual Int32 StatusCodeId
		{
			get { return _statusCodeId; }
			set { _statusCodeId = value; }
		}

		[DataMember]
		public virtual Int32? CompanyLocationZoneId
		{
			get { return _companyLocationZoneId; }
			set { _companyLocationZoneId = value; }
		}

		[DataMember]
		public virtual InventoryItem ParentInventoryItem
		{
			get { return _parentInventoryItem; }
			set { _parentInventoryItem = value; }
		}

		[DataMember]
		public virtual Item Item
		{
			get { return _item; }
			set { _item = value; }
		}

		[DataMember]
		public virtual KitHeader KitHeader
		{
			get { return _kitHeader; }
			set { _kitHeader = value; }
		}

		[DataMember]
		public virtual LicensePlate LicensePlate
		{
			get { return _licensePlate; }
			set { _licensePlate = value; }
		}

		[DataMember]
		public virtual ICollection<Alert> Alerts
		{
			get { return _alerts; }
			set { _alerts = value; }
		}

		[DataMember]
		public virtual ICollection<AlertDetail> AlertDetails
		{
			get { return _alertDetails; }
			set { _alertDetails = value; }
		}

		[DataMember]
		public virtual ICollection<InspectionDetail> InspectionDetails
		{
			get { return _inspectionDetails; }
			set { _inspectionDetails = value; }
		}

		[DataMember]
		public virtual ICollection<InspectionHeader> InspectionHeaders
		{
			get { return _inspectionHeaders; }
			set { _inspectionHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryDocument> InventoryDocuments
		{
			get { return _inventoryDocuments; }
			set { _inventoryDocuments = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryFinancialSchedule> InventoryFinancialSchedules
		{
			get { return _inventoryFinancialSchedules; }
			set { _inventoryFinancialSchedules = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryHistory> InventoryHistories
		{
			get { return _inventoryHistories; }
			set { _inventoryHistories = value; }
		}

        [DataMember]
        public virtual ICollection<InventoryItemDetail> InventoryItemDetails
        {
            get { return _inventoryItemDetails; }
            set { _inventoryItemDetails = value; }
        }

        [DataMember]
		public virtual ICollection<InventoryItem> ChildInventoryItems
		{
			get { return _childInventoryItems; }
			set { _childInventoryItems = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryShadow> InventoryShadows
		{
			get { return _inventoryShadows; }
			set { _inventoryShadows = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryTask> InventoryTasks
		{
			get { return _inventoryTasks; }
			set { _inventoryTasks = value; }
		}

		[DataMember]
		public virtual ICollection<ItemFulfillment> ItemFulfillments
		{
			get { return _itemFulfillments; }
			set { _itemFulfillments = value; }
		}

		[DataMember]
		public virtual ICollection<MaintenanceRequest> MaintenanceRequests
		{
			get { return _maintenanceRequests; }
			set { _maintenanceRequests = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantAsset> ParticipantAssets
		{
			get { return _participantAssets; }
			set { _participantAssets = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantImmunization> ParticipantImmunizations
		{
			get { return _participantImmunizations; }
			set { _participantImmunizations = value; }
		}

		[DataMember]
		public virtual ICollection<RecallItem> RecallItems
		{
			get { return _recallItems; }
			set { _recallItems = value; }
		}

		[DataMember]
		public virtual ICollection<RequisitionDetail> RequisitionDetails
		{
			get { return _requisitionDetails; }
			set { _requisitionDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ReturnDetail> ReturnDetails
		{
			get { return _returnDetails; }
			set { _returnDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ShipmentDetail> ShipmentDetails
		{
			get { return _shipmentDetails; }
			set { _shipmentDetails = value; }
		}

		[DataMember]
		public virtual ICollection<UdfInventoryValue> UdfInventoryValues
		{
			get { return _udfInventoryValues; }
			set { _udfInventoryValues = value; }
		}

		[DataMember]
		public virtual ICollection<WorkOrderDetail> WorkOrderDetails
		{
			get { return _workOrderDetails; }
			set { _workOrderDetails = value; }
		}

		[DataMember]
		public virtual ICollection<WorkOrderHeader> WorkOrderHeaders
		{
			get { return _workOrderHeaders; }
			set { _workOrderHeaders = value; }
		}

		[DataMember]
		public virtual Location HomeLocation
		{
			get { return _homeLocation; }
			set { _homeLocation = value; }
		}

		[DataMember]
		public virtual Location Location
		{
			get { return _location; }
			set { _location = value; }
		}

		[DataMember]
		public virtual PoolItem PoolItem
		{
			get { return _poolItem; }
			set { _poolItem = value; }
		}

		[DataMember]
		public virtual ProviderOrganizationalUnit ProviderOrganizationalUnit
		{
			get { return _providerOrganizationalUnit; }
			set { _providerOrganizationalUnit = value; }
		}

		[DataMember]
		public virtual ReceiptDetail ReceiptDetail
		{
			get { return _receiptDetail; }
			set { _receiptDetail = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual StatusCode StockStatusCode
		{
			get { return _stockStatusCode; }
			set { _stockStatusCode = value; }
		}

		[DataMember]
		public virtual String AssetCode
		{
			get { return _assetCode; }
			set { _assetCode = value; }
		}

		[DataMember]
		public virtual String Caliber
		{
			get { return _caliber; }
			set { _caliber = value; }
		}

		[DataMember]
		public virtual String Comments
		{
			get { return _comments; }
			set { _comments = value; }
		}

		[DataMember]
		public virtual String CycleCount
		{
			get { return _cycleCount; }
			set { _cycleCount = value; }
		}

		[DataMember]
		public virtual String DisposalNotes
		{
			get { return _disposalNotes; }
			set { _disposalNotes = value; }
		}

		[DataMember]
		public virtual String Leased
		{
			get { return _leased; }
			set { _leased = value; }
		}

        [DataMember]
		public virtual String LocationPickType
        {
			get { return _locationPickType; }
			set { _locationPickType = value; }
		}

		[DataMember]
		public virtual String LotNumber
		{
			get { return _lotNumber; }
			set { _lotNumber = value; }
		}

		[DataMember]
		public virtual String NonItemCode
		{
			get { return _nonItemCode; }
			set { _nonItemCode = value; }
		}

		[DataMember]
		public virtual String NonItemDescription
		{
			get { return _nonItemDescription; }
			set { _nonItemDescription = value; }
		}

		[DataMember]
		public virtual String PaplItem
		{
			get { return _paplItem; }
			set { _paplItem = value; }
		}

		[DataMember]
		public virtual String SerialNumber
		{
			get { return _serialNumber; }
			set { _serialNumber = value; }
		}

		[DataMember]
		public virtual UnitOfMeasure UnitOfMeasure
		{
			get { return _unitOfMeasure; }
			set { _unitOfMeasure = value; }
		}
        [DataMember]
        public virtual String primaryPick
        {
            get { return _primaryPick; }
            set { _primaryPick = value; }
        }

        #endregion
    }
}
