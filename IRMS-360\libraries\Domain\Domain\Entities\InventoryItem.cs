using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;

using NHibernate.Criterion;
using NHibernate.SqlCommand;
using NHibernate.Transform;

using Upp.Irms.Constants;
using Upp.Irms.Core;
using Upp.Shared.Extensions;
using Upp.Shared.Utilities;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class InventoryItem : Entity
	{
		#region Properties

		[DataMember]
		public virtual Boolean EditReturnDate { get; set; }
		[DataMember]
		public virtual Boolean Issued { get; set; }
		[DataMember]
		public virtual Boolean ParentIssuance { get; set; }
		[DataMember]
		public virtual Boolean ParticipantIssuance { get; set; }
		[DataMember]
		public virtual Boolean PerformByUom { get; set; }
		[DataMember]
		public virtual Boolean PopulateItem { get; set; }
        [DataMember]
        public virtual Boolean PreviouslyCounted { get; set; }
        [DataMember]
        public virtual Boolean PrimaryPick { get; set; }
        [DataMember]
        public virtual Boolean ShowQuantity { get; set; }
        [DataMember]
        public virtual Boolean SkipSerailNumberPrompt { get; set; }
		[DataMember]
		public virtual DateTime? AdjustedLotExpiration { get; set; }
		[DataMember]
		public virtual DateTime? ReturnDate { get; set; }
		[DataMember]
		public virtual Decimal AdjustedQuantity { get; set; }
		[DataMember]
		public virtual Decimal MovementQuantity { get; set; }
        [DataMember]
        public virtual Decimal PrimaryLocationQuantity { get; set; }
		[DataMember]
		public virtual Decimal PutawayQuantity { get; set; }
		[DataMember]
		public virtual Decimal? BoxQuantity { get; set; }
		[DataMember]
		public virtual Decimal? CaseQuantity { get; set; }
        [DataMember]
        public virtual Decimal? PrimaryLocationMaxQuantity { get; set; }
		[DataMember]
		public virtual Int32 Copies { get; set; }
        [DataMember]
        public virtual CartonSize CartonSize { get; set; }
        [DataMember]
        public virtual CartonSize CartonSizeTo { get; set; }
		[DataMember]
		public virtual InventoryAdjustmentCode AdjustmentCode { get; set; }
		[DataMember]
		public virtual InventoryItem InventoryItemTo { get; set; }
		[DataMember]
		public virtual LicensePlate LicensePlateTo { get; set; }
		[DataMember]
		public virtual LicensePlate ParentLicensePlate { get; set; }
		[DataMember]
		public virtual Location LocationTo { get; set; }
		[DataMember]
		public virtual OrganizationParticipant Participant { get; set; }
		[DataMember]
		public virtual PrinterTypePrinter PrinterTypePrinter { get; set; }
		[DataMember]
		public virtual PoolItem AdjustedPoolItem { get; set; }
		[DataMember]
		public virtual StatusCode AdjustedStatusCode { get; set; }
		[DataMember]
		public virtual StatusReasonCode StatusReasonCode { get; set; }
		[DataMember]
		public virtual String AdjustedLotNumber { get; set; }
		[DataMember]
		public virtual String AisleCode { get; set; }
		[DataMember]
		public virtual String CompanyCode { get; set; }
		[DataMember]
		public virtual String CompanyLocationCode { get; set; }
        [DataMember]
        public virtual String CustomerCode { get; set; }
        [DataMember]
        public virtual Int32? CustomerId { get; set; }
        [DataMember]
		public virtual String InventoryItems { get; set; }
		[DataMember]
		public virtual String ItemCode { get; set; }
		[DataMember]
		public virtual String ItemDescription { get; set; }
		[DataMember]
		public virtual String ItemLongDescription { get; set; }
		[DataMember]
		public virtual String ItemTypeCode { get; set; }
		[DataMember]
		public virtual String LocationCode { get; set; }
		[DataMember]
		public virtual String LocationCodeTo { get; set; }
		[DataMember]
		public virtual String Locations { get; set; }
		[DataMember]
		public virtual String Memo { get; set; }
		[DataMember]
		public virtual String OrderCode { get; set; }
		[DataMember]
		public virtual String OrganizationCode { get; set; }
		[DataMember]
		public virtual String ParentAssetCode { get; set; }
		[DataMember]
		public virtual String ParentLicensePlateCode { get; set; }
		[DataMember]
		public virtual String ParentLicensePlateStatusCodeCode { get; set; }
		[DataMember]
		public virtual String ParticipantName { get; set; }
		[DataMember]
		public virtual String PrimaryLocationCode { get; set; }
		[DataMember]
		public virtual String SerialNumbers { get; set; }
		[DataMember]
		public virtual String StatusCodeCode { get; set; }
		[DataMember]
		public virtual String UomCode { get; set; }
		[DataMember]
		public virtual String UomDescription { get; set; }
		[DataMember]
		public virtual String UpcCode { get; set; }
		[DataMember]
		public virtual String ZoneCode { get; set; }
		[DataMember]
		public virtual Task Task { get; set; }
		[DataMember]
		public virtual TransactionType TransactionType { get; set; }

        [DataMember]
        public virtual String Provider { get; set; }
        [DataMember]
        public virtual String Client { get; set; }
        [DataMember]
        public virtual String SnapshotDate { get; set; }
        [DataMember]
        public virtual String SnapshotTime { get; set; }
        [DataMember]
        public virtual Decimal Days0_90 { get; set; }

        [DataMember]
        public virtual Decimal Days91_180 { get; set; }

        [DataMember]
        public virtual Decimal Days181_270 { get; set; }

        [DataMember]
        public virtual Decimal Days271_365 { get; set; }

        [DataMember]
        public virtual Decimal Days365 { get; set; }

        #endregion

        #region Properties.Reports
        public virtual Decimal AllocatedQuantity { get; set; }
        public virtual Decimal AvailableQuantity { get; set; }
        public virtual Decimal DemandQuantity { get; set; }
        public virtual Decimal ExpectedQuantity { get; set; }
        public virtual Decimal IntransitQuantity { get; set; }
        public virtual decimal BackOrderDemandQuantity { get; set; }
        public virtual Decimal OnHandQuantity { get; set; }
        public virtual Decimal OnHand { get; set; }
        public virtual Decimal PickedQuantity { get; set; }
        public virtual Decimal ReservedQuantity { get; set; }
        public virtual Decimal UnAvailableDemandQuantity { get; set; }
        public virtual Decimal UnAvailableReservedQuantity { get; set; }
        public virtual Decimal UnavailableQuantity { get; set; }
        public virtual Decimal Weight { get; set; }
        public virtual Int32? ItemsId { get; set; }
        public virtual Int32? InventoryHistoryId { get; set; }
        public virtual Int32? OrderDetailId { get; set; }
        public virtual Int32? OrderHeaderId { get; set; } 
        public virtual Int32? StandardUOMID { get; set; }
        public virtual Int32? StatusCodesId { get; set; }        
        public virtual String CompanyName { get; set; }
        public virtual String Color { get; set; }
        public virtual String HostBatch { get; set; }
        public virtual String ItemGroupCode { get; set; }
        public virtual String OrderHeaderSuffix { get; set; }
        public virtual String LicenseTypeCode { get; set; }
        public virtual String StatusCodeDescription { get; set; }
        public virtual String Warehouse { get; set; }
        public virtual Decimal InboundInspectionQuantity { get; set; }        

        
        public virtual Decimal QaHoldQuantity { get; set; }
        public virtual Decimal GuaranteedQuantity { get; set; }
        public virtual String Division { get; set; }
        public virtual Decimal? CycleCountQuantity { get; set; }
        public virtual Decimal OnOrder { get; set; }
        public virtual String PurchaseOrderCode { get; set; }
        public virtual String PurchaseOrderSuffix { get; set; }
        public virtual String ItemNumber { get; set; }
        public virtual String Description { get; set; }
        public virtual Int32? UnitOfMeasureId { get; set; }
        
        #endregion

        #region Constructor

        public InventoryItem()
		{
			//
		}

		#endregion

        #region Methods.Private.PhysicalInventory

        private InventoryTask FindInvenotryTask()
        {
            StatusCode complete = Entity.Retrieve<StatusCode>(TaskStatuses.Complete);
            TransactionType physical = Entity.Retrieve<TransactionType>(InventoryTransactions.Physical);
            //
            DetachedCriteria criteria = DetachedCriteria.For<InventoryTask>()
                                        .Add("InventoryItem", this)
                                        .Add("Task.StatusCode", "!=", complete)
                                        .Add("Task.TransactionType", physical);
            IList<InventoryTask> tasks = Repositories.Get<InventoryTask>().List(criteria);

            if (tasks == null)
            {
                criteria = DetachedCriteria.For<InventoryTask>()
                                        .Add("Item", _item)
                                        .Add("LocationFrom.LocationCode", this.LocationCode)
                                        .Add("LicensePlate.LicensePlateCode", this.ParentLicensePlateCode)
                                        .Add("Task.StatusCode", "!=", complete)
                                        .Add("Task.TransactionType", physical);               
                
                tasks = Repositories.Get<InventoryTask>().List(criteria);
            }
            foreach (InventoryTask task in tasks)
            {
                task.ChangeStatus(complete);
            }
            //          
            if (tasks != null && tasks.Count > 0)
            {
                criteria = DetachedCriteria.For<InventoryTask>()
                    .Add("StatusCode", "!=", complete)
                    .Add("Task", tasks[0].Task);
                Int32 remaining = Repositories.Get<InventoryTask>().Count(criteria);
                if (remaining == 0)
                {
                    tasks[0].Task.Completed = tasks[0].Task.DateModified = DateTime.Now;
                    tasks[0].Task.StatusCode = complete;
                    tasks[0].Task.UserModified = Registry.Find<UserAccount>().UserName;
                    Repositories.Get<Task>().Update(tasks[0].Task);
                }
                return tasks[0];
            }
            else return null;
        }

        private Decimal WritePhyscialCount(bool showQuantity, Decimal adjust, InventoryItem element)
        {
            element.CycleCount = "N";
            if (element.Location != null)
            {
                element.Location.SetCycleflag();
                element.Location.SetPrimarypick(this.PrimaryPick);
            }
            //
            if (adjust > 0)
            {
                Decimal from = element.Quantity;
                //                
                element.ChangeQuantity(element.Quantity + adjust);
                element.WritePhyscialInventory(from, element.Quantity);
                adjust = 0;
            }
            else if (adjust < 0)
            {
                adjust += element.Quantity;
                //
                Decimal from = element.Quantity;
                StatusCode fromStatus = element.StatusCode;

                if (adjust < 0) element.ChangeQuantity(0M);
                else element.ChangeQuantity(adjust);
                //                    
                element.WritePhyscialInventory(from, element.Quantity);
                if (adjust >= 0) adjust = 0;
            }
            else element.WritePhyscialInventory(element.Quantity, element.Quantity);
            return adjust;
        }     

        #endregion

		#region Methods.Private.Transactions

		private void WriteCheckIn()
		{
			TransactionType type = Entity.Retrieve<TransactionType>(InventoryTransactions.CheckIn);
			ItemTransaction transaction = Entity.Activate<ItemTransaction>();
			transaction.InventoryItem = this;
			transaction.Item = _item;
			transaction.LocationFrom = null;
			transaction.LocationTo = this.LocationTo;
			transaction.LotNumber = _lotNumber;
			transaction.Occurred = DateTime.Now;
			if (this.Participant == null)
			{
				if (this.InventoryItemTo != null) transaction.ParentInventoryItem = this.InventoryItemTo;
				else transaction.ParentInventoryItem = _parentInventoryItem;
			}
			transaction.ParticipantBy = Registry.Find<UserAccount>().OrganizationParticipant;
			transaction.ParticipantFrom = this.Participant;
			if ("N".Equals(_item.ItemType.Consumable)) transaction.Quantity = 1;
			else transaction.Quantity = Converter.ToInt32(this.MovementQuantity);
			transaction.SerialNumber = _serialNumber;
			transaction.TransactionType = type;
			//
			Repositories.Get<ItemTransaction>().Add(transaction);
		}

		private void WriteCheckOut()
		{
			TransactionType type = Entity.Retrieve<TransactionType>(InventoryTransactions.CheckOut);
			ItemTransaction transaction = Entity.Activate<ItemTransaction>();
			transaction.InventoryItem = this;
			transaction.Item = _item;
			transaction.LocationFrom = _location;
			transaction.LotNumber = _lotNumber;
			transaction.Occurred = DateTime.Now;
			if (this.Participant == null) transaction.ParentInventoryItem = this.InventoryItemTo;
			transaction.ParticipantBy = Registry.Find<UserAccount>().OrganizationParticipant;
			transaction.ParticipantTo = this.Participant;
			if ("N".Equals(_item.ItemType.Consumable)) transaction.Quantity = 1;
			else transaction.Quantity = Converter.ToInt32(this.MovementQuantity);
			transaction.SerialNumber = _serialNumber;
			transaction.TransactionType = type;
			//
			Repositories.Get<ItemTransaction>().Add(transaction);
		}

		private void WriteKitAssignment()
		{
			TransactionType type = Entity.Retrieve<TransactionType>(InventoryTransactions.KitAssignment);
			ItemTransaction transaction = Entity.Activate<ItemTransaction>();
			transaction.InventoryItem = this;
			transaction.Item = _item;
			transaction.LocationFrom = _location;
			transaction.LocationTo = this.InventoryItemTo.Location;
			transaction.LotNumber = _lotNumber;
			transaction.Occurred = DateTime.Now;
			transaction.ParticipantBy = Registry.Find<UserAccount>().OrganizationParticipant;
			transaction.Quantity = Converter.ToInt32(this.MovementQuantity);
			transaction.ParentInventoryItem = this.InventoryItemTo;
			transaction.SerialNumber = _serialNumber;
			transaction.Task = this.Task;
			transaction.TransactionType = type;
			//
			Repositories.Get<ItemTransaction>().Add(transaction);
		}

		#endregion

		#region Methods.Public

		public virtual void ChangeLocation(Location location)
		{
			_dateModified = DateTime.Now;
			_location = location;
			_userModified = Registry.Find<UserAccount>().UserName;
			//
			Repositories.Get<InventoryItem>().Update(this);
		}

		public virtual void ChangeLotNumber(string lotNumber, DateTime? lotExpiration)
		{
			_dateModified = DateTime.Now;
			_lotExpiration = lotExpiration;
			_lotNumber = lotNumber;
			_manufactureDate = InventoryItem.CalculateManufactureDate(_lotNumber);
			_statusCode = InventoryItem.UpdateStatus(_item, _statusCode, _manufactureDate);
			_userModified = Registry.Find<UserAccount>().UserName;
			//
			Repositories.Get<InventoryItem>().Update(this);
		}

		public virtual void ChangePoolItem(PoolItem pool)
		{
			_dateModified = DateTime.Now;
			_poolItem = pool;
			_userModified = Registry.Find<UserAccount>().UserName;
			//
			Repositories.Get<InventoryItem>().Update(this);
		}

		public virtual void ChangeQuantity(Decimal quantity)
		{
			_dateModified = DateTime.Now;
			_quantity = quantity;
			//
			if (_quantity == 0)
			{
				/*if (_licensePlate != null)
				{
					_licensePlate.ParentLicensePlate = null;
					Repositories.Get<LicensePlate>().Update(_licensePlate);
				}*/
				_statusCode = Entity.Retrieve<StatusCode>(InventoryStatuses.Unavailable);
			}
            else if (_quantity > 0 && "U".Equals(_statusCode.Code, StringComparison.InvariantCultureIgnoreCase))
            {
                _statusCode = Entity.Retrieve<StatusCode>(InventoryStatuses.Available);
            }
			_userModified = Registry.Find<UserAccount>().UserName;
			//
			Repositories.Get<InventoryItem>().Update(this);
		}

		public virtual void ChangeStatus(StatusCode status)
		{
			_dateModified = DateTime.Now;
			_statusCode = status;
			_userModified = Registry.Find<UserAccount>().UserName;
			//
			Repositories.Get<InventoryItem>().Update(this);
		}

        public virtual void CheckSkipSerailNumberPrompt()
        {
            Boolean? skip = BusinessRule.RetrieveBoolean("11133");
            this.SkipSerailNumberPrompt = skip.HasValue ? skip.Value : false;
        }

		public virtual Decimal Consume(Decimal remaining)
		{
			_dateModified = DateTime.Now;
			if (remaining < _quantity)
			{
				_quantity -= remaining;
				remaining = 0;
			}
			else
			{
				remaining -= _quantity;
				_quantity = 0;
			}
			_userModified = Registry.Find<UserAccount>().UserName;
			Repositories.Get<InventoryItem>().Update(this);
			//
			return remaining;
		}

		public virtual void FindBoxQuantity()
		{
			DetachedCriteria criteria = DetachedCriteria.For<ItemUomRelationship>()
				.Add("Active", "A")
				.Add("Item", _item)
				.Add("UnitOfMeasure.UomCode", CodeValue.GetCode(InventoryUoms.Box));
			ItemUomRelationship relationship = Repositories.Get<ItemUomRelationship>().Retrieve(criteria);
			if (relationship == null) this.BoxQuantity = null;
			else this.BoxQuantity = relationship.Factor;
		}

		public virtual void FindCaseQuantity()
		{
			DetachedCriteria criteria = DetachedCriteria.For<ItemUomRelationship>()
				.Add("Active", "A")
				.Add("Item", _item)
				.Add("UnitOfMeasure.UomCode", CodeValue.GetCode(InventoryUoms.Case));
			ItemUomRelationship relationship = Repositories.Get<ItemUomRelationship>().Retrieve(criteria);
			if (relationship == null) this.CaseQuantity = null;
			else this.CaseQuantity = relationship.Factor;
		}

        public virtual void FindCartonSize()
        {
            ProjectionList projections = Projections.ProjectionList()
                   .Add(Projections.Property("CartonSize"), "CartonSize");
            DetachedCriteria criteria = DetachedCriteria.For<CartonHeader>()
                                        .Add("LicensePlate.LicensePlateCode", this.ParentLicensePlateCode)
                                        .SetProjection(projections)
                                        .SetResultTransformer(Transformers.AliasToBean<CartonHeader>())
                                        .SetMaxResults(1);
            CartonHeader header = Repositories.Get<CartonHeader>().Retrieve(criteria);

            if (header == null) this.CartonSize = null;
            else this.CartonSize = header.CartonSize;
        }

        public virtual void FindPhysicalCount()
        {
            StatusCode complete = Entity.Retrieve<StatusCode>(TaskStatuses.Complete);
            TransactionType type = Entity.Retrieve<TransactionType>(InventoryTransactions.PhysicalCount);

            ProjectionList projections = Projections.ProjectionList()
                   .Add(Projections.Property("Id"), "Id");
         
            DetachedCriteria criteria = DetachedCriteria.For<ItemTransaction>()                                       
                                        .Add("LocationFrom", this.Location)
                                        .Add("Item", this.Item)
                                        .Add("Task.StatusCode", "!=", complete)
                                        .Add("TransactionType", type)                                                                                
                                        .SetProjection(projections)
                                        .SetResultTransformer(Transformers.AliasToBean<ItemTransaction>())
                                        .SetMaxResults(1);

            if (!String.IsNullOrEmpty(this.ParentLicensePlateCode))
                criteria = criteria.Add("InventoryItem.LicensePlate.ParentLicensePlate.LicensePlateCode", this.ParentLicensePlateCode); 

            if(!string.IsNullOrEmpty(this.LotNumber))
                criteria=criteria.Add("LotNumber", this.LotNumber);
            //
            ItemTransaction item = Repositories.Get<ItemTransaction>().Retrieve(criteria);
            //
            this.PreviouslyCounted = (item != null);
        }

        public virtual void FindPrimaryPick()
        {
            CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();

            ProjectionList projections = Projections.ProjectionList()
                   .Add(Projections.Property("PrimaryPick"), "PrimaryPick");

            DetachedCriteria criteria = DetachedCriteria.For<Location>()
                                        .Add("CompanyLocationType", warehouse)
                                        .Add("LocationCode", this.LocationCode)
                                        .SetProjection(projections)
                                        .SetResultTransformer(Transformers.AliasToBean<Location>())
                                        .SetMaxResults(1);
            Location location = Repositories.Get<Location>().Retrieve(criteria);

            if (location != null) this.PrimaryPick = "Y".Equals(location.PrimaryPick);
        }

        public virtual void FindUomInfo()
        {
            CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
            String inventory = CodeValue.GetCode(FunctionalAreas.Inventory);
            //           
            if (_item.PurchaseUom == null)
            {
                String error = String.Format("Missing Purchase UOM for item {0}.", this.ItemCode);
                throw new Exception(error);
            }
            //
            DetachedCriteria criteria = DetachedCriteria.For<UnitOfMeasure>()
                .CreateAlias("FunctionalAreaCode", "fac")
                .Add(Expression.Eq("fac.Code", inventory))
                .Add(Expression.Eq("UomCode", _item.PurchaseUom.UomCode))
                .SetMaxResults(1);
            UnitOfMeasure unitOfMeasure = Repositories.Get<UnitOfMeasure>().Retrieve(criteria);
            if (unitOfMeasure == null)
            {
                String error = String.Format("Unable to find UOM for {0}.", _item.PurchaseUom.UomCode);
                throw new Exception(error);
            }           
            //
            criteria = DetachedCriteria.For<ItemUomRelationship>()
                .Add("Active", "A")
                .Add("Item", _item)
                .Add("UnitOfMeasure", unitOfMeasure);
            ItemUomRelationship relationship = Repositories.Get<ItemUomRelationship>().Retrieve(criteria);
            if (relationship == null)
            {
                String error = String.Format("Missing Item UOM relationship for item {0} and UOM {1}.", this.ItemCode, this.UomDescription);
                throw new Exception(error);
            }
            else this.UomQuantity = Converter.ToInt32(relationship.Factor);
            if (_item.UnitOfMeasure != null) this.UomDescription = String.Format(" {0}", Inflector.Pluralize(_item.UnitOfMeasure.Description));            
        }

		public virtual void Move(Location to, LicensePlate parent)
		{
			if (!_location.SameAs(to) && this.TransactionType != null) // <-- Far from ideal.
				this.WriteStockMovement(this.TransactionType, to, parent, _quantity, null);
			//
			_dateModified = DateTime.Now;
			if (_licensePlate != null) _licensePlate.ChangeParent(parent);
			//
			_location = to;
			_userModified = Registry.Find<UserAccount>().UserName;
			//
			Repositories.Get<InventoryItem>().Update(this);
		}

		public virtual Decimal Move(Location to, LicensePlate parent, Decimal remaining)
		{
			if (remaining < _quantity)
			{
				this.Split(_quantity - remaining);
				//
				_quantity = remaining;
				this.Move(to, parent);
				//
				remaining = 0;
			}
			else
			{
				this.Move(to, parent);
				remaining -= _quantity;
			}
			//
			return remaining;
		}

		public virtual void MoveInventory(Location to, LicensePlate parent, Decimal quantity)
		{
            if (parent != null)
            {
                if (!parent.Id.HasValue)
                {
                    parent.CompanyLocationType = Registry.Find<CompanyLocationType>();
                    parent.LicenseType = Entity.Retrieve<LicenseType>(InventoryLicenseTypes.Pallet);
                    Repositories.Get<LicensePlate>().Add(parent);
                    //
                    parent.ChangeStatus(Entity.Retrieve<StatusCode>(LicensePlateStatuses.Open));
                }
                parent.ChangeLocation(to);
            }
            //
			Decimal remaining = quantity;
			Location from = _location;
			//
			DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
				.Add("Item", _item)
				.Add("Quantity", ">", 0M)
				.Add("StatusCode", _statusCode)
				.AddOrder("Received");
			if (_location != null) criteria = criteria.Add("Location", _location);
			if (!String.IsNullOrEmpty(_lotNumber)) criteria = criteria.Add("LotNumber", _lotNumber);
			if (!String.IsNullOrEmpty(this.ParentLicensePlateCode))
				criteria = criteria.Add("LicensePlate.ParentLicensePlate.LicensePlateCode", this.ParentLicensePlateCode);
			IList<InventoryItem> inventory = Repositories.Get<InventoryItem>().List(criteria);
			foreach (InventoryItem element in inventory)
			{
				if (remaining <= 0) break;
				element.TransactionType = this.TransactionType;
				remaining = element.Move(to, parent, remaining);
			}
			//
			from.MoveAllocations(_item, to, null);
			//
			if ("Y".Equals(to.PrimaryPick)) to.DecrementReplenishments(_item, null);
		}

		public virtual void MoveInventory(Location to, LicensePlate parent, string serialNumbers)
		{
            if (parent != null)
            {
                if (!parent.Id.HasValue)
                {
                    parent.CompanyLocationType = Registry.Find<CompanyLocationType>();
                    parent.LicenseType = Entity.Retrieve<LicenseType>(InventoryLicenseTypes.Pallet);
                    Repositories.Get<LicensePlate>().Add(parent);
                    //
                    parent.ChangeStatus(Entity.Retrieve<StatusCode>(LicensePlateStatuses.Open));
                }
                parent.ChangeLocation(to);
            }
			//
			Location from = _location;
            if (!String.IsNullOrEmpty(serialNumbers))
            {
                String[] numbers = serialNumbers.Split('|');
                for (int i = 0; i < numbers.Length; i++)
                {
                    DetachedCriteria criteria = DetachedCriteria.For<InventoryItemDetail>()
                        .Add("InventoryItem.Item", _item)
                        .Add("SerialNumber", numbers[i])
                        .SetMaxResults(1);
                    InventoryItemDetail detail = Repositories.Get<InventoryItemDetail>().Retrieve(criteria);
                    if (detail != null)
                    {
                        detail.Move(to, parent);
                        detail.InventoryItem.WriteStockMovement(this.TransactionType, to, parent, 1, numbers[i]);
                    }
                    else
                    {
                        criteria = DetachedCriteria.For<InventoryItem>()
                            .Add("Item", _item)
                            .Add("SerialNumber", numbers[i])
                            .SetMaxResults(1);
                        InventoryItem item = Repositories.Get<InventoryItem>().Retrieve(criteria);
                        if (item != null)
                        {
                            item.TransactionType = this.TransactionType;
                            item.Move(to, parent, 1);
                        }
                    }
                }
            }
            else if (!String.IsNullOrEmpty(_serialNumber))
            {
                DetachedCriteria criteria = DetachedCriteria.For<InventoryItemDetail>()
                        .Add("InventoryItem.Item", _item)
                        .Add("SerialNumber",_serialNumber)
                        .SetMaxResults(1);
                InventoryItemDetail detail = Repositories.Get<InventoryItemDetail>().Retrieve(criteria);
                if (detail != null)
                {
                    detail.Move(to, parent);
                    detail.InventoryItem.WriteStockMovement(this.TransactionType, to, parent, 1, _serialNumber);
                }
                else
                {
                    criteria = DetachedCriteria.For<InventoryItem>()
                        .Add("Item", _item)
                        .Add("SerialNumber", _serialNumber)
                        .SetMaxResults(1);
                    InventoryItem item = Repositories.Get<InventoryItem>().Retrieve(criteria);
                    if (item != null)
                    {
                        item.TransactionType = this.TransactionType;
                        item.Move(to, parent, 1);
                    }
                }
            }
			//
			from.MoveAllocations(_item, to, null);
			//
			if ("Y".Equals(to.PrimaryPick)) to.DecrementReplenishments(_item, null);
		}
      
		public virtual Decimal Pick(Decimal quantity)
		{
			if (quantity < _quantity) this.Split(_quantity - quantity);
			quantity -= _quantity;
			//
			this.ChangeQuantity(0M);
			//
			return quantity;
		}

		public virtual void Split(Decimal remaining)
		{
			InventoryItem entity = InventoryItem.Create(_item, _location, _statusCode, remaining);
			entity.LicensePlate.ChangeParent(_licensePlate.ParentLicensePlate);
			entity.LotExpiration = _lotExpiration;
			entity.LotNumber = _lotNumber;
			entity.ManufactureDate = _manufactureDate; //hack
			entity.ReceiptDetail = _receiptDetail;
			entity.Received = _received;
			entity.UnitOfMeasure = _unitOfMeasure; // <-- Just in case.
			Repositories.Get<InventoryItem>().Add(entity);
		}

		#endregion

		#region Methods.Public.Adjust

		public virtual void AdjustLotNumber(string lotNumber, DateTime? lotExpiration)
		{
			if (_lotNumber == lotNumber && _lotExpiration == lotExpiration) return;
			//
			DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
				.Add("Item", _item)
				.Add("Location", _location)
				.Add("LotNumber", _lotNumber)
				.Add("Quantity", ">", 0M)
				.Add("StatusCode", _statusCode);
			if (!String.IsNullOrEmpty(this.ParentLicensePlateCode))
				criteria = criteria.Add("LicensePlate.ParentLicensePlate.LicensePlateCode", this.ParentLicensePlateCode);
			IList<InventoryItem> inventory = Repositories.Get<InventoryItem>().List(criteria);
			foreach (InventoryItem element in inventory)
			{
				element.ChangeLotNumber(lotNumber, lotExpiration);
				element.Memo = this.Memo;
				element.WriteStockAdjustment(null, this.AdjustmentCode, element.Quantity, element.StatusCode);
			}
		}

		public virtual void AdjustPoolItem(PoolItem pool)
		{
			if (_poolItem == null && pool == null) return;
			if (_poolItem != null && _poolItem.SameAs(pool)) return;
			if (pool != null && pool.SameAs(_poolItem)) return;
			//
			DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
				.Add("Item", _item)
				.Add("Location", _location)
				.Add("Quantity", ">", 0M)
				.Add("StatusCode", _statusCode);
			if (!String.IsNullOrEmpty(_lotNumber)) criteria = criteria.Add("LotNumber", _lotNumber);
			if (!String.IsNullOrEmpty(this.ParentLicensePlateCode))
				criteria = criteria.Add("LicensePlate.ParentLicensePlate.LicensePlateCode", this.ParentLicensePlateCode);
			if (_poolItem != null) criteria = criteria.Add("PoolItem", _poolItem);
			IList<InventoryItem> inventory = Repositories.Get<InventoryItem>().List(criteria);
			foreach (InventoryItem element in inventory)
			{
				element.ChangePoolItem(pool);
				element.Memo = this.Memo;
				element.WriteStockAdjustment(null, this.AdjustmentCode, element.Quantity, element.StatusCode);
			}
		}

		public virtual void AdjustQuantity(decimal quantity)
        {
            DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
                .Add("Item", _item)
                .Add("Location", _location)
                .Add("StatusCode", _statusCode)
                .AddOrder("Received");
            if (!String.IsNullOrEmpty(_lotNumber)) criteria = criteria.Add("LotNumber", _lotNumber);
            if (!String.IsNullOrEmpty(this.ParentLicensePlateCode))
                criteria = criteria.Add("LicensePlate.ParentLicensePlate.LicensePlateCode", this.ParentLicensePlateCode);
            IList<InventoryItem> inventory = Repositories.Get<InventoryItem>().List(criteria);

            Decimal delta = inventory.Sum(e => e.Quantity) - quantity;
            InventoryItem negative = inventory.Where(e => e.Quantity < 0).FirstOrDefault();
            //
            if (negative != null)
            {
                Decimal from = negative.Quantity;
                StatusCode fromStatus = negative.StatusCode;
                //
                negative.ChangeQuantity(negative.Quantity - delta);
                negative.WriteStockAdjustment(null, this.AdjustmentCode, from, fromStatus);
            }
            else if (delta < 0)
            {
                InventoryItem positive = inventory.Where(e => e.Quantity > 0).FirstOrDefault();
                if (positive != null)
                {
                    Decimal from = positive.Quantity;
                    StatusCode fromStatus = positive.StatusCode;
                    //
                    positive.CreateQuantityShadow(positive.Quantity - delta);
                    positive.ChangeQuantity(positive.Quantity - delta);
                    positive.Memo = this.Memo;
                    positive.WriteStockAdjustment(null, this.AdjustmentCode, from, fromStatus);
                }
            }
            else
                for (int i = 0; i < inventory.Count; i++)
                {
                    if (delta <= 0) break;
                    //
                    Decimal from = inventory[i].Quantity;
                    StatusCode fromStatus = inventory[i].StatusCode;
                    inventory[i].Memo = this.Memo;
                    //
                    if (delta >= inventory[i].Quantity)
                    {
                        if (i == inventory.Count - 1)
                        {
                            inventory[i].CreateQuantityShadow(inventory[i].Quantity - delta);
                            inventory[i].ChangeQuantity(inventory[i].Quantity - delta);
                            inventory[i].WriteStockAdjustment(null, this.AdjustmentCode, from, fromStatus);
                            //
                            break;
                        }
                        else
                        {
                            delta -= inventory[i].Quantity;
                            //
                            inventory[i].CreateQuantityShadow(0);
                            inventory[i].ChangeQuantity(0);
                            inventory[i].WriteStockAdjustment(null, this.AdjustmentCode, from, fromStatus);
                        }
                    }
                    else
                    {
                        inventory[i].CreateQuantityShadow(inventory[i].Quantity - delta);
                        inventory[i].ChangeQuantity(inventory[i].Quantity - delta);
                        inventory[i].WriteStockAdjustment(null, this.AdjustmentCode, from, fromStatus);
                        //
                        break;
                    }
                }
        }

		/*public virtual void AdjustQuantity(decimal quantity, string serialNumbers)
		{
			if (_quantity == quantity) return;
			Decimal adjust = quantity - _quantity;
			//
			InventoryItem master = null;
			StatusCode done = null;
			if (adjust < 0) done = Entity.Retrieve<StatusCode>(InventoryStatuses.Unavailable);
			else
			{
				DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
					.Add("Item", _item)
					.Add("Location", _location)
					.Add("Quantity", ">", 0M)
					.Add("StatusCode", _statusCode)
					.SetMaxResults(1);
				if (!String.IsNullOrEmpty(_lotNumber)) criteria = criteria.Add("LotNumber", _lotNumber);
				if (!String.IsNullOrEmpty(this.ParentLicensePlateCode))
					criteria = criteria.Add("LicensePlate.ParentLicensePlate.LicensePlateCode", this.ParentLicensePlateCode);
				master = Repositories.Get<InventoryItem>().Retrieve(criteria);
			}
			//
			String[] numbers = serialNumbers.Split('|');
			for (int i = 0; i < numbers.Length; i++)
			{
				if (adjust > 0 && master != null)
				{
					InventoryItemDetail detail = Entity.Activate<InventoryItemDetail>();
					detail.InventoryItem = master;
					detail.LotNumber = _lotNumber;
					detail.Quantity = 1;
					detail.SerialNumber = numbers[i];
					detail.StatusCode = _statusCode;
					//
					Repositories.Get<InventoryItemDetail>().Add(detail);
					master.ChangeQuantity(master.Quantity + 1);
					//
					master.AdjustmentCode = this.AdjustmentCode;
					master.WriteStockAdjustment(numbers[i]);
				}
				else
				{
					DetachedCriteria criteria = DetachedCriteria.For<InventoryItemDetail>()
						.Add("InventoryItem.Item", _item)
						.Add("SerialNumber", numbers[i])
						.SetMaxResults(1);
					InventoryItemDetail detail = Repositories.Get<InventoryItemDetail>().Retrieve(criteria);
					if (detail != null)
					{
						detail.ChangeStatus(done);
						detail.InventoryItem.ChangeQuantity(detail.InventoryItem.Quantity - 1);
						//
						detail.InventoryItem.AdjustmentCode = this.AdjustmentCode;
						detail.InventoryItem.WriteStockAdjustment(numbers[i]);
					}
				}
			}
		}*/

		public virtual void AdjustStatus(StatusCode status)
		{
			if (_statusCode.SameAs(status)) return;
			//
			DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
				.Add("Item", _item)
				.Add("Location", _location)
				.Add("Quantity", ">", 0M)
				.Add("StatusCode", _statusCode);
			if (!String.IsNullOrEmpty(_lotNumber)) criteria = criteria.Add("LotNumber", _lotNumber);
			if (!String.IsNullOrEmpty(this.ParentLicensePlateCode))
				criteria = criteria.Add("LicensePlate.ParentLicensePlate.LicensePlateCode", this.ParentLicensePlateCode);
			IList<InventoryItem> inventory = Repositories.Get<InventoryItem>().List(criteria);
			foreach (InventoryItem element in inventory)
			{
				StatusCode from = element.StatusCode;
				//
				element.CreateStatusShadow(status);
				element.ChangeStatus(status);
				element.Memo = this.Memo;
				element.WriteStockAdjustment(null, this.AdjustmentCode, element.Quantity, from);
			}
		}

		public virtual void CreateInventoryItemDetails(string serialNumbers)
		{
			String[] numbers = serialNumbers.Split('|');
			for (int i = 0; i < numbers.Length; i++)
			{
				InventoryItemDetail detail = Entity.Activate<InventoryItemDetail>();
				detail.InventoryItem = this;
				detail.LotNumber = _lotNumber;
				detail.Quantity = 1;
				detail.SerialNumber = numbers[i];
				detail.StatusCode = _statusCode;
				Repositories.Get<InventoryItemDetail>().Add(detail);
				//
				Decimal from = _quantity;
				this.ChangeQuantity(_quantity + 1);
				this.WriteStockAdjustment(numbers[i], this.AdjustmentCode, from, _statusCode);
			}
		}

        public virtual void AdjustRepackBoxQuantity(CartonSize cartonSzieTo)
        {
            if (cartonSzieTo == null) return;
            //
            if (cartonSzieTo.Item != null)
            {
                DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
                      .Add("CompanyLocationType", Registry.Find<CompanyLocationType>())
                      .Add("Item", cartonSzieTo.Item)
                      .Add("Quantity", ">", 0M)
                      .Add("StatusCode.Code", CodeValue.GetCode(InventoryStatuses.Available))
                      .AddOrder("Received")
                      .SetMaxResults(1);
                InventoryItem item = Repositories.Get<InventoryItem>().Retrieve(criteria);
                //
                if (item != null)
                {
                    Decimal from = item.Quantity;
                    Decimal to = item.Quantity - 1;
                    StatusCode fromStatus = item.StatusCode;
                    //
                    item.CreateQuantityShadow(to);
                    item.ChangeQuantity(to);
                    //
                    InventoryAdjustmentCode adjustment = Entity.Retrieve<InventoryAdjustmentCode>(InventoryAdjustmentCodes.RepackBox);
                    item.WriteStockAdjustment(null, adjustment, from, fromStatus);
                }
            } 
        }

		#endregion

		#region Methods.Public.Assets

		public virtual void AssignToKit(InventoryItem kit)
		{
			this.WriteKitAssignment();
			//
			if (!"Y".Equals(_item.AssetIndicator) || "Y".Equals(_item.ItemType.Consumable))
			{
				DetachedCriteria criteria = DetachedCriteria.For<KitHeader>()
					.Add("Active", "A")
					.Add("Item", kit.Item)
					.SetMaxResults(1);
				KitHeader header = Repositories.Get<KitHeader>().Retrieve(criteria);
				if (header != null)
				{
					InventoryItemDetail detail = InventoryItemDetail.Create(this, header);
					Repositories.Get<InventoryItemDetail>().Add(detail);
				}
				//
				_quantity -= this.MovementQuantity;
				if (_quantity <= 0M) _statusCode = Entity.Retrieve<StatusCode>(InventoryStatuses.Kit);
			}
			else
			{
				_parentInventoryItem = this.InventoryItemTo;
				_statusCode = Entity.Retrieve<StatusCode>(InventoryStatuses.Kit);
			}
		}

		public virtual void CalculateReturnDate()
		{
			DateTime now = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 23, 59, 59);
			//
			if ("Y".Equals(_item.ItemType.Expendable))
			{
				this.EditReturnDate = false;
				this.ReturnDate = null;
			}
			else if (_item.ItemGroup == null)
			{
				this.EditReturnDate = false;
				this.ReturnDate = now.AddDays(1);
			}
			else
			{
				Int32? days = _item.ItemGroup.AmReturnDays;
				Int32? minutes = _item.ItemGroup.AmReturnMinutes;
				Int32? years = _item.ItemGroup.AmReturnYears;
				//
				this.EditReturnDate = "Y".Equals(_item.ItemGroup.AmEditReturnDays);
				if (minutes != null)
				{
					now = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day,
						DateTime.Now.Hour, DateTime.Now.Minute, 59);
					this.ReturnDate = now.AddMinutes(minutes.Value);
				}
				else if (days != null) this.ReturnDate = now.AddDays(days.Value);
				else if (years != null) this.ReturnDate = now.AddYears(years.Value);
				else this.ReturnDate = now.AddDays(1);
			}
		}

		public virtual void CheckIn()
		{
			Boolean consumable = "Y".Equals(_item.ItemType.Consumable);
			this.WriteCheckIn();
			//
			if (consumable) _quantity += this.MovementQuantity;
			_parentInventoryItem = null;
			_statusCode = Entity.Retrieve<StatusCode>(InventoryStatuses.Available);
			//
			if (this.InventoryItemTo != null)
			{
				DetachedCriteria criteria = DetachedCriteria.For<InventoryItemDetail>()
					.Add("InventoryItem", this)
					.Add("ParentInventoryItem", this.InventoryItemTo)
					.SetMaxResults(1);
				IList<InventoryItemDetail> details = Repositories.Get<InventoryItemDetail>().List(criteria);
				if (details.Count == 1) Repositories.Get<InventoryItemDetail>().Remove(details[0]);
			}
			else
			{
				DetachedCriteria criteria = DetachedCriteria.For<ParticipantAsset>()
					.Add("InventoryItem", this)
					.Add("Quantity", "!=", 0)
					.Add("Quantity", "!=", null)
					.SetMaxResults(1);
				if (consumable) criteria = criteria.Add("OrganizationParticipant", this.Participant);
				IList<ParticipantAsset> issuances = Repositories.Get<ParticipantAsset>().List(criteria);
				if (issuances.Count == 1)
				{
					issuances[0].ActualReturn = DateTime.Now;
					issuances[0].DateModified = DateTime.Now;
					issuances[0].Quantity = 0;
					issuances[0].UserModified = Registry.Find<UserAccount>().UserName;
					Repositories.Get<ParticipantAsset>().Update(issuances[0]);
				}
			}
			//
			if (this.LocationTo != null && !this.LocationTo.SameAs(_location)) this.Move(this.LocationTo, null);
		}

		public virtual void CheckIssuance()
		{
			DetachedCriteria criteria = DetachedCriteria.For<InventoryItemDetail>()
				.Add("InventoryItem", this)
				.Add("KitHeader", null)
				.Add("ParentInventoryItem", "!=", null)
				.Add("Quantity", "!=", 0)
				.SetProjection(Projections.Count("Id"));
			this.ParentIssuance = (Repositories.Get<InventoryItemDetail>().Function<Int32>(criteria) > 0);
			//
			criteria = DetachedCriteria.For<ParticipantAsset>()
				.Add("InventoryItem", this)
				.Add("Quantity", "!=", 0)
				.SetProjection(Projections.Count("Id"));
			this.ParticipantIssuance = (Repositories.Get<InventoryItemDetail>().Function<Int32>(criteria) > 0);
		}

		public virtual void CheckIssued()
		{
			if ("N".Equals(_item.ItemType.Consumable)) this.Issued =
				(CodeValue.GetCode(InventoryStatuses.Issued).Equals(_statusCode.Code) &&
				CodeValue.GetCode(FunctionalAreas.Inventory).Equals(_statusCode.FunctionalAreaCode.Code));
			else
			{
				this.CheckIssuance();
				this.Issued = (this.ParentIssuance || this.ParticipantIssuance);
			}
		}

		public virtual void CheckOut()
		{
			Boolean consumable = "Y".Equals(_item.ItemType.Consumable);
			this.WriteCheckOut();
			//
			if (consumable)
			{
				_quantity -= this.MovementQuantity;
				if (_quantity <= 0M) _statusCode = Entity.Retrieve<StatusCode>(InventoryStatuses.Issued);
			}
			else _statusCode = Entity.Retrieve<StatusCode>(InventoryStatuses.Issued);
			//
			if (this.InventoryItemTo != null)
			{
				if (consumable)
				{
					InventoryItemDetail detail = InventoryItemDetail.Create(this);
					Repositories.Get<InventoryItemDetail>().Add(detail);
				}
				else _parentInventoryItem = this.InventoryItemTo;
			}
			else if (this.Participant != null)
			{
				ParticipantAsset issuance = ParticipantAsset.Create(this);
				Repositories.Get<ParticipantAsset>().Add(issuance);
			}
		}

		public virtual void FindIssuedQuantity(InventoryItem asset)
		{
			DetachedCriteria criteria = DetachedCriteria.For<InventoryItemDetail>()
				.Add("InventoryItem", asset)
				.Add("ParentInventoryItem", this);
			IList<InventoryItemDetail> details = Repositories.Get<InventoryItemDetail>().List(criteria);
			if (details.Count == 1) this.MovementQuantity = details[0].Quantity;
			else throw new Exception(String.Format("{0} is not checked out to {1}.", asset.AssetCode, _assetCode));
		}

		public virtual void FindParticipantAsset()
		{
			DetachedCriteria criteria = DetachedCriteria.For<ParticipantAsset>()
				.Add("InventoryItem", this)
				.Add("Quantity", "!=", 0)
				.Add("Quantity", "!=", null)
				.SetMaxResults(1);
			IList<ParticipantAsset> issuances = Repositories.Get<ParticipantAsset>().List(criteria);
			if (issuances.Count == 1)
			{
				OrganizationParticipant participant = issuances[0].OrganizationParticipant;
				if (participant != null)
				{
					Participant person = participant.ParticipantRole.Participant;
					//
					this.Participant = issuances[0].OrganizationParticipant;
					this.Participant.FullName = this.ParticipantName = String.Format("{0} {1}", person.FirstName, person.LastName);
					this.ReturnDate = issuances[0].ExpectedReturn;
				}
			}
		}

		public virtual void MassCheckIn()
		{
			this.CheckIn();
			if (String.IsNullOrEmpty(this.InventoryItems)) return;
			//
			String[] assets = this.InventoryItems.Split('|');
			foreach (String element in assets)
			{
				String[] parts = element.Split(';');
				Decimal issued = Converter.ToDecimal(parts[1]);
				Int32 employee = Converter.ToInt32(parts[3]);
				Int32 id = Converter.ToInt32(parts[0]);
				Int32 parent = Converter.ToInt32(parts[2]);
				//
				InventoryItem asset = Repositories.Get<InventoryItem>().Retrieve(id);
				if (parent != 0) asset.InventoryItemTo = Repositories.Get<InventoryItem>().Retrieve(parent);
				asset.LocationTo = this.LocationTo;
				asset.MovementQuantity = issued;
				if (employee != 0) asset.Participant = Repositories.Get<OrganizationParticipant>().Retrieve(employee);
				//
				asset.CheckIn();
				//
				asset.DateModified = DateTime.Now;
				asset.UserModified = Registry.Find<UserAccount>().UserName;
				Repositories.Get<InventoryItem>().Update(asset);
			}
		}

		public virtual void MassCheckOut()
		{
			this.CheckOut();
			if (String.IsNullOrEmpty(this.InventoryItems)) return;
			//
			String[] assets = this.InventoryItems.Split('|');
			foreach (String element in assets)
			{
				String[] parts = element.Split(';');
				DateTime due = Converter.ToDateTime(parts[2]);
				Decimal issued = Converter.ToDecimal(parts[1]);
				Int32 id = Converter.ToInt32(parts[0]);
				//
				InventoryItem asset = Repositories.Get<InventoryItem>().Retrieve(id);
				asset.InventoryItemTo = this.InventoryItemTo;
				asset.MovementQuantity = issued;
				asset.Participant = this.Participant;
				asset.ReturnDate = due;
				//
				asset.CheckOut();
				//
				asset.DateModified = DateTime.Now;
				asset.UserModified = Registry.Find<UserAccount>().UserName;
				Repositories.Get<InventoryItem>().Update(asset);
			}
		}

		public virtual void MoveAsset(Location to)
		{
			this.WriteStockMovement(this.TransactionType, to, null, _quantity, null);
			_location = to;
		}

		public virtual void Validate()
		{
			Agency agency = Registry.Find<Agency>();
			if (agency == null)
			{
				agency = Registry.Find<AgencyOrganizationalUnit>().Agency;
				if (agency == null) agency = Registry.Find<AgencyOrganizationalUnit>().AgencyLocation.Agency;
			}
			//
			DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
				.AddOr("AgencyOrganizationalUnit.Agency",
					agency, "AgencyOrganizationalUnit.AgencyLocation.Agency", agency)
				.Add("AssetCode", _assetCode)
				.Add("Item.AssetIndicator", "Y")
				.SetProjection(Projections.Count("Id"));
			if (_id != null) criteria = criteria.Add("Id", "!=", _id);
			Int32 count = Repositories.Get<InventoryItem>().Function<Int32>(criteria);
			if (count > 0)
			{
				String error = String.Format("Asset {0} already exists in {1}.", _assetCode, agency.Name);
				throw new Exception(error);
			}
		}

		#endregion

		#region Methods.Public.Assets.CycleCount

		public virtual void ClearCycleCount()
		{
			_cycleCount = "N";
			_dateModified = DateTime.Now;
			_lastCycleCount = DateTime.Now;
			_userModified = Registry.Find<UserAccount>().UserName;
			Repositories.Get<InventoryItem>().Update(this);
		}

		public virtual void CreateCycleCount()
		{
			TransactionType cycle = Entity.Retrieve<TransactionType>(InventoryTransactions.CycleCountParent);
			this.Task = Task.Create(cycle);
			this.Task.Started = DateTime.Now;
			this.Task.StatusCode = Entity.Retrieve<StatusCode>(TaskStatuses.Active);
			Repositories.Get<Task>().Add(this.Task);
			//
			InventoryTask main = InventoryTask.Create(this.Task);
			main.InventoryItem = this;
			main.Quantity = 0;
			Repositories.Get<InventoryTask>().Add(main);
			//
			this.SetCycleCount(this.Task);
			//
			DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
				.Add("ParentInventoryItem", this)
				.Add("Quantity", ">", 0M)
				.Add("StatusCode", Entity.Retrieve<StatusCode>(InventoryStatuses.Issued));
			IList<InventoryItem> assets = Repositories.Get<InventoryItem>().List(criteria);
			foreach (InventoryItem element in assets) element.SetCycleCount(this.Task);
			//
			criteria = DetachedCriteria.For<InventoryItemDetail>()
				.Add("KitHeader", null)
				.Add("ParentInventoryItem", this);
			IList<InventoryItemDetail> details = Repositories.Get<InventoryItemDetail>().List(criteria);
			foreach (InventoryItemDetail element in details) element.SetCycleCount(this.Task);
		}

		public virtual void ProcessFoundItem()
		{
			_statusCode = Entity.Retrieve<StatusCode>(InventoryStatuses.QaHold);
			this.WriteFoundItem(this.Task, Converter.ToInt32(this.MovementQuantity));
			this.ClearCycleCount();
		}

		public virtual void SetCycleCount(Task task)
		{
			_cycleCount = "Y";
			_dateModified = DateTime.Now;
			_lastCycleCount = null;
			_userModified = Registry.Find<UserAccount>().UserName;
			Repositories.Get<InventoryItem>().Update(this);
			//
			InventoryTask count = InventoryTask.Create(task);
			count.InventoryItem = this;
			count.Item = _item;
			count.LotNumber = _lotNumber;
			count.Quantity = Converter.ToInt32(_quantity);
			Repositories.Get<InventoryTask>().Add(count);
		}

		#endregion

        #region Methods.Public.WM.CycleCount

        public virtual void AdjustCycleCountQuantity(InventoryAdjustmentCode adjustmentCode, decimal quantity)
        {
            DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
                .Add("Item", _item)
                .Add("Location", _location)
                .AddOrder("Received");
            if (!String.IsNullOrEmpty(_lotNumber)) criteria = criteria.Add("LotNumber", _lotNumber);
            if (_licensePlate.ParentLicensePlate != null )
                criteria = criteria.Add("LicensePlate.ParentLicensePlate", _licensePlate.ParentLicensePlate);
            IList<InventoryItem> inventory = Repositories.Get<InventoryItem>().List(criteria);

            Decimal delta = inventory.Sum(e => e.Quantity) - quantity;
            InventoryItem negative = inventory.Where(e => e.Quantity < 0).FirstOrDefault();
            //
            if (negative != null)
            {
                Decimal from = negative.Quantity;
                StatusCode fromStatus = negative.StatusCode;
                //
                negative.ChangeQuantity(negative.Quantity - delta);
                negative.WriteStockAdjustment(null, this.AdjustmentCode, from, fromStatus);
            }
            else if (delta < 0)
            {
                InventoryItem positive = inventory.Where(e => e.Quantity > 0).FirstOrDefault();
                if (positive != null)
                {
                    Decimal from = positive.Quantity;
                    StatusCode fromStatus = positive.StatusCode;
                    //
                    positive.CreateQuantityShadow(positive.Quantity - delta);
                    positive.ChangeQuantity(positive.Quantity - delta);
                    positive.Memo = this.Memo;
                    positive.WriteStockAdjustment(null, adjustmentCode, from, fromStatus);
                }
                else
                {
                    InventoryItem zero = inventory.Where(e => e.Quantity == 0).FirstOrDefault();
                    //
                    if (zero != null)
                    {
                        Decimal from = zero.Quantity;
                        StatusCode fromStatus = zero.StatusCode;
                        //
                        zero.CreateQuantityShadow(zero.Quantity - delta);
                        zero.ChangeQuantity(zero.Quantity - delta);
                        zero.Memo = this.Memo;
                        zero.WriteStockAdjustment(null, adjustmentCode, from, fromStatus);
                    }
                }
            }
            else
                for (int i = 0; i < inventory.Count; i++)
                {
                    if (delta <= 0) break;
                    //
                    Decimal from = inventory[i].Quantity;
                    StatusCode fromStatus = inventory[i].StatusCode;
                    inventory[i].Memo = this.Memo;
                    //
                    if (delta >= inventory[i].Quantity)
                    {
                        if (i == inventory.Count - 1)
                        {
                            inventory[i].CreateQuantityShadow(inventory[i].Quantity - delta);
                            inventory[i].ChangeQuantity(inventory[i].Quantity - delta);
                            inventory[i].WriteStockAdjustment(null, adjustmentCode, from, fromStatus);
                            //
                            break;
                        }
                        else
                        {
                            delta -= inventory[i].Quantity;
                            //
                            inventory[i].CreateQuantityShadow(0);
                            inventory[i].ChangeQuantity(0);
                            inventory[i].WriteStockAdjustment(null, adjustmentCode, from, fromStatus);
                        }
                    }
                    else
                    {
                        inventory[i].CreateQuantityShadow(inventory[i].Quantity - delta);
                        inventory[i].ChangeQuantity(inventory[i].Quantity - delta);
                        inventory[i].WriteStockAdjustment(null, adjustmentCode, from, fromStatus);
                        //
                        break;
                    }
                }
        }

        #endregion

        #region Methods.Public.PhysicalInventorty

        public virtual void CreateInventoryItemDetails(InventoryTask task)
        {
            _location.PrimaryPick = this.PrimaryPick ? "Y" : "N";
            // 
            if (!string.IsNullOrEmpty(this.SerialNumbers))
            {
                String[] numbers = this.SerialNumbers.Split('|');
                for (int i = 0; i < numbers.Length; i++)
                {
                    InventoryItemDetail detail = Entity.Activate<InventoryItemDetail>();
                    detail.InventoryItem = this;
                    detail.LotNumber = _lotNumber;
                    detail.Quantity = 1;
                    detail.SerialNumber = numbers[i];
                    detail.StatusCode = _statusCode;
                    Repositories.Get<InventoryItemDetail>().Add(detail);
                    //
                    Decimal from = _quantity;
                    this.ChangeQuantity(_quantity + 1);
                    this.WritePhyscialInventory(from, this.Quantity);
                }
            }
            else this.WritePhyscialInventory(this.Quantity, this.Quantity, task);

            if (this.Location != null)
            {
                Location current = Repositories.Get<Location>().Retrieve(this.Location.Id);
                current.SetCycleflag();
                current.SetPrimarypick(this.PrimaryPick);
            }
        }

        public virtual void PerformPhysicalCount(ref InventoryTask task)
        {
            CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
            StatusCode complete = Entity.Retrieve<StatusCode>(TaskStatuses.Complete);
            TransactionType physical = Entity.Retrieve<TransactionType>(InventoryTransactions.Physical);

            ProjectionList projections = Projections.ProjectionList()
                   .Add(Projections.Property("Task"), "Task");

            DetachedCriteria criteria = DetachedCriteria.For<InventoryTask>()
                                        .Add("CompanyLocationType", warehouse)                                         
                                        .Add("Task.StatusCode", "!=", complete)
                                        .Add("Task.TransactionType", physical)
                                        .SetProjection(projections)
                                        .SetResultTransformer(Transformers.AliasToBean<InventoryTask>())
                                        .SetMaxResults(1);
            this.Task = Repositories.Get<InventoryTask>().Retrieve(criteria).Task;
            //
            InventoryTask main = InventoryTask.Create(this.Task);
            main.InventoryItem = this;
            main.Quantity = 0;
            Repositories.Get<InventoryTask>().Add(main);
            task = main;            
        }

        public virtual void PhysicalCount(bool showQuantity)
        {
            CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
            //
            DetachedCriteria criteria = DetachedCriteria.For<Item>()
                .Add("CompanyLocationType", warehouse)
                .Add("ItemCode", this.ItemCode)
                .Add("Active", "A")
                .SetMaxResults(1);
            _item = Repositories.Get<Item>().Retrieve(criteria);
            //
            if (String.IsNullOrEmpty(this.ParentLicensePlateCode)) _licensePlate = null;
            else
            {
                criteria = DetachedCriteria.For<LicensePlate>()
                    .Add("CompanyLocationType", warehouse)
                    .Add("LicensePlateCode", this.ParentLicensePlateCode)
                    .SetMaxResults(1);
                _licensePlate = Repositories.Get<LicensePlate>().Retrieve(criteria);
            }
            //
            criteria = DetachedCriteria.For<Location>()
                .Add("CompanyLocationType", warehouse)
                .Add("LocationCode", this.LocationCode)
                .Add("Active", "A")
                .SetMaxResults(1);
            _location = Repositories.Get<Location>().Retrieve(criteria);
            //
            criteria = DetachedCriteria.For<InventoryItem>()
                .CreateAlias("StatusCode", "StatusCode")
                .Add("Item", _item)
                .Add("Location", _location)
                .Add("StatusCode", this.StatusCode)
                .Add(Expression.Not(Expression.Eq("StatusCode.Code", CodeValue.GetCode(InventoryStatuses.Unavailable))))
                .AddOrder("Received");
            if (_licensePlate != null) criteria = criteria.Add("LicensePlate.ParentLicensePlate", _licensePlate);
            if (!string.IsNullOrEmpty(this.LotNumber)) criteria = criteria.Add("LotNumber", this.LotNumber);
            if (!string.IsNullOrEmpty(this.SerialNumber)) criteria = criteria.Add("SerialNumber", this.SerialNumber);
            if (_unitOfMeasure!=null) criteria = criteria.Add("UnitOfMeasure", this.UnitOfMeasure);

            if (!String.IsNullOrEmpty(this.SerialNumbers))
            {
                String[] numbers = this.SerialNumbers.Split('|');
                InventoryItem item = null;
                Decimal adjust = this.AdjustedQuantity - _quantity;
                for (int i = 0; i < numbers.Length; i++)
                {
                    criteria = DetachedCriteria.For<InventoryItemDetail>()
                       .Add("InventoryItem.Item", _item)
                       .Add("InventoryItem.Location", _location)
                       .Add("SerialNumber", numbers[i])
                       .SetMaxResults(1);
                    if (_licensePlate != null) criteria = criteria.Add("LicensePlate.ParentLicensePlate", _licensePlate);
                    InventoryItemDetail detail = Repositories.Get<InventoryItemDetail>().Retrieve(criteria);
                    //
                    if (detail != null) item = detail.InventoryItem;
                    else
                    {
                        criteria = DetachedCriteria.For<InventoryItem>()
                            .Add("Item", _item)
                            .Add("Location", _location)
                            .Add("SerialNumber", numbers[i])
                            .AddOrder("Received")
                            .SetMaxResults(1);
                        if (_licensePlate != null) criteria = criteria.Add("LicensePlate.ParentLicensePlate", _licensePlate);
                        item = Repositories.Get<InventoryItem>().Retrieve(criteria);
                    }
                    if (item != null) adjust = WritePhyscialCount(showQuantity, adjust, item);
                }
            }
            else
            {
                Decimal adjust = this.AdjustedQuantity - _quantity;
                IList<InventoryItem> inventory = Repositories.Get<InventoryItem>().List(criteria);
                foreach (InventoryItem element in inventory)
                {
                    adjust = WritePhyscialCount(showQuantity, adjust, element);
                }
            }
        }

        public virtual void WritePhyscialInventory(Decimal? fromquantity, Decimal? toquantity, InventoryTask task = null)
        {

            TransactionType type = Entity.Retrieve<TransactionType>(InventoryTransactions.PhysicalCount);
            if (task == null) task = this.FindInvenotryTask();
            //
            ItemTransaction transaction = Entity.Activate<ItemTransaction>();
            //
            transaction.InventoryItem = this;
            transaction.Item = _item;
            transaction.LocationFrom = _location;
            transaction.LotNumber = _lotNumber;
            transaction.Occurred = DateTime.Now;
            transaction.ParticipantBy = Registry.Find<UserAccount>().OrganizationParticipant;
            transaction.Quantity = Converter.ToInt32(toquantity);
            transaction.QuantityFrom = Converter.ToInt32(fromquantity);
            transaction.StatusCode = _statusCode;
            transaction.StatusCodeFrom = _statusCode;
            transaction.SerialNumber = _serialNumber;
            transaction.TransactionType = type;
            transaction.IntegrationStatusCode = Entity.Retrieve<StatusCode>(IntegrationStatuses.Open);
            if (task != null) transaction.Task = task.Task;
            //           
            Repositories.Get<ItemTransaction>().Add(transaction);
        }

        #endregion

        #region Methods.Public.Putaway

        public virtual void Putaway(Location to, LicensePlate parent, Decimal quantity)
		{
			DetachedCriteria criteria = null;
			if (parent != null && parent.SameAs(this.ParentLicensePlate))
			{
				ProjectionList projections = Projections.ProjectionList()
					.Add(Projections.GroupProperty("i.ItemCode"), "ItemCode")
					.Add(Projections.Sum("Quantity"), "Quantity");
				criteria = DetachedCriteria.For<InventoryItem>()
					.CreateAlias("Item", "i")
					.CreateAlias("LicensePlate", "lp")
					.Add(Expression.Eq("lp.ParentLicensePlate", parent))
					.Add(Expression.Gt("Quantity", 0M))
					.SetProjection(projections)
					.SetResultTransformer(Transformers.AliasToBean<InventoryItem>());
				IList<InventoryItem> items = Repositories.Get<InventoryItem>().List(criteria);
				if (items.Count == 1 && items[0].Quantity == quantity) parent.ChangeLocation(to);
				else
				{
					String error = String.Format("Unable to move {0} to {1}. LPN contains additional inventory.", 
						parent.LicensePlateCode, to.LocationCode);
					throw new Exception(error);
				}
			}
			//
			criteria = DetachedCriteria.For<InventoryItem>()
				.Add(Expression.Eq("Item", _item))
				.AddOrder(Order.Asc("Received"));
			if (this.ParentLicensePlate != null) criteria = criteria
				.CreateAlias("LicensePlate", "lp")
				.Add(Expression.Eq("lp.ParentLicensePlate", this.ParentLicensePlate));
			else criteria = criteria
				.Add(Expression.Eq("Location", _location));
			IList<InventoryItem> inventory = Repositories.Get<InventoryItem>().List(criteria);
			foreach (InventoryItem element in inventory)
			{
				if (quantity <= 0) break;
				element.TransactionType = this.TransactionType;
				quantity = element.Move(to, parent, quantity);
			}
		}

		#endregion

		#region Methods.Public.Receive

		public virtual void WriteUdfValues(IList<UdfMetadataValue> metadata, ICollection<UdfItemValue> values)
		{
			if (metadata == null || metadata.Count == 0) return;
			if (values == null || values.Count == 0) return;
			//
			foreach (UdfItemValue element in values)
			{
				UdfMetadataValue match = metadata.Where(m => m.Label.Equals(element.Label)).FirstOrDefault();
				if (match == null) continue;
				//
				UdfItemValue value = values.Where(v => v.Label.Equals(element.Label)).FirstOrDefault();
				if (value == null) continue;
				//
				UdfInventoryValue created = Entity.Activate<UdfInventoryValue>();
				created.InventoryItem = this;
				created.UdfMetadataValue = match;
				created.UserDefinedValue = value.UserDefinedValue;
				Repositories.Get<UdfInventoryValue>().Add(created);
			}
		}

		#endregion

		#region Methods.Public.Shadows

		public virtual void CreateAssetShadows()
		{
			Dictionary<String, String> properties = new Dictionary<String, String>()
			{
				{ "AgencyOrganizationalUnit", "AgencyOrganizationalUnit.OrganizationCode" },
				{ "AssetCode", "AssetCode" },
				{ "DepreciationStartDate", "DepreciationStartDate" },
				{ "HomeLocationCode", "HomeLocation.LocationCode" },
				{ "ItemCode", "Item.ItemCode" },
				{ "LocationCode", "Location.LocationCode" },
				{ "Quantity", "Quantity" },
				{ "SalvageValue", "SalvageValue" },
				{ "SerialNumber", "SerialNumber" },
				{ "StatusCode", "StatusCode.Code" },
				{ "UnitCost", "UnitCost" },
				{ "UsefulLife", "UsefulLife" },
			};
			//
			InventoryItem current = Repositories.Get<InventoryItem>().Retrieve(_id);
			foreach (String key in properties.Keys)
			{
				String cvalue = Converter.ToString(current.GetProperty(properties[key]));
				if (String.IsNullOrEmpty(cvalue)) cvalue = "null";
				String evalue = Converter.ToString(this.GetProperty(properties[key]));
				if (String.IsNullOrEmpty(evalue)) evalue = "null";
				//
				if (cvalue.Equals(evalue)) continue;
				//
				InventoryShadow shadow = Entity.Activate<InventoryShadow>();
				shadow.ColumnName = key;
				shadow.InventoryItem = current;
				shadow.NewValue = evalue;
				shadow.Occurred = DateTime.Now;
				shadow.OldValue = cvalue;
				shadow.TransactionType = "Update";
				Repositories.Get<InventoryShadow>().Add(shadow);
			}
			//
			Repositories.Get<InventoryItem>().Evict(current);
			//
			properties.Clear();
		}

		public virtual void CreateQuantityShadow(Decimal quantity)
		{
			InventoryShadow shadow = Entity.Activate<InventoryShadow>();
			shadow.ColumnName = "Quantity";
			shadow.InventoryItem = this;
			if (quantity == -1) shadow.NewValue = Converter.ToString(_quantity);
			else shadow.NewValue = Converter.ToString(quantity);
			shadow.Occurred = DateTime.Now;
			if (quantity == -1) shadow.OldValue = null;
			else shadow.OldValue = Converter.ToString(_quantity);
			shadow.TransactionType = CodeValue.GetCode(StockAdjustmentTransactions.StockAdjustment);
			Repositories.Get<InventoryShadow>().Add(shadow);
		}

		public virtual void CreateStatusShadow(StatusCode status)
		{
			InventoryShadow shadow = Entity.Activate<InventoryShadow>();
			shadow.ColumnName = "StatusCode";
			shadow.InventoryItem = this;
			if (status == null) shadow.NewValue = Converter.ToString(_statusCode.Id);
			else shadow.NewValue = Converter.ToString(status.Id);
			shadow.Occurred = DateTime.Now;
			if (status == null) shadow.OldValue = null;
			else shadow.OldValue = Converter.ToString(_statusCode.Id);
			shadow.TransactionType = CodeValue.GetCode(StockAdjustmentTransactions.StockAdjustment);
			Repositories.Get<InventoryShadow>().Add(shadow);
		}

		#endregion

		#region Methods.Public.Transactions

		public virtual void WriteCycleCount(Task task, int quantity)
		{
			TransactionType type = Entity.Retrieve<TransactionType>(InventoryTransactions.CycleCount);
			ItemTransaction transaction = Entity.Activate<ItemTransaction>();
			transaction.InventoryItem = this;
			transaction.Item = _item;
			transaction.LocationFrom = _location;
			transaction.LotNumber = _lotNumber;
			transaction.Occurred = DateTime.Now;
			transaction.ParticipantBy = Registry.Find<UserAccount>().OrganizationParticipant;
			transaction.Quantity = quantity;
			transaction.SerialNumber = _serialNumber;
			transaction.StatusCode = _statusCode;
			transaction.TransactionType = type;
			transaction.Task = task;
			//
			Repositories.Get<ItemTransaction>().Add(transaction);
		}

		public virtual void WriteFoundItem(Task task, Decimal quantity)
		{
			TransactionType type = Entity.Retrieve<TransactionType>(InventoryTransactions.FoundItem);
			ItemTransaction transaction = Entity.Activate<ItemTransaction>();
			transaction.InventoryItem = this;
			transaction.Item = _item;
			transaction.LocationFrom = _location;
			transaction.LotNumber = _lotNumber;
			transaction.Occurred = DateTime.Now;
			transaction.ParticipantBy = Registry.Find<UserAccount>().OrganizationParticipant;
			transaction.Quantity = quantity;
			transaction.SerialNumber = _serialNumber;
			transaction.StatusCode = _statusCode;
			transaction.Task = task;
			transaction.TransactionType = type;
			//
			Repositories.Get<ItemTransaction>().Add(transaction);
		}

		public virtual void WriteMissingItem(Task task, Decimal quantity)
		{
			TransactionType type = Entity.Retrieve<TransactionType>(InventoryTransactions.MissingItem);
			ItemTransaction transaction = Entity.Activate<ItemTransaction>();
			transaction.InventoryItem = this;
			transaction.Item = _item;
			transaction.LocationFrom = _location;
			transaction.LotNumber = _lotNumber;
			transaction.Occurred = DateTime.Now;
			transaction.ParticipantBy = Registry.Find<UserAccount>().OrganizationParticipant;
			transaction.Quantity = quantity;
			transaction.SerialNumber = _serialNumber;
			transaction.StatusCode = _statusCode;
			transaction.Task = task;
			transaction.TransactionType = type;
			//
			Repositories.Get<ItemTransaction>().Add(transaction);
		}

		public virtual void WriteReceived(ReceiptDetail detail)
		{
			TransactionType type = Entity.Retrieve<TransactionType>(OrderTransactions.Received);
			//
			ItemTransaction transaction = Entity.Activate<ItemTransaction>();
			transaction.InventoryItem = this;
			transaction.Item = _item;
			transaction.LicensePlateTo = detail.LicensePlate;
			transaction.LocationTo = _location;
			transaction.LotNumber = _lotNumber;
			transaction.Occurred = DateTime.Now;
			transaction.ParticipantBy = Registry.Find<UserAccount>().OrganizationParticipant;
			transaction.Quantity = Converter.ToInt32(_quantity);
			transaction.ReceiptDetail = detail;
			transaction.SerialNumber = _serialNumber;
			transaction.StatusCode = _statusCode;
			transaction.StatusReasonCode = _statusCode.StatusReasonCode;
			transaction.TransactionType = type;
			transaction.UnitOfMeasure = _unitOfMeasure;
			//
			Repositories.Get<ItemTransaction>().Add(transaction);
		}

		public virtual void WriteRepack(LicensePlate licensePlateTo)
		{
			TransactionType type = Entity.Retrieve<TransactionType>(InventoryTransactions.Repack);
			//
			ItemTransaction transaction = Entity.Activate<ItemTransaction>();
			transaction.InventoryItem = this;
			transaction.Item = _item;
			transaction.LicensePlateTo = licensePlateTo;
			transaction.Occurred = DateTime.Now;
			transaction.ParticipantBy = Registry.Find<UserAccount>().OrganizationParticipant;
			transaction.Quantity = Converter.ToInt32(_quantity);
			transaction.QuantityFrom = 0;
			transaction.StatusCode = _statusCode;
			transaction.TransactionType = type;
			//
			Repositories.Get<ItemTransaction>().Add(transaction);
		}

		public virtual void WriteReturned(ReturnDetail detail)
		{
			TransactionType type = Entity.Retrieve<TransactionType>(InventoryTransactions.Return);
			//
			ItemTransaction transaction = Entity.Activate<ItemTransaction>();
			transaction.InventoryItem = this;
			transaction.Item = _item;
			transaction.LicensePlateTo = _licensePlate.ParentLicensePlate;
			transaction.LocationTo = _location;
			transaction.LotNumber = _lotNumber;
			transaction.Occurred = DateTime.Now;
			transaction.ParticipantBy = Registry.Find<UserAccount>().OrganizationParticipant;
			transaction.Quantity = Converter.ToInt32(_quantity);
			transaction.ReturnDetail = detail;
			transaction.SerialNumber = _serialNumber;
			transaction.StatusCode = _statusCode;
			transaction.StatusReasonCode = _statusCode.StatusReasonCode;
			transaction.TransactionType = type;
			transaction.UnitOfMeasure = _unitOfMeasure;
			//
			Repositories.Get<ItemTransaction>().Add(transaction);
		}

		public virtual void WriteStockAdjustment(string serialNumber, InventoryAdjustmentCode adjustment,
			Decimal? fromQuantity, StatusCode fromStatus)
		{
			TransactionType type = Entity.Retrieve<TransactionType>(StockAdjustmentTransactions.StockAdjustment);
			ItemTransaction transaction = Entity.Activate<ItemTransaction>();
			if (adjustment != null)
			{
				if ("N".Equals(adjustment.SendToHost)) transaction.IntegrationStatusCode = Entity.Retrieve<StatusCode>(IntegrationStatuses.Complete);
				else if ("Y".Equals(adjustment.SendToHost)) transaction.IntegrationStatusCode = Entity.Retrieve<StatusCode>(IntegrationStatuses.Open);
			}
			transaction.InventoryAdjustmentCode = adjustment;
			transaction.InventoryItem = this;
			transaction.Item = _item;
			transaction.LotNumber = _lotNumber;
			transaction.Memo = this.Memo;
			transaction.Occurred = DateTime.Now;
			transaction.OrderCode = this.OrderCode;
			transaction.ParticipantBy = Registry.Find<UserAccount>().OrganizationParticipant;
			transaction.Quantity = Converter.ToInt32(_quantity);
			transaction.QuantityFrom = Converter.ToNullableInt32(fromQuantity);
			transaction.SerialNumber = serialNumber;
			transaction.StatusCode = _statusCode;
			transaction.StatusCodeFrom = fromStatus;
			transaction.StatusReasonCode = _statusCode.StatusReasonCode;
			transaction.TransactionType = type;
			transaction.UnitOfMeasure = _unitOfMeasure;
			//
			Repositories.Get<ItemTransaction>().Add(transaction);
		}

		public virtual void WriteStockMovement(TransactionType type, Location to, LicensePlate parent, decimal quantity, string serialNumber)
		{
			ItemTransaction transaction = Entity.Activate<ItemTransaction>();
			transaction.InventoryItem = this;
			transaction.Item = _item;
			if (_licensePlate != null && _licensePlate.ParentLicensePlate != null)
				transaction.LicensePlateFrom = _licensePlate.ParentLicensePlate;
			transaction.LicensePlateTo = parent;
			transaction.LocationFrom = _location;
			transaction.LocationTo = to;
			transaction.LotNumber = _lotNumber;
			transaction.Occurred = DateTime.Now;
			transaction.OrderCode = this.OrderCode;
			transaction.ParticipantBy = Registry.Find<UserAccount>().OrganizationParticipant;
			transaction.Quantity = Converter.ToInt32(quantity);
			transaction.SerialNumber = _serialNumber;
			transaction.StatusCode = _statusCode;
			transaction.TransactionType = type;
			//
			Repositories.Get<ItemTransaction>().Add(transaction);
		}

		#endregion

		#region Methods.Static

		public static DateTime? CalculateManufactureDate(string lotNumber)
		{
			if (String.IsNullOrEmpty(lotNumber)) return null;
			else if (lotNumber.Length == 6)
			{
				Boolean? calculate = BusinessRule.RetrieveBoolean("11025");
				if (!calculate.HasValue || !calculate.Value) return null;
				//
				Int32 day = -1;
				if (Char.IsNumber(lotNumber[4]) && Char.IsNumber(lotNumber[5]))
					day = Converter.ToInt32(lotNumber.Substring(4, 2));
				//
				Int32 month = -1;
				if (Char.IsLetter(lotNumber[3]))
				{
					month = (Int32)Char.ToUpper(lotNumber[3]) - 64;
					if (month > 9) --month;
				}
				//
				Int32 year = -1;
				if (Char.IsNumber(lotNumber[2])) year = Converter.ToInt32(lotNumber[2].ToString());
				//
				if (day > 0 && month > 0 && year != -1)
				{
					Int32 decade = DateTime.Now.Year - (DateTime.Now.Year % 10);
					DateTime date = new DateTime(decade + year, month, day);
					//
					return (date > DateTime.Now.Date) ? date.AddYears(-10) : date;
				}
				else return null;
			}
			else if (lotNumber.Length == 10)
			{
				Boolean? calculate = BusinessRule.RetrieveBoolean("11025");
				if (!calculate.HasValue || !calculate.Value) return null;
				//
				Int32 julianDay = -1;
				if (Char.IsNumber(lotNumber[2]) && Char.IsNumber(lotNumber[3]) && Char.IsNumber(lotNumber[4]))
					julianDay = Converter.ToInt32(lotNumber.Substring(2, 3));
				//
				Int32 year = -1;
				if (Char.IsNumber(lotNumber[0]) && Char.IsNumber(lotNumber[1]))
				{
					year = Converter.ToInt32(lotNumber.Substring(0, 2));
					//
					if (julianDay > 0 && year != -1)
					{
						year = Convert.ToInt32(DateTime.Now.Year.ToString().Substring(0, 2) + year.ToString());
						DateTime date = new DateTime(year, 1, 1).AddDays(julianDay - 1);
						//
						DateTime heinzDate = new DateTime(date.Year, 02, 28);
						if (year % 4 != 0 && year % 100 != 0 && date > heinzDate) date = date.AddDays(-1);
						//
						return date;
					}
					else return null;
				}
				else if (Char.IsLetter(lotNumber[0]) && Char.IsLetter(lotNumber[1]))
				{
					year = Converter.ToInt32(lotNumber.Substring(5, 2));
					//
					if (julianDay > 0 && year != -1)
					{
						year = Convert.ToInt32(DateTime.Now.Year.ToString().Substring(0, 2) + year.ToString());
						DateTime date = new DateTime(year, 1, 1).AddDays(julianDay - 1);
						//
						return date;
					}
					else return null;
				}
				else return null;
			}
			else return null;
		}

		public static InventoryItem Create(Item item, Location location, StatusCode status, Decimal quantity)
		{
			InventoryItem created = Entity.Activate<InventoryItem>();
			if (Registry.Find<AgencyOrganizationalUnit>() != null) created.AgencyOrganizationalUnit = Registry.Find<AgencyOrganizationalUnit>();
			else created.AgencyOrganizationalUnit = location.AgencyOrganizationalUnit;
			created.AssetCode = null;
			created.CompanyLocationType = Registry.Find<CompanyLocationType>();
			if ("Y".Equals(item.AssetIndicator)) created.HomeLocation = item.Location;
			created.Item = item;
			//
			if (Registry.Find<CompanyLocationType>() != null)
			{
				LicenseType inventory = Entity.Retrieve<LicenseType>(InventoryLicenseTypes.InventoryItem);
				LicensePlate plate = LicensePlate.Create(inventory);
				plate.LicensePlateCode = LicensePlateCode.GetCurrentValue(inventory);
				Repositories.Get<LicensePlate>().Add(plate);
				//
				created.LicensePlate = plate;
			}
			//
			created.Location = location;
			created.Quantity = quantity;
			created.Received = DateTime.Now;
			created.StatusCode = (status == null) ? Entity.Retrieve<StatusCode>(InventoryStatuses.Available) : status;
			created.UnitOfMeasure = item.UnitOfMeasure;
			if (created.UnitOfMeasure == null)
			{
				UnitOfMeasure each = Entity.Retrieve<UnitOfMeasure>(InventoryUoms.Each);
				created.UnitOfMeasure = each;
			}
			created.UomQuantity = Converter.ToNullableInt32(created.UnitOfMeasure.Factor);
			//
			return created;
		}

		public static StatusCode UpdateStatus(Item item, StatusCode status, DateTime? manufacture)
		{
			if (!manufacture.HasValue) return status;
			//
			Boolean? incubation = BusinessRule.RetrieveBoolean("11027");
			if (!incubation.HasValue || !incubation.Value) return status;
			//
			DetachedCriteria criteria = DetachedCriteria.For<UdfItemValue>()
				.Add("Item", item)
				.Add("UdfMetadataValue.Label", "Incubation Hold")
				.SetMaxResults(1);
			UdfItemValue value = Repositories.Get<UdfItemValue>().Retrieve(criteria);
			if (value == null) return status;
			//
			Int32 days = String.IsNullOrEmpty(value.UserDefinedValue) ? 0 : Converter.ToInt32(value.UserDefinedValue);
			if (manufacture.Value.Date.AddDays(days) <= DateTime.Now.Date) return status;
			else return Entity.Retrieve<StatusCode>(InventoryStatuses.IncubationHold);
		}

		#endregion
	}
}
