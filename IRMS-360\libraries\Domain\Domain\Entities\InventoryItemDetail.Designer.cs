using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InventoryItemDetail : Entity
	{
		#region Fields

		private Decimal _quantity;
		private InventoryItem _inventoryItem;
		private InventoryItem _parentInventoryItem;
		private KitHeader _kitHeader;
		private Location _location;
		private OrderDetail _orderDetail;
		private StatusCode _statusCode;
		private String _cycleCount;
		private String _lotNumber;
		private String _reservationCode;
		private String _serialNumber;

		#endregion

		#region Properties

		[DataMember]
		public virtual Decimal Quantity
		{
			get { return _quantity; }
			set { _quantity = value; }
		}

		[DataMember]
		public virtual InventoryItem InventoryItem
		{
			get { return _inventoryItem; }
			set { _inventoryItem = value; }
		}

		[DataMember]
		public virtual InventoryItem ParentInventoryItem
		{
			get { return _parentInventoryItem; }
			set { _parentInventoryItem = value; }
		}

		[DataMember]
		public virtual KitHeader KitHeader
		{
			get { return _kitHeader; }
			set { _kitHeader = value; }
		}

		[DataMember]
		public virtual Location Location
		{
			get { return _location; }
			set { _location = value; }
		}

		[DataMember]
		public virtual OrderDetail OrderDetail
		{
			get { return _orderDetail; }
			set { _orderDetail = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual String CycleCount
		{
			get { return _cycleCount; }
			set { _cycleCount = value; }
		}

		[DataMember]
		public virtual String LotNumber
		{
			get { return _lotNumber; }
			set { _lotNumber = value; }
		}

		[DataMember]
		public virtual String ReservationCode
		{
			get { return _reservationCode; }
			set { _reservationCode = value; }
		}

		[DataMember]
		public virtual String SerialNumber
		{
			get { return _serialNumber; }
			set { _serialNumber = value; }
		}


		#endregion
	}
}
