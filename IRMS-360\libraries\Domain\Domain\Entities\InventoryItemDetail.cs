using System;
using System.Runtime.Serialization;

using NHibernate.Criterion;

using Upp.Irms.Constants;
using Upp.Irms.Core;
using Upp.Shared.Utilities;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class InventoryItemDetail : Entity
	{
		#region Constructor

		public InventoryItemDetail()
		{
			//
		}

		#endregion

		#region Methods.Public

		public virtual void ChangeStatus(StatusCode status)
		{
			_dateModified = DateTime.Now;
			_statusCode = status;
			_userModified = Registry.Find<UserAccount>().UserName;
			//
			Repositories.Get<InventoryItemDetail>().Update(this);
		}

		public virtual void Move(Location to, LicensePlate parent)
		{
			DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
				.Add("Item", _inventoryItem.Item)
				.Add("Location", to)
				.Add("Quantity", ">", 0M)
				.Add("StatusCode", _inventoryItem.StatusCode)
				.SetMaxResults(1);
			if (parent != null) criteria = criteria.Add("LicensePlate.ParentLicensePlate", parent);
			InventoryItem master = Repositories.Get<InventoryItem>().Retrieve(criteria);
			if (master == null)
			{
				master = InventoryItem.Create(_inventoryItem.Item, to, _inventoryItem.StatusCode, 0M);
				if (parent != null) master.LicensePlate.ChangeParent(parent);
				master.ReceiptDetail = _inventoryItem.ReceiptDetail;
				master.Received = _inventoryItem.Received;
				master.UnitOfMeasure = _inventoryItem.UnitOfMeasure;
				//
				Repositories.Get<InventoryItem>().Add(master);
			}
			//
			_dateModified = DateTime.Now;
			_inventoryItem = master;
			_inventoryItem.ChangeQuantity(_inventoryItem.Quantity + 1);
			_userModified = Registry.Find<UserAccount>().UserName;
			//
			Repositories.Get<InventoryItemDetail>().Update(this);
		}

		#endregion

		#region Methods.Public.Assets

		public virtual void ClearCycleCount()
		{
			_cycleCount = "N";
			_dateModified = DateTime.Now;
			_userModified = Registry.Find<UserAccount>().UserName;
			Repositories.Get<InventoryItemDetail>().Update(this);
		}

		public virtual void SetCycleCount(Task task)
		{
			_cycleCount = "Y";
			_dateModified = DateTime.Now;
			_userModified = Registry.Find<UserAccount>().UserName;
			Repositories.Get<InventoryItemDetail>().Update(this);
			//
			InventoryTask count = InventoryTask.Create(task);
			count.InventoryItem = _inventoryItem;
			count.Item = _inventoryItem.Item;
			count.LotNumber = _inventoryItem.LotNumber;
			count.Quantity = _quantity;
			Repositories.Get<InventoryTask>().Add(count);
		}

		#endregion

		#region Methods.Static

		public static InventoryItemDetail Create(InventoryItem asset)
		{
			InventoryItemDetail entity = Entity.Activate<InventoryItemDetail>();
			entity.InventoryItem = asset;
			entity.ParentInventoryItem = asset.InventoryItemTo;
			entity.Quantity = Converter.ToInt32(asset.MovementQuantity);
			entity.StatusCode = Entity.Retrieve<StatusCode>(InventoryStatuses.Issued);
			//
			return entity;
		}

		public static InventoryItemDetail Create(InventoryItem asset, KitHeader kit)
		{
			InventoryItemDetail entity = Entity.Activate<InventoryItemDetail>();
			entity.InventoryItem = asset;
			entity.KitHeader = kit;
			entity.ParentInventoryItem = asset.InventoryItemTo;
			entity.Quantity = Converter.ToInt32(asset.MovementQuantity);
			entity.StatusCode = Entity.Retrieve<StatusCode>(InventoryStatuses.Kit);
			//
			return entity;
		}

		#endregion
	}
}
