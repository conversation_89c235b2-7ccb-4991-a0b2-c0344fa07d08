using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InventoryPick : Entity
	{
		#region Fields

		private DateTime _picked;
		private Decimal _quantity;
		private ItemFulfillment _itemFulfillment;
		private LicensePlate _licensePlate;
        private LicensePlateStatus _licensePlateStatus;
		private ICollection<CartonDetail> _cartonDetails = new HashSet<CartonDetail>();
		private ICollection<ItemTransaction> _itemTransactions = new HashSet<ItemTransaction>();
		private ICollection<ShipmentDetail> _shipmentDetails = new HashSet<ShipmentDetail>();
		private Location _location;
		private OrderDetail _orderDetail;
		private OrganizationParticipant _picker;
        private ShipmentDetail _shipmentDetail;
        private StatusCode _statusCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime Picked
		{
			get { return _picked; }
			set { _picked = value; }
		}

		[DataMember]
		public virtual Decimal Quantity
		{
			get { return _quantity; }
			set { _quantity = value; }
		}

		[DataMember]
		public virtual ItemFulfillment ItemFulfillment
		{
			get { return _itemFulfillment; }
			set { _itemFulfillment = value; }
		}

		[DataMember]
		public virtual LicensePlate LicensePlate
		{
			get { return _licensePlate; }
			set { _licensePlate = value; }
		}

        [DataMember]
        public virtual LicensePlateStatus LicensePlateStatus
        {
            get { return _licensePlateStatus; }
            set { _licensePlateStatus = value; }
        }

        [DataMember]
		public virtual ICollection<CartonDetail> CartonDetails
		{
			get { return _cartonDetails; }
			set { _cartonDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ItemTransaction> ItemTransactions
		{
			get { return _itemTransactions; }
			set { _itemTransactions = value; }
		}

		[DataMember]
		public virtual ICollection<ShipmentDetail> ShipmentDetails
		{
			get { return _shipmentDetails; }
			set { _shipmentDetails = value; }
		}

		[DataMember]
		public virtual Location Location
		{
			get { return _location; }
			set { _location = value; }
		}

		[DataMember]
		public virtual OrderDetail OrderDetail
		{
			get { return _orderDetail; }
			set { _orderDetail = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant Picker
		{
			get { return _picker; }
			set { _picker = value; }
		}
        [DataMember]
        public virtual ShipmentDetail ShipmentDetail
        {
            get { return _shipmentDetail; }
            set { _shipmentDetail = value; }
        }
        [DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}


		#endregion
	}
}
