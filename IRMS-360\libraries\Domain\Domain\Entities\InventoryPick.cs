using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;

using NHibernate.Criterion;
using NHibernate.Transform;

using Upp.Irms.Constants;
using Upp.Irms.Core;
using Upp.Shared.Utilities;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class InventoryPick : Entity
	{
		#region Properties
        [DataMember]
        public virtual Decimal? MaximumQuantity { get; set; }
        [DataMember]
        public virtual Boolean Last { get; set; }
        [DataMember]
        public virtual Boolean SuggestStageLocation { get; set; }
		[DataMember]
		public virtual CartonHeader CartonHeader { get; set; }
        [DataMember]
        public virtual Decimal PalletFactor { get; set; }
		public virtual Decimal? Weight { get; set; }
		[DataMember]
		public virtual decimal CartonQuantity { get; set; }
		[DataMember]
		public virtual Int32 LineNumber { get; set; }
        public virtual int? OrderId { get; set; }
        public virtual int? PalletId { get; set; }
		[DataMember]
		public virtual Int32 PackedQuantity { get; set; }
		public virtual Item Item { get; set; }
		[DataMember]
		public virtual LicensePlate LicensePlateTo { get; set; }
		[DataMember]
		public virtual Location LocationTo { get; set; }
        [DataMember]
        public virtual Location StageLocation { get; set; }
		[DataMember]
		public virtual OrderComment OrderComment { get; set; }
		[DataMember]
		public virtual OrderHeader OrderHeader { get; set; }
        public virtual String CarrierCode { get; set; }
        public virtual String CarrierName { get; set; }
        public virtual String CustomerCode { get; set; }
		[DataMember]
		public virtual String ItemCode { get; set; }
		[DataMember]
		public virtual String ItemDescription { get; set; }
		[DataMember]
		public virtual String LicensePlateCode { get; set; }
		[DataMember]
		public virtual String LocationCode { get; set; }
        public virtual String OrderCode { get; set; }
		[DataMember]
		public virtual String StockStatusCode { get; set; }
        public virtual String PackBox { get; set; }
		public virtual String UpcCode { get; set; }
		public virtual UnitOfMeasure UnitOfMeasure { get; set; }
		public virtual Wave Wave { get; set; }
		public virtual Int32? LicensePlateId { get; set; }        

        #endregion

        #region Report Properties
        public virtual Decimal? CaseQuantity { get; set; }
        public virtual Decimal? CatchWeight { get; set; }        
        public virtual Decimal? OriginalQuantity { get; set; }
        public virtual int? CartonLPNId { get; set; }
        public virtual int? CompanyId { get; set; }
        public virtual int? FulfillmentId { get; set; }
        public virtual Decimal OriginalLineQuantity { get; set; }
        public virtual int? ItemId { get; set; }
        public virtual int? LpnId { get; set; }
        public virtual int? OrderLineId { get; set; }
        public virtual int? PickerId { get; set; }
        public virtual int? ShipmentHeaderId { get; set; }
        public virtual int? StatusCodeId { get; set; }
        public virtual decimal PalletQuantity { get; set; }
        public virtual decimal PalletWeight { get; set; }
        public virtual decimal PickWeight { get; set; }
        public virtual decimal ItemWeight { get; set; }
        public virtual decimal LpnWeight { get; set; }
        public virtual Decimal LpnOrignalWeight { get; set; }
        public virtual Decimal? UomFactor { get; set; }
        public virtual String CartonLpnCode { get; set; }
        public virtual String CompanyCode { get; set; }
        public virtual String CompanyLocationCode { get; set; }
        public virtual String CompanyName { get; set; }
        
        public virtual String CustomerItemCode { get; set; }
        public virtual String CustomerItemDescription { get; set; }
        public virtual String FreightClassCode { get; set; }
        public virtual String FreightCodeCode { get; set; }
        public virtual String FreightCodeDescription { get; set; }
        public virtual String HasHazmat { get; set; }
        public virtual String HazmatCode { get; set; }
        public virtual String HazmatUom { get; set; }
        public virtual String ItemUom { get; set; }
        public virtual int? InventoryItemId { get; set; }        
        public virtual String PackageType { get; set; }
        public virtual String PalletLpnCode { get; set; }
        public virtual String PickerFirstName { get; set; }
        public virtual String PickerLastName { get; set; }
        public virtual String LicenseTypeCode { get; set; }
        public virtual String LineComments { get; set; }
        public virtual String OrderSuffix { get; set; }
        public virtual DateTime? ShipDate { get; set; }
        public virtual String ShiptoName { get; set; }
        public virtual String OrderNumber { get; set; }
        public virtual String Carrier { get; set; }
        public virtual String ProNumber { get; set; }
        public virtual String TrackingCode { get; set; }
        public virtual String LotNumber { get; set; }
        public virtual String CustomerPO { get; set; }
        public virtual String OrderHeaderCode { get; set; }
        public virtual String Warehouse { get; set; }
        public virtual String Company { get; set; }
        public virtual String ToDate { get; set; }
        public virtual String FromDate { get; set; }
        public virtual String Customer { get; set; }
        public virtual DateTime? Ordered { get; set; }
        public virtual DateTime? StatusDate { get; set; }
        public virtual DateTime? LineStatusDate { get; set; }
        public virtual DateTime? OrderDate { get; set; }
        public virtual DateTime CurrentDate { get; set; }
        public virtual String CurrentTime { get; set; }
        public virtual String LineStatus { get; set; }
        public virtual Int32? ShipmentCartonCount { get; set; }
        public virtual String ShipmentNo { get; set; }
        public virtual Int32? ShipmentPalletCount { get; set; }    
        public virtual String SerialNumber{ get; set; }
        public virtual Int32? CartonCount { get; set; }
        public virtual Int32? PalletCount { get; set; }
        public virtual Decimal? ParentLPNWeight { get; set; }
        public virtual decimal ItemsWeight { get; set; }
        public virtual string ParentLicensePlateCode { get; set; }
        public virtual Int32? ParentLicensePlateId { get; set; }
        public virtual string ItemGroupLine { get; set; }
        public virtual string IsSerialized { get; set; }
        public virtual decimal PickCube { get; set; }
        public virtual string FOBCodeDescription { get; set; }
        public virtual int? OrderHeaderId { get; set; }
        #endregion

        #region Constructor

        public InventoryPick()
		{
			//
		}

		#endregion

		#region Methods.Private.Transactions

		private void WritePacked()
		{
			TransactionType type = Entity.Retrieve<TransactionType>(OrderTransactions.Packed);
			ItemTransaction transaction = Entity.Activate<ItemTransaction>();
			transaction.InventoryPick = this;
			transaction.Item = _itemFulfillment.Item;
			transaction.LicensePlateTo = this.LicensePlate;
			transaction.LocationFrom = _location;
			transaction.Occurred = DateTime.Now;
			transaction.OrderHeader = _orderDetail.OrderHeader;
			transaction.ParticipantBy = Registry.Find<UserAccount>().OrganizationParticipant;
			transaction.Quantity = _quantity;
			transaction.TransactionType = type;
			//
			Repositories.Get<ItemTransaction>().Add(transaction);
		}

		private void WriteUndoPick(InventoryItem item)
		{
			TransactionType type = Entity.Retrieve<TransactionType>(OrderTransactions.Picked);
			ItemTransaction transaction = Entity.Activate<ItemTransaction>();
			transaction.InventoryItem = item;
			transaction.InventoryPick = null;
			transaction.Item = item.Item;
			transaction.LocationTo = item.Location;
			transaction.Occurred = DateTime.Now;
			transaction.OrderHeader = _orderDetail.OrderHeader;
			transaction.ParticipantBy = Registry.Find<UserAccount>().OrganizationParticipant;
			transaction.Quantity = Converter.ToInt32(0 - item.Quantity);
			transaction.StatusCode = item.StatusCode;
			transaction.TransactionType = type;
			transaction.Wave = _itemFulfillment.Wave;
			//
			Repositories.Get<ItemTransaction>().Add(transaction);
		}

		#endregion

		#region Methods.Public

        public virtual void CheckLast(String pickBy)
        {
            StatusCode packed = Entity.Retrieve<StatusCode>(OrderStatuses.Packed);
            DetachedCriteria criteria = DetachedCriteria.For<InventoryPick>()
                .Add("StatusCode.SortOrder", "<", packed.SortOrder)
                .SetProjection(Projections.Count("Id"));

            switch (pickBy)
            {
                case "Lpn":
                    criteria = criteria.Add("LicensePlate", this.CartonHeader.LicensePlate);
                    break;
                case "Order":
                    criteria = criteria.Add("OrderDetail.OrderHeader", _orderDetail.OrderHeader);
                    break;
            }
            this.Last = (Repositories.Get<InventoryPick>().Function<Int32>(criteria) == 0);
        }

        public virtual void CheckSuggestStageLocation()
        {
            Boolean? suggest = BusinessRule.RetrieveBoolean("11131");
            this.SuggestStageLocation = suggest.HasValue ? suggest.Value : false;
        }

		public virtual void FindOrderComment()
		{
			if (_orderDetail == null) return;
			//
			DetachedCriteria criteria = DetachedCriteria.For<OrderComment>()
				.Add("Active", "A")
				.Add("OrderDetail", _orderDetail)
				.SetMaxResults(1);
			IList<OrderComment> comments = Repositories.Get<OrderComment>().List(criteria);
			if (comments.Count > 0) this.OrderComment = comments[0];
		}

        public virtual void FindStageLocation()
        {
            if (_orderDetail.OrderHeader.Carrier == null) return;
            //
            DetachedCriteria criteria = DetachedCriteria.For<Dock>()
                .Add("Carrier.CarrierCode", _orderDetail.OrderHeader.Carrier.CarrierCode)
                .Add("CompanyLocationType", Registry.Find<CompanyLocationType>())
                .SetMaxResults(1);
            IList<Dock> docks = Repositories.Get<Dock>().List(criteria);

            if (docks != null && docks.Count > 0) this.StageLocation = docks[0].Location;
        }

		public virtual void Pack(Int32 quantity)
		{
			CartonDetail detail = CartonDetail.Create(this);
			if (!this.CartonHeader.SameAs(detail.CartonHeader))
			{
				this.CartonHeader = Repositories.Get<CartonHeader>().Retrieve(this.CartonHeader.Id);
				this.CartonHeader.LicensePlate.ChangeParent(_licensePlate);
				//
				if (detail.CartonHeader.OrderHeader != null)
				{
					this.CartonHeader.OrderHeader = detail.CartonHeader.OrderHeader;
					Repositories.Get<CartonHeader>().Update(this.CartonHeader);
				}
				//
				detail.CartonHeader = this.CartonHeader;
				detail.LicensePlate = this.CartonHeader.LicensePlate;
			}
			detail.Quantity = quantity;
			detail.StatusCode = Entity.Retrieve<StatusCode>(LicensePlateStatuses.Packed);
			this.CartonDetails.Add(detail);
			//
			if (quantity < _quantity)
			{
				InventoryPick pick = Entity.Clone<InventoryPick>(this);
				pick.Quantity = _quantity - quantity;
				pick.StatusCode = Entity.Retrieve<StatusCode>(OrderStatuses.Picked);
				Repositories.Get<InventoryPick>().Add(pick);
			}
			//
			_licensePlate = detail.LicensePlate;
			_quantity = quantity;
			_statusCode = Entity.Retrieve<StatusCode>(OrderStatuses.Packed);
			//
			_itemFulfillment.UpdateStatus();
			_orderDetail.UpdateStatus();
			if (_orderDetail.OrderHeader.ShouldUpdateStatus()) _orderDetail.OrderHeader.UpdateStatus();
			//
			this.WritePacked();
		}

		public virtual void Ship()
		{
			_dateModified = DateTime.Now;
			_statusCode = Entity.Retrieve<StatusCode>(OrderStatuses.Shipped);
			_userModified = Registry.Find<UserAccount>().UserName;
			Repositories.Get<InventoryPick>().Update(this);
			//
			_itemFulfillment.UpdateStatus();
			_orderDetail.UpdateStatus();
		}

		public virtual void UndoPick(Location location, LicensePlate plate, Decimal quantity)
		{
			StatusCode available = Entity.Retrieve<StatusCode>(InventoryStatuses.Available);
			StatusCode distributed = Entity.Retrieve<StatusCode>(OrderStatuses.Distributed);
			StatusCode open = Entity.Retrieve<StatusCode>(IntegrationStatuses.Open);
			StatusCode packed = Entity.Retrieve<StatusCode>(LicensePlateStatuses.Packed);
			StatusCode picking = Entity.Retrieve<StatusCode>(OrderStatuses.InPick);
			StatusCode shipped = Entity.Retrieve<StatusCode>(OrderStatuses.Shipped);
			StatusCode undo = Entity.Retrieve<StatusCode>(LicensePlateStatuses.UndoPacking);
			//
			InventoryItem item = InventoryItem.Create(_itemFulfillment.Item, location, available, quantity);
			Repositories.Get<InventoryItem>().Add(item);
			if (plate != null) item.LicensePlate.ChangeParent(plate);
			//
			_licensePlate.FindCurrentStatus();
			if (packed.SameAs(_licensePlate.CurrentStatus)/* || picking.SameAs(_licensePlate.CurrentStatus)*/)
				_licensePlate.ChangeStatus(undo, open);
			//
			if (quantity < _quantity)
			{
				InventoryPick split = Entity.Clone<InventoryPick>(this);
				split.Quantity = _quantity - quantity;
				Repositories.Get<InventoryPick>().Add(split);
				//
				_itemFulfillment.StatusCode = picking;
			}
			else _itemFulfillment.StatusCode = distributed;
			Repositories.Get<ItemFulfillment>().Update(_itemFulfillment);
			//
			_quantity = 0;
			_statusCode = shipped;
			//
			_orderDetail.UpdateStatus();
			if (_orderDetail.OrderHeader.ShouldUpdateStatus()) _orderDetail.OrderHeader.UpdateStatus();
			//
			if (_itemFulfillment.Wave != null) _itemFulfillment.Wave.Open();
            //
            Decimal remaining = quantity;
			foreach (CartonDetail element in this.CartonDetails)
			{
				if (remaining <= 0) break;
				//
				element.DateModified = DateTime.Now;
				if (element.Quantity < remaining)
				{
					element.Quantity = 0;
					remaining -= element.Quantity;
				}
				else
				{
					element.Quantity -= remaining;
					remaining = 0;
				}
				element.StatusCode = undo;
				element.UserModified = Registry.Find<UserAccount>().UserName;
				//
				Repositories.Get<CartonDetail>().Update(element);
			}
			if (this.CartonDetails != null && this.CartonDetails.Count > 0)
				this.CartonDetails.First().CartonHeader.UpdateStatus();
			//
			this.WriteUndoPick(item);
		}

		#endregion

        #region Methods.Public.Transactions

        public virtual void WriteVerified()
        {
            TransactionType type = Entity.Retrieve<TransactionType>(LicensePlateTransactions.ItemVerified);
            ItemTransaction transaction = Entity.Activate<ItemTransaction>();
            transaction.InventoryPick = this;
            transaction.Item = this.ItemFulfillment.Item;
            transaction.Occurred = DateTime.Now;
            transaction.OrderHeader = this.OrderDetail.OrderHeader;
            transaction.ParticipantBy = Registry.Find<UserAccount>().OrganizationParticipant;
            transaction.Quantity = _quantity;
            transaction.QuantityFrom = _quantity;
            transaction.TransactionType = type;
            transaction.Wave = this.ItemFulfillment.Wave;
            //
            Repositories.Get<ItemTransaction>().Add(transaction);
        }

        #endregion

		#region Methods.Static

		public static InventoryPick Create(ItemFulfillment fulfillment)
		{
			InventoryPick entity = Entity.Activate<InventoryPick>();
			entity.ItemFulfillment = fulfillment;
			entity.LicensePlate = fulfillment.LicensePlate;
			entity.Location = fulfillment.Location;
			entity.OrderDetail = fulfillment.OrderDetail;
			entity.Picked = DateTime.Now;
			entity.Picker = Registry.Find<UserAccount>().OrganizationParticipant;
			//
			return entity;
		}

		#endregion
	}
}
