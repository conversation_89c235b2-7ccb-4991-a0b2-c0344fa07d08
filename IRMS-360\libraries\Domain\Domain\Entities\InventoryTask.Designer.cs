using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InventoryTask : Entity
	{
		#region Fields

		private AgencyOrganizationalUnit _agencyOrganizationalUnit;
		private CompanyLocationType _companyLocationType;
		private CompanyLocationType _companyLocationTypeTo;
		private CompanyLocationZone _companyLocationZoneFrom;
		private CompanyLocationZone _companyLocationZoneTo;
		private DateTime? _completed;
		private DateTime? _started;
		private Decimal _quantity;
		private Decimal? _actualQuantity;
		private DispositionCode _dispositionCode;
		private InventoryItem _inventoryItem;
		private Item _item;
		private LicensePlate _licensePlate;
		private Location _locationFrom;
		private Location _locationTo;
		private OrderDetail _orderDetail;
		private OrganizationParticipant _organizationParticipant;
		private Priority _priority;
		private ReceiptHeader _receiptHeader;
		private StatusCode _statusCode;
		private String _lotNumber;
		private String _orderCode;
		private String _orderSuffix;
		private String _serialRangeBegin;
		private String _serialRangeEnd;
		private Task _task;
		private WorkOrderDetail _workOrderDetail;

		#endregion

		#region Properties

		[DataMember]
		public virtual AgencyOrganizationalUnit AgencyOrganizationalUnit
		{
			get { return _agencyOrganizationalUnit; }
			set { _agencyOrganizationalUnit = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationTypeTo
		{
			get { return _companyLocationTypeTo; }
			set { _companyLocationTypeTo = value; }
		}

		[DataMember]
		public virtual CompanyLocationZone CompanyLocationZoneFrom
		{
			get { return _companyLocationZoneFrom; }
			set { _companyLocationZoneFrom = value; }
		}

		[DataMember]
		public virtual CompanyLocationZone CompanyLocationZoneTo
		{
			get { return _companyLocationZoneTo; }
			set { _companyLocationZoneTo = value; }
		}

		[DataMember]
		public virtual DateTime? Completed
		{
			get { return _completed; }
			set { _completed = value; }
		}

		[DataMember]
		public virtual DateTime? Started
		{
			get { return _started; }
			set { _started = value; }
		}

		[DataMember]
		public virtual Decimal Quantity
		{
			get { return _quantity; }
			set { _quantity = value; }
		}

		[DataMember]
		public virtual Decimal? ActualQuantity
		{
			get { return _actualQuantity; }
			set { _actualQuantity = value; }
		}

		[DataMember]
		public virtual DispositionCode DispositionCode
		{
			get { return _dispositionCode; }
			set { _dispositionCode = value; }
		}

		[DataMember]
		public virtual InventoryItem InventoryItem
		{
			get { return _inventoryItem; }
			set { _inventoryItem = value; }
		}

		[DataMember]
		public virtual Item Item
		{
			get { return _item; }
			set { _item = value; }
		}

		[DataMember]
		public virtual LicensePlate LicensePlate
		{
			get { return _licensePlate; }
			set { _licensePlate = value; }
		}

		[DataMember]
		public virtual Location LocationFrom
		{
			get { return _locationFrom; }
			set { _locationFrom = value; }
		}

		[DataMember]
		public virtual Location LocationTo
		{
			get { return _locationTo; }
			set { _locationTo = value; }
		}

		[DataMember]
		public virtual OrderDetail OrderDetail
		{
			get { return _orderDetail; }
			set { _orderDetail = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant OrganizationParticipant
		{
			get { return _organizationParticipant; }
			set { _organizationParticipant = value; }
		}

		[DataMember]
		public virtual Priority Priority
		{
			get { return _priority; }
			set { _priority = value; }
		}

		[DataMember]
		public virtual ReceiptHeader ReceiptHeader
		{
			get { return _receiptHeader; }
			set { _receiptHeader = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual String LotNumber
		{
			get { return _lotNumber; }
			set { _lotNumber = value; }
		}

		[DataMember]
		public virtual String OrderCode
		{
			get { return _orderCode; }
			set { _orderCode = value; }
		}

		[DataMember]
		public virtual String OrderSuffix
		{
			get { return _orderSuffix; }
			set { _orderSuffix = value; }
		}

		[DataMember]
		public virtual String SerialRangeBegin
		{
			get { return _serialRangeBegin; }
			set { _serialRangeBegin = value; }
		}

		[DataMember]
		public virtual String SerialRangeEnd
		{
			get { return _serialRangeEnd; }
			set { _serialRangeEnd = value; }
		}

		[DataMember]
		public virtual Task Task
		{
			get { return _task; }
			set { _task = value; }
		}

		[DataMember]
		public virtual WorkOrderDetail WorkOrderDetail
		{
			get { return _workOrderDetail; }
			set { _workOrderDetail = value; }
		}


		#endregion
	}
}
