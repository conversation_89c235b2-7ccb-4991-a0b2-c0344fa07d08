using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;

using NHibernate.Criterion;

using Upp.Irms.Constants;
using Upp.Irms.Core;
using Upp.Shared.Utilities;

namespace Upp.Irms.Domain
{
    [DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
    public partial class InventoryTask : Entity
    {
        #region Properties

        [DataMember]
        public virtual Boolean Close { get; set; }
        [DataMember]
        public virtual Boolean PerformByUom { get; set; }
        [DataMember]
        public virtual Int32 Counts { get; set; }
        [DataMember]
        public virtual Int32 Locations { get; set; }
        [DataMember]
        public virtual Int32 OrderdetailId { get; set; }
        [DataMember]
        public virtual Decimal PulledQuantity { get; set; }
        [DataMember]
        public virtual Decimal RemainingQuantity { get; set; }
        [DataMember]
        public virtual Int32 ReplenishmentSequence { get; set; }
        [DataMember]
        public virtual Decimal RequiredQuantity { get; set; }
        [DataMember]
        public virtual Decimal? UomQuantity { get; set; }
        [DataMember]
        public virtual LicensePlate LicensePlateTo { get; set; }
        [DataMember]
        public virtual Location CartLocation { get; set; }
        [DataMember]
        public virtual String AisleCode { get; set; }
        [DataMember]
        public virtual String AssetCode { get; set; }
        [DataMember]
        public virtual String ItemCode { get; set; }
        [DataMember]
        public virtual String ItemDescription { get; set; }
        [DataMember]
        public virtual String ItemLongDescription { get; set; }
        [DataMember]
        public virtual String LicensePlateCode { get; set; }
        [DataMember]
        public virtual String LocationCodeFrom { get; set; }
        [DataMember]
        public virtual String LocationCodeTo { get; set; }
        [DataMember]
        public virtual String OrganizationCode { get; set; }
        [DataMember]
        public virtual String SerialNumbers { get; set; }
        [DataMember]
        public virtual String ShowQuantity { get; set; }
        [DataMember]
        public virtual String TaskCode { get; set; }
        [DataMember]
        public virtual String UomDescription { get; set; }
        [DataMember]
        public virtual String UpcCode { get; set; }
        [DataMember]
        public virtual String ZoneCode { get; set; }

        #endregion

        #region Constructor

        public InventoryTask()
        {
            //
        }

        #endregion

        #region Methods.Private.Transactions

        private void WriteCycleCount(Decimal quantity)
        {
            TransactionType type = Entity.Retrieve<TransactionType>(InventoryTransactions.CycleCount);
            ItemTransaction transaction = Entity.Activate<ItemTransaction>();
            transaction.InventoryItem = _inventoryItem;
            transaction.Item = _item;
            transaction.LicensePlateFrom = _licensePlate;
            transaction.LocationFrom = _locationFrom;
            transaction.Occurred = DateTime.Now;
            transaction.ParticipantBy = Registry.Find<UserAccount>().OrganizationParticipant;
            transaction.Quantity = quantity;
            transaction.Task = _task;
            transaction.TransactionType = type;
            //
            Repositories.Get<ItemTransaction>().Add(transaction);
        }

        private void WriteDiscrepancy(Decimal quantity)
        {
            if (_quantity == quantity) return;
            //
            ItemTransaction transaction = Entity.Activate<ItemTransaction>();
            transaction.InventoryItem = _inventoryItem;
            transaction.Item = _item;
            transaction.LicensePlateFrom = _licensePlate;
            transaction.LocationFrom = _locationFrom;
            transaction.Occurred = DateTime.Now;
            transaction.ParticipantBy = Registry.Find<UserAccount>().OrganizationParticipant;
            transaction.Quantity = (_quantity > quantity) ? _quantity - quantity : quantity - _quantity;
            transaction.Task = _task;
            transaction.TransactionType = (_quantity > quantity) ?
                Entity.Retrieve<TransactionType>(InventoryTransactions.MissingItem) :
                Entity.Retrieve<TransactionType>(InventoryTransactions.FoundItem);
            //
            Repositories.Get<ItemTransaction>().Add(transaction);
        }

        private void WriteKitBuild()
        {
            TransactionType type = Entity.Retrieve<TransactionType>(InventoryTransactions.KitBuild);
            ItemTransaction transaction = Entity.Activate<ItemTransaction>();
            transaction.InventoryItem = _inventoryItem;
            transaction.Item = _inventoryItem.Item;
            transaction.LocationTo = _locationTo;
            transaction.LotNumber = null;
            transaction.Occurred = DateTime.Now;
            transaction.ParticipantBy = Registry.Find<UserAccount>().OrganizationParticipant;
            transaction.Quantity = 1;
            transaction.SerialNumber = null;
            transaction.StatusCode = _inventoryItem.StatusCode;
            transaction.Task = _task;
            transaction.TransactionType = type;
            //
            Repositories.Get<ItemTransaction>().Add(transaction);
        }

        #endregion

        #region Methods.Public.Assets

        public virtual void ClearCycleCount()
        {
            Boolean consumable = "Y".Equals(_inventoryItem.Item.ItemType.Consumable);
            String type = _task.TransactionType.TransactionTypeCode;
            //
            if (type.Equals(CodeValue.GetCode(InventoryTransactions.CycleCountEmployee)))
            {
                this.WriteCycleCount(this.PulledQuantity);
                //
                if (consumable)
                {
                    DetachedCriteria criteria = DetachedCriteria.For<InventoryTask>()
                        .Add("Quantity", 0)
                        .Add("Task", _task)
                        .SetMaxResults(1);
                    InventoryTask main = Repositories.Get<InventoryTask>().Retrieve(criteria);
                    //
                    criteria = DetachedCriteria.For<ParticipantAsset>()
                        .Add("InventoryItem", _inventoryItem)
                        .Add("OrganizationParticipant", main.OrganizationParticipant)
                        .Add("Quantity", ">", 0)
                        .SetMaxResults(1);
                    ParticipantAsset issuance = Repositories.Get<ParticipantAsset>().Retrieve(criteria);
                    if (issuance != null) issuance.ClearCycleCount();
                }
                else _inventoryItem.ClearCycleCount();
            }
            else if (type.Equals(CodeValue.GetCode(InventoryTransactions.CycleCountParent)))
            {
                this.WriteCycleCount(this.PulledQuantity);
                //
                if (consumable)
                {
                    DetachedCriteria criteria = DetachedCriteria.For<InventoryTask>()
                        .Add("Quantity", 0)
                        .Add("Task", _task)
                        .SetMaxResults(1);
                    InventoryTask main = Repositories.Get<InventoryTask>().Retrieve(criteria);
                    //
                    criteria = DetachedCriteria.For<InventoryItemDetail>()
                        .Add("InventoryItem", _inventoryItem)
                        .Add("KitHeader", null)
                        .Add("ParentInventoryItem", main.InventoryItem)
                        .SetMaxResults(1);
                    InventoryItemDetail issuance = Repositories.Get<InventoryItemDetail>().Retrieve(criteria);
                    if (issuance != null) issuance.ClearCycleCount();
                }
                else _inventoryItem.ClearCycleCount();
            }
            else
            {
                _inventoryItem.ClearCycleCount();
                if (consumable && _quantity != this.PulledQuantity)
                {
                    if (_quantity < this.PulledQuantity)
                    {
                        this.WriteCycleCount(this.PulledQuantity);
                        _inventoryItem.WriteFoundItem(_task, this.PulledQuantity - _quantity);
                    }
                    else if (_quantity > this.PulledQuantity)
                    {
                        this.WriteCycleCount(_quantity);
                        _inventoryItem.WriteMissingItem(_task, _quantity - this.PulledQuantity);
                    }
                }
                else this.WriteCycleCount(_quantity);
            }
            //
            _statusCode = Entity.Retrieve<StatusCode>(TaskStatuses.Complete);
        }

        public virtual void CreateAssetCode()
        {
            if (_inventoryItem != null)
            {
                this.AssetCode = _inventoryItem.AssetCode;
                if (String.IsNullOrEmpty(this.AssetCode) && _inventoryItem.LicensePlate != null)
                    this.AssetCode = _inventoryItem.LicensePlate.LicensePlateCode;
            }
            else if (Registry.Find<CompanyLocationType>() != null)
            {
                LicenseType inventory = Entity.Retrieve<LicenseType>(InventoryLicenseTypes.InventoryItem);
                this.AssetCode = Upp.Irms.Domain.LicensePlateCode.GetCurrentValue(inventory);
            }
            else
            {
                Boolean? generate = BusinessRule.RetrieveBoolean("ASSETGEN");
                if (generate.HasValue && generate.Value) this.AssetCode = EntityCode.GetCurrentValue(EntityCodes.Kit);
                else this.AssetCode = null;
            }
        }

        public virtual void CreateKitAsset()
        {
            if (_inventoryItem != null) return;
            //
            DetachedCriteria criteria = DetachedCriteria.For<InventoryTask>()
                .Add("Item", "!=", null)
                .Add("Task", _task)
                .SetMaxResults(1);
            InventoryTask main = Repositories.Get<InventoryTask>().Retrieve(criteria);
            //
            CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
            StatusCode pending = Entity.Retrieve<StatusCode>(InventoryStatuses.Pending);
            //
            if (warehouse != null)
            {
                LicensePlate plate = Entity.Activate<LicensePlate>();
                plate.CompanyLocationType = warehouse;
                plate.LicensePlateCode = this.AssetCode;
                plate.LicenseType = Entity.Retrieve<LicenseType>(InventoryLicenseTypes.InventoryItem);
                Repositories.Get<LicensePlate>().Add(plate);
                //
                plate.ChangeLocation(_locationTo);
                //
                _inventoryItem = Entity.Activate<InventoryItem>();
                _inventoryItem.CompanyLocationType = warehouse;
                _inventoryItem.Item = main.Item;
                _inventoryItem.LicensePlate = plate;
                _inventoryItem.Location = _locationTo;
                _inventoryItem.Quantity = 1;
                _inventoryItem.StatusCode = pending;
                _inventoryItem.UnitOfMeasure = main.Item.UnitOfMeasure;
                //
                if (_inventoryItem.UnitOfMeasure == null)
                    _inventoryItem.UnitOfMeasure = Entity.Retrieve<UnitOfMeasure>(InventoryUoms.Each);
            }
            else
            {
                _inventoryItem = InventoryItem.Create(main.Item, _locationTo, pending, 1);
                _inventoryItem.AssetCode = this.AssetCode;
            }
            Repositories.Get<InventoryItem>().Add(_inventoryItem);
            //
            _statusCode = Entity.Retrieve<StatusCode>(TaskStatuses.Active);
        }

        public virtual void CreateKitTasks()
        {
            StatusCode active = Entity.Retrieve<StatusCode>(TaskStatuses.Active);
            if (active.SameAs(_task.StatusCode)) return;
            //
            for (int i = 0; i < _quantity; i++)
            {
                InventoryTask child = InventoryTask.Create(_task);
                child.AgencyOrganizationalUnit = _agencyOrganizationalUnit;
                child.CompanyLocationType = _companyLocationType;
                child.Quantity = 1;
                child.StatusCode = Entity.Retrieve<StatusCode>(TaskStatuses.Open);
                Repositories.Get<InventoryTask>().Add(child);
            }
            //
            _statusCode = active;
            _task.Activate();
        }

        public virtual void FindDescription()
        {
            if (_inventoryItem != null) this.ItemDescription = String.Format("Parent Asset: {0}", _inventoryItem.AssetCode);
            else if (_item != null) this.ItemDescription = String.Format("Item: {0}", _item.ItemCode);
            else if (_locationFrom != null) this.ItemDescription = String.Format("Location: {0}", _locationFrom.LocationCode);
            else if (_organizationParticipant != null)
            {
                Participant participant = _organizationParticipant.ParticipantRole.Participant;
                //
                _organizationParticipant.FullName = String.Format("{0} {1}", participant.FirstName, participant.LastName);
                this.ItemDescription = String.Format("Employee: {0} {1}", participant.FirstName, participant.LastName);
            }
        }

        public virtual void FinishKitBuild()
        {
            _inventoryItem.StatusCode = Entity.Retrieve<StatusCode>(InventoryStatuses.Available);
            Repositories.Get<InventoryItem>().Update(_inventoryItem);
            //
            this.WriteKitBuild();
            //
            _statusCode = Entity.Retrieve<StatusCode>(TaskStatuses.Complete);
            //
            DetachedCriteria criteria = DetachedCriteria.For<InventoryTask>()
                .Add("Item", null)
                .Add("StatusCode", "!=", _statusCode)
                .Add("Task", _task)
                .SetProjection(Projections.Count("Id"));
            Int32 active = Repositories.Get<InventoryTask>().Function<Int32>(criteria);
            if (active == 0)
            {
                criteria = DetachedCriteria.For<InventoryTask>()
                    .Add("Item", "!=", null)
                    .Add("Task", _task)
                    .SetMaxResults(1);
                InventoryTask main = Repositories.Get<InventoryTask>().Retrieve(criteria);
                if (main != null) main.Complete();
                //
                _task.Completed = DateTime.Now;
                _task.DateModified = DateTime.Now;
                _task.StatusCode = _statusCode;
                _task.UserModified = Registry.Find<UserAccount>().UserName;
                Repositories.Get<Task>().Update(_task);
            }
        }

        #endregion

        #region Methods.Public

        public virtual void CalculateRemainingQuantity()
        {
            DetachedCriteria criteria = DetachedCriteria.For<InventoryTask>()
                .Add("Item", "!=", null)
                .Add("Task", _task)
                .SetMaxResults(1);
            InventoryTask main = Repositories.Get<InventoryTask>().Retrieve(criteria);
            //
            this.ItemCode = main.Item.ItemCode;
            this.ItemDescription = main.Item.Description;
            this.RequiredQuantity = main.Quantity;
            //
            criteria = DetachedCriteria.For<ItemTransaction>()
                .Add("Item", main.Item)
                .Add("Task", _task)
                .Add("TransactionType", _task.TransactionType)
                .SetProjection(Projections.Sum("Quantity"));
            Int32 built = Repositories.Get<ItemTransaction>().Function<Int32>(criteria);
            this.RemainingQuantity = main.Quantity - built;
        }

        public virtual void ChangeStatus(StatusCode status)
        {
            _dateModified = DateTime.Now;
            _statusCode = status;
            _userModified = Registry.Find<UserAccount>().UserName;
            Repositories.Get<InventoryTask>().Update(this);
        }

        public virtual void ChangeQuantity(Int32 qunatity)
        {
            _dateModified = DateTime.Now;
            _quantity = qunatity;
            _userModified = Registry.Find<UserAccount>().UserName;
            Repositories.Get<InventoryTask>().Update(this);
        }

        public virtual void Complete()
        {
            _dateModified = DateTime.Now;
            _statusCode = Entity.Retrieve<StatusCode>(TaskStatuses.Complete);
            _userModified = Registry.Find<UserAccount>().UserName;
            Repositories.Get<InventoryTask>().Update(this);
        }

        public virtual void CycleCount(Int32 quantity)
        {
            CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
            StatusCode complete = Entity.Retrieve<StatusCode>(TaskStatuses.Complete);
            TransactionType count = Entity.Retrieve<TransactionType>(InventoryTransactions.CycleCount);
            //
            DetachedCriteria criteria = DetachedCriteria.For<Item>()
                .Add("CompanyLocationType", warehouse)
                .Add("ItemCode", this.ItemCode)
                .SetMaxResults(1);
            _item = Repositories.Get<Item>().Retrieve(criteria);
            //
            if (String.IsNullOrEmpty(this.LicensePlateCode)) _licensePlate = null;
            else
            {
                criteria = DetachedCriteria.For<LicensePlate>()
                    .Add("CompanyLocationType", warehouse)
                    .Add("LicensePlateCode", this.LicensePlateCode)
                    .SetMaxResults(1);
                _licensePlate = Repositories.Get<LicensePlate>().Retrieve(criteria);
            }
            //
            criteria = DetachedCriteria.For<Location>()
                .Add("CompanyLocationType", warehouse)
                .Add("LocationCode", this.LocationCodeFrom)
                .SetMaxResults(1);
            _locationFrom = Repositories.Get<Location>().Retrieve(criteria);
            //
            criteria = DetachedCriteria.For<Task>()
                .Add("CompanyLocationType", warehouse)
                .Add("TaskCode", this.TaskCode)
                .Add("TransactionType", count)
                .SetMaxResults(1);
            _task = Repositories.Get<Task>().Retrieve(criteria);
            //
            criteria = DetachedCriteria.For<InventoryTask>()
                .Add("Item", _item)
                .Add("LocationFrom", _locationFrom)
                .Add("StatusCode", "!=", complete)
                .Add("Task", _task);
            if (_licensePlate != null) criteria = criteria.Add("LicensePlate", _licensePlate);
            IList<InventoryTask> tasks = Repositories.Get<InventoryTask>().List(criteria);
            foreach (InventoryTask element in tasks) element.ChangeStatus(complete);
            //
            if (_quantity != quantity)
            {
                criteria = DetachedCriteria.For<InventoryCountProfile>()
                        .Add("CompanyLocationType", Registry.Find<CompanyLocationType>())
                        .SetMaxResults(1);
                InventoryCountProfile profile = Repositories.Get<InventoryCountProfile>().Retrieve(criteria);
                //
                if (profile != null && "Y".Equals(profile.CcAdjustOnCount))
                {
                    criteria = DetachedCriteria.For<InventoryAdjustmentCode>()
                        .Add("Code", profile.CcDecreaseAdjustmentCode)
                        .Add("CompanyLocationType", Registry.Find<CompanyLocationType>())
                        .SetMaxResults(1);
                    InventoryAdjustmentCode decreaseAdjustmentCode = Repositories.Get<InventoryAdjustmentCode>().Retrieve(criteria);
                    //
                    criteria = DetachedCriteria.For<InventoryAdjustmentCode>()
                        .Add("Code", profile.CcIncreaseAdjustmentCode)
                        .Add("CompanyLocationType", Registry.Find<CompanyLocationType>())
                        .SetMaxResults(1);
                    InventoryAdjustmentCode increaseAdjustmentCode = Repositories.Get<InventoryAdjustmentCode>().Retrieve(criteria);
                    //
                    foreach (InventoryTask element in tasks)
                    {
                        if (quantity <= 0)
                            element.InventoryItem.AdjustCycleCountQuantity(decreaseAdjustmentCode, 0M);
                        else
                        {
                            if (quantity < _quantity)
                                element.InventoryItem.AdjustCycleCountQuantity(decreaseAdjustmentCode, quantity);
                            else if (quantity > _quantity)
                                element.InventoryItem.AdjustCycleCountQuantity(increaseAdjustmentCode, quantity);
                        }
                        //
                        break;
                    }
                }
            }
            // find the "placeholder" task and close it:
            criteria = DetachedCriteria.For<InventoryTask>()
                .AddOr("Item", null, "LocationFrom", null)
                .Add("Quantity", 0)
                .Add("StatusCode", "!=", complete)
                .Add("Task", _task);
            tasks = Repositories.Get<InventoryTask>().List(criteria);
            foreach (InventoryTask element in tasks) element.ChangeStatus(complete);
            //
            criteria = DetachedCriteria.For<InventoryTask>()
                .Add("LocationFrom", _locationFrom)
                .Add("StatusCode", "!=", complete)
                .Add("Task.TransactionType", count);
            Int32 remaining = Repositories.Get<InventoryTask>().Count(criteria);
            if (remaining == 0) _locationFrom.ClearCycleCount();
            //
            criteria = DetachedCriteria.For<InventoryTask>()
                .Add("StatusCode", "!=", complete)
                .Add("Task", _task);
            remaining = Repositories.Get<InventoryTask>().Count(criteria);
            if (remaining == 0)
            {
                _task.Completed = _task.DateModified = DateTime.Now;
                _task.StatusCode = complete;
                _task.UserModified = Registry.Find<UserAccount>().UserName;
                Repositories.Get<Task>().Update(_task);
            }
            //
            this.WriteCycleCount(this.PulledQuantity);
            this.WriteDiscrepancy(this.PulledQuantity);
        }

        public virtual void FindCountProfile()
        {
            DetachedCriteria criteria = DetachedCriteria.For<InventoryCountProfile>()
                .Add("CompanyLocationType", Registry.Find<CompanyLocationType>())
                .SetMaxResults(1);
            InventoryCountProfile profile = Repositories.Get<InventoryCountProfile>().Retrieve(criteria);
            //
            if (profile != null) this.ShowQuantity = profile.CcShowQuantity;
        }

        public virtual void FindUomInfo()
        {
            CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
            String inventory = CodeValue.GetCode(FunctionalAreas.Inventory);
            //
            DetachedCriteria criteria = DetachedCriteria.For<Location>()
                .Add("CompanyLocationType", warehouse)
                .Add("LocationCode", this.LocationCodeTo)
                .SetMaxResults(1);
            _locationTo = Repositories.Get<Location>().Retrieve(criteria);
            if (_locationTo.ReplenishmentUom == null)
            {
                String error = String.Format("Missing Replenishment UOM for location {0}.", this.LocationCodeTo);
                throw new Exception(error);
            }
            //
            criteria = DetachedCriteria.For<UnitOfMeasure>()
                .CreateAlias("FunctionalAreaCode", "fac")
                .Add(Expression.Eq("fac.Code", inventory))
                .Add(Expression.Eq("UomCode", _locationTo.ReplenishmentUom.UomCode))
                .SetMaxResults(1);
            UnitOfMeasure unitOfMeasure = Repositories.Get<UnitOfMeasure>().Retrieve(criteria);
            if (unitOfMeasure == null)
            {
                String error = String.Format("Unable to find UOM for {0}.", _locationTo.ReplenishmentUom.UomCode);
                throw new Exception(error);
            }
            this.UomDescription = String.Format(" {0}", Inflector.Pluralize(unitOfMeasure.Description));
            //
            criteria = DetachedCriteria.For<ItemUomRelationship>()
                .Add("Active", "A")
                .Add("Item.CompanyLocationType", warehouse)
                .Add("Item.ItemCode", this.ItemCode)
                .Add("UnitOfMeasure", unitOfMeasure);
            ItemUomRelationship relationship = Repositories.Get<ItemUomRelationship>().Retrieve(criteria);
            if (relationship == null)
            {
                String error = String.Format("Missing Item UOM relationship for item {0} and UOM {1}.", this.ItemCode, this.UomDescription);
                throw new Exception(error);
            }
            else this.UomQuantity = Converter.ToInt32(relationship.Factor);
            //
            this.Quantity = Round.Down(Converter.ToDecimal(this.Quantity / this.UomQuantity.Value));
        }

        #endregion

        #region Methods.Public.Replenish

        public virtual void BuildCart()
        {
            CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
            StatusCode complete = Entity.Retrieve<StatusCode>(TaskStatuses.Complete);
            TransactionType replenishment = Entity.Retrieve<TransactionType>(InventoryTransactions.Replenishment);
            //
            DetachedCriteria criteria = DetachedCriteria.For<Item>()
                .Add("CompanyLocationType", warehouse)
                .Add("ItemCode", this.ItemCode)
                .SetMaxResults(1);
            _item = Repositories.Get<Item>().Retrieve(criteria);
            //
            if (String.IsNullOrEmpty(this.LicensePlateCode)) _licensePlate = null;
            else
            {
                criteria = DetachedCriteria.For<LicensePlate>()
                    .Add("CompanyLocationType", warehouse)
                    .Add("LicensePlateCode", this.LicensePlateCode)
                    .SetMaxResults(1);
                _licensePlate = Repositories.Get<LicensePlate>().Retrieve(criteria);
            }
            //
            criteria = DetachedCriteria.For<Location>()
                .Add("CompanyLocationType", warehouse)
                .Add("LocationCode", this.LocationCodeFrom)
                .SetMaxResults(1);
            _locationFrom = Repositories.Get<Location>().Retrieve(criteria);
            //
            if (this.LicensePlateTo != null)
            {
                Location cart = this.CartLocation;
                Location pallet = this.LicensePlateTo.CurrentLocation;
                //
                this.LicensePlateTo.ChangeLocation(cart);
            }
            //
            if ("Y".Equals(_item.Serialized) &&
                ("B".Equals(_item.SerialCaptureAt) || "I".Equals(_item.SerialCaptureAt)))
                _locationFrom.ReplenishInventory(_item, this.CartLocation, this.LicensePlateTo, this.SerialNumbers);
            else _locationFrom.Replenish(_item, this.CartLocation, this.LicensePlateTo, this.PulledQuantity);
            //
            criteria = DetachedCriteria.For<Task>()
               .Add("CompanyLocationType", warehouse)
               .Add("TaskCode", this.TaskCode)
               .Add("TransactionType", replenishment)
               .SetMaxResults(1);
            _task = Repositories.Get<Task>().Retrieve(criteria);
            //
            criteria = DetachedCriteria.For<InventoryTask>()
                .Add("Item", _item)
                .Add("LocationFrom", _locationFrom)
                .Add("StatusCode", "!=", complete)
                .Add("Task", _task);
            if (_licensePlate != null) criteria = criteria.Add("LicensePlate", _licensePlate);
            IList<InventoryTask> tasks = Repositories.Get<InventoryTask>().List(criteria);
            //
            Int32 inventoryTaskCount = 1;
            Decimal pulled = this.PulledQuantity;
            foreach (InventoryTask element in tasks)
            {
                if (pulled <= 0) break;
                //
                if (pulled < element.Quantity)
                {
                    InventoryTask split = Entity.Clone<InventoryTask>(element);
                    split.Quantity = element.Quantity - pulled;
                    Repositories.Get<InventoryTask>().Add(split);
                    //
                    element.Quantity = pulled;
                    //
                    pulled = 0;
                }
                else
                {
                    if (inventoryTaskCount == tasks.Count && pulled > element.Quantity)
                    {
                        InventoryTask split = Entity.Clone<InventoryTask>(element);
                        split.CompanyLocationZoneFrom = this.CartLocation.CompanyLocationZone;
                        split.LocationFrom = this.CartLocation;
                        split.Quantity = pulled - element.Quantity;
                        Repositories.Get<InventoryTask>().Add(split);
                        //
                        pulled = 0;
                    }
                    else pulled -= element.Quantity;
                }
                //
                element.CompanyLocationZoneFrom = this.CartLocation.CompanyLocationZone;
                element.DateModified = DateTime.Now;
                element.LocationFrom = this.CartLocation;
                element.UserModified = Registry.Find<UserAccount>().UserName;
                Repositories.Get<InventoryTask>().Update(element);
                //
                inventoryTaskCount++;
            }
        }

        public virtual void CloseTask()
        {
            CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
            StatusCode complete = Entity.Retrieve<StatusCode>(TaskStatuses.Complete);
            TransactionType replenishment = Entity.Retrieve<TransactionType>(InventoryTransactions.Replenishment);
            //
            DetachedCriteria criteria = DetachedCriteria.For<Item>()
                .Add("CompanyLocationType", warehouse)
                .Add("ItemCode", this.ItemCode)
                .SetMaxResults(1);
            _item = Repositories.Get<Item>().Retrieve(criteria);
            //
            if (String.IsNullOrEmpty(this.LicensePlateCode)) _licensePlate = null;
            else
            {
                criteria = DetachedCriteria.For<LicensePlate>()
                    .Add("CompanyLocationType", warehouse)
                    .Add("LicensePlateCode", this.LicensePlateCode)
                    .SetMaxResults(1);
                _licensePlate = Repositories.Get<LicensePlate>().Retrieve(criteria);
            }
            //
            criteria = DetachedCriteria.For<Location>()
                .Add("CompanyLocationType", warehouse)
                .Add("LocationCode", this.LocationCodeFrom)
                .SetMaxResults(1);
            _locationFrom = Repositories.Get<Location>().Retrieve(criteria);
            //
            criteria = DetachedCriteria.For<Location>()
                .Add("CompanyLocationType", warehouse)
                .Add("LocationCode", this.LocationCodeTo)
                .SetMaxResults(1);
            _locationTo = Repositories.Get<Location>().Retrieve(criteria);
            //
            criteria = DetachedCriteria.For<Task>()
               .Add("CompanyLocationType", warehouse)
               .Add("TaskCode", this.TaskCode)
               .Add("TransactionType", replenishment)
               .SetMaxResults(1);
            _task = Repositories.Get<Task>().Retrieve(criteria);
            //
            criteria = DetachedCriteria.For<InventoryTask>()
                .Add("Item", _item)
                .Add("LocationFrom", _locationFrom)
                .Add("StatusCode", "!=", complete)
                .Add("Task", _task);
            if (_licensePlate != null) criteria = criteria.Add("LicensePlate", _licensePlate);
            IList<InventoryTask> tasks = Repositories.Get<InventoryTask>().List(criteria);
            //
            foreach (InventoryTask element in tasks)
            {
                element.DateModified = DateTime.Now;
                element.StatusCode = complete;
                element.UserModified = Registry.Find<UserAccount>().UserName;
                Repositories.Get<InventoryTask>().Update(element);
            }
            //
            criteria = DetachedCriteria.For<InventoryTask>()
               .Add("StatusCode", "!=", complete)
               .Add("Task", _task);
            Int32 remaining = Repositories.Get<InventoryTask>().Count(criteria);
            if (remaining == 0)
            {
                _task.Completed = _task.DateModified = DateTime.Now;
                _task.StatusCode = complete;
                _task.UserModified = Registry.Find<UserAccount>().UserName;
                Repositories.Get<Task>().Update(_task);
            }
        }

        public virtual void ReleaseCart()
        {
            CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
            StatusCode complete = Entity.Retrieve<StatusCode>(TaskStatuses.Complete);
            TransactionType replenishment = Entity.Retrieve<TransactionType>(InventoryTransactions.Replenishment);
            //
            DetachedCriteria criteria = DetachedCriteria.For<Item>()
                .Add("CompanyLocationType", warehouse)
                .Add("ItemCode", this.ItemCode)
                .SetMaxResults(1);
            _item = Repositories.Get<Item>().Retrieve(criteria);
            //
            if (String.IsNullOrEmpty(this.LicensePlateCode)) _licensePlate = null;
            else
            {
                criteria = DetachedCriteria.For<LicensePlate>()
                    .Add("CompanyLocationType", warehouse)
                    .Add("LicensePlateCode", this.LicensePlateCode)
                    .SetMaxResults(1);
                _licensePlate = Repositories.Get<LicensePlate>().Retrieve(criteria);
            }
            //
            criteria = DetachedCriteria.For<Location>()
                .Add("CompanyLocationType", warehouse)
                .Add("LocationCode", this.LocationCodeFrom)
                .SetMaxResults(1);
            _locationFrom = Repositories.Get<Location>().Retrieve(criteria);
            //
            criteria = DetachedCriteria.For<Location>()
                .Add("CompanyLocationType", warehouse)
                .Add("LocationCode", this.LocationCodeTo)
                .SetMaxResults(1);
            _locationTo = Repositories.Get<Location>().Retrieve(criteria);
            //
            if ("Y".Equals(_item.Serialized) &&
                 ("B".Equals(_item.SerialCaptureAt) || "I".Equals(_item.SerialCaptureAt)))
                _locationFrom.ReplenishInventory(_item, _locationTo, this.LicensePlateTo, this.SerialNumbers, true);
            else _locationFrom.Replenish(_item, _locationTo, this.LicensePlateTo, this.PulledQuantity, true);
            //
            criteria = DetachedCriteria.For<Task>()
               .Add("CompanyLocationType", warehouse)
               .Add("TaskCode", this.TaskCode)
               .Add("TransactionType", replenishment)
               .SetMaxResults(1);
            _task = Repositories.Get<Task>().Retrieve(criteria);
            //
            criteria = DetachedCriteria.For<InventoryTask>()
                .Add("Item", _item)
                .Add("LocationFrom", _locationFrom)
                .Add("StatusCode", "!=", complete)
                .Add("Task", _task);
            if (_licensePlate != null) criteria = criteria.Add("LicensePlate", _licensePlate);
            IList<InventoryTask> tasks = Repositories.Get<InventoryTask>().List(criteria);
            //
            Decimal pulled = this.PulledQuantity;
            foreach (InventoryTask element in tasks)
            {
                if (pulled <= 0) break;
                //
                if (pulled < element.Quantity)
                {
                    InventoryTask split = Entity.Clone<InventoryTask>(element);
                    split.Quantity = element.Quantity - pulled;
                    Repositories.Get<InventoryTask>().Add(split);
                    //
                    element.Quantity = pulled;
                    //
                    pulled = 0;
                }
                else pulled -= element.Quantity;
                //
                element.DateModified = DateTime.Now;
                element.StatusCode = complete;
                element.UserModified = Registry.Find<UserAccount>().UserName;
                Repositories.Get<InventoryTask>().Update(element);
            }
            //
            criteria = DetachedCriteria.For<InventoryTask>()
               .Add("StatusCode", "!=", complete)
               .Add("Task", _task);
            Int32 remaining = Repositories.Get<InventoryTask>().Count(criteria);
            if (remaining == 0)
            {
                _task.Completed = _task.DateModified = DateTime.Now;
                _task.StatusCode = complete;
                _task.UserModified = Registry.Find<UserAccount>().UserName;
                Repositories.Get<Task>().Update(_task);
            }
            //
            Boolean? replenishByXItems = BusinessRule.RetrieveBoolean("11120");
            if (replenishByXItems.HasValue && replenishByXItems.Value)
                if (_locationTo.Item != null && !_item.Equals(_locationTo.Item)) this.AdjustSubstitutes(_item, _locationTo, this.PulledQuantity);
        }

        public virtual void Replenish()
        {
            CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
            StatusCode complete = Entity.Retrieve<StatusCode>(TaskStatuses.Complete);
            TransactionType replenishment = Entity.Retrieve<TransactionType>(InventoryTransactions.Replenishment);
            //
            DetachedCriteria criteria = DetachedCriteria.For<Item>()
                .Add("CompanyLocationType", warehouse)
                .Add("ItemCode", this.ItemCode)
                .SetMaxResults(1);
            _item = Repositories.Get<Item>().Retrieve(criteria);
            //
            if (String.IsNullOrEmpty(this.LicensePlateCode)) _licensePlate = null;
            else
            {
                criteria = DetachedCriteria.For<LicensePlate>()
                    .Add("CompanyLocationType", warehouse)
                    .Add("LicensePlateCode", this.LicensePlateCode)
                    .SetMaxResults(1);
                _licensePlate = Repositories.Get<LicensePlate>().Retrieve(criteria);
            }
            //
            criteria = DetachedCriteria.For<Location>()
                .Add("CompanyLocationType", warehouse)
                .Add("LocationCode", this.LocationCodeFrom)
                .SetMaxResults(1);
            _locationFrom = Repositories.Get<Location>().Retrieve(criteria);
            //
            criteria = DetachedCriteria.For<Location>()
                .Add("CompanyLocationType", warehouse)
                .Add("LocationCode", this.LocationCodeTo)
                .SetMaxResults(1);
            _locationTo = Repositories.Get<Location>().Retrieve(criteria);
            //
            if ("Y".Equals(_item.Serialized) &&
                ("B".Equals(_item.SerialCaptureAt) || "I".Equals(_item.SerialCaptureAt)))
                _locationFrom.ReplenishInventory(_item, _locationTo, this.LicensePlateTo, this.SerialNumbers);
            else _locationFrom.Replenish(_item, _locationTo, this.LicensePlateTo, this.PulledQuantity);
            //
            criteria = DetachedCriteria.For<Task>()
               .Add("CompanyLocationType", warehouse)
               .Add("TaskCode", this.TaskCode)
               .Add("TransactionType", replenishment)
               .SetMaxResults(1);
            _task = Repositories.Get<Task>().Retrieve(criteria);
            //
            criteria = DetachedCriteria.For<InventoryTask>()
                .Add("Item", _item)
                .Add("LocationFrom", _locationFrom)
                .Add("StatusCode", "!=", complete)
                .Add("Task", _task);
            if (_licensePlate != null) criteria = criteria.Add("LicensePlate", _licensePlate);
            IList<InventoryTask> tasks = Repositories.Get<InventoryTask>().List(criteria);
            //
            Decimal pulled = this.PulledQuantity;
            foreach (InventoryTask element in tasks)
            {
                if (pulled <= 0) break;
                //
                if (pulled < element.Quantity)
                {
                    InventoryTask split = Entity.Clone<InventoryTask>(element);
                    split.Quantity = element.Quantity - pulled;
                    Repositories.Get<InventoryTask>().Add(split);
                    //
                    element.Quantity = pulled;
                    //
                    pulled = 0;
                }
                else pulled -= element.Quantity;
                //
                element.DateModified = DateTime.Now;
                element.StatusCode = complete;
                element.UserModified = Registry.Find<UserAccount>().UserName;
                Repositories.Get<InventoryTask>().Update(element);
            }
            //
            criteria = DetachedCriteria.For<InventoryTask>()
                .Add("StatusCode", "!=", complete)
                .Add("Task", _task);
            Int32 remaining = Repositories.Get<InventoryTask>().Count(criteria);
            if (remaining == 0)
            {
                _task.Completed = _task.DateModified = DateTime.Now;
                _task.StatusCode = complete;
                _task.UserModified = Registry.Find<UserAccount>().UserName;
                Repositories.Get<Task>().Update(_task);
            }
            //
            Boolean? replenishByXItems = BusinessRule.RetrieveBoolean("11120");
            if (replenishByXItems.HasValue && replenishByXItems.Value)
                if (_locationTo.Item != null && !_item.Equals(_locationTo.Item)) this.AdjustSubstitutes(_item, _locationTo, this.PulledQuantity);
        }

        #endregion

        #region Methods.Public.xItem.Adjustments

        private void AdjustSubstitutes(Item item, Location location, Decimal quantity)
        {
            String replenihsFromCode = BusinessRule.RetrieveString("11140");
            if (string.IsNullOrEmpty(replenihsFromCode))
            {
                String error = "Business Rule not configured for Replenish From Substitute Inventory Adjustment Code";
                throw new Exception(error);
            }
            //
            InventoryAdjustmentCode adjustment = Entity.Retrieve<InventoryAdjustmentCode>(replenihsFromCode, null);
            if (adjustment == null)
            {
                String error = String.Format("Inventory Adjustment Code {0} is not configured", replenihsFromCode);
                throw new Exception(error);
            }
            //
            this.AdjustQuantity(-quantity, item, location, adjustment);
            //
            DetachedCriteria criteria = DetachedCriteria.For<ItemSubstitute>()
                .CreateAlias("ItemUomRelationship", "iur")
                .Add(Expression.Eq("Active", "A"))
                .Add(Expression.Eq("iur.Active", "A"))
                .Add(Expression.Eq("iur.Item", location.Item))
                .Add(Expression.Eq("Item", item))
                .SetMaxResults(1);
            ItemSubstitute substitute = Repositories.Get<ItemSubstitute>().Retrieve(criteria);
            if (substitute != null && substitute.ItemUomRelationship != null && substitute.ItemUomRelationship.Factor > 0)
            {
                String replenishToCode = BusinessRule.RetrieveString("11141");
                if (string.IsNullOrEmpty(replenishToCode))
                {
                    String error = "Business Rule not configured for Replenish To Substitute Inventory Adjustment Code";
                    throw new Exception(error);
                }
                //
                adjustment = Entity.Retrieve<InventoryAdjustmentCode>(replenishToCode, null);
                if (adjustment == null)
                {
                    String error = String.Format("Inventory Adjustment Code {0} is not configured", replenishToCode);
                    throw new Exception(error);
                }
                //
                this.AdjustQuantity(quantity * substitute.ItemUomRelationship.Factor, location.Item, location, adjustment);
            }
        }

        private void AdjustQuantity(decimal quantity, Item item, Location location, InventoryAdjustmentCode adjsutmentCode)
        {
            StatusCode available = Entity.Retrieve<StatusCode>(InventoryStatuses.Available);
            DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
                .Add("Item", item)
                .Add("Location", location)
                .Add("StatusCode", available)
                .AddOrder("Received");
            IList<InventoryItem> inventory = Repositories.Get<InventoryItem>().List(criteria);

            Decimal adjustTo = inventory.Sum(e => e.Quantity) + quantity;
            Decimal delta = inventory.Sum(e => e.Quantity) - adjustTo;
            InventoryItem negative = inventory.Where(e => e.Quantity < 0).FirstOrDefault();
            //
            if (negative != null)
            {
                Decimal from = negative.Quantity;
                StatusCode fromStatus = negative.StatusCode;
                //
                negative.ChangeQuantity(negative.Quantity - delta);
                negative.WriteStockAdjustment(null, adjsutmentCode, from, fromStatus);
            }
            else if (delta < 0)
            {
                InventoryItem positive = inventory.Where(e => e.Quantity > 0).FirstOrDefault();
                if (positive != null)
                {
                    Decimal from = positive.Quantity;
                    StatusCode fromStatus = positive.StatusCode;
                    //
                    positive.CreateQuantityShadow(positive.Quantity - delta);
                    positive.ChangeQuantity(positive.Quantity - delta);
                    positive.WriteStockAdjustment(null, adjsutmentCode, from, fromStatus);
                }
            }
            else
                for (int i = 0; i < inventory.Count; i++)
                {
                    if (delta <= 0) break;
                    //
                    Decimal from = inventory[i].Quantity;
                    StatusCode fromStatus = inventory[i].StatusCode;
                    //
                    if (delta >= inventory[i].Quantity)
                    {
                        if (i == inventory.Count - 1)
                        {
                            inventory[i].CreateQuantityShadow(inventory[i].Quantity - delta);
                            inventory[i].ChangeQuantity(inventory[i].Quantity - delta);
                            inventory[i].WriteStockAdjustment(null, adjsutmentCode, from, fromStatus);
                            //
                            break;
                        }
                        else
                        {
                            delta -= inventory[i].Quantity;
                            //
                            inventory[i].CreateQuantityShadow(0);
                            inventory[i].ChangeQuantity(0);
                            inventory[i].WriteStockAdjustment(null, adjsutmentCode, from, fromStatus);
                        }
                    }
                    else
                    {
                        inventory[i].CreateQuantityShadow(inventory[i].Quantity - delta);
                        inventory[i].ChangeQuantity(inventory[i].Quantity - delta);
                        inventory[i].WriteStockAdjustment(null, adjsutmentCode, from, fromStatus);
                        //
                        break;
                    }
                }
        }

        #endregion

        #region Methods.Static

        public static InventoryTask Create(Task task)
        {
            InventoryTask entity = Entity.Activate<InventoryTask>();
            entity.AgencyOrganizationalUnit = Registry.Find<AgencyOrganizationalUnit>();
            entity.CompanyLocationType = Registry.Find<CompanyLocationType>();
            entity.StatusCode = Entity.Retrieve<StatusCode>(TaskStatuses.Open);
            entity.Task = task;
            //
            return entity;
        }

        #endregion
    }
}
