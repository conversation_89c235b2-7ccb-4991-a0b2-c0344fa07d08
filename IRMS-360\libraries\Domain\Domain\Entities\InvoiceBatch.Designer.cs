using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InvoiceBatch : Entity
	{
		#region Fields

		private CompanyLocationType _companyLocationType;
		private DateTime _invoiceBatchEnd;
		private DateTime _invoiceBatchStart;
		private ICollection<InvoiceHeader> _invoiceHeaders = new HashSet<InvoiceHeader>();
		private StatusCode _statusCode;
		private String _invoiceBatchCode;
		private String _invoiceStart;

		#endregion

		#region Properties

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual DateTime InvoiceBatchEnd
		{
			get { return _invoiceBatchEnd; }
			set { _invoiceBatchEnd = value; }
		}

		[DataMember]
		public virtual DateTime InvoiceBatchStart
		{
			get { return _invoiceBatchStart; }
			set { _invoiceBatchStart = value; }
		}

		[DataMember]
		public virtual ICollection<InvoiceHeader> InvoiceHeaders
		{
			get { return _invoiceHeaders; }
			set { _invoiceHeaders = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual String InvoiceBatchCode
		{
			get { return _invoiceBatchCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("InvoiceBatchCode must not be blank or null.");
				else _invoiceBatchCode = value;
			}
		}

		[DataMember]
		public virtual String InvoiceStart
		{
			get { return _invoiceStart; }
			set { _invoiceStart = value; }
		}


		#endregion
	}
}
