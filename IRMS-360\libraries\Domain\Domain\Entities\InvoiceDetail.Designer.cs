using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InvoiceDetail : Entity
	{
		#region Fields

		private BillingRate _billingRate;
		private Decimal _amount;
		private Int32 _lineNumber;
		private InventoryHistory _inventoryHistory;
		private InvoiceHeader _invoiceHeader;
		private ItemTransaction _itemTransaction;
		private Location _location;
		private OrderHeader _orderHeader;
		private OrderService _orderService;
		private PurchaseOrderHeader _purchaseOrderHeader;
		private PurchaseOrderService _purchaseOrderService;
		private ReceiptDetail _receiptDetail;
		private ShipmentDetail _shipmentDetail;
		private String _notes;
		private WorkOrderHeader _workOrderHeader;
		private WorkOrderService _workOrderService;

		#endregion

		#region Properties

		[DataMember]
		public virtual BillingRate BillingRate
		{
			get { return _billingRate; }
			set { _billingRate = value; }
		}

		[DataMember]
		public virtual Decimal Amount
		{
			get { return _amount; }
			set { _amount = value; }
		}

		[DataMember]
		public virtual Int32 LineNumber
		{
			get { return _lineNumber; }
			set { _lineNumber = value; }
		}

		[DataMember]
		public virtual InventoryHistory InventoryHistory
		{
			get { return _inventoryHistory; }
			set { _inventoryHistory = value; }
		}

		[DataMember]
		public virtual InvoiceHeader InvoiceHeader
		{
			get { return _invoiceHeader; }
			set { _invoiceHeader = value; }
		}

		[DataMember]
		public virtual ItemTransaction ItemTransaction
		{
			get { return _itemTransaction; }
			set { _itemTransaction = value; }
		}

		[DataMember]
		public virtual Location Location
		{
			get { return _location; }
			set { _location = value; }
		}

		[DataMember]
		public virtual OrderHeader OrderHeader
		{
			get { return _orderHeader; }
			set { _orderHeader = value; }
		}

		[DataMember]
		public virtual OrderService OrderService
		{
			get { return _orderService; }
			set { _orderService = value; }
		}

		[DataMember]
		public virtual PurchaseOrderHeader PurchaseOrderHeader
		{
			get { return _purchaseOrderHeader; }
			set { _purchaseOrderHeader = value; }
		}

		[DataMember]
		public virtual PurchaseOrderService PurchaseOrderService
		{
			get { return _purchaseOrderService; }
			set { _purchaseOrderService = value; }
		}

		[DataMember]
		public virtual ReceiptDetail ReceiptDetail
		{
			get { return _receiptDetail; }
			set { _receiptDetail = value; }
		}

		[DataMember]
		public virtual ShipmentDetail ShipmentDetail
		{
			get { return _shipmentDetail; }
			set { _shipmentDetail = value; }
		}

		[DataMember]
		public virtual String Notes
		{
			get { return _notes; }
			set { _notes = value; }
		}

		[DataMember]
		public virtual WorkOrderHeader WorkOrderHeader
		{
			get { return _workOrderHeader; }
			set { _workOrderHeader = value; }
		}

		[DataMember]
		public virtual WorkOrderService WorkOrderService
		{
			get { return _workOrderService; }
			set { _workOrderService = value; }
		}


		#endregion
	}
}
