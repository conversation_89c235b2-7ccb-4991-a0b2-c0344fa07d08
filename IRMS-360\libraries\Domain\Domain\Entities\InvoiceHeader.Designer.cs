using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class InvoiceHeader : Entity
	{
		#region Fields

		private CompanyLocationType _companyLocationType;
		private Customer _customer;
		private CustomerBillingPeriod _customerBillingPeriod;
		private DateTime _invoiced;
		private InvoiceBatch _invoiceBatch;
		private ICollection<ApprovalRequest> _approvalRequests = new HashSet<ApprovalRequest>();
		private ICollection<InvoiceDetail> _invoiceDetails = new HashSet<InvoiceDetail>();
		private OrderHeader _orderHeader;
		private PurchaseOrderHeader _purchaseOrderHeader;
		private String _customerPo;
		private String _invoiceGenerated;
		private String _invoiceNumber;
		private String _invoiceTypeCode;
		private String _notes;
		private WorkOrderHeader _workOrderHeader;

		#endregion

		#region Properties

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual Customer Customer
		{
			get { return _customer; }
			set { _customer = value; }
		}

		[DataMember]
		public virtual CustomerBillingPeriod CustomerBillingPeriod
		{
			get { return _customerBillingPeriod; }
			set { _customerBillingPeriod = value; }
		}

		[DataMember]
		public virtual DateTime Invoiced
		{
			get { return _invoiced; }
			set { _invoiced = value; }
		}

		[DataMember]
		public virtual InvoiceBatch InvoiceBatch
		{
			get { return _invoiceBatch; }
			set { _invoiceBatch = value; }
		}

		[DataMember]
		public virtual ICollection<ApprovalRequest> ApprovalRequests
		{
			get { return _approvalRequests; }
			set { _approvalRequests = value; }
		}

		[DataMember]
		public virtual ICollection<InvoiceDetail> InvoiceDetails
		{
			get { return _invoiceDetails; }
			set { _invoiceDetails = value; }
		}

		[DataMember]
		public virtual OrderHeader OrderHeader
		{
			get { return _orderHeader; }
			set { _orderHeader = value; }
		}

		[DataMember]
		public virtual PurchaseOrderHeader PurchaseOrderHeader
		{
			get { return _purchaseOrderHeader; }
			set { _purchaseOrderHeader = value; }
		}

		[DataMember]
		public virtual String CustomerPo
		{
			get { return _customerPo; }
			set { _customerPo = value; }
		}

		[DataMember]
		public virtual String InvoiceGenerated
		{
			get { return _invoiceGenerated; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("InvoiceGenerated must not be blank or null.");
				else _invoiceGenerated = value;
			}
		}

		[DataMember]
		public virtual String InvoiceNumber
		{
			get { return _invoiceNumber; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("InvoiceNumber must not be blank or null.");
				else _invoiceNumber = value;
			}
		}

		[DataMember]
		public virtual String InvoiceTypeCode
		{
			get { return _invoiceTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("InvoiceTypeCode must not be blank or null.");
				else _invoiceTypeCode = value;
			}
		}

		[DataMember]
		public virtual String Notes
		{
			get { return _notes; }
			set { _notes = value; }
		}

		[DataMember]
		public virtual WorkOrderHeader WorkOrderHeader
		{
			get { return _workOrderHeader; }
			set { _workOrderHeader = value; }
		}


		#endregion
	}
}
