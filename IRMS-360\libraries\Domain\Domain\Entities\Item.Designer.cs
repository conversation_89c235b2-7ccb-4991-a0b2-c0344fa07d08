using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Item : Entity
	{
		#region Fields

		private AgencyOrganizationalUnit _agencyOrganizationalUnit;
		private AgencyZone _agencyZone;
		private CartonSize _cartonSize;
		private ColorCode _color;
		private CompanyLocationType _companyLocationType;
		private CompanyLocationZone _companyLocationZone;
		private CountryCode _countryCode;
		private Customer _customer;
		private Decimal? _acceptableOverage;
		private Decimal? _cube;
		private Decimal? _height;
		private Decimal? _length;
		private Decimal? _msrp;
		private Decimal? _nestingFactor;
		private Decimal? _sellingPrice;
		private Decimal? _tareWeight;
		private Decimal? _unitCost;
		private Decimal? _weight;
		private Decimal? _width;
		private DepreciationMethod _depreciationMethod;
		private Finish _finish;
		private FreightClass _freightClass;
		private FreightCode _freightCode;
		private Int32? _lotShelfLife;
		private Decimal? _maximumQuantity;
		private Decimal? _minimumQuantity;
		private Int32? _qaEvery;
		private Decimal? _qaSampleAmount;
		private Int32? _receivingThreshold;
		private Decimal? _reorderQuantity;
		private Int32? _shippingThreshold;
		private Int32? _yearCode;
		private Item _parentItem;
		private ItemGroup _itemGroup;
		private ItemGroupLine _itemGroupLine;
		private ItemGroupSubLine _itemGroupSubLine;
		private ItemType _itemType;
		private LicensePlate _licensePlate;
		private ICollection<Alert> _alerts = new HashSet<Alert>();
		private ICollection<BillingRate> _billingRates = new HashSet<BillingRate>();
		private ICollection<CartonSize> _cartonSizes = new HashSet<CartonSize>();
		private ICollection<Comment> _comments = new HashSet<Comment>();
		private ICollection<CustomerItem> _customerItems = new HashSet<CustomerItem>();
		private ICollection<InspectionTemplate> _inspectionTemplates = new HashSet<InspectionTemplate>();
		private ICollection<InventoryItem> _inventoryItems = new HashSet<InventoryItem>();
		private ICollection<InventoryTask> _inventoryTasks = new HashSet<InventoryTask>();
		private ICollection<Item> _childItems = new HashSet<Item>();
		private ICollection<ItemDocument> _itemDocuments = new HashSet<ItemDocument>();
		private ICollection<ItemFulfillment> _itemFulfillments = new HashSet<ItemFulfillment>();
		private ICollection<ItemHistory> _itemHistories = new HashSet<ItemHistory>();
		private ICollection<ItemSubstitute> _itemSubstitutes = new HashSet<ItemSubstitute>();
		private ICollection<ItemTransaction> _itemTransactions = new HashSet<ItemTransaction>();
		private ICollection<ItemUomRelationship> _itemUomRelationships = new HashSet<ItemUomRelationship>();
		private ICollection<JobItem> _jobItems = new HashSet<JobItem>();
		private ICollection<KitDetail> _kitDetails = new HashSet<KitDetail>();
		private ICollection<KitHeader> _kitHeaders = new HashSet<KitHeader>();
		private ICollection<Location> _locations = new HashSet<Location>();
		private ICollection<MaintenanceDetail> _maintenanceDetails = new HashSet<MaintenanceDetail>();
		private ICollection<MaintenanceProgram> _maintenancePrograms = new HashSet<MaintenanceProgram>();
		private ICollection<OrderDetail> _orderDetails = new HashSet<OrderDetail>();
		private ICollection<ParticipantImmunization> _participantImmunizations = new HashSet<ParticipantImmunization>();
		private ICollection<PoolItem> _poolItems = new HashSet<PoolItem>();
		private ICollection<PreReceiptDetail> _preReceiptDetails = new HashSet<PreReceiptDetail>();
		private ICollection<PurchaseOrderDetail> _purchaseOrderDetails = new HashSet<PurchaseOrderDetail>();
		private ICollection<ReceiptDetail> _receiptDetails = new HashSet<ReceiptDetail>();
		private ICollection<RequisitionDetail> _requisitionDetails = new HashSet<RequisitionDetail>();
		private ICollection<ReturnDetail> _returnDetails = new HashSet<ReturnDetail>();
		private ICollection<UdfItemValue> _udfItemValues = new HashSet<UdfItemValue>();
		private ICollection<VendorItem> _vendorItems = new HashSet<VendorItem>();
		private ICollection<ZoneItem> _zoneItems = new HashSet<ZoneItem>();
		private Location _location;
		private Manufacturer _manufacturer;
		private OrganizationParticipant _custodian;
		private OrganizationParticipant _steward;
		private ParameterValue _lotExpiryParameter;
		private ParameterValue _lotUnavailableParameter;
		private ParameterValue _serialCaptureAtParameter;
		private ParameterValue _serialGenerateParameter;
		private ParameterValue _serialTrackByParameter;
		private Stratification _stratification;
		private String _active;
		private String _assetIndicator;
		private String _bodyCode;
        private string _hazmatId;
        private String _bodyDescription;
		private String _brandCode;
		private String _cartonize;
		private String _colorCode;
		private String _colorDescription;
		private String _contentDescription;
		private String _conveyable;
		private String _description;
		private String _divisionCode;
		private String _divisionDescription;
		private String _eanCode;
		private String _ecoFriendly;
		private String _epcCode;
		private String _gtinCode;
		private String _harmonizedCode;
		private String _hazardousMaterial;
		private String _itemCode;
		private String _itemVerification;
		private String _longDescription;
		private String _lotCalculateExpirationFrom;
		private String _lotControlled;
		private String _lotIssueFromSame;
		private String _lotUnavailableAt;
		private String _makeCode;
		private String _modelCode;
		private String _packAlone;
		private String _pilferage;
		private String _qaAll;
		private String _qaSampleEntire;
		private String _qaSamplePartial;
		private String _qaSampleType;
		private String _qualityAssurance;
		private String _requisitionable;
		private String _seasonCode;
		private String _serialCaptureAt;
		private String _serialGenerated;
		private String _serialized;
		private String _serialTrackBy;
		private String _shelfLife;
		private String _sizeDescription;
		private String _sizeScale;
		private String _sku;
		private String _styleCode;
		private String _styleDescription;
		private String _substitute;
		private String _upcCode;
		private String _wineAppellation;
		private String _wineDesignation;
		private String _wineVineyard;
		private UnitOfMeasure _dimensionalUom;
		private UnitOfMeasure _purchaseUom;
		private UnitOfMeasure _quantityUom;
		private UnitOfMeasure _saleUom;
		private UnitOfMeasure _shelfLifeUom;
		private UnitOfMeasure _unitOfMeasure;
		private UnitOfMeasure _weightUom;

		#endregion

		#region Properties

		[DataMember]
		public virtual AgencyOrganizationalUnit AgencyOrganizationalUnit
		{
			get { return _agencyOrganizationalUnit; }
			set { _agencyOrganizationalUnit = value; }
		}

		[DataMember]
		public virtual AgencyZone AgencyZone
		{
			get { return _agencyZone; }
			set { _agencyZone = value; }
		}

		[DataMember]
		public virtual CartonSize CartonSize
		{
			get { return _cartonSize; }
			set { _cartonSize = value; }
		}

		[DataMember]
		public virtual ColorCode Color
		{
			get { return _color; }
			set { _color = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual CompanyLocationZone CompanyLocationZone
		{
			get { return _companyLocationZone; }
			set { _companyLocationZone = value; }
		}

		[DataMember]
		public virtual CountryCode CountryCode
		{
			get { return _countryCode; }
			set { _countryCode = value; }
		}

		[DataMember]
		public virtual Customer Customer
		{
			get { return _customer; }
			set { _customer = value; }
		}

		[DataMember]
		public virtual Decimal? AcceptableOverage
		{
			get { return _acceptableOverage; }
			set { _acceptableOverage = value; }
		}

		[DataMember]
		public virtual Decimal? Cube
		{
			get { return _cube; }
			set { _cube = value; }
		}

		[DataMember]
		public virtual Decimal? Height
		{
			get { return _height; }
			set { _height = value; }
		}

		[DataMember]
		public virtual Decimal? Length
		{
			get { return _length; }
			set { _length = value; }
		}

		[DataMember]
		public virtual Decimal? Msrp
		{
			get { return _msrp; }
			set { _msrp = value; }
		}

		[DataMember]
		public virtual Decimal? NestingFactor
		{
			get { return _nestingFactor; }
			set { _nestingFactor = value; }
		}

		[DataMember]
		public virtual Decimal? SellingPrice
		{
			get { return _sellingPrice; }
			set { _sellingPrice = value; }
		}

		[DataMember]
		public virtual Decimal? TareWeight
		{
			get { return _tareWeight; }
			set { _tareWeight = value; }
		}

		[DataMember]
		public virtual Decimal? UnitCost
		{
			get { return _unitCost; }
			set { _unitCost = value; }
		}

		[DataMember]
		public virtual Decimal? Weight
		{
			get { return _weight; }
			set { _weight = value; }
		}

		[DataMember]
		public virtual Decimal? Width
		{
			get { return _width; }
			set { _width = value; }
		}

		[DataMember]
		public virtual DepreciationMethod DepreciationMethod
		{
			get { return _depreciationMethod; }
			set { _depreciationMethod = value; }
		}

		[DataMember]
		public virtual Finish Finish
		{
			get { return _finish; }
			set { _finish = value; }
		}

		[DataMember]
		public virtual FreightClass FreightClass
		{
			get { return _freightClass; }
			set { _freightClass = value; }
		}

		[DataMember]
		public virtual FreightCode FreightCode
		{
			get { return _freightCode; }
			set { _freightCode = value; }
		}

		[DataMember]
		public virtual Int32? LotShelfLife
		{
			get { return _lotShelfLife; }
			set { _lotShelfLife = value; }
		}

		[DataMember]
		public virtual Decimal? MaximumQuantity
		{
			get { return _maximumQuantity; }
			set { _maximumQuantity = value; }
		}

		[DataMember]
		public virtual Decimal? MinimumQuantity
		{
			get { return _minimumQuantity; }
			set { _minimumQuantity = value; }
		}

		[DataMember]
		public virtual Int32? QaEvery
		{
			get { return _qaEvery; }
			set { _qaEvery = value; }
		}

		[DataMember]
		public virtual Decimal? QaSampleAmount
		{
			get { return _qaSampleAmount; }
			set { _qaSampleAmount = value; }
		}

		[DataMember]
		public virtual Int32? ReceivingThreshold
		{
			get { return _receivingThreshold; }
			set { _receivingThreshold = value; }
		}

		[DataMember]
		public virtual Decimal? ReorderQuantity
		{
			get { return _reorderQuantity; }
			set { _reorderQuantity = value; }
		}

		[DataMember]
		public virtual Int32? ShippingThreshold
		{
			get { return _shippingThreshold; }
			set { _shippingThreshold = value; }
		}

		[DataMember]
		public virtual Int32? YearCode
		{
			get { return _yearCode; }
			set { _yearCode = value; }
		}

		[DataMember]
		public virtual Item ParentItem
		{
			get { return _parentItem; }
			set { _parentItem = value; }
		}

		[DataMember]
		public virtual ItemGroup ItemGroup
		{
			get { return _itemGroup; }
			set { _itemGroup = value; }
		}

		[DataMember]
		public virtual ItemGroupLine ItemGroupLine
		{
			get { return _itemGroupLine; }
			set { _itemGroupLine = value; }
		}

		[DataMember]
		public virtual ItemGroupSubLine ItemGroupSubLine
		{
			get { return _itemGroupSubLine; }
			set { _itemGroupSubLine = value; }
		}

		[DataMember]
		public virtual ItemType ItemType
		{
			get { return _itemType; }
			set { _itemType = value; }
		}

		[DataMember]
		public virtual LicensePlate LicensePlate
		{
			get { return _licensePlate; }
			set { _licensePlate = value; }
		}

		[DataMember]
		public virtual ICollection<Alert> Alerts
		{
			get { return _alerts; }
			set { _alerts = value; }
		}

		[DataMember]
		public virtual ICollection<BillingRate> BillingRates
		{
			get { return _billingRates; }
			set { _billingRates = value; }
		}

		[DataMember]
		public virtual ICollection<CartonSize> CartonSizes
		{
			get { return _cartonSizes; }
			set { _cartonSizes = value; }
		}

		[DataMember]
		public virtual ICollection<Comment> Comments
		{
			get { return _comments; }
			set { _comments = value; }
		}

		[DataMember]
		public virtual ICollection<CustomerItem> CustomerItems
		{
			get { return _customerItems; }
			set { _customerItems = value; }
		}

		[DataMember]
		public virtual ICollection<InspectionTemplate> InspectionTemplates
		{
			get { return _inspectionTemplates; }
			set { _inspectionTemplates = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryItem> InventoryItems
		{
			get { return _inventoryItems; }
			set { _inventoryItems = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryTask> InventoryTasks
		{
			get { return _inventoryTasks; }
			set { _inventoryTasks = value; }
		}

		[DataMember]
		public virtual ICollection<Item> ChildItems
		{
			get { return _childItems; }
			set { _childItems = value; }
		}

		[DataMember]
		public virtual ICollection<ItemDocument> ItemDocuments
		{
			get { return _itemDocuments; }
			set { _itemDocuments = value; }
		}

		[DataMember]
		public virtual ICollection<ItemFulfillment> ItemFulfillments
		{
			get { return _itemFulfillments; }
			set { _itemFulfillments = value; }
		}

		[DataMember]
		public virtual ICollection<ItemHistory> ItemHistories
		{
			get { return _itemHistories; }
			set { _itemHistories = value; }
		}

		[DataMember]
		public virtual ICollection<ItemSubstitute> ItemSubstitutes
		{
			get { return _itemSubstitutes; }
			set { _itemSubstitutes = value; }
		}

		[DataMember]
		public virtual ICollection<ItemTransaction> ItemTransactions
		{
			get { return _itemTransactions; }
			set { _itemTransactions = value; }
		}

		[DataMember]
		public virtual ICollection<ItemUomRelationship> ItemUomRelationships
		{
			get { return _itemUomRelationships; }
			set { _itemUomRelationships = value; }
		}

		[DataMember]
		public virtual ICollection<JobItem> JobItems
		{
			get { return _jobItems; }
			set { _jobItems = value; }
		}

		[DataMember]
		public virtual ICollection<KitDetail> KitDetails
		{
			get { return _kitDetails; }
			set { _kitDetails = value; }
		}

		[DataMember]
		public virtual ICollection<KitHeader> KitHeaders
		{
			get { return _kitHeaders; }
			set { _kitHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<Location> Locations
		{
			get { return _locations; }
			set { _locations = value; }
		}

		[DataMember]
		public virtual ICollection<MaintenanceDetail> MaintenanceDetails
		{
			get { return _maintenanceDetails; }
			set { _maintenanceDetails = value; }
		}

		[DataMember]
		public virtual ICollection<MaintenanceProgram> MaintenancePrograms
		{
			get { return _maintenancePrograms; }
			set { _maintenancePrograms = value; }
		}

		[DataMember]
		public virtual ICollection<OrderDetail> OrderDetails
		{
			get { return _orderDetails; }
			set { _orderDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantImmunization> ParticipantImmunizations
		{
			get { return _participantImmunizations; }
			set { _participantImmunizations = value; }
		}

		[DataMember]
		public virtual ICollection<PoolItem> PoolItems
		{
			get { return _poolItems; }
			set { _poolItems = value; }
		}

		[DataMember]
		public virtual ICollection<PreReceiptDetail> PreReceiptDetails
		{
			get { return _preReceiptDetails; }
			set { _preReceiptDetails = value; }
		}

		[DataMember]
		public virtual ICollection<PurchaseOrderDetail> PurchaseOrderDetails
		{
			get { return _purchaseOrderDetails; }
			set { _purchaseOrderDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ReceiptDetail> ReceiptDetails
		{
			get { return _receiptDetails; }
			set { _receiptDetails = value; }
		}

		[DataMember]
		public virtual ICollection<RequisitionDetail> RequisitionDetails
		{
			get { return _requisitionDetails; }
			set { _requisitionDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ReturnDetail> ReturnDetails
		{
			get { return _returnDetails; }
			set { _returnDetails = value; }
		}

		[DataMember]
		public virtual ICollection<UdfItemValue> UdfItemValues
		{
			get { return _udfItemValues; }
			set { _udfItemValues = value; }
		}

		[DataMember]
		public virtual ICollection<VendorItem> VendorItems
		{
			get { return _vendorItems; }
			set { _vendorItems = value; }
		}

		[DataMember]
		public virtual ICollection<ZoneItem> ZoneItems
		{
			get { return _zoneItems; }
			set { _zoneItems = value; }
		}

		[DataMember]
		public virtual Location Location
		{
			get { return _location; }
			set { _location = value; }
		}

		[DataMember]
		public virtual Manufacturer Manufacturer
		{
			get { return _manufacturer; }
			set { _manufacturer = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant Custodian
		{
			get { return _custodian; }
			set { _custodian = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant Steward
		{
			get { return _steward; }
			set { _steward = value; }
		}

		[DataMember]
		public virtual ParameterValue LotExpiryParameter
		{
			get { return _lotExpiryParameter; }
			set { _lotExpiryParameter = value; }
		}

		[DataMember]
		public virtual ParameterValue LotUnavailableParameter
		{
			get { return _lotUnavailableParameter; }
			set { _lotUnavailableParameter = value; }
		}

		[DataMember]
		public virtual ParameterValue SerialCaptureAtParameter
		{
			get { return _serialCaptureAtParameter; }
			set { _serialCaptureAtParameter = value; }
		}

		[DataMember]
		public virtual ParameterValue SerialGenerateParameter
		{
			get { return _serialGenerateParameter; }
			set { _serialGenerateParameter = value; }
		}

		[DataMember]
		public virtual ParameterValue SerialTrackByParameter
		{
			get { return _serialTrackByParameter; }
			set { _serialTrackByParameter = value; }
		}

		[DataMember]
		public virtual Stratification Stratification
		{
			get { return _stratification; }
			set { _stratification = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String AssetIndicator
		{
			get { return _assetIndicator; }
			set { _assetIndicator = value; }
		}

		[DataMember]
		public virtual String BodyCode
		{
			get { return _bodyCode; }
			set { _bodyCode = value; }
		}

        [DataMember]
        public virtual String HazmatId
        {
            get { return _hazmatId; }
            set { _hazmatId = value; }
        }


        [DataMember]
		public virtual String BodyDescription
		{
			get { return _bodyDescription; }
			set { _bodyDescription = value; }
		}

		[DataMember]
		public virtual String BrandCode
		{
			get { return _brandCode; }
			set { _brandCode = value; }
		}

		[DataMember]
		public virtual String Cartonize
		{
			get { return _cartonize; }
			set { _cartonize = value; }
		}

		[DataMember]
		public virtual String ColorCode
		{
			get { return _colorCode; }
			set { _colorCode = value; }
		}

		[DataMember]
		public virtual String ColorDescription
		{
			get { return _colorDescription; }
			set { _colorDescription = value; }
		}

		[DataMember]
		public virtual String ContentDescription
		{
			get { return _contentDescription; }
			set { _contentDescription = value; }
		}

		[DataMember]
		public virtual String Conveyable
		{
			get { return _conveyable; }
			set { _conveyable = value; }
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String DivisionCode
		{
			get { return _divisionCode; }
			set { _divisionCode = value; }
		}

		[DataMember]
		public virtual String DivisionDescription
		{
			get { return _divisionDescription; }
			set { _divisionDescription = value; }
		}

		[DataMember]
		public virtual String EanCode
		{
			get { return _eanCode; }
			set { _eanCode = value; }
		}

		[DataMember]
		public virtual String EcoFriendly
		{
			get { return _ecoFriendly; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("EcoFriendly must not be blank or null.");
				else _ecoFriendly = value;
			}
		}

		[DataMember]
		public virtual String EpcCode
		{
			get { return _epcCode; }
			set { _epcCode = value; }
		}

		[DataMember]
		public virtual String GtinCode
		{
			get { return _gtinCode; }
			set { _gtinCode = value; }
		}

		[DataMember]
		public virtual String HarmonizedCode
		{
			get { return _harmonizedCode; }
			set { _harmonizedCode = value; }
		}

		[DataMember]
		public virtual String HazardousMaterial
		{
			get { return _hazardousMaterial; }
			set { _hazardousMaterial = value; }
		}

		[DataMember]
		public virtual String ItemCode
		{
			get { return _itemCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ItemCode must not be blank or null.");
				else _itemCode = value;
			}
		}

		[DataMember]
		public virtual String ItemVerification
		{
			get { return _itemVerification; }
			set { _itemVerification = value; }
		}

		[DataMember]
		public virtual String LongDescription
		{
			get { return _longDescription; }
			set { _longDescription = value; }
		}

		[DataMember]
		public virtual String LotCalculateExpirationFrom
		{
			get { return _lotCalculateExpirationFrom; }
			set { _lotCalculateExpirationFrom = value; }
		}

		[DataMember]
		public virtual String LotControlled
		{
			get { return _lotControlled; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("LotControlled must not be blank or null.");
				else _lotControlled = value;
			}
		}

		[DataMember]
		public virtual String LotIssueFromSame
		{
			get { return _lotIssueFromSame; }
			set { _lotIssueFromSame = value; }
		}

		[DataMember]
		public virtual String LotUnavailableAt
		{
			get { return _lotUnavailableAt; }
			set { _lotUnavailableAt = value; }
		}

		[DataMember]
		public virtual String MakeCode
		{
			get { return _makeCode; }
			set { _makeCode = value; }
		}

		[DataMember]
		public virtual String ModelCode
		{
			get { return _modelCode; }
			set { _modelCode = value; }
		}

		[DataMember]
		public virtual String PackAlone
		{
			get { return _packAlone; }
			set { _packAlone = value; }
		}

		[DataMember]
		public virtual String Pilferage
		{
			get { return _pilferage; }
			set { _pilferage = value; }
		}

		[DataMember]
		public virtual String QaAll
		{
			get { return _qaAll; }
			set { _qaAll = value; }
		}

		[DataMember]
		public virtual String QaSampleEntire
		{
			get { return _qaSampleEntire; }
			set { _qaSampleEntire = value; }
		}

		[DataMember]
		public virtual String QaSamplePartial
		{
			get { return _qaSamplePartial; }
			set { _qaSamplePartial = value; }
		}

		[DataMember]
		public virtual String QaSampleType
		{
			get { return _qaSampleType; }
			set { _qaSampleType = value; }
		}

		[DataMember]
		public virtual String QualityAssurance
		{
			get { return _qualityAssurance; }
			set { _qualityAssurance = value; }
		}

		[DataMember]
		public virtual String Requisitionable
		{
			get { return _requisitionable; }
			set { _requisitionable = value; }
		}

		[DataMember]
		public virtual String SeasonCode
		{
			get { return _seasonCode; }
			set { _seasonCode = value; }
		}

		[DataMember]
		public virtual String SerialCaptureAt
		{
			get { return _serialCaptureAt; }
			set { _serialCaptureAt = value; }
		}

		[DataMember]
		public virtual String SerialGenerated
		{
			get { return _serialGenerated; }
			set { _serialGenerated = value; }
		}

		[DataMember]
		public virtual String Serialized
		{
			get { return _serialized; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Serialized must not be blank or null.");
				else _serialized = value;
			}
		}

		[DataMember]
		public virtual String SerialTrackBy
		{
			get { return _serialTrackBy; }
			set { _serialTrackBy = value; }
		}

		[DataMember]
		public virtual String ShelfLife
		{
			get { return _shelfLife; }
			set { _shelfLife = value; }
		}

		[DataMember]
		public virtual String SizeDescription
		{
			get { return _sizeDescription; }
			set { _sizeDescription = value; }
		}

		[DataMember]
		public virtual String SizeScale
		{
			get { return _sizeScale; }
			set { _sizeScale = value; }
		}

		[DataMember]
		public virtual String Sku
		{
			get { return _sku; }
			set { _sku = value; }
		}

		[DataMember]
		public virtual String StyleCode
		{
			get { return _styleCode; }
			set { _styleCode = value; }
		}

		[DataMember]
		public virtual String StyleDescription
		{
			get { return _styleDescription; }
			set { _styleDescription = value; }
		}

		[DataMember]
		public virtual String Substitute
		{
			get { return _substitute; }
			set { _substitute = value; }
		}

		[DataMember]
		public virtual String UpcCode
		{
			get { return _upcCode; }
			set { _upcCode = value; }
		}

		[DataMember]
		public virtual String WineAppellation
		{
			get { return _wineAppellation; }
			set { _wineAppellation = value; }
		}

		[DataMember]
		public virtual String WineDesignation
		{
			get { return _wineDesignation; }
			set { _wineDesignation = value; }
		}

		[DataMember]
		public virtual String WineVineyard
		{
			get { return _wineVineyard; }
			set { _wineVineyard = value; }
		}

		[DataMember]
		public virtual UnitOfMeasure DimensionalUom
		{
			get { return _dimensionalUom; }
			set { _dimensionalUom = value; }
		}

		[DataMember]
		public virtual UnitOfMeasure PurchaseUom
		{
			get { return _purchaseUom; }
			set { _purchaseUom = value; }
		}

		[DataMember]
		public virtual UnitOfMeasure QuantityUom
		{
			get { return _quantityUom; }
			set { _quantityUom = value; }
		}

		[DataMember]
		public virtual UnitOfMeasure SaleUom
		{
			get { return _saleUom; }
			set { _saleUom = value; }
		}

		[DataMember]
		public virtual UnitOfMeasure ShelfLifeUom
		{
			get { return _shelfLifeUom; }
			set { _shelfLifeUom = value; }
		}

		[DataMember]
		public virtual UnitOfMeasure UnitOfMeasure
		{
			get { return _unitOfMeasure; }
			set { _unitOfMeasure = value; }
		}

		[DataMember]
		public virtual UnitOfMeasure WeightUom
		{
			get { return _weightUom; }
			set { _weightUom = value; }
		}


		#endregion
	}
}
