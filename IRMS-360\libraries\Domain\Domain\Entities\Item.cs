using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;

using NHibernate.Criterion;
using NHibernate.SqlCommand;
using NHibernate.Transform;

using Upp.Irms.Constants;
using Upp.Irms.Core;
using Upp.Shared.Extensions;
using Upp.Shared.Utilities;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class Item : Entity
	{
		#region Fields.Static

		private static String[] _properties = new String[]
		{
			"Active",
			"Description",
			"Height",
			"Length",
			"PurchaseUom",
			"SalePrice",
			"UnitOfMeasure",
			"Weight",
			"Width"
		};

		#endregion

		#region Properties

		[DataMember]
		public virtual Boolean InventoryAvailable { get; set; }
		[DataMember]
		public virtual Boolean PoolControlled { get; set; }
		[DataMember]
		public virtual Boolean Repack { get; set; }
		[DataMember]
		public virtual DateTime? LotExpiration { get; set; }
        [DataMember]
        public virtual Decimal AvailableQuantity { get; set; }
        [DataMember]
        public virtual Decimal InTransitQuantity { get; set; }
        [DataMember]
        public virtual Decimal NextReceiptQuantity { get; set; }
        [DataMember]
        public virtual Decimal OverseasQuantity { get; set; }
        [DataMember]
        public virtual Decimal PurchaseOrderQuantity { get; set; }
		[DataMember]
		public virtual Decimal Quantity { get; set; }
        [DataMember]
        public virtual Decimal QuantityOnHand { get; set; }
        [DataMember]
        public virtual Decimal ShowRoomQuantity { get; set; }
        [DataMember]
        public virtual Decimal ReservedQuantity { get; set; }
        [DataMember]
        public virtual Decimal BackOrderQuantity { get; set; }
        [DataMember]
        public virtual Decimal CaseQuantity { get; set; }
        [DataMember]
        public virtual Decimal DemandQuantity { get; set; }
        [DataMember]
        public virtual Int32 Discontinued { get; set; }
		[DataMember]
		public virtual Int32 LpnCount { get; set; }
		[DataMember]
		public virtual Int32 PrimaryLocationCount { get; set; }
        [DataMember]
        public virtual Int32? PurchaseOrderDetailId { get; set; }
        [DataMember]
        public virtual Decimal QuantityBackOrdered { get; set; }
        [DataMember]
        public virtual Int32? UomId { get; set; }
		[DataMember]
		public virtual Location PrimaryLocation { get; set; }
		[DataMember]
		public virtual PrinterTypePrinter PrinterTypePrinter { get; set; }
		[DataMember]
		public virtual StatusCode StatusCode { get; set; }
        [DataMember]
        public virtual String DistributionCenter { get; set; }
        [DataMember]
        public virtual String Format { get; set; }
        [DataMember]
        public virtual String FullCasePrimeLocationCode { get; set; }
        [DataMember]
        public virtual String FullCasePrimeZoneCode { get; set; }
		[DataMember]
		public virtual String LicensePlateIds { get; set; }
        [DataMember]
        public virtual String LocationCode { get; set; }
        [DataMember]
		public virtual String LotNumber { get; set; }
        [DataMember]
        public virtual String ItemNextAvailableDate { get; set; }
		[DataMember]
		public virtual String ReceiptCode { get; set; }
        [DataMember]
        public virtual String SplitCasePrimeLocationCode { get; set; }
        [DataMember]
        public virtual String SplitCasePrimeZoneCode { get; set; }
        [DataMember]
        public virtual String SupplierId { get; set; }
        [DataMember]
        public virtual String GroupCode { get; set; }
        [DataMember]
        public virtual String GroupLineCode { get; set; }
        [DataMember]
        public virtual String GroupSubLineCode { get; set; }
        [DataMember]
        public virtual String UserName { get; set; }
		[DataMember]
		public virtual String WorkOrderCode { get; set; }
		[DataMember]
		public virtual String ZoneCode { get; set; }
		[DataMember]
		public virtual Task Task { get; set; }
        [DataMember]
        public virtual Int32? ReceiptDetailId { get; set; }

        #endregion

        #region Properties.Reports

        public virtual Decimal PackFactor { get; set; }
		public virtual Decimal? PickedQuantity { get; set; }
        public virtual String BCItemNumber { get; set; }
        public virtual String BCPrice { get; set; }
        public virtual String BCQuantity { get; set; }
        public virtual String BCWeight { get; set; }
        public virtual String CaseWeight { get; set; }
        public virtual String CustomerItem { get; set; }
        public virtual String CustomerItemDescription { get; set; }
        public virtual String CustItem { get; set; }
        public virtual String CustItemDescription { get; set; }
        public virtual String Delimeter { get; set; }
        public virtual String FreightClassCode { get; set; }
        public virtual String FreightCodeCode { get; set; }
        public virtual String FreightCodeDescription { get; set; }
		public virtual String ItemColor { get; set; }
        public virtual String ItemMSRP { get; set; }
		public virtual String ItemSize { get; set; }
        public virtual String Latex { get; set; }
        public virtual String LicensePlateCode { get; set; }
        public virtual String LinerFeetCode { get; set; }
        public virtual String ManufacturerName { get; set; }
        public virtual String PackSizeCode { get; set; }
		public virtual String PurchaseOrderCode { get; set; }
		public virtual String PurchaseOrderSuffix { get; set; }
        public virtual String Publisher { get; set; }
		public virtual String SaleUomCode { get; set; }
        public virtual String Status { get; set; }
		public virtual String UomCode { get; set; }
		public virtual String VendorCode { get; set; }
		public virtual String VendorItemCode { get; set; }
		public virtual String VendorName { get; set; }
		public virtual String XrefItemCode { get; set; }
		public virtual String ZoneGroupCode { get; set; }
        public virtual String HazmatCode { get; set; }
        public virtual String ItemGroupCode { get; set; }
        public virtual Decimal? BoxQty { get; set; }
        public virtual Decimal? CaseQty { get; set; }
        public virtual Decimal? PalletQuantity { get; set; }
        public virtual Decimal AllocatedQuantity { get; set; }
        public virtual Decimal UnavailableQuantity { get; set; }
        public virtual Decimal UnAvailableReservedQuantity { get; set; }
        public virtual Decimal UnAvailableDemandQuantity { get; set; }
        public virtual Decimal PackedQuantity { get; set; }
        public virtual Decimal OnHand { get; set; }
        public virtual Int32? StandardUOMID { get; set; }
        public virtual String CustomerCode { get; set; }
        public virtual String Company { get; set; }
        public virtual String Warehouse { get; set; }
        public virtual String CountryCodeCode { get; set; }
        #endregion

        #region Constructor

        public Item()
		{
			//
		}

		#endregion

		#region Methods.Private.Putaway

		private Decimal SuggestBulk(ref IList<InventoryItem> list, StatusCode status, decimal quantity)
		{
			if (quantity == 0) return quantity;
			//
			if (_companyLocationZone == null)
			{
                list.Add(new InventoryItem()
                {
                    Item = this,
                    ItemCode = _itemCode,
                    ItemDescription = _description,
                    ItemLongDescription = _longDescription,
                    LocationCodeTo = "USER DIRECT",
                    Quantity = quantity,
                    UpcCode = _upcCode,
                });
				//
				return 0;
			}
			/* Consolidate inventory in non-primes? */
			Boolean? consolidate = BusinessRule.RetrieveBoolean("5766");
			if (consolidate.HasValue && !consolidate.Value) return quantity;
			//
			LocationType mobile = Entity.Retrieve<LocationType>(InventoryLocationTypes.Mobile);
			LocationType stage = Entity.Retrieve<LocationType>(InventoryLocationTypes.Staging);
			//
            ProjectionList projections = Projections.ProjectionList()
				.Add(Projections.GroupProperty("a.AisleCode"), "AisleCode")
				.Add(Projections.GroupProperty("l.LocationCode"), "LocationCode")
				.Add(Projections.GroupProperty("Received"), "Received")
				.Add(Projections.GroupProperty("z.ZoneCode"), "ZoneCode")
				.Add(Projections.Sum("Quantity"), "Quantity");
            DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
				.CreateAlias("Location", "l")
					.CreateAlias("l.Aisle", "a")
					.CreateAlias("l.CompanyLocationZone", "clz")
						.CreateAlias("clz.Zone", "z")
				.Add(Expression.Eq("clz.AllowPutaway", "Y"))
				.Add(Expression.Eq("Item", this))
                .Add(Expression.Eq("l.Active", "A"))
				.Add(Expression.Eq("l.CompanyLocationZone", _companyLocationZone))
				.Add(Expression.Eq("StatusCode", status))
				.Add(Expression.Gt("Quantity", 0M))
				.Add(Expression.IsNull("l.Item"))
				.Add(Expression.Not(Expression.Eq("l.LocationType", mobile)))
				.Add(Expression.Not(Expression.Eq("l.LocationType", stage)))
				.AddOrder(Order.Desc("Received"))
				.SetMaxResults(1)
				.SetProjection(projections)
				.SetResultTransformer(Transformers.AliasToBean<InventoryItem>());
			IList<InventoryItem> stock = Repositories.Get<InventoryItem>().List(criteria);
			//
			if (stock.Count == 0) return quantity;
			//
			list.Add(new InventoryItem()
			{
				AisleCode = stock[0].AisleCode,
				Item = this,
				ItemCode = _itemCode,
				ItemDescription = _description,
				ItemLongDescription = _longDescription,
				LocationCodeTo = stock[0].LocationCode,
				Quantity = quantity,
				UpcCode = _upcCode,
				ZoneCode = stock[0].ZoneCode,
			});
			//
			return 0;
		}

        private Decimal SuggestCustomerSpecific(ref IList<InventoryItem> list, String customerCode, decimal quantity)
        {
            if (quantity == 0) return quantity;
            //
            DetachedCriteria count = DetachedCriteria.For<InventoryItem>("cii")
                .CreateAlias("LicensePlate", "lpn")
                    .CreateAlias("lpn.ParentLicensePlate", "plpn")
                .Add(Expression.EqProperty("l.Id", "cii.Location.Id"))
                .SetProjection(Projections.CountDistinct("plpn.Id"));
            ProjectionList projections = Projections.ProjectionList()
                .Add(Projections.Property("a.AisleCode"), "AisleCode")
                .Add(Projections.Property("LocationCode"), "LocationCode")
                .Add(Projections.Property("z.ZoneCode"), "ZoneCode")
                .Add(Projections.SubQuery(count), "LpnCount");
            DetachedCriteria criteria = DetachedCriteria.For<Location>("l")
                .CreateAlias("Aisle", "a", JoinType.LeftOuterJoin)
                .CreateAlias("CompanyLocationZone", "clz", JoinType.LeftOuterJoin)
                    .CreateAlias("clz.Zone", "z", JoinType.LeftOuterJoin)
                .CreateAlias("LocationCustomers", "lc")
                    .CreateAlias("lc.Customer", "c")
                .Add(Expression.Eq("Active", "A"))
                .Add(Expression.Eq("c.CustomerCode", customerCode))
                .Add(Expression.Le("lc.Effective", DateTime.Now.Date))
                .Add(Expression.Ge("lc.Expiration", DateTime.Now.Date))
                .SetMaxResults(1)
                .SetProjection(projections)
                .SetResultTransformer(Transformers.AliasToBean<Location>());
            if (_companyLocationZone != null) criteria = criteria.Add(Expression.Eq("CompanyLocationZone", _companyLocationZone));
            IList<Location> locations = Repositories.Get<Location>().List(criteria);
            locations = locations.OrderBy(c => c.LpnCount).ToList<Location>();
            if (locations.Count == 0)
            {
                list.Add(new InventoryItem()
                {
                    Item = this,
                    ItemCode = _itemCode,
                    ItemDescription = _description,
                    ItemLongDescription = _longDescription,
                    LocationCodeTo = "USER DIRECT",
                    Quantity = quantity,
                    UpcCode = _upcCode,
                });
                //
                return 0;
            }
            //
            list.Add(new InventoryItem()
            {
                AisleCode = locations[0].AisleCode,
                Item = this,
                ItemCode = _itemCode,
                ItemDescription = _description,
                ItemLongDescription = _longDescription,
                LocationCodeTo = locations[0].LocationCode,
                Quantity = quantity,
                UpcCode = _upcCode,
                ZoneCode = locations[0].ZoneCode,
            });
            //
            return 0;
        }

		private Decimal SuggestOpen(ref IList<InventoryItem> list, decimal quantity)
		{
			if (quantity == 0) return quantity;
			//
            LocationType mobile = Entity.Retrieve<LocationType>(InventoryLocationTypes.Mobile);
            LocationType stage = Entity.Retrieve<LocationType>(InventoryLocationTypes.Staging);
			StatusCode unavailable = Entity.Retrieve<StatusCode>(InventoryStatuses.Unavailable);
            String locationCodeTo = string.Empty;
			ProjectionList projections = Projections.ProjectionList()
				.Add(Projections.Property("a.AisleCode"), "AisleCode")
				.Add(Projections.Property("LocationCode"), "LocationCode")
				.Add(Projections.Property("z.ZoneCode"), "ZoneCode");
			DetachedCriteria count = DetachedCriteria.For<InventoryItem>("cii")
				.Add(Expression.EqProperty("l.Id", "cii.Location.Id"))
				.Add(Expression.Not(Expression.Eq("StatusCode", unavailable)))
				.SetProjection(Projections.Count("cii.Id"));
			DetachedCriteria sum = DetachedCriteria.For<InventoryItem>("sii")
				.Add(Expression.EqProperty("l.Id", "sii.Location.Id"))
				.Add(Expression.Not(Expression.Eq("StatusCode", unavailable)))
				.SetProjection(Projections.Sum("sii.Quantity"));
			DetachedCriteria criteria = DetachedCriteria.For<Location>("l")
				.CreateAlias("Aisle", "a")
				.CreateAlias("CompanyLocationZone", "clz")
					.CreateAlias("clz.Zone", "z")
				.CreateAlias("LocationType", "lt")
                .Add(Expression.Eq("Active", "A"))
				.Add(Expression.Eq("clz.AllowPutaway", "Y"))
				.Add(Expression.Eq("CompanyLocationZone", _companyLocationZone))
				.Add(Expression.IsNull("Item"))
                .Add(Expression.Not(Expression.Eq("LocationType", mobile)))
                .Add(Expression.Not(Expression.Eq("LocationType", stage)))
				.Add(Expression.Or(Expression.Eq(Projections.SubQuery(count), 0), Expression.Eq(Projections.SubQuery(sum), 0)))
				.SetMaxResults(1)
				.SetProjection(projections)
				.SetResultTransformer(Transformers.AliasToBean<Location>());
			if (_stratification != null) criteria = criteria
				.CreateAlias("Stratification", "s")
				.Add(Expression.Ge("s.StratificationCode", _stratification.StratificationCode));
			Location location = Repositories.Get<Location>().Retrieve(criteria);
            if (location != null) locationCodeTo = location.LocationCode;
			//
			list.Add(new InventoryItem()
			{
				AisleCode = (location == null) ? null : location.AisleCode,
				Item = this,
				ItemCode = _itemCode,
				ItemDescription = _description,
				ItemLongDescription = _longDescription,
				LocationCodeTo = (string.IsNullOrEmpty(locationCodeTo)) ? "USER DIRECT" : locationCodeTo,
				Quantity = quantity,
				UpcCode = _upcCode,
				ZoneCode = (location == null) ? null : location.ZoneCode,
			});
			//
			return 0;
		}

        private Decimal SuggestPreviousInventory(ref IList<InventoryItem> list, decimal quantity)
        {
            if (quantity == 0) return quantity;
            //
            TransactionType picked = Entity.Retrieve<TransactionType>(OrderTransactions.Picked);
            ProjectionList projections = Projections.ProjectionList()
                .Add(Projections.Property("a.AisleCode"), "AisleCode")
                .Add(Projections.Property("l.LocationCode"), "LocationCode")
                .Add(Projections.Property("z.ZoneCode"), "ZoneCode");
            DetachedCriteria criteria = DetachedCriteria.For<ItemTransaction>()
                .CreateAlias("InventoryItem", "ii")
                    .CreateAlias("ii.Location", "l")
                        .CreateAlias("l.Aisle", "a")
                        .CreateAlias("l.CompanyLocationZone", "clz")
                            .CreateAlias("clz.Zone", "z")
                .Add(Expression.Eq("ii.Quantity", 0M))
                .Add(Expression.Eq("l.Active", "A"))
                .Add(Expression.Eq("Item", this))
                .Add(Expression.Eq("TransactionType", picked))
                .AddOrder("Occurred", false)
                .SetMaxResults(1)
                .SetProjection(projections)
                .SetResultTransformer(Transformers.AliasToBean<ItemTransaction>());
            IList<ItemTransaction> stock = Repositories.Get<ItemTransaction>().List(criteria);
            //
            if (stock.Count == 0) return quantity;
            //
            list.Add(new InventoryItem()
            {
                AisleCode = stock[0].AisleCode,
                Item = this,
                ItemCode = _itemCode,
                ItemDescription = _description,
                ItemLongDescription = _longDescription,
                LocationCodeTo = stock[0].LocationCode,
                Quantity = quantity,
                UpcCode = _upcCode,
                ZoneCode = stock[0].ZoneCode,
            });
            //
            return 0;
        }

		private Decimal SuggestPrimary(ref IList<InventoryItem> list, decimal quantity)
		{
			if (quantity == 0) return quantity;
			//
			DetachedCriteria criteria = null;
			ProjectionList projections = null;
			/* Create primary location top-offs? */
			String topoff = BusinessRule.RetrieveString("5750");
			if (String.IsNullOrEmpty(topoff) || "N".Equals(topoff)) return quantity;
			/* Consider pending replenishments? */
			Boolean? pending = BusinessRule.RetrieveBoolean("5751");
			IList<InventoryTask> replenishments = new List<InventoryTask>();
			if (pending.HasValue && pending.Value)
			{
				CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
				StatusCode complete = Entity.Retrieve<StatusCode>(TaskStatuses.Complete);
				TransactionType replenishment = Entity.Retrieve<TransactionType>(InventoryTransactions.Replenishment);
				projections = Projections.ProjectionList()
					.Add(Projections.GroupProperty("lt.LocationCode"), "LocationCodeTo")
					.Add(Projections.Sum("Quantity"), "Quantity");
				criteria = DetachedCriteria.For<InventoryTask>()
				   .CreateAlias("LocationTo", "lt")
				   .CreateAlias("Task", "t")
				   .Add(Expression.Eq("Item", this))
				   .Add(Expression.Eq("t.CompanyLocationType", warehouse))
				   .Add(Expression.Eq("t.TransactionType", replenishment))
				   .Add(Expression.Not(Expression.Eq("StatusCode", complete)))
				   .SetProjection(projections)
				   .SetResultTransformer(Transformers.AliasToBean<InventoryTask>());
				replenishments = Repositories.Get<InventoryTask>().List(criteria);
			}
			//            
			projections = Projections.ProjectionList()
				.Add(Projections.Property("a.AisleCode"), "AisleCode")
				.Add(Projections.Property("LocationCode"), "LocationCode")
				.Add(Projections.Property("MaximumQuantity"), "MaximumQuantity")
				.Add(Projections.Property("MinimumQuantity"), "MinimumQuantity")
				.Add(Projections.Property("pt.PickTypeCode"), "PickTypeCode")
				.Add(Projections.Property("z.ZoneCode"), "ZoneCode");
			criteria = DetachedCriteria.For<Location>()
				.CreateAlias("Aisle", "a")
				.CreateAlias("CompanyLocationZone", "clz")
					.CreateAlias("clz.Zone", "z")
				.CreateAlias("PickType", "pt")
                .Add(Expression.Eq("Active", "A"))
				.Add(Expression.Eq("Item", this))
				.Add(Expression.Eq("PrimaryPick", "Y"))
				.SetProjection(projections)
				.SetResultTransformer(Transformers.AliasToBean<Location>());
			IList<Location> primes = Repositories.Get<Location>().List(criteria);
			//
			if (primes.Count == 0) return quantity;
			//
			projections = Projections.ProjectionList()
				.Add(Projections.GroupProperty("l.LocationCode"), "LocationCode")
				.Add(Projections.Sum("Quantity"), "Quantity");
			criteria = DetachedCriteria.For<InventoryItem>()
				.CreateAlias("Location", "l")
				.Add(Expression.Eq("Item", this))
                .Add(Expression.Eq("l.Active", "A"))
				.Add(Expression.Eq("l.Item", this))
				.Add(Expression.Eq("l.PrimaryPick", "Y"))
				.Add(Expression.IsNotNull("l.PickType"))
				.SetProjection(projections)
				.SetResultTransformer(Transformers.AliasToBean<InventoryItem>());
			IList<InventoryItem> stock = Repositories.Get<InventoryItem>().List(criteria);
			//
			foreach (Location prime in primes)
			{
				InventoryItem match = stock
					.Where(s => s.LocationCode.Equals(prime.LocationCode))
					.FirstOrDefault();
				prime.AvailableQuantity = (match == null) ? 0 : Converter.ToInt32(match.Quantity);
			}
			//
			Decimal factor = 1;
			String[] units = new String[]
			{
				CodeValue.GetCode(InventoryUoms.Case),
				CodeValue.GetCode(InventoryUoms.Pallet),
			};
			projections = Projections.ProjectionList()
				.Add(Projections.Property("Factor"), "Factor")
				.Add(Projections.Property("uom.UomCode"), "UomCode");
			criteria = DetachedCriteria.For<ItemUomRelationship>()
				.CreateAlias("UnitOfMeasure", "uom")
				.Add(Expression.Eq("Active", "A"))
				.Add(Expression.Eq("Item", this))
				.Add(Expression.In("uom.UomCode", units))
				.SetProjection(projections)
				.SetResultTransformer(Transformers.AliasToBean<ItemUomRelationship>());
			IList<ItemUomRelationship> relationships = Repositories.Get<ItemUomRelationship>().List(criteria);
			//
			ItemUomRelationship relationship = null;
			foreach (PickTypes element in new PickTypes[] { PickTypes.Pallet, PickTypes.FullCase, PickTypes.SplitCase })
			{
				if (element.Equals(PickTypes.Pallet))
				{
					relationship = relationships
						.Where(e => e.UomCode.Equals(CodeValue.GetCode(InventoryUoms.Pallet)))
						.FirstOrDefault();
					if (relationship == null) continue;
					else factor = relationship.Factor;
				}
				else if (element.Equals(PickTypes.FullCase))
				{
					relationship = relationships
						.Where(e => e.UomCode.Equals(CodeValue.GetCode(InventoryUoms.Case)))
						.FirstOrDefault();
					if (relationship == null) continue;
					else factor = relationship.Factor;
				}
				else if (element.Equals(PickTypes.SplitCase))
				{
					relationship = relationships
						.Where(e => e.UomCode.Equals(CodeValue.GetCode(InventoryUoms.Case)))
						.FirstOrDefault();
					if (relationship == null) continue;
					else factor = relationship.Factor;
				}
				else factor = 1;
				//
				IList<Location> locations = primes
					.Where(e => e.AvailableQuantity < e.MaximumQuantity)
					.Where(e => e.PickTypeCode.Equals(CodeValue.GetCode(element)))
					.ToList();
				if (locations.Count == 0) continue;
				//
				foreach (Location prime in locations)
				{
					if ("MIN".Equals(topoff) && prime.AvailableQuantity >= prime.MinimumQuantity) continue;
					//
					Decimal required = prime.MaximumQuantity.Value - prime.AvailableQuantity;
					if (factor > 1) required = Round.Down(required / factor) * factor;
					if (required > quantity) required = quantity;
					if (required == 0) continue;
					//
					InventoryTask task = replenishments
						.Where(e => e.LocationCodeTo.Equals(prime.LocationCode))
						.FirstOrDefault();
					if (task != null)
					{
						if (task.Quantity >= required) continue;
						else required -= task.Quantity;
					}
					//
					list.Add(new InventoryItem()
					{
						AisleCode = prime.AisleCode,
						Item = this,
						ItemCode = _itemCode,
						ItemDescription = _description,
						ItemLongDescription = _longDescription,
						LocationCodeTo = prime.LocationCode,
						Quantity = required,
						UpcCode = _upcCode,
						ZoneCode = prime.ZoneCode,
					});
					//
					quantity -= required;
				}
			}
			//
			return quantity;
		}

		#endregion

		#region Methods.Public

        public virtual void CalculateAvailableQuantity()
        {
            DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
                .Add("Item", this)
                .SetProjection(Projections.Sum("Quantity"));
            this.Quantity = Repositories.Get<InventoryItem>().Function<Decimal>(criteria);
        }

        public virtual void CalculateCaseQuantity()
        {
            ProjectionList projections = Projections.ProjectionList()
                .Add(Projections.Property("Factor"), "Factor");
            DetachedCriteria criteria = DetachedCriteria.For<ItemUomRelationship>()
                .CreateAlias("UnitOfMeasure", "uom")
                .Add(Expression.Eq("Active", "A"))
                .Add(Expression.Eq("Item", this))
                .Add(Expression.Eq("uom.UomCode", CodeValue.GetCode(InventoryUoms.Case)))
                .SetProjection(projections)
                .SetResultTransformer(Transformers.AliasToBean<ItemUomRelationship>());
            ItemUomRelationship relationship = Repositories.Get<ItemUomRelationship>().Retrieve(criteria);
            //
            if (relationship != null) this.CaseQuantity = Converter.ToInt32(relationship.Factor);
        }

		public virtual void CalculatePrimaryLocationCount()
		{
			DetachedCriteria criteria = DetachedCriteria.For<Location>()
				.Add("Active", "A")
				.Add("CompanyLocationType", Registry.Find<CompanyLocationType>())
				.Add("Item", this)
				.Add("PrimaryPick", "Y")
				.SetProjection(Projections.Count("Id"));
			this.PrimaryLocationCount = Repositories.Get<Location>().Function<Int32>(criteria);
		}

		public virtual void CalculateQuantity(Location location, LicensePlate plate)
		{
			DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
				.Add("Item", this)
				.Add("Location", location)
				.SetProjection(Projections.Sum("Quantity"));
			if (plate != null) criteria = criteria.Add("LicensePlate.ParentLicensePlate", plate);
			this.Quantity = Repositories.Get<InventoryItem>().Function<Decimal>(criteria);
		}

		public virtual void CheckPoolControlled()
		{
			DetachedCriteria criteria = DetachedCriteria.For<PoolItem>()
				.Add("Active", "A")
				.Add("Item", this)
				.SetProjection(Projections.Count("Id"));
			this.PoolControlled = (Repositories.Get<PoolItem>().Function<Int32>(criteria) > 0);
		}

		public virtual void CreateReceiptCode()
		{
			this.ReceiptCode = EntityCode.GetCurrentValue(EntityCodes.ReceiptHeader);
			this.StatusCode = Entity.Retrieve<StatusCode>(InventoryStatuses.Available);
			if (_companyLocationZone != null) this.ZoneCode = _companyLocationZone.Zone.ZoneCode;
		}

		public virtual void FindPrimaryLocation()
		{
			DetachedCriteria criteria = DetachedCriteria.For<Location>()
				.Add("CompanyLocationType", Registry.Find<CompanyLocationType>())
				.Add("Item", this)
				.Add("PrimaryPick", "Y")
				.SetMaxResults(1);
			this.PrimaryLocation = Repositories.Get<Location>().Retrieve(criteria);
		}

        public virtual void FindPrimaryLocationInfo()
        {
            ProjectionList projections = Projections.ProjectionList()
                .Add(Projections.Property("LocationCode"), "LocationCode")
                .Add(Projections.Property("pt.PickTypeCode"), "PickTypeCode")
                .Add(Projections.Property("z.ZoneCode"), "ZoneCode");
            DetachedCriteria criteria = DetachedCriteria.For<Location>()
                .CreateAlias("CompanyLocationZone", "clz")
                    .CreateAlias("clz.Zone", "z")
                .CreateAlias("PickType", "pt")
                .Add(Expression.Eq("Item", this))
                .Add(Expression.Eq("PrimaryPick", "Y"))
                .SetProjection(projections)
                .SetResultTransformer(Transformers.AliasToBean<Location>());
            IList<Location> primes = Repositories.Get<Location>().List(criteria);
            //
            if (primes != null && primes.Count > 0)
            {
                Location fullCaseLocation = primes.Where(e => e.PickTypeCode.Equals(CodeValue.GetCode(PickTypes.FullCase))).FirstOrDefault();
                if (fullCaseLocation != null)
                {
                    this.FullCasePrimeLocationCode = fullCaseLocation.LocationCode;
                    this.FullCasePrimeZoneCode = fullCaseLocation.ZoneCode;
                }
                //
                Location splitCaseLocation = primes.Where(e => e.PickTypeCode.Equals(CodeValue.GetCode(PickTypes.SplitCase))).FirstOrDefault();
                if (splitCaseLocation != null)
                {
                    this.SplitCasePrimeLocationCode = splitCaseLocation.LocationCode;
                    this.SplitCasePrimeZoneCode = splitCaseLocation.ZoneCode;
                }
            }
        }

        public virtual void FindSplitCasePrimaryLocation()
        {
            PickType each = Entity.Retrieve<PickType>(PickTypes.SplitCase);
            //
            DetachedCriteria criteria = DetachedCriteria.For<Location>()
                .Add("CompanyLocationType", Registry.Find<CompanyLocationType>())
                .Add("Item", this)
                .Add("PickType", each)
                .Add("PrimaryPick", "Y")
                .SetMaxResults(1);
            this.PrimaryLocation = Repositories.Get<Location>().Retrieve(criteria);
        }

		public virtual IList<UdfItemValue> FindUdfItemValues()
		{
			ProjectionList projections = Projections.ProjectionList()
				.Add(Projections.Property("Id"), "Id")
				.Add(Projections.Property("umv.Label"), "Label");
			DetachedCriteria criteria = DetachedCriteria.For<UdfItemValue>()
				.CreateAlias("UdfMetadataValue", "umv")
				.Add(Expression.Eq("Item", this))
				.Add(Expression.Eq("PromptAtReceiving", "Y"))
				.SetProjection(projections)
				.SetResultTransformer(Transformers.AliasToBean<UdfItemValue>());
			return Repositories.Get<UdfItemValue>().List(criteria);
		}

		public virtual void Receive()
		{
			ReceiptHeader header = ReceiptHeader.Create(this);
			Repositories.Get<ReceiptHeader>().Add(header);
			//
			ReceiptDetail detail = ReceiptDetail.Create(this);
			detail.ReceiptHeader = header;
			Repositories.Get<ReceiptDetail>().Add(detail);
			//
			DateTime? manufacture = InventoryItem.CalculateManufactureDate(this.LotNumber);
			this.StatusCode = InventoryItem.UpdateStatus(this, this.StatusCode, manufacture);
			//
			String[] ids = this.LicensePlateIds.Split('|');
			for (int i = 0; i < ids.Length; i++)
			{
				String[] parts = ids[i].Split(';');
				InventoryItem item = InventoryItem.Create(this, this.PrimaryLocation, this.StatusCode, 1);
				item.LotExpiration = this.LotExpiration;
				item.LotNumber = this.LotNumber;
				item.ManufactureDate = manufacture;
				item.ReceiptDetail = detail;
				Repositories.Get<InventoryItem>().Add(item);
				//
				Int32? id = Converter.ToNullableInt32(parts[0]);
				LicensePlate parent = Repositories.Get<LicensePlate>().Retrieve(id);
				item.LicensePlate.ChangeParent(parent);
				parent.ChangeLocation(this.PrimaryLocation);
				//
				//detail.WriteReceived(item);
			}
		}

		#endregion

		#region Methods.Public.Assets

		public virtual void CreateCycleCount()
		{
			DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
				.Add("Item", this)
				.Add("Quantity", ">", 0M)
				.Add("StatusCode", Entity.Retrieve<StatusCode>(InventoryStatuses.Available));
			if (Registry.Find<AgencyOrganizationalUnit>() != null) criteria = criteria.Add("AgencyOrganizationalUnit", Registry.Find<AgencyOrganizationalUnit>());
			else if (Registry.Find<Agency>() != null)
			{
				Agency agency = Registry.Find<Agency>();
				criteria = criteria.AddOr("AgencyOrganizationalUnit.Agency", agency,
					"AgencyOrganizationalUnit.AgencyLocation.Agency", agency);
			}
			IList<InventoryItem> assets = Repositories.Get<InventoryItem>().List(criteria);
			if (assets.Count == 0) throw new Exception(String.Format("Unable to find any assets for {0}.", _itemCode));
			//
			TransactionType cycle = Entity.Retrieve<TransactionType>(InventoryTransactions.CycleCountItem);
			this.Task = Task.Create(cycle);
			this.Task.Started = DateTime.Now;
			this.Task.StatusCode = Entity.Retrieve<StatusCode>(TaskStatuses.Active);
			Repositories.Get<Task>().Add(this.Task);
			//
			InventoryTask main = InventoryTask.Create(this.Task);
			main.Item = this;
			main.Quantity = 0;
			Repositories.Get<InventoryTask>().Add(main);
			//
			foreach (InventoryItem element in assets) element.SetCycleCount(this.Task);
		}

		#endregion

		#region Methods.Public.Print

		public virtual void Print(PrinterTypePrinter printer, int copies)
		{
			if (printer == null) return;
			//
			Dictionary<String, String> parameters = new Dictionary<String, String>();
			parameters.Add("Company", Registry.Find<Company>().CompanyCode);
			parameters.Add("ItemCode", _itemCode);
			parameters.Add("Warehouse", Registry.Find<CompanyLocationType>().CompanyLocationCode);
			//
			ReportRequestHeader request = ReportRequestHeader.Create(CodeValue.GetCode(Reports.ItemLabel1x2),
				parameters, printer, copies);
			Repositories.Get<ReportRequestHeader>().Add(request);
			//
			Task task = Task.Create(request);
			Repositories.Get<Task>().Add(task);
		}

		#endregion

		#region Methods.Public.Putaway

		public virtual IList<InventoryItem> SuggestLocations(string location, string plate)
		{
			CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
			LicensePlate parent = null;
			//
			ProjectionList projections = Projections.ProjectionList()
				.Add(Projections.GroupProperty("sc.Code"), "StatusCodeCode")
                .Add(Projections.GroupProperty("c.CustomerCode"), "CustomerCode")
				.Add(Projections.Sum("Quantity"), "Quantity");
			DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
				.CreateAlias("Location", "l")
				.CreateAlias("StatusCode", "sc")
                .CreateAlias("Customer", "c", JoinType.LeftOuterJoin)
				.Add(Expression.Eq("CompanyLocationType", warehouse))
				.Add(Expression.Eq("Item", this))
				.Add(Expression.Eq("l.LocationCode", location))
				.SetMaxResults(1)
				.SetProjection(projections)
				.SetResultTransformer(Transformers.AliasToBean<InventoryItem>());
			if (!String.IsNullOrEmpty(plate)) criteria = criteria
				.CreateAlias("LicensePlate", "lp")
					.CreateAlias("lp.ParentLicensePlate", "plp")
				.Add("plp.LicensePlateCode", plate);
			InventoryItem inventory = Repositories.Get<InventoryItem>().Retrieve(criteria);
			//
			if (inventory == null) return new List<InventoryItem>();
			//
			Decimal quantity = inventory.Quantity;
			if (!String.IsNullOrEmpty(plate))
			{
				criteria = DetachedCriteria.For<LicensePlate>()
					.Add(Expression.Eq("CompanyLocationType", warehouse))
					.Add(Expression.Eq("LicensePlateCode", plate))
					.SetMaxResults(1);
				parent = Repositories.Get<LicensePlate>().Retrieve(criteria);
			}
			//
			criteria = DetachedCriteria.For<StatusCode>()
				.CreateAlias("FunctionalAreaCode", "fac")
				.Add(Expression.Eq("Code", inventory.StatusCodeCode))
				.Add(Expression.Eq("fac.Code", CodeValue.GetCode(FunctionalAreas.Inventory)))
				.SetMaxResults(1);
			StatusCode status = Repositories.Get<StatusCode>().Retrieve(criteria);
			//
			IList<InventoryItem> list = new List<InventoryItem>();
            //
            Boolean? suggestCustomerSpecific = BusinessRule.RetrieveBoolean("11121");
            if (suggestCustomerSpecific.HasValue && suggestCustomerSpecific.Value)
            {
                quantity = this.SuggestCustomerSpecific(ref list, inventory.CustomerCode, quantity);
                foreach (InventoryItem element in list)
                {
                    element.ParentLicensePlate = parent;
                    element.StatusCode = status;
                }
                //
                return list;
            }
            //
			quantity = this.SuggestPrimary(ref list, quantity);
			quantity = this.SuggestBulk(ref list, status, quantity);
            Boolean? suggest = BusinessRule.RetrieveBoolean("11123");
            if (suggest.HasValue && suggest.Value) quantity = this.SuggestPreviousInventory(ref list, quantity);
			quantity = this.SuggestOpen(ref list, quantity);
			foreach (InventoryItem element in list)
			{
				element.ParentLicensePlate = parent;
				element.StatusCode = status;
			}
			//
			return list;
		}

		#endregion

		#region Methods.Public.WorkCenter

		public virtual void CheckKitQuantity(Location location, int quantity)
		{
			this.InventoryAvailable = true;

			DetachedCriteria criteria = DetachedCriteria.For<KitDetail>()
				.Add("Active", "A")
				.Add("KitHeader.Item", this);
			IList<KitDetail> components = Repositories.Get<KitDetail>().List(criteria);

			if (components != null && components.Count > 0)
			{
				foreach (KitDetail component in components)
				{
					criteria = DetachedCriteria.For<InventoryItem>()
						.Add("CompanyLocationType", Registry.Find<CompanyLocationType>())
						.Add("Item", component.Item)
						.Add("Location", location)
						.Add("Quantity", ">", 0M)
						.Add("StatusCode", Entity.Retrieve<StatusCode>(InventoryStatuses.WorkInProgress))
						.SetProjection(Projections.Sum("Quantity"));

					if ((component.MinimumQuantity * quantity) > Repositories.Get<Location>().Function<Decimal>(criteria))
					{
						this.InventoryAvailable = false;
						break;
					}
				}
			}
		}

		public virtual void CheckRepackQuantity(Location location, int quantity)
		{
			DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
				.Add("Item", this)
				.Add("Location", location)
				.Add("Quantity", ">", 0M)
				.Add("StatusCode", Entity.Retrieve<StatusCode>(InventoryStatuses.WorkInProgress))
				.SetProjection(Projections.Sum("Quantity"));
			Decimal current = Repositories.Get<Location>().Function<Decimal>(criteria);
			//
			this.InventoryAvailable = (current >= quantity);
		}

		public virtual void ConsumeInventory(Location from, Decimal quantity, StatusCode status)
		{
			String code = BusinessRule.RetrieveString("15");
			if (String.IsNullOrEmpty(code)) throw new Exception("BR15 is not set.");
			//
			DetachedCriteria criteria = DetachedCriteria.For<InventoryAdjustmentCode>()
				.Add("Active", "A")
				.Add("Code", code)
				.Add("CompanyLocationType", Registry.Find<CompanyLocationType>())
				.SetMaxResults(1);
			InventoryAdjustmentCode adjustment = Repositories.Get<InventoryAdjustmentCode>().Retrieve(criteria);
			if (adjustment == null) throw new Exception(String.Format("Unable to find adjustment code for {0}.", code));
			//
			String order = BusinessRule.RetrieveString("11052");
			criteria = DetachedCriteria.For<KitDetail>()
				.Add("Active", "A")
				.Add("KitHeader.Item", this);
			IList<KitDetail> components = Repositories.Get<KitDetail>().List(criteria);
			foreach (KitDetail component in components)
			{
				criteria = DetachedCriteria.For<InventoryItem>()
					.Add("Item", component.Item)
					.Add("Location", from)
					.Add("Quantity", ">", 0M)
					.Add("StatusCode", status);
				if ("FEFO".Equals(order)) criteria.AddOrder("LotExpiration", true);
				else if ("FIFO".Equals(order)) criteria = criteria.AddOrder("Received", true);
				else if ("FMFO".Equals(order)) criteria = criteria.AddOrder("ManufactureDate", true);
				else if ("STOL".Equals(order)) criteria = criteria.AddOrder("Quantity", true);
				else throw new Exception("BR11052 is not set.");
				IList<InventoryItem> inventory = Repositories.Get<InventoryItem>().List(criteria);
				//
				Decimal remaining = quantity * Converter.ToDecimal(component.MinimumQuantity);
				foreach (InventoryItem element in inventory)
				{
					if (remaining <= 0) break;
					//
					Decimal fromQuantity = element.Quantity;
					remaining = element.Consume(remaining);
					//
					element.OrderCode = this.WorkOrderCode;
					element.WriteStockAdjustment(element.SerialNumber, adjustment, fromQuantity, element.StatusCode);
				}
			}
		}

		public virtual void CreateInventory(CompanyOrganizationalUnit center)
		{
			Location locationFrom = null;
			Location locationTo = null;
			//
			StatusCode available = Entity.Retrieve<StatusCode>(InventoryStatuses.Available);
			StatusCode workInProgress = Entity.Retrieve<StatusCode>(Upp.Irms.Constants.InventoryStatuses.WorkInProgress);
			LicenseType palletLicenseType = Entity.Retrieve<LicenseType>(InventoryLicenseTypes.Pallet);
			//
			if ("A".Equals(center.WorkOrderTypeCode)) locationFrom = center.PickLocation;
			else if ("M".Equals(center.WorkOrderTypeCode)) locationFrom = center.StageInLocation;
			//
			locationTo = center.StageOutLocation;
			//
			if (locationTo != null)
			{
				String adjustmentCode = BusinessRule.RetrieveString("14");
				DetachedCriteria criteria = DetachedCriteria.For<InventoryAdjustmentCode>()
											  .Add("Active", "A")
											  .Add("Code", adjustmentCode)
											  .SetMaxResults(1);
				InventoryAdjustmentCode adjustment = Repositories.Get<InventoryAdjustmentCode>().Retrieve(criteria);
				//
				DateTime? manufacture = InventoryItem.CalculateManufactureDate(this.LotNumber);
				this.StatusCode = InventoryItem.UpdateStatus(this, available, manufacture);
				//
				for (int i = 0; i < this.LpnCount; i++)
				{
					InventoryItem item = InventoryItem.Create(this, locationTo, this.StatusCode, this.Quantity);
					item.LotExpiration = this.LotExpiration;
					item.LotNumber = this.LotNumber;
					item.ManufactureDate = manufacture;
					Repositories.Get<InventoryItem>().Add(item);
					//
					if (Registry.Find<CompanyLocationType>() != null)
					{
						LicensePlate plate = LicensePlate.Create(palletLicenseType);
						plate.CompanyLocationType = locationTo.CompanyLocationType;
                        plate.LicensePlateCode = Upp.Irms.Domain.LicensePlateCode.GetCurrentValue(palletLicenseType);
						Repositories.Get<LicensePlate>().Add(plate);
						//
						item.LicensePlate.ChangeParent(plate);
						plate.ChangeLocation(locationTo);
						//
						if (this.Repack) item.WriteRepack(plate);
						//
						if (this.PrinterTypePrinter != null) plate.PrintLabel(this.PrinterTypePrinter);
					}
					//
					item.OrderCode = this.WorkOrderCode;
					item.WriteStockAdjustment(item.SerialNumber, adjustment, 0, null);
				}
				//
				ConsumeInventory(locationFrom, this.Quantity * this.LpnCount, workInProgress);
			}
		}

		public virtual void CreateInventory(CompanyOrganizationalUnit center, string licensePlateIds)
		{
			if (center.StageOutLocation == null) return;
			//
			Location from = null;
			StatusCode available = Entity.Retrieve<StatusCode>(InventoryStatuses.Available);
			StatusCode workInProgress = Entity.Retrieve<StatusCode>(InventoryStatuses.WorkInProgress);
			//
			if ("A".Equals(center.WorkOrderTypeCode)) from = center.PickLocation;
			else if ("M".Equals(center.WorkOrderTypeCode)) from = center.StageInLocation;
			//
			String adjustmentCode = BusinessRule.RetrieveString("14");
			DetachedCriteria criteria = DetachedCriteria.For<InventoryAdjustmentCode>()
				.Add("Active", "A")
				.Add("Code", adjustmentCode)
				.Add("CompanyLocationType", Registry.Find<CompanyLocationType>())
				.SetMaxResults(1);
			InventoryAdjustmentCode adjustment = Repositories.Get<InventoryAdjustmentCode>().Retrieve(criteria);
			//
			if (this.Repack) for (int i = 0; i < this.LpnCount; i++) this.RepackInventory(from, this.Quantity, workInProgress);
			else this.ConsumeInventory(from, this.Quantity * this.LpnCount, workInProgress);
			//
			DateTime? manufacture = InventoryItem.CalculateManufactureDate(this.LotNumber);
			this.StatusCode = InventoryItem.UpdateStatus(this, available, manufacture);
			//
			String[] ids = licensePlateIds.Split('|');
			for (int i = 0; i < ids.Length; i++)
			{
				String[] parts = ids[i].Split(';');
				InventoryItem item = InventoryItem.Create(this, center.StageOutLocation, this.StatusCode, this.Quantity);
				item.LotExpiration = this.LotExpiration;
				item.LotNumber = this.LotNumber;
				item.ManufactureDate = manufacture;
				Repositories.Get<InventoryItem>().Add(item);
				//
				Int32? id = Converter.ToNullableInt32(parts[0]);
				LicensePlate parent = Repositories.Get<LicensePlate>().Retrieve(id);
				item.LicensePlate.ChangeParent(parent);
				parent.ChangeLocation(center.StageOutLocation);
				//
				if (this.PrinterTypePrinter != null) parent.PrintLabel(this.PrinterTypePrinter);
				//
				if (this.Repack) item.WriteRepack(parent);
				//
				item.OrderCode = this.WorkOrderCode;
				item.WriteStockAdjustment(item.SerialNumber, adjustment, 0, null);
			}
		}

		public virtual void CreateInventory(CompanyOrganizationalUnit center, String[] licensePlateCodes)
		{
			Location locationTo = null;
			LicenseType pallet = Entity.Retrieve<LicenseType>(InventoryLicenseTypes.Pallet);
			StatusCode available = Entity.Retrieve<StatusCode>(InventoryStatuses.Available);
			StatusCode workInProgress = Entity.Retrieve<StatusCode>(InventoryStatuses.WorkInProgress);
			//
			Location from = null;
			if ("A".Equals(center.WorkOrderTypeCode)) from = center.PickLocation;
			else if ("M".Equals(center.WorkOrderTypeCode)) from = center.StageInLocation;
			//
			locationTo = center.StageOutLocation;
			//
			if (locationTo != null)
			{
				String adjustmentCode = BusinessRule.RetrieveString("14");
				DetachedCriteria criteria = DetachedCriteria.For<InventoryAdjustmentCode>()
											  .Add("Active", "A")
											  .Add("Code", adjustmentCode)
											  .SetMaxResults(1);
				InventoryAdjustmentCode adjustment = Repositories.Get<InventoryAdjustmentCode>().Retrieve(criteria);
				//
				DateTime? manufacture = InventoryItem.CalculateManufactureDate(this.LotNumber);
				this.StatusCode = InventoryItem.UpdateStatus(this, available, manufacture);
				//
				for (int i = 0; i < licensePlateCodes.Length; i++)
				{
					InventoryItem item = InventoryItem.Create(this, locationTo, this.StatusCode, this.Quantity);
					item.LotExpiration = this.LotExpiration;
					item.LotNumber = this.LotNumber;
					item.ManufactureDate = manufacture;
					Repositories.Get<InventoryItem>().Add(item);
					//
					if (Registry.Find<CompanyLocationType>() != null)
					{
						LicensePlate plate = LicensePlate.Create(pallet);
						plate.CompanyLocationType = locationTo.CompanyLocationType;
						plate.LicensePlateCode = licensePlateCodes[i];
						Repositories.Get<LicensePlate>().Add(plate);
						//
						item.LicensePlate.ChangeParent(plate);
						plate.ChangeLocation(locationTo);
						//
						if (this.Repack) item.WriteRepack(plate);
						//
						if (this.PrinterTypePrinter != null) plate.PrintLabel(this.PrinterTypePrinter);
					}
					//
					item.OrderCode = this.WorkOrderCode;
					item.WriteStockAdjustment(item.SerialNumber, adjustment, 0, null);
				}
				//
				ConsumeInventory(from, this.Quantity * this.LpnCount, workInProgress);
			}
		}

		public virtual void RepackInventory(Location from, Decimal quantity, StatusCode status)
		{
			String code = BusinessRule.RetrieveString("15");
			if (String.IsNullOrEmpty(code)) throw new Exception("BR15 is not set.");
			//
			DetachedCriteria criteria = DetachedCriteria.For<InventoryAdjustmentCode>()
				.Add("Active", "A")
				.Add("Code", code)
				.Add("CompanyLocationType", Registry.Find<CompanyLocationType>())
				.SetMaxResults(1);
			InventoryAdjustmentCode adjustment = Repositories.Get<InventoryAdjustmentCode>().Retrieve(criteria);
			if (adjustment == null) throw new Exception(String.Format("Unable to find adjustment code for {0}.", code));
			//
			String order = BusinessRule.RetrieveString("11052");
			criteria = DetachedCriteria.For<InventoryItem>()
				.Add("Item", this)
				.Add("Location", from)
				.Add("Quantity", ">", 0M)
				.Add("StatusCode", status);
			if ("FEFO".Equals(order)) criteria.AddOrder("LotExpiration", true);
			else if ("FIFO".Equals(order)) criteria = criteria.AddOrder("Received", true);
			else if ("FMFO".Equals(order)) criteria = criteria.AddOrder("ManufactureDate", true);
			else if ("STOL".Equals(order)) criteria = criteria.AddOrder("Quantity", true);
			else throw new Exception("BR11052 is not set.");
			IList<InventoryItem> inventory = Repositories.Get<InventoryItem>().List(criteria);
			//
			foreach (InventoryItem element in inventory)
			{
				if (quantity <= 0) break;
				//
				Decimal fromQuantity = element.Quantity;
				quantity = element.Consume(quantity);
				//
				element.OrderCode = this.WorkOrderCode;
				element.WriteStockAdjustment(element.SerialNumber, adjustment, fromQuantity, element.StatusCode);
			}
		}

		public virtual void ValidateKitHeader()
		{
			DetachedCriteria criteria = DetachedCriteria.For<KitHeader>()
				.Add(Expression.Eq("Active", "A"))
				.Add(Expression.Eq("Item", this))
				.SetMaxResults(1);
			KitHeader header = Repositories.Get<KitHeader>().Retrieve(criteria);
			if (header == null)
			{
				String error = String.Format("Item {0} is not a kit item.", _itemCode);
				throw new Exception(error);
			}
		}

		#endregion

		#region Methods.Public.Transactions

		public virtual void WriteDimensions()
		{
			TransactionType type = Entity.Retrieve<TransactionType>(InventoryTransactions.Dimensions);
			ItemTransaction transaction = Entity.Activate<ItemTransaction>();
			transaction.Item = this;
			transaction.Occurred = DateTime.Now;
			transaction.ParticipantBy = Registry.Find<UserAccount>().OrganizationParticipant;
			transaction.TransactionType = type;
			//
			this.ItemTransactions.Add(transaction);
		}

		#endregion
	}
}