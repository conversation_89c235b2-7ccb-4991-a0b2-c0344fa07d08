using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ItemCrossReference : Entity
	{
		#region Fields

		private CrossReferenceType _crossReferenceType;
		private DateTime _effective;
		private DateTime? _expiration;
		private Item _crossReferenceItem;
		private Item _item;
		private String _description;
		private String _itemCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual CrossReferenceType CrossReferenceType
		{
			get { return _crossReferenceType; }
			set { _crossReferenceType = value; }
		}

		[DataMember]
		public virtual DateTime Effective
		{
			get { return _effective; }
			set { _effective = value; }
		}

		[DataMember]
		public virtual DateTime? Expiration
		{
			get { return _expiration; }
			set { _expiration = value; }
		}

		[DataMember]
		public virtual Item CrossReferenceItem
		{
			get { return _crossReferenceItem; }
			set { _crossReferenceItem = value; }
		}

		[DataMember]
		public virtual Item Item
		{
			get { return _item; }
			set { _item = value; }
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set { _description = value; }
		}

		[DataMember]
		public virtual String ItemCode
		{
			get { return _itemCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ItemCode must not be blank or null.");
				else _itemCode = value;
			}
		}


		#endregion
	}
}
