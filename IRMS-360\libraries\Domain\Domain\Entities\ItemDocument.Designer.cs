using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ItemDocument : Entity
	{
		#region Fields

		private Byte[] _document;
		private DateTime? _effective;
		private DateTime? _expiration;
		private DocumentType _documentType;
		private Item _item;
		private String _alternateLocation;
		private String _documentCode;
		private String _fileName;
		private String _notes;
		private VendorLocationType _vendorLocationType;

		#endregion

		#region Properties

		[DataMember]
		public virtual Byte[] Document
		{
			get { return _document; }
			set { _document = value; }
		}

		[DataMember]
		public virtual DateTime? Effective
		{
			get { return _effective; }
			set { _effective = value; }
		}

		[DataMember]
		public virtual DateTime? Expiration
		{
			get { return _expiration; }
			set { _expiration = value; }
		}

		[DataMember]
		public virtual DocumentType DocumentType
		{
			get { return _documentType; }
			set { _documentType = value; }
		}

		[DataMember]
		public virtual Item Item
		{
			get { return _item; }
			set { _item = value; }
		}

		[DataMember]
		public virtual String AlternateLocation
		{
			get { return _alternateLocation; }
			set { _alternateLocation = value; }
		}

		[DataMember]
		public virtual String DocumentCode
		{
			get { return _documentCode; }
			set { _documentCode = value; }
		}

		[DataMember]
		public virtual String FileName
		{
			get { return _fileName; }
			set { _fileName = value; }
		}

		[DataMember]
		public virtual String Notes
		{
			get { return _notes; }
			set { _notes = value; }
		}

		[DataMember]
		public virtual VendorLocationType VendorLocationType
		{
			get { return _vendorLocationType; }
			set { _vendorLocationType = value; }
		}


		#endregion
	}
}
