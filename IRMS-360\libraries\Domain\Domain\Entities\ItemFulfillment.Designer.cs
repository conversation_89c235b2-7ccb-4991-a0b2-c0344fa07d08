using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ItemFulfillment : Entity
	{
		#region Fields

		private CartonHeader _cartonHeader;
		private Decimal _quantity;
		private InventoryItem _inventoryItem;
		private Item _item;
		private LicensePlate _licensePlate;
		private ICollection<InventoryPick> _inventoryPicks = new HashSet<InventoryPick>();
		private ICollection<LicensePlate> _licensePlates = new HashSet<LicensePlate>();
		private ICollection<ShipmentDetail> _shipmentDetails = new HashSet<ShipmentDetail>();
		private Location _location;
		private OrderDetail _orderDetail;
		private OrganizationParticipant _organizationParticipant;
		private PreReceiptDetail _preReceiptDetail;
		private PurchaseOrderDetail _purchaseOrderDetail;
		private RequisitionDetail _requisitionDetail;
		private StatusCode _requiredStatusCode;
		private StatusCode _statusCode;
		private String _waveBatch;
		private Wave _wave;
		private WorkOrderDetail _workOrderDetail;

		#endregion

		#region Properties

		[DataMember]
		public virtual CartonHeader CartonHeader
		{
			get { return _cartonHeader; }
			set { _cartonHeader = value; }
		}

		[DataMember]
		public virtual Decimal Quantity
		{
			get { return _quantity; }
			set { _quantity = value; }
		}

		[DataMember]
		public virtual InventoryItem InventoryItem
		{
			get { return _inventoryItem; }
			set { _inventoryItem = value; }
		}

		[DataMember]
		public virtual Item Item
		{
			get { return _item; }
			set { _item = value; }
		}

		[DataMember]
		public virtual LicensePlate LicensePlate
		{
			get { return _licensePlate; }
			set { _licensePlate = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryPick> InventoryPicks
		{
			get { return _inventoryPicks; }
			set { _inventoryPicks = value; }
		}

		[DataMember]
		public virtual ICollection<LicensePlate> LicensePlates
		{
			get { return _licensePlates; }
			set { _licensePlates = value; }
		}

		[DataMember]
		public virtual ICollection<ShipmentDetail> ShipmentDetails
		{
			get { return _shipmentDetails; }
			set { _shipmentDetails = value; }
		}

		[DataMember]
		public virtual Location Location
		{
			get { return _location; }
			set { _location = value; }
		}

		[DataMember]
		public virtual OrderDetail OrderDetail
		{
			get { return _orderDetail; }
			set { _orderDetail = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant OrganizationParticipant
		{
			get { return _organizationParticipant; }
			set { _organizationParticipant = value; }
		}

		[DataMember]
		public virtual PreReceiptDetail PreReceiptDetail
		{
			get { return _preReceiptDetail; }
			set { _preReceiptDetail = value; }
		}

		[DataMember]
		public virtual PurchaseOrderDetail PurchaseOrderDetail
		{
			get { return _purchaseOrderDetail; }
			set { _purchaseOrderDetail = value; }
		}

		[DataMember]
		public virtual RequisitionDetail RequisitionDetail
		{
			get { return _requisitionDetail; }
			set { _requisitionDetail = value; }
		}

		[DataMember]
		public virtual StatusCode RequiredStatusCode
		{
			get { return _requiredStatusCode; }
			set { _requiredStatusCode = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual String WaveBatch
		{
			get { return _waveBatch; }
			set { _waveBatch = value; }
		}

		[DataMember]
		public virtual Wave Wave
		{
			get { return _wave; }
			set { _wave = value; }
		}

		[DataMember]
		public virtual WorkOrderDetail WorkOrderDetail
		{
			get { return _workOrderDetail; }
			set { _workOrderDetail = value; }
		}


		#endregion
	}
}
