using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;

using NHibernate;
using NHibernate.Criterion;
using NHibernate.Transform;

using Upp.Irms.Constants;
using Upp.Irms.Core;

using Upp.Shared.Utilities;

namespace Upp.Irms.Domain
{
    [DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
    public partial class ItemFulfillment : Entity
    {

        #region Properties.Reports

        public virtual Decimal? CatchWeight { get; set; }
        public virtual Decimal Weight { get; set; }
        public virtual Int32 InvItemId { get; set; }
        public virtual Int32? LocationId { get; set; }
        public virtual Int32? FulfillmentItemId { get; set; }
        public virtual String PickedLicensePlateCode { get; set; }
        public virtual String PurchaseOrderNumber { get; set; }

        #endregion Properties.Reports

        #region Properties

        [DataMember]
        public virtual Boolean AllowNegative { get; set; }
        [DataMember]
        public virtual Boolean BackOrderedLine { get; set; }
        [DataMember]
        public virtual Boolean Close { get; set; }
        [DataMember]
        public virtual Boolean Last { get; set; }
        [DataMember]
        public virtual Boolean Move { get; set; }
        [DataMember]
        public virtual Boolean NotifyUser { get; set; }
        [DataMember]
        public virtual Boolean PopulateItem { get; set; }
        [DataMember]
        public virtual Boolean PopulateLocation { get; set; }
        [DataMember]
        public virtual Boolean PopulateDefaultCarton { get; set; }
        [DataMember]
        public virtual Boolean PromptSlotID { get; set; }
        [DataMember]
        public virtual Boolean Replenished { get; set; }
        [DataMember]
        public virtual Boolean RequireCartonSize { get; set; }
        [DataMember]
        public virtual Boolean ShowComments { get; set; }
        [DataMember]
        public virtual Boolean SuggestStageLocation { get; set; }
        [DataMember]
        public virtual Boolean ZoneComplete { get; set; }
        [DataMember]
        public virtual CartonSize CartonSize { get; set; }
        [DataMember]
        public virtual Decimal? ItemWeight { get; set; }
        [DataMember]
        public virtual Dock Dock { get; set; }
        [DataMember]
        public virtual Decimal AvailableQuantity { get; set; }
        [DataMember]
        public virtual Int32 LineNumber { get; set; }
        [DataMember]
        public virtual Decimal PendingQuantity { get; set; }
        [DataMember]
        public virtual Decimal PickedQuantity { get; set; }
        [DataMember]
        public virtual Int32 UomFactor { get; set; }
        [DataMember]
        public virtual Int32? LpnCount { get; set; }
        [DataMember]
        public virtual Int32? PickSequence { get; set; }
        [DataMember]
        public virtual Int32? ZonePickSequence { get; set; }
        [DataMember]
        public virtual Int32? LocationPickSequence { get; set; }
        [DataMember]
        public virtual Int32 StatusCodeId { get; set; }
        [DataMember]
        public virtual Int32? StockStatusCodeId { get; set; }
        [DataMember]
        public virtual LicensePlate CartonLicensePlate { get; set; }
        [DataMember]
        public virtual LicensePlate LicensePlateFrom { get; set; }
        [DataMember]
        public virtual Location StageLocation { get; set; }
        [DataMember]
        public virtual Location LocationTo { get; set; }
        [DataMember]
        public virtual OrderHeader OrderHeader { get; set; }
        [DataMember]
        public virtual PrinterTypePrinter PrinterTypePrinter { get; set; }
        [DataMember]
        public virtual String AisleCode { get; set; }
        [DataMember]
        public virtual String BillOfLading { get; set; }
        [DataMember]
        public virtual String CarrierCode { get; set; }
        [DataMember]
        public virtual String CartonLicensePlateCode { get; set; }
        [DataMember]
        public virtual String CustomerHidePrice { get; set; }
        [DataMember]
        public virtual String ItemCode { get; set; }
        [DataMember]
        public virtual String ItemDescription { get; set; }
        [DataMember]
        public virtual String ItemLongDescription { get; set; }
        [DataMember]
        public virtual String LicensePlateIds { get; set; }
        [DataMember]
        public virtual String LocationCode { get; set; }
        [DataMember]
        public virtual String LotNumber { get; set; }
        [DataMember]
        public virtual String OrderClassCode { get; set; }
        [DataMember]
        public virtual Int32? OrderDetailId { get; set; }
        [DataMember]
        public virtual Int32? OrderHeaderId { get; set; }
        [DataMember]
        public virtual String OrderCode { get; set; }
        [DataMember]
        public virtual String OrderCustomerCode { get; set; }
        [DataMember]
        public virtual String OrderDetailComments { get; set; }
        [DataMember]
        public virtual String OrderHeaderComments { get; set; }
        [DataMember]
        public virtual String OrderStatusCode { get; set; }
        [DataMember]
        public virtual String OrderSuffix { get; set; }
        [DataMember]
        public virtual String OrderTypeCode { get; set; }
        [DataMember]
        public virtual String PickingUser { get; set; }
        [DataMember]
        public virtual string PickTypeCode { get; set; }
        [DataMember]
        public virtual String RequisitionCode { get; set; }
        [DataMember]
        public virtual String SerialNumbers { get; set; }
        [DataMember]
        public virtual String SlotNumber { get; set; }
        [DataMember]
        public virtual String StatusCodeCode { get; set; }
        [DataMember]
        public virtual String StatusCodeDescription { get; set; }
        [DataMember]
        public virtual String StockStatusCodeCode { get; set; }
        [DataMember]
        public virtual String TruckCode { get; set; }
        [DataMember]
        public virtual String UomDescription { get; set; }
        [DataMember]
        public virtual String WaveCode { get; set; }
        [DataMember]
        public virtual String ZoneCode { get; set; }
        [DataMember]
        public virtual String Pallet { get; set; }
        [DataMember]
        public virtual Boolean IsMixed { get; set; }
        [DataMember]
        public virtual String DimensionalUomCode { get; set; }
        [DataMember]
        public virtual Decimal? ItemCube { get; set; }
        #endregion

        #region Properties.Reports

        [DataMember]
        public virtual Int32 CartonCount { get; set; }
        [DataMember]
        public virtual Int32? CartonId { get; set; }
        public virtual int? ItemsId { get; set; }
        [DataMember]
        public virtual Int32? LpnId { get; set; }
        [DataMember]
        public virtual Int32 CartonSequence { get; set; }
        [DataMember]
        public virtual Decimal? CaseFactor { get; set; }
        [DataMember]
        public virtual Decimal CartQuantity { get; set; }
        [DataMember]
        public virtual Decimal? PalletFactor { get; set; }
        [DataMember]
        public virtual Decimal? InnerPackFactor { get; set; }
        [DataMember]
        public virtual Decimal ShippedQuantity { get; set; }
        [DataMember]
        public virtual Int32? LicensePlateId { get; set; }
        [DataMember]
        public virtual Int32? OrderId { get; set; }
        [DataMember]
        public virtual Int32? OrderLineId { get; set; }
        public virtual Int32? OrderDetailLineNumber { get; set; }
        [DataMember]
        public virtual Int32 PickItemId { get; set; }
        [DataMember]
        public virtual Int32 WaveId { get; set; }
        [DataMember]
        public virtual String Conveyable { get; set; }
        [DataMember]
        public virtual String CustomerPackingList { get; set; }
        [DataMember]
        public virtual String CustomerPackingListPath { get; set; }
        [DataMember]
        public virtual String HazardousMaterial { get; set; }
        [DataMember]
        public virtual String NetworkPassword { get; set; }
        [DataMember]
        public virtual String NetworkUsername { get; set; }
        [DataMember]
        public virtual String OrderHostBatch { get; set; }
        [DataMember]
        public virtual String PackAlone { get; set; }
        [DataMember]
        public virtual String TrackingCode { get; set; }
        [DataMember]
        public virtual String Ucc128 { get; set; }
        [DataMember]
        public virtual String UpcCode { get; set; }
        [DataMember]
        public virtual String WaveHostBatch { get; set; }
        [DataMember]
        public virtual String ZoneGroupCode { get; set; }

        #endregion

        #region Constructor

        public ItemFulfillment()
        {
            //
        }

        #endregion

        #region Methods.Private

        private StatusCode GetStatusCode(OrderHeader order)
        {
            Boolean allowInduction = BusinessRule.RetrieveBoolean("10201").Value;
            StatusCode inducted = Entity.Retrieve<StatusCode>(LicensePlateStatuses.Inducted);
            StatusCode open = Entity.Retrieve<StatusCode>(LicensePlateStatuses.Open);
            StatusCode status = open;

            if (true.Equals(allowInduction))
            {
                String branchCodes = BusinessRule.RetrieveString("10200");
                //
                if (!String.IsNullOrEmpty(branchCodes))
                {
                    if (!String.IsNullOrEmpty(order.BranchCode) && branchCodes.Contains(order.BranchCode.ToUpper()))
                        status = inducted;
                }
                else status = inducted;
            }
            //
            return status;
        }

        #endregion

        #region Methods.Private.Transactions

        public virtual void WritePacked(InventoryPick pick, Decimal quantity, string lotNumber, string serialNumber)
        {
            TransactionType type = Entity.Retrieve<TransactionType>(OrderTransactions.Packed);
            ItemTransaction transaction = Entity.Activate<ItemTransaction>();
            transaction.InventoryPick = pick;
            transaction.Item = _item;
            transaction.LicensePlateTo = pick.LicensePlate;
            transaction.LocationFrom = pick.Location;
            transaction.LotNumber = lotNumber;
            transaction.Occurred = DateTime.Now;
            if (_orderDetail != null) transaction.OrderHeader = _orderDetail.OrderHeader;
            transaction.ParticipantBy = Registry.Find<UserAccount>().OrganizationParticipant;
            transaction.Quantity = quantity;
            transaction.RequisitionDetail = _requisitionDetail;
            transaction.SerialNumber = serialNumber;
            transaction.TransactionType = type;
            //
            Repositories.Get<ItemTransaction>().Add(transaction);
        }

        public virtual void WritePicked(InventoryPick pick, InventoryItem item, Decimal quantity, string lotNumber, string serialNumber)
        {
            ItemTransaction transaction = Entity.Activate<ItemTransaction>();
            transaction.InventoryItem = item;
            transaction.InventoryPick = pick;
            transaction.Item = _item;
            if (item != null && item.LicensePlate != null) transaction.LicensePlateFrom = item.LicensePlate.ParentLicensePlate;
            transaction.LicensePlateTo = pick.LicensePlate;
            transaction.LocationFrom = pick.Location;
            transaction.LocationTo = this.LocationTo;
            transaction.LotNumber = lotNumber;
            transaction.Occurred = DateTime.Now;
            if (_orderDetail != null) transaction.OrderHeader = _orderDetail.OrderHeader;
            transaction.ParticipantBy = Registry.Find<UserAccount>().OrganizationParticipant;
            transaction.Quantity = quantity;
            transaction.RequisitionDetail = _requisitionDetail;
            transaction.SerialNumber = serialNumber;
            if (item != null) transaction.StatusCode = item.StatusCode;
            if (_orderDetail != null) transaction.TransactionType = Entity.Retrieve<TransactionType>(OrderTransactions.Picked);
            if (_requisitionDetail != null) transaction.TransactionType = Entity.Retrieve<TransactionType>(RequisitionTransactions.Picked);
            transaction.Wave = _wave;
            if (_orderDetail != null) transaction.UnitOfMeasure = _orderDetail.UnitOfMeasure;
            else if (item != null) transaction.UnitOfMeasure = item.UnitOfMeasure;
            //
            Repositories.Get<ItemTransaction>().Add(transaction);
        }

        private void WriteStaged(InventoryPick pick, InventoryItem item)
        {
            TransactionType type = Entity.Retrieve<TransactionType>(RequisitionTransactions.Staged);
            ItemTransaction transaction = Entity.Activate<ItemTransaction>();
            transaction.InventoryItem = item;
            transaction.InventoryPick = pick;
            transaction.Item = _item;
            transaction.LocationFrom = _location;
            transaction.LocationTo = this.LocationTo;
            transaction.Occurred = DateTime.Now;
            transaction.ParticipantBy = Registry.Find<UserAccount>().OrganizationParticipant;
            transaction.Quantity = pick.Quantity;
            transaction.RequisitionDetail = _requisitionDetail;
            transaction.StatusCode = item.StatusCode;
            transaction.TransactionType = type;
            //
            Repositories.Get<ItemTransaction>().Add(transaction);
        }

        #endregion

        #region Methods.Public

        public virtual void Activate()
        {
            _dateModified = DateTime.Now;
            /*if (_orderDetail != null)
			{
				_statusCode = Entity.Retrieve<StatusCode>(OrderStatuses.InPick);
				_orderDetail.UpdateStatus();
				if (_orderDetail.OrderHeader.ShouldUpdateStatus()) _orderDetail.OrderHeader.UpdateStatus();
			}
			*/
            if (_requisitionDetail != null)
            {
                _statusCode = Entity.Retrieve<StatusCode>(RequisitionStatuses.InPick);
                _requisitionDetail.UpdateStatus();
                if (_requisitionDetail.RequisitionHeader.ShouldUpdateStatus()) _requisitionDetail.RequisitionHeader.UpdateStatus();
            }
            //
            _userModified = Registry.Find<UserAccount>().UserName;
            Repositories.Get<ItemFulfillment>().Update(this);
        }

        public virtual void AdjustRepackBoxQuantity(CartonSize cartonSzieTo)
        {
            if (cartonSzieTo == null) return;
            //
            if (cartonSzieTo.Item != null)
            {
                DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
                      .Add("CompanyLocationType", Registry.Find<CompanyLocationType>())
                      .Add("Item", cartonSzieTo.Item)
                      .Add("Quantity", ">", 0M)
                      .Add("StatusCode.Code", CodeValue.GetCode(InventoryStatuses.Available))
                      .AddOrder("Received")
                      .SetMaxResults(1);
                InventoryItem item = Repositories.Get<InventoryItem>().Retrieve(criteria);
                //
                if (item != null)
                {
                    Decimal from = item.Quantity;
                    Decimal to = item.Quantity - 1;
                    StatusCode fromStatus = item.StatusCode;
                    //
                    item.CreateQuantityShadow(to);
                    item.ChangeQuantity(to);
                    //
                    InventoryAdjustmentCode adjustment = Entity.Retrieve<InventoryAdjustmentCode>(InventoryAdjustmentCodes.RepackBox);
                    item.WriteStockAdjustment(null, adjustment, from, fromStatus);
                }
            }
        }

        public virtual void BulkPick(bool close)
        {
            StatusCode active = Entity.Retrieve<StatusCode>(OrderStatuses.Active);
            StatusCode comeplete = Entity.Retrieve<StatusCode>(OrderStatuses.Complete);
            StatusCode lpnPacked = Entity.Retrieve<StatusCode>(LicensePlateStatuses.Packed);
            StatusCode lpnShipped = Entity.Retrieve<StatusCode>(LicensePlateStatuses.Shipped);
            StatusCode open = Entity.Retrieve<StatusCode>(ShippingStatuses.Open);
            StatusCode packed = Entity.Retrieve<StatusCode>(OrderStatuses.Packed);
            StatusCode picked = Entity.Retrieve<StatusCode>(OrderStatuses.Picked);
            StatusCode shipped = Entity.Retrieve<StatusCode>(OrderStatuses.Shipped);
            //
            if (this.PickedQuantity == 0)
            {
                InventoryPick pick = InventoryPick.Create(this);
                pick.Location = this.LocationTo;
                pick.Quantity = 0;
                pick.StatusCode = shipped;
                Repositories.Get<InventoryPick>().Add(pick);
                //
                _statusCode = picked;
            }
            else if (_quantity > this.PickedQuantity * this.UomFactor)
            {
                ItemFulfillment split = Entity.Clone<ItemFulfillment>(this);
                split.InventoryItem = null;
                split.Quantity = _quantity - (this.PickedQuantity * this.UomFactor);
                split.StatusCode = Entity.Retrieve<StatusCode>(OrderStatuses.Distributed);
                if (close) split.StatusCode = shipped;
                Repositories.Get<ItemFulfillment>().Add(split);
                //
                _quantity = this.PickedQuantity * this.UomFactor;
            }
            //
            String[] ids = new String[0];
            if (!String.IsNullOrEmpty(this.LicensePlateIds)) ids = this.LicensePlateIds.Split('|');
            for (int i = 0; i < ids.Length; i++)
            {
                String[] parts = ids[i].Split(';');
                DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
                    .Add("LicensePlate.ParentLicensePlate.Id", Converter.ToNullableInt32(parts[0]))
                    .Add("Quantity", ">", 0M)
                    .SetMaxResults(1);
                InventoryItem item = Repositories.Get<InventoryItem>().Retrieve(criteria);
                //
                InventoryPick pick = null;
                if (i < this.PickedQuantity - 1)
                {
                    ItemFulfillment split = Entity.Clone<ItemFulfillment>(this);
                    split.InventoryItem = item;
                    split.Location = this.LocationTo;
                    split.Quantity = this.UomFactor;
                    split.StatusCode = picked;
                    Repositories.Get<ItemFulfillment>().Add(split);
                    //
                    pick = InventoryPick.Create(split);
                    //
                    _quantity -= this.UomFactor;
                }
                else
                {
                    _inventoryItem = item;
                    _location = this.LocationTo;
                    _quantity = this.UomFactor;
                    _statusCode = picked;
                    //
                    pick = InventoryPick.Create(this);
                }
                //
                pick.LicensePlate = item.LicensePlate.ParentLicensePlate;
                pick.Quantity = this.UomFactor;
                pick.StatusCode = picked;
                Repositories.Get<InventoryPick>().Add(pick);
                //
                if (parts.Length == 1) this.WritePicked(pick, item, this.UomFactor, null, null);
                else this.WritePicked(pick, item, this.UomFactor, parts[1], null);
                //
                item.ChangeQuantity(item.Quantity - this.UomFactor);
            }
            //
            _orderDetail.UpdateStatus();
            if (_orderDetail.OrderHeader.ShouldUpdateStatus()) _orderDetail.OrderHeader.UpdateStatus();
            if (_wave != null) _wave.ChangeStatus(active);
            //
            for (int i = 0; i < ids.Length; i++)
            {
                String[] parts = ids[i].Split(';');
                DetachedCriteria criteria = DetachedCriteria.For<InventoryPick>()
                    .Add("LicensePlate.Id", Converter.ToNullableInt32(parts[0]))
                    .SetMaxResults(1);
                InventoryPick pick = Repositories.Get<InventoryPick>().Retrieve(criteria);
                if (pick != null)
                {
                    pick.StatusCode = packed;
                    Repositories.Get<InventoryPick>().Update(pick);
                    //
                    pick.ItemFulfillment.StatusCode = packed;
                    Repositories.Get<ItemFulfillment>().Update(pick.ItemFulfillment);
                    //
                    pick.LicensePlate.ChangeStatus(lpnPacked);
                    //
                    if (parts.Length == 1) this.WritePacked(pick, this.UomFactor, null, null);
                    else this.WritePacked(pick, this.UomFactor, parts[1], null);
                }
            }
            //
            if (this.PickedQuantity == 0) _statusCode = packed;
            _orderDetail.UpdateStatus();
            if (_orderDetail.OrderHeader.ShouldUpdateStatus()) _orderDetail.OrderHeader.UpdateStatus();
            //
            if (this.Dock != null)
            {
                this.Dock.FindShipmentHeader();
                if (!String.IsNullOrEmpty(this.BillOfLading))
                {
                    this.Dock.ShipmentHeader.BillOfLading = this.BillOfLading;
                    this.Dock.ShipmentHeader.DateModified = DateTime.Now;
                    this.Dock.ShipmentHeader.UserModified = Registry.Find<UserAccount>().UserName;
                    Repositories.Get<ShipmentHeader>().Update(this.Dock.ShipmentHeader);
                }
                //
                Decimal weight = 0;
                DetachedCriteria criteria = DetachedCriteria.For<ItemUomRelationship>()
                    .Add("Item", _item)
                    .Add("UnitOfMeasure", _orderDetail.UnitOfMeasure)
                    .SetMaxResults(1);
                ItemUomRelationship relationship = Repositories.Get<ItemUomRelationship>().Retrieve(criteria);
                if (relationship != null && relationship.Weight != null) weight = relationship.Weight.Value;
                //
                for (int i = 0; i < ids.Length; i++)
                {
                    String[] parts = ids[i].Split(';');
                    criteria = DetachedCriteria.For<InventoryPick>()
                        .Add("LicensePlate.Id", Converter.ToNullableInt32(parts[0]))
                        .SetMaxResults(1);
                    InventoryPick pick = Repositories.Get<InventoryPick>().Retrieve(criteria);
                    if (pick != null)
                    {
                        pick.StatusCode = shipped;
                        Repositories.Get<InventoryPick>().Update(pick);
                        //
                        pick.ItemFulfillment.StatusCode = shipped;
                        Repositories.Get<ItemFulfillment>().Update(pick.ItemFulfillment);
                        //
                        pick.LicensePlate.ChangeStatus(lpnShipped);
                        //
                        ShipmentDetail detail = Entity.Activate<ShipmentDetail>();
                        detail.LicensePlate = pick.LicensePlate;
                        detail.Quantity = 1;
                        detail.ShipmentHeader = this.Dock.ShipmentHeader;
                        detail.StatusCode = open;
                        detail.Weight = weight;
                        Repositories.Get<ShipmentDetail>().Add(detail);
                    }
                }
                if (_wave != null) _wave.Close();
                //
                if (this.PickedQuantity == 0) _statusCode = shipped;
                _orderDetail.UpdateStatus();
                //
                OrderHeader order = _orderDetail.OrderHeader;
                if (_orderDetail.OrderHeader.ShouldUpdateStatus())
                {
                    order.UpdateStatus();
                    if (this.PrinterTypePrinter != null) order.PrintBillOfLading(this.PrinterTypePrinter);
                    //
                    if (this.Last && this.LpnCount.HasValue && this.LpnCount.Value > 0)
                    {
                        Boolean? chepCount = BusinessRule.RetrieveBoolean("11026");
                        if (chepCount.HasValue && chepCount.Value)
                        {
                            ReferenceCode chep = Entity.Retrieve<ReferenceCode>(OrderReferences.Chep);
                            order.CreateReference(chep, this.LpnCount.Value);
                        }
                        else
                        {
                            Boolean? pecoCount = BusinessRule.RetrieveBoolean("11261");
                            if (pecoCount.HasValue && pecoCount.Value)
                            {
                                ReferenceCode peco = Entity.Retrieve<ReferenceCode>(OrderReferences.Peco);
                                order.CreateReference(peco, this.LpnCount.Value);
                            }
                        }
                    }
                }
            }
        }

        public virtual void ChangeLocation(Location to)
        {
            _dateModified = DateTime.Now;
            _location = to;
            _userModified = Registry.Find<UserAccount>().UserName;
            //
            Repositories.Get<ItemFulfillment>().Update(this);
        }

        public virtual void CheckAllowNegativePicking()
        {
            Boolean? negative = BusinessRule.RetrieveBoolean("11092");
            this.AllowNegative = negative.HasValue ? negative.Value : false;
        }

        public virtual Boolean CheckKitPicking()
        {
            DetachedCriteria kitItem = DetachedCriteria.For<KitHeader>("kit")
                    .Add(Expression.EqProperty("_root.Item.Id", "kit.Item.Id"))
                    .SetProjection(Projections.Property("Item.Id"));

            DetachedCriteria criteria = DetachedCriteria.For<OrderDetail>("_root")
                .Add("LineNumber", this.OrderDetail.LineNumber)
                .Add("LineNumberSequence", 0)
                .Add("OrderHeader", this.OrderDetail.OrderHeader)
                .Add(Subqueries.PropertyEq("Item.Id", kitItem))
                .SetProjection(Projections.Count("Id"));

            return (Repositories.Get<OrderDetail>().Function<Int32>(criteria) > 0);
        }

        public virtual void CheckLast()
        {
            StatusCode shipped = Entity.Retrieve<StatusCode>(OrderStatuses.Shipped);
            DetachedCriteria criteria = DetachedCriteria.For<ItemFulfillment>()
                .Add("OrderDetail.OrderHeader", _orderDetail.OrderHeader)
                .Add("StatusCode.SortOrder", "<", shipped.SortOrder)
                .SetProjection(Projections.Count("Id"));
            this.Last = (Repositories.Get<ItemFulfillment>().Function<Int32>(criteria) == 1);
        }

        public virtual void CheckLast(String pickBy)
        {
            StatusCode picked = Entity.Retrieve<StatusCode>(OrderStatuses.Picked);
            DetachedCriteria criteria = DetachedCriteria.For<ItemFulfillment>()
                .Add("StatusCode.SortOrder", "<", picked.SortOrder)
                .SetProjection(Projections.Count("Id"));

            switch (pickBy)
            {
                case "Lpn":
                    criteria = criteria.Add("CartonHeader.LicensePlate", this.LicensePlate);
                    break;
                case "Order":
                    criteria = criteria.Add("OrderDetail.OrderHeader", _orderDetail.OrderHeader);
                    break;
                case "Wave":
                    criteria = criteria.Add("Wave", _wave);
                    break;
            }
            this.Last = (Repositories.Get<ItemFulfillment>().Function<Int32>(criteria) == 0);
        }

        public virtual void CheckPopulateItem()
        {
            Boolean? populate = BusinessRule.RetrieveBoolean("1084");
            if (populate.HasValue && populate.Value)
            {
                StatusCode available = Entity.Retrieve<StatusCode>(InventoryStatuses.Available);
                DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
                    .Add("Item", "!=", _item)
                    .Add("Location", _location)
                    .Add("Quantity", ">", 0M)
                    .Add("StatusCode", available)
                    .SetProjection(Projections.Count("Id"));
                this.PopulateItem = (Repositories.Get<InventoryItem>().Function<Int32>(criteria) == 0);
            }
        }

        public virtual void CheckPopulateLocation()
        {
            Boolean? populate = BusinessRule.RetrieveBoolean("2057");
            if (populate != null) this.PopulateLocation = populate.Value;
        }

        public virtual void CheckPopulateDefaultCarton()
        {
            Boolean? populate = BusinessRule.RetrieveBoolean("1057");
            if (populate != null) this.PopulateDefaultCarton = populate.Value;
        }

        public virtual void CheckPromptSlotID()
        {
            Boolean? populate = BusinessRule.RetrieveBoolean("11085");
            if (populate != null) this.PromptSlotID = populate.Value;
        }

        public virtual void CheckShowComments()
        {
            Boolean? show = BusinessRule.RetrieveBoolean("1");
            if (show != null) this.ShowComments = show.Value;
        }

        public virtual void CheckSuggestStageLocation()
        {
            Boolean? suggest = BusinessRule.RetrieveBoolean("11131");
            this.SuggestStageLocation = suggest.HasValue ? suggest.Value : false;
        }

        public virtual void CheckZoneComplete(CompanyLocationZone zone, OrderHeader order)
        {
            StatusCode active = Entity.Retrieve<StatusCode>(OrderStatuses.InPick);
            StatusCode open = Entity.Retrieve<StatusCode>(OrderStatuses.Distributed);
            //
            DetachedCriteria criteria = DetachedCriteria.For<ItemFulfillment>()
                .CreateAlias("OrderDetail", "od")
                .Add(Expression.Eq("od.OrderHeader", order))
                .AddOr("StatusCode", open, "StatusCode", active)
                .SetProjection(Projections.Count("Id"));
            Int32 count = Repositories.Get<ItemFulfillment>().Function<Int32>(criteria);
            if (count == 0) return;
            //
            criteria = DetachedCriteria.For<ItemFulfillment>()
                .CreateAlias("Location", "l")
                .CreateAlias("OrderDetail", "od")
                .Add(Expression.Eq("l.CompanyLocationZone", zone))
                .Add(Expression.Eq("od.OrderHeader", order))
                .Add(Expression.Eq("StatusCode", open))
                .SetProjection(Projections.Count("Id"));
            count = Repositories.Get<ItemFulfillment>().Function<Int32>(criteria);
            if (count > 0) return;
            //
            criteria = DetachedCriteria.For<ItemFulfillment>()
                .CreateAlias("Location", "l")
                .CreateAlias("OrderDetail", "od")
                .Add(Expression.Eq("l.CompanyLocationZone", zone))
                .Add(Expression.Eq("od.OrderHeader", order))
                .Add(Expression.Eq("StatusCode", active))
                .SetProjection(Projections.Count("Id"));
            count = Repositories.Get<ItemFulfillment>().Function<Int32>(criteria);
            if (count > 0) return;
            //
            this.ZoneComplete = true;
        }

        public virtual void CreateCartonHeader(Boolean movePicks)
        {
            CartonHeader fromCarton = _cartonHeader;
            StatusCode staus = GetStatusCode(_cartonHeader.OrderHeader);
            //
            LicenseType carton = Entity.Retrieve<LicenseType>(InventoryLicenseTypes.Carton);
            LicensePlate plate = LicensePlate.Create(carton);
            plate.LicensePlateCode = LicensePlateCode.GetCurrentValue(carton);
            Repositories.Get<LicensePlate>().Add(plate);
            //
            CartonHeader header = Entity.Activate<CartonHeader>();
            header.CartonSize = _cartonHeader.CartonSize;
            header.LicensePlate = plate;
            header.OrderHeader = _cartonHeader.OrderHeader;
            header.StatusCode = staus;
            Repositories.Get<CartonHeader>().Add(header);
            //
            header.LicensePlate.ChangeStatus(staus);
            //
            StatusCode distributed = Entity.Retrieve<StatusCode>(OrderStatuses.Distributed);
            StatusCode inPick = Entity.Retrieve<StatusCode>(OrderStatuses.InPick);
            //            
            DetachedCriteria criteria = DetachedCriteria.For<ItemFulfillment>()
                .Add("CartonHeader", _cartonHeader)
                .AddOr("StatusCode", distributed, "StatusCode", inPick);
            if (!movePicks) criteria = criteria.Add("Id", _id);
            IList<ItemFulfillment> picks = Repositories.Get<ItemFulfillment>().List(criteria);
            //
            decimal weight = 0.0M;
            foreach (ItemFulfillment element in picks)
            {
                element.CartonHeader = header;
                element.DateModified = DateTime.Now;
                element.UserModified = Registry.Find<UserAccount>().UserName;
                Repositories.Get<ItemFulfillment>().Update(element);
                //
                if (element.Item != null && element.Item.Weight != null)
                    weight += Convert.ToDecimal(element.Item.Weight * element.Quantity);
            }
            //
            header.CartonWeight = weight;
            header.DateModified = DateTime.Now;
            header.UserModified = Registry.Find<UserAccount>().UserName;
            Repositories.Get<CartonHeader>().Update(header);
            //
            fromCarton.CartonWeight -= weight;
            fromCarton.Cancel();
            Repositories.Get<CartonHeader>().Update(fromCarton);
            //
            String type = this.LicensePlate.LicenseType.LicenseTypeCode;
            if (CodeValue.GetCode(InventoryLicenseTypes.Tote).Equals(type))
            {
                StatusCode full = Entity.Retrieve<StatusCode>(LicensePlateStatuses.FullTote);
                StatusCode open = Entity.Retrieve<StatusCode>(IntegrationStatuses.Open);
                //
                this.LicensePlate.ChangeStatus(full, open);
            }
        }

        public virtual void FindDefaultCartonHeader()
        {
            StatusCode packed = Entity.Retrieve<StatusCode>(OrderStatuses.Packed);
            StatusCode picked = Entity.Retrieve<StatusCode>(OrderStatuses.Picked);
            //
            DetachedCriteria criteria = DetachedCriteria.For<ItemFulfillment>()
                .Add("CartonHeader", "!=", null)
                .Add("OrderDetail.OrderHeader", _orderDetail.OrderHeader)
                .AddOr("StatusCode", packed, "StatusCode", picked)
                .SetMaxResults(1);
            IList<ItemFulfillment> picks = Repositories.Get<ItemFulfillment>().List(criteria);

            if (picks != null && picks.Count > 0) this.CartonLicensePlateCode = picks[0].CartonHeader.LicensePlate.LicensePlateCode;
        }

        public virtual void FindOpenLpnOnSlot(string slotnumber, string truckCode)
        {
            CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
            //  
            DetachedCriteria criteria = DetachedCriteria.For<LicensePlateLocation>()
                   .Add("LicensePlate.CompanyLocationType", warehouse)
                   .Add("Location.LocationCode", "=", truckCode)
                   .Add("SlotNumber", slotnumber)
                   .SetFetchMode("LicensePlate", FetchMode.Join)
                   .SetFetchMode("LicensePlate.LicenseType", FetchMode.Join)
                   .SetMaxResults(1);
            LicensePlateLocation lpnlocation = Repositories.Get<LicensePlateLocation>().Retrieve(criteria);
            if (lpnlocation != null)
            {
                StatusCode open = Entity.Retrieve<StatusCode>(Upp.Irms.Constants.LicensePlateStatuses.Open);
                //
                lpnlocation.LicensePlate.FindCurrentStatus();
                if (lpnlocation.LicensePlate.CurrentStatus != null
                    && lpnlocation.LicensePlate.CurrentStatus.Code.Equals(open.Code))
                    this.LicensePlate = lpnlocation.LicensePlate;
            }
        }

        public virtual void FindStageLocation()
        {
            if (_orderDetail.OrderHeader.Carrier == null) return;
            //
            DetachedCriteria criteria = DetachedCriteria.For<Dock>()
                .Add("Carrier.CarrierCode", _orderDetail.OrderHeader.Carrier.CarrierCode)
                .Add("CompanyLocationType", Registry.Find<CompanyLocationType>())
                .SetMaxResults(1);
            IList<Dock> docks = Repositories.Get<Dock>().List(criteria);

            if (docks != null && docks.Count > 0) this.StageLocation = docks[0].Location;
        }

        public virtual void CheckOtherPickingUser()
        {
            Boolean? prompt = BusinessRule.RetrieveBoolean("11115");
            if (!prompt.HasValue || !prompt.Value) return;
            //
            TransactionType picked = Entity.Retrieve<TransactionType>(OrderTransactions.Picked);
            DetachedCriteria criteria = DetachedCriteria.For<ItemTransaction>()
                    .Add(Expression.Eq("OrderHeader", _orderDetail.OrderHeader))
                    .Add(Expression.Eq("TransactionType", picked))
                    .Add(Expression.Not(Expression.Eq("UserCreated", Registry.Find<UserAccount>().UserName)))
                    .SetProjection(Projections.Count("Id"));
            //
            this.NotifyUser = Repositories.Get<ItemTransaction>().Function<Int32>(criteria) > 0;
        }

        public virtual void FindTruckInfo()
        {
            if (_cartonHeader == null) return;
            //
            LocationType mobile = Entity.Retrieve<LocationType>(InventoryLocationTypes.Mobile);
            //
            DetachedCriteria criteria = DetachedCriteria.For<LicensePlateLocation>()
                .Add("LicensePlate", _cartonHeader.LicensePlate)
                .Add("Location.LocationType", mobile)
                .Add("SlotNumber", "!=", null)
                .AddOrder("Occurred", false)
                .SetMaxResults(1);
            IList<LicensePlateLocation> results = Repositories.Get<LicensePlateLocation>().List(criteria);
            if (results != null && results.Count > 0)
            {
                this.TruckCode = results[0].Location.LocationCode;
                this.SlotNumber = results[0].SlotNumber;
            }
        }

        public virtual void FindUomFactor()
        {
            DetachedCriteria criteria = DetachedCriteria.For<ItemUomRelationship>()
                .Add("Active", "A")
                .Add("Item", _item)
                .Add("UnitOfMeasure", _orderDetail.UnitOfMeasure)
                .SetMaxResults(1);
            ItemUomRelationship factor = Repositories.Get<ItemUomRelationship>().Retrieve(criteria);
            if (factor == null) this.UomFactor = 1;
            else this.UomFactor = Converter.ToInt32(factor.Factor);
        }

        public virtual void MovePicks(CartonHeader fromCarton, CartonHeader toCarton)
        {
            StatusCode distributed = Entity.Retrieve<StatusCode>(OrderStatuses.Distributed);
            StatusCode inPick = Entity.Retrieve<StatusCode>(OrderStatuses.InPick);
            //            
            DetachedCriteria criteria = DetachedCriteria.For<ItemFulfillment>()
                .Add("CartonHeader", fromCarton)
                .AddOr("StatusCode", distributed, "StatusCode", inPick);
            IList<ItemFulfillment> picks = Repositories.Get<ItemFulfillment>().List(criteria);
            //
            decimal weight = 0.0M;
            foreach (ItemFulfillment element in picks)
            {
                element.CartonHeader = toCarton;
                element.DateModified = DateTime.Now;
                element.UserModified = Registry.Find<UserAccount>().UserName;
                Repositories.Get<ItemFulfillment>().Update(element);
                //
                if (element.Item != null && element.Item.Weight != null)
                    weight += Convert.ToDecimal(element.Item.Weight * element.Quantity);
            }
            //
            toCarton.CartonWeight += weight;
            toCarton.DateModified = DateTime.Now;
            toCarton.UserModified = Registry.Find<UserAccount>().UserName;
            Repositories.Get<CartonHeader>().Update(toCarton);
            //
            criteria = DetachedCriteria.For<ItemFulfillment>()
                .Add("CartonHeader", fromCarton)
                .SetProjection(Projections.Count("Id"));
            if (Repositories.Get<ItemFulfillment>().Function<Int32>(criteria) == 0)
            {
                StatusCode cancelled = Entity.Retrieve<StatusCode>(OrderStatuses.Cancelled);
                fromCarton.StatusCode = cancelled;
                fromCarton.LicensePlate.ChangeStatus(cancelled);
            }
            fromCarton.CartonWeight -= weight;
            fromCarton.DateModified = DateTime.Now;
            fromCarton.UserModified = Registry.Find<UserAccount>().UserName;
            Repositories.Get<CartonHeader>().Update(fromCarton);
        }

        public virtual void Open()
        {
            _dateModified = DateTime.Now;
            _statusCode = Entity.Retrieve<StatusCode>(OrderStatuses.Distributed);
            _userModified = Registry.Find<UserAccount>().UserName;
            Repositories.Get<ItemFulfillment>().Update(this);
            //
            _orderDetail.UpdateStatus();
            if (_orderDetail.OrderHeader.ShouldUpdateStatus()) _orderDetail.OrderHeader.UpdateStatus();
        }

        public virtual void Pick(Decimal quantity)
        {
            StatusCode packed = Entity.Retrieve<StatusCode>(RequisitionStatuses.Packed);
            StatusCode staged = Entity.Retrieve<StatusCode>(InventoryStatuses.Staged);
            //
            InventoryPick pick = InventoryPick.Create(this);
            pick.Quantity = quantity;
            pick.StatusCode = packed;
            Repositories.Get<InventoryPick>().Add(pick);
            //
            if (_quantity > quantity)
            {
                ItemFulfillment split = Entity.Clone<ItemFulfillment>(this);
                split.InventoryItem = null;
                split.Quantity = _quantity - quantity;
                split.StatusCode = Entity.Retrieve<StatusCode>(RequisitionStatuses.Distributed);
                Repositories.Get<ItemFulfillment>().Add(split);
                //
                _quantity -= quantity;
            }
            //
            _statusCode = packed;
            _requisitionDetail.UpdateStatus();
            if (_requisitionDetail.RequisitionHeader.ShouldUpdateStatus()) _requisitionDetail.RequisitionHeader.UpdateStatus();
            //
            _inventoryItem = Repositories.Get<InventoryItem>().Retrieve(_inventoryItem.Id);
            if ("Y".Equals(_item.ItemType.Consumable))
            {
                InventoryItemDetail detail = Entity.Activate<InventoryItemDetail>();
                detail.InventoryItem = _inventoryItem;
                detail.Location = this.LocationTo;
                detail.Quantity = quantity;
                detail.StatusCode = staged;
                Repositories.Get<InventoryItemDetail>().Add(detail);
                //
                _inventoryItem.ChangeQuantity(_inventoryItem.Quantity - this.PickedQuantity);
            }
            else
            {
                _inventoryItem.Location = this.LocationTo;
                _inventoryItem.ChangeStatus(staged);
            }
            //
            this.WritePicked(pick, _inventoryItem, quantity, null, null);
            this.WriteStaged(pick, _inventoryItem);
        }

        public virtual void Pick(Decimal quantity, bool toCarton, bool close)
        {
            CartonHeader original = _cartonHeader;
            Decimal remaining = Converter.ToDecimal(quantity);
            StatusCode active = Entity.Retrieve<StatusCode>(OrderStatuses.Active);
            StatusCode available = Entity.Retrieve<StatusCode>(InventoryStatuses.Available);
            StatusCode complete = Entity.Retrieve<StatusCode>(OrderStatuses.Complete);
            StatusCode lpnPacked = Entity.Retrieve<StatusCode>(LicensePlateStatuses.Packed);
            StatusCode packed = Entity.Retrieve<StatusCode>(OrderStatuses.Packed);
            StatusCode picked = Entity.Retrieve<StatusCode>(OrderStatuses.Picked);
            StatusCode shipped = Entity.Retrieve<StatusCode>(OrderStatuses.Shipped);
            //
            InventoryPick pick = InventoryPick.Create(this);
            pick.Location = this.LocationTo;
            pick.Quantity = quantity;
            pick.StatusCode = picked;
            if (toCarton) pick.StatusCode = packed;
            if (quantity == 0)
            {
                pick.StatusCode = shipped;
                //
                this.WritePicked(pick, null, quantity, null, null);
            }
            Repositories.Get<InventoryPick>().Add(pick);
            //
            if (quantity > 0 && toCarton)
            {
                CartonDetail detail = CartonDetail.Create(pick);
                detail.Quantity = pick.Quantity;
                detail.StatusCode = lpnPacked;
                Repositories.Get<CartonDetail>().Add(detail);
                //
                if (detail.CartonHeader != null)
                {
                    if (_cartonHeader != null && !_cartonHeader.Equals(detail.CartonHeader))
                    {
                        if (!string.IsNullOrEmpty(this.SlotNumber) && !string.IsNullOrEmpty(this.TruckCode))
                            detail.CartonHeader.LicensePlate.AddToTruck(this.SlotNumber, this.TruckCode);
                        if (this.Move) this.MovePicks(_cartonHeader, detail.CartonHeader);
                    }
                    //
                    _cartonHeader = detail.CartonHeader;
                    //
                    Boolean fullCase = detail.CartonHeader.CheckFullCase();
                    if (!fullCase)
                    {
                        this.LicensePlate.CheckVerify();
                        if (this.LicensePlate.Verify)
                        {
                            StatusCode verify = Entity.Retrieve<StatusCode>(OrderStatuses.Verify);
                            detail.CartonHeader.ChangeStatus(verify);
                        }
                        else
                        {
                            ParticipantRole role = Registry.Find<UserAccount>().OrganizationParticipant.ParticipantRole;
                            if ("Y".Equals(role.PickVerification))
                            {
                                StatusCode verify = Entity.Retrieve<StatusCode>(OrderStatuses.Verify);
                                detail.CartonHeader.ChangeStatus(verify);
                            }
                        }
                    }
                }
            }
            //
            Int32 index = 0;
            String[] numbers = null;
            if ("O".Equals(_item.SerialCaptureAt) &&
                "Y".Equals(_item.Serialized) &&
                !String.IsNullOrEmpty(this.SerialNumbers)) numbers = this.SerialNumbers.Split('|');
            //
            Company company = Registry.Find<Company>();
            CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
            DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
                .Add("Item", _item)
                .Add("Location", this.LocationTo)
                .Add("Quantity", ">", 0M)
                .AddOrder("Received");
            if (warehouse != null) criteria = criteria.Add("CompanyLocationType", warehouse);
            else if (company != null) criteria = criteria.Add("CompanyLocationType.CompanyLocation.Company", company);
            if (this.LicensePlateFrom != null) criteria = criteria.Add("LicensePlate.ParentLicensePlate", this.LicensePlateFrom);
            if (!String.IsNullOrEmpty(this.LotNumber)) criteria = criteria.Add("LotNumber", this.LotNumber);
            IList<InventoryItem> inventory = Repositories.Get<InventoryItem>().List(criteria);
            //
            if (inventory.Count == 0 && quantity > 0)
            {
                if (this.AllowNegative)
                {
                    InventoryItem entity = InventoryItem.Create(_item, this.LocationTo, available, -quantity);
                    if (this.LicensePlateFrom != null) entity.LicensePlate.ChangeParent(this.LicensePlateFrom);
                    if (!String.IsNullOrEmpty(this.LotNumber)) entity.LotNumber = this.LotNumber;
                    Repositories.Get<InventoryItem>().Add(entity);
                    //
                    if (Log.Instance != null) Log.Instance.WriteEntry("CYCLE COUNT: picking, no inventory");
                    entity.Location.CreateCycleCount(_item, this.LotNumber);
                    //
                    this.WritePicked(pick, entity, quantity, null, null);
                    if (toCarton) this.WritePacked(pick, quantity, null, null);
                }
                else throw new Exception("No inventory found.");
            }
            else if (!this.AllowNegative && inventory.Count > 0 && quantity > 0)
            {
                Decimal current = inventory.Sum(c => c.Quantity);
                if (quantity > current) throw new Exception("Not enoguh inventory found.");
            }
            //
            Int32 inventoryCount = 1;
            Int32 portion = 0;
            foreach (InventoryItem element in inventory)
            {
                if (remaining <= 0) break;
                //
                if (remaining < element.Quantity) portion = Converter.ToInt32(remaining);
                else if (this.AllowNegative && inventoryCount == inventory.Count) portion = Converter.ToInt32(remaining);
                else portion = Converter.ToInt32(element.Quantity);
                //
                if (numbers != null)
                {
                    for (int i = 0; i < portion; i++)
                    {
                        this.WritePicked(pick, element, 1, null, numbers[index + i]);
                        if (toCarton) this.WritePacked(pick, 1, null, numbers[index + i]);
                    }
                    //
                    index += portion;
                }
                else
                {
                    this.WritePicked(pick, element, portion, null, null);
                    if (toCarton) this.WritePacked(pick, portion, null, null);
                }
                //
                if (this.AllowNegative)
                {
                    if (inventoryCount == inventory.Count)
                    {
                        element.DateModified = DateTime.Now;
                        element.Quantity -= remaining;
                        element.UserModified = Registry.Find<UserAccount>().UserName;
                        Repositories.Get<InventoryItem>().Update(element);
                        //
                        if (Log.Instance != null) Log.Instance.WriteEntry("CYCLE COUNT: picking, last chunk");
                        element.Location.CreateCycleCount(_item, this.LotNumber);
                        //
                        remaining = 0;
                    }
                    else remaining = element.Pick(remaining);
                }
                else remaining = element.Pick(remaining);
                //
                inventoryCount++;
            }
            //
            if (inventory != null && inventory.Count > 0) _inventoryItem = inventory[0];
            //
            if (_quantity > quantity && quantity > 0)
            {
                ItemFulfillment split = Entity.Clone<ItemFulfillment>(this);
                split.CartonHeader = original;
                split.Quantity = _quantity - quantity;
                split.StatusCode = Entity.Retrieve<StatusCode>(OrderStatuses.Distributed);
                if (close) split.StatusCode = shipped;
                Repositories.Get<ItemFulfillment>().Add(split);
                //
                _quantity = quantity;
            }
            //
            _location = this.LocationTo;
            _statusCode = picked;
            _orderDetail.UpdateStatus();
            if (_orderDetail.OrderHeader.ShouldUpdateStatus()) _orderDetail.OrderHeader.UpdateStatus();
            if (_wave != null) _wave.ChangeStatus(active);
            //
            if ("Y".Equals(this.LocationTo.PrimaryPick))
            {
                this.LocationTo = Repositories.Get<Location>().Retrieve(this.LocationTo.Id);
                if (this.LocationTo.Item != null && this.LocationTo.Item.SameAs(_item)) this.Replenished = this.LocationTo.Replenish();
            }
            //
            if (toCarton)
            {
                _statusCode = packed;
                _orderDetail.UpdateStatus();
                if (_orderDetail.OrderHeader.ShouldUpdateStatus()) _orderDetail.OrderHeader.UpdateStatus();
            }
            //
            if (quantity == 0)
            {
                _statusCode = shipped;
                _orderDetail.UpdateStatus();
                if (_orderDetail.OrderHeader.ShouldUpdateStatus()) _orderDetail.OrderHeader.UpdateStatus();
                if (_wave != null) _wave.Close();
            }
            //
            if (toCarton && _cartonHeader != null)
            {
                _cartonHeader.Pack();
                //
                if (!this.Move && original != null && !original.Equals(_cartonHeader))
                {
                    _cartonHeader.UpdateWeight();
                    original.UpdateWeight();
                }
                //
                if (quantity == 0) _cartonHeader.Cancel();
            }
        }

        public virtual void Pick(string serialNumbers, bool toCarton, bool close)
        {
            CartonHeader original = _cartonHeader;
            Company company = Registry.Find<Company>();
            CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
            DetachedCriteria criteria = null;
            //
            StatusCode active = Entity.Retrieve<StatusCode>(OrderStatuses.Active);
            StatusCode complete = Entity.Retrieve<StatusCode>(OrderStatuses.Complete);
            StatusCode done = Entity.Retrieve<StatusCode>(InventoryStatuses.Unavailable);
            StatusCode lpnPacked = Entity.Retrieve<StatusCode>(LicensePlateStatuses.Packed);
            StatusCode packed = Entity.Retrieve<StatusCode>(OrderStatuses.Packed);
            StatusCode picked = Entity.Retrieve<StatusCode>(OrderStatuses.Picked);
            StatusCode shipped = Entity.Retrieve<StatusCode>(OrderStatuses.Shipped);
            //
            InventoryPick pick = InventoryPick.Create(this);
            pick.Location = this.LocationTo;
            pick.Quantity = this.PickedQuantity;
            pick.StatusCode = picked;
            if (toCarton) pick.StatusCode = packed;
            if (this.PickedQuantity == 0) pick.StatusCode = shipped;
            Repositories.Get<InventoryPick>().Add(pick);
            //
            if (this.PickedQuantity > 0 && toCarton)
            {
                CartonDetail detail = CartonDetail.Create(pick);
                detail.Quantity = pick.Quantity;
                detail.StatusCode = lpnPacked;
                Repositories.Get<CartonDetail>().Add(detail);
                //
                if (detail.CartonHeader != null)
                {
                    if (_cartonHeader != null && !_cartonHeader.Equals(detail.CartonHeader))
                    {
                        if (!string.IsNullOrEmpty(this.SlotNumber) && !string.IsNullOrEmpty(this.TruckCode))
                            detail.CartonHeader.LicensePlate.AddToTruck(this.SlotNumber, this.TruckCode);
                        if (this.Move) this.MovePicks(_cartonHeader, detail.CartonHeader);
                    }
                    //
                    _cartonHeader = detail.CartonHeader;
                    //
                    this.LicensePlate.CheckVerify();
                    if (this.LicensePlate.Verify)
                    {
                        StatusCode verify = Entity.Retrieve<StatusCode>(OrderStatuses.Verify);
                        detail.CartonHeader.ChangeStatus(verify);
                    }
                    else
                    {
                        ParticipantRole role = Registry.Find<UserAccount>().OrganizationParticipant.ParticipantRole;
                        if ("Y".Equals(role.PickVerification))
                        {
                            StatusCode verify = Entity.Retrieve<StatusCode>(OrderStatuses.Verify);
                            detail.CartonHeader.ChangeStatus(verify);
                        }
                    }
                }
            }
            //
            String[] numbers = serialNumbers.Split('|');
            for (int i = 0; i < numbers.Length; i++)
            {
                criteria = DetachedCriteria.For<InventoryItemDetail>()
                    .Add("InventoryItem.Item", _item)
                    .Add("SerialNumber", numbers[i])
                    .Add("StatusCode", "!=", done)
                    .SetMaxResults(1);
                if (warehouse != null) criteria = criteria.Add("InventoryItem.CompanyLocationType", warehouse);
                else if (company != null) criteria = criteria.Add("InventoryItem.CompanyLocationType.CompanyLocation.Company", company);
                if (!String.IsNullOrEmpty(this.LotNumber)) criteria = criteria.Add("InventoryItem.LotNumber", this.LotNumber);
                InventoryItemDetail detail = Repositories.Get<InventoryItemDetail>().Retrieve(criteria);
                if (detail != null)
                {
                    detail.InventoryItem.ChangeQuantity(detail.InventoryItem.Quantity - 1);
                    detail.ChangeStatus(done);
                    //
                    this.WritePicked(pick, detail.InventoryItem, 1, null, numbers[i]);
                    if (toCarton) this.WritePacked(pick, 1, null, numbers[i]);
                }
                else
                {
                    criteria = DetachedCriteria.For<InventoryItem>()
                        .Add("Item", _item)
                        .Add("SerialNumber", numbers[i])
                        .Add("StatusCode", "!=", done)
                        .SetMaxResults(1);
                    if (warehouse != null) criteria = criteria.Add("CompanyLocationType", warehouse);
                    else if (company != null) criteria = criteria.Add("CompanyLocationType.CompanyLocation.Company", company);
                    if (!String.IsNullOrEmpty(this.LotNumber)) criteria = criteria.Add("LotNumber", this.LotNumber);
                    InventoryItem inventoryItem = Repositories.Get<InventoryItem>().Retrieve(criteria);

                    if (inventoryItem == null) throw new Exception(String.Format("Unable to find the inventory for {0}.", numbers[i]));
                    //
                    inventoryItem.ChangeQuantity(inventoryItem.Quantity - 1);
                    inventoryItem.ChangeStatus(done);
                    //
                    this.WritePicked(pick, inventoryItem, 1, null, numbers[i]);
                    if (toCarton) this.WritePacked(pick, 1, null, numbers[i]);
                }
            }
            //
            if (_quantity > this.PickedQuantity && this.PickedQuantity > 0)
            {
                ItemFulfillment split = Entity.Clone<ItemFulfillment>(this);
                split.CartonHeader = original;
                split.Quantity = _quantity - this.PickedQuantity;
                split.StatusCode = Entity.Retrieve<StatusCode>(OrderStatuses.Distributed);
                if (close) split.StatusCode = shipped;
                Repositories.Get<ItemFulfillment>().Add(split);
                //
                _quantity = this.PickedQuantity;
            }
            //
            _location = this.LocationTo;
            _statusCode = picked;
            _orderDetail.UpdateStatus();
            if (_orderDetail.OrderHeader.ShouldUpdateStatus()) _orderDetail.OrderHeader.UpdateStatus();
            if (_wave != null) _wave.ChangeStatus(active);
            //
            if ("Y".Equals(this.LocationTo.PrimaryPick))
            {
                criteria = DetachedCriteria.For<Location>()
                    .Add("CompanyLocationType", warehouse)
                    .Add("LocationCode", this.LocationTo.LocationCode)
                    .SetMaxResults(1);
                this.LocationTo = Repositories.Get<Location>().Retrieve(criteria);

                if (this.LocationTo.Item != null && this.LocationTo.Item.SameAs(_item)) this.Replenished = this.LocationTo.Replenish();
            }
            //
            if (toCarton)
            {
                _statusCode = packed;
                _orderDetail.UpdateStatus();
                if (_orderDetail.OrderHeader.ShouldUpdateStatus()) _orderDetail.OrderHeader.UpdateStatus();
            }
            //
            if (this.PickedQuantity == 0)
            {
                _statusCode = shipped;
                _orderDetail.UpdateStatus();
                if (_orderDetail.OrderHeader.ShouldUpdateStatus()) _orderDetail.OrderHeader.UpdateStatus();
                if (_wave != null) _wave.Close();
            }
            //
            if (toCarton && _cartonHeader != null)
            {
                _cartonHeader.Pack();
                //
                if (!this.Move && original != null && !original.Equals(_cartonHeader))
                {
                    _cartonHeader.UpdateWeight();
                    original.UpdateWeight();
                }
                //
                if (this.PickedQuantity == 0) _cartonHeader.Cancel();
            }
        }

        public virtual void StageLpn(String pickBy, Location locationTo)
        {
            DetachedCriteria criteria = null;
            IList<ItemFulfillment> picks = null;
            ProjectionList projections = null;
            //
            switch (pickBy)
            {
                case "Lpn":
                    this.LicensePlate.ChangeLocation(locationTo);
                    break;
                case "Order":
                    projections = Projections.ProjectionList()
                           .Add(Projections.Property("ch.LicensePlate"), "LicensePlate");
                    criteria = DetachedCriteria.For<ItemFulfillment>()
                           .CreateAlias("CartonHeader", "ch")
                           .CreateAlias("OrderDetail", "od")
                           .Add(Expression.Eq("od.OrderHeader", _orderDetail.OrderHeader))
                           .SetProjection(Projections.Distinct(projections))
                           .SetResultTransformer(Transformers.AliasToBean<ItemFulfillment>());
                    picks = Repositories.Get<ItemFulfillment>().List(criteria);
                    foreach (ItemFulfillment element in picks) element.LicensePlate.ChangeLocation(locationTo);
                    break;
                case "Truck":
                    //ToDo
                    break;
                case "Wave":
                    projections = Projections.ProjectionList()
                         .Add(Projections.Property("ch.LicensePlate"), "LicensePlate");
                    criteria = DetachedCriteria.For<ItemFulfillment>()
                           .CreateAlias("CartonHeader", "ch")
                           .Add(Expression.Eq("Wave", _wave))
                           .SetProjection(Projections.Distinct(projections))
                           .SetResultTransformer(Transformers.AliasToBean<ItemFulfillment>());
                    picks = Repositories.Get<ItemFulfillment>().List(criteria);
                    foreach (ItemFulfillment element in picks) element.LicensePlate.ChangeLocation(locationTo);
                    break;
            }
        }

        public virtual void UpdateStatus()
        {
            StatusCode packed = Entity.Retrieve<StatusCode>(OrderStatuses.Packed);
            StatusCode picked = Entity.Retrieve<StatusCode>(OrderStatuses.Picked);
            StatusCode shipped = Entity.Retrieve<StatusCode>(OrderStatuses.Shipped);
            //
            foreach (StatusCode status in new StatusCode[] { picked, packed, shipped })
            {
                DetachedCriteria criteria = DetachedCriteria.For<InventoryPick>()
                    .Add("ItemFulfillment", this)
                    .Add("StatusCode", status)
                    .SetProjection(Projections.Count("Id"));
                Int32 count = Repositories.Get<InventoryPick>().Function<Int32>(criteria);
                if (count > 0)
                {
                    _dateModified = DateTime.Now;
                    _statusCode = status;
                    _userModified = Registry.Find<UserAccount>().UserName;
                    Repositories.Get<ItemFulfillment>().Update(this);
                    //
                    break;
                }
            }
        }

        public virtual void UpdateKitHeaderLine()
        {
            StatusCode distributed = Entity.Retrieve<StatusCode>(OrderStatuses.Distributed);
            StatusCode inPick = Entity.Retrieve<StatusCode>(OrderStatuses.InPick);
            StatusCode picked = Entity.Retrieve<StatusCode>(OrderStatuses.Picked);
            StatusCode shipped = Entity.Retrieve<StatusCode>(OrderStatuses.Shipped);
            StatusCode statusCodeBeforeUpdate = null;

            DetachedCriteria criteria = DetachedCriteria.For<OrderDetail>()
                    .Add("LineNumber", _orderDetail.LineNumber)
                    .Add("LineNumberSequence", ">", 0)
                    .Add("OrderHeader", _orderDetail.OrderHeader);
            IList<OrderDetail> kitComponents = Repositories.Get<OrderDetail>().List(criteria);
            //
            Int32 count = kitComponents.Where(e => e.StatusCode.SortOrder > distributed.SortOrder).Count();
            if (count > 0)
            {
                criteria = DetachedCriteria.For<OrderDetail>()
                    .Add("LineNumber", _orderDetail.LineNumber)
                    .Add("LineNumberSequence", 0)
                    .Add("OrderHeader", _orderDetail.OrderHeader);
                OrderDetail kitHeader = Repositories.Get<OrderDetail>().Retrieve(criteria);

                statusCodeBeforeUpdate = kitHeader.StatusCode;
                kitHeader.StatusCode = inPick;
                kitHeader.Quantity = 0;
                //
                count = kitComponents.Where(e => e.StatusCode.SortOrder < picked.SortOrder).Count();
                if (count == 0)
                {
                    String rule = BusinessRule.RetrieveString("1075");

                    if (!string.IsNullOrEmpty(rule) && "Round Down Kit Quantity".Equals(rule.Trim(), StringComparison.InvariantCultureIgnoreCase))
                    {
                        Dictionary<Int32, Decimal> components = new Dictionary<Int32, Decimal>();
                        //
                        foreach (OrderDetail component in kitComponents)
                        {
                            criteria = DetachedCriteria.For<InventoryPick>()
                                .Add("OrderDetail", component)
                                .SetProjection(Projections.Sum("Quantity"));
                            Decimal pickedQuantity = Repositories.Get<InventoryPick>().Function<Decimal>(criteria);
                            //
                            components.Add(component.Id.Value, component.Quantity - pickedQuantity);
                        }
                        //
                        OrderDetail limitingFactor = kitComponents
                                                        .Where(e => e.Id.Value.Equals(components.FirstOrDefault(x => x.Value == components.Values.Max()).Key))
                                                        .FirstOrDefault();

                        //Need order_details.quantity coloumn to be changed to support decimals
                        kitHeader.Quantity = Converter.ToInt32(Converter.ToDecimal(kitHeader.OriginalQuantity.Value) / Converter.ToDecimal(limitingFactor.Quantity) * limitingFactor.InventoryPicks.Sum(e => e.Quantity));
                    }
                    else if (!string.IsNullOrEmpty(rule) && "Exact Kit Quantity".Equals(rule.Trim(), StringComparison.InvariantCultureIgnoreCase))
                    {
                        criteria = DetachedCriteria.For<InventoryPick>()
                           .Add("OrderDetail.LineNumber", _orderDetail.LineNumber)
                           .Add("OrderDetail.OrderHeader", _orderDetail.OrderHeader)
                           .SetProjection(Projections.Sum("Quantity"));
                        Int32 pickedQuantity = Repositories.Get<InventoryPick>().Function<Int32>(criteria);

                        //Need order_details.quantity coloumn to be changed to support decimals
                        kitHeader.Quantity = Converter.ToInt32(Converter.ToDecimal(pickedQuantity) / Converter.ToDecimal(kitComponents.Sum(e => e.Quantity)) * kitHeader.OriginalQuantity.Value);
                    }
                    //
                    statusCodeBeforeUpdate = kitHeader.StatusCode;
                    kitHeader.StatusCode = shipped;
                }
                //
                kitHeader.DateModified = DateTime.Now;
                kitHeader.UserModified = Registry.Find<UserAccount>().UserName;
                //
                Repositories.Get<OrderDetail>().Update(kitHeader);

                //Insert into orde_detail_status table
                Boolean WriteOrderDetailStatus = Converter.ToBoolean(BusinessRule.RetrieveBoolean("11558").Value);
                if (WriteOrderDetailStatus && statusCodeBeforeUpdate != kitHeader.StatusCode)
                {
                    OrderDetailStatus created = Entity.Activate<OrderDetailStatus>();
                    created.OrderHeader = this.OrderHeader;
                    created.OrderDetail = this.OrderDetail;
                    created.StatusCode = kitHeader.StatusCode;
                    created.Occured = DateTime.Now;
                    created.Version = 1;
                    created.UserCreated = Registry.Find<UserAccount>().UserName;
                    Repositories.Get<OrderDetailStatus>().Add(created);

                }
            }
        }

        #endregion

        #region Methods.Public.Pick

        public virtual void InvalidateOverpicks(int quantity)
        {
            DetachedCriteria criteria = DetachedCriteria.For<InventoryPick>()
                .Add(Expression.Eq("OrderDetail", _orderDetail));
            IList<InventoryPick> picks = Repositories.Get<InventoryPick>().List(criteria);
            Decimal picked = picks.Sum(e => e.Quantity);
            //
            if (picked + quantity > _orderDetail.Quantity)
                throw new Exception("Over-picking has been detected. Exit the picking screen and select another line to pick.");
        }

        #endregion
    }
}
