using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ItemGroup : Entity
	{
		#region Fields

		private Int32? _amMaxReturnDays;
		private Int32? _amReturnDays;
		private Int32? _amReturnMinutes;
		private Int32? _amReturnYears;
		private ICollection<BillingRate> _billingRates = new HashSet<BillingRate>();
		private ICollection<InspectionTemplate> _inspectionTemplates = new HashSet<InspectionTemplate>();
		private ICollection<Item> _items = new HashSet<Item>();
		private ICollection<ItemGroupLine> _itemGroupLines = new HashSet<ItemGroupLine>();
		private ICollection<MaintenanceDetail> _maintenanceDetails = new HashSet<MaintenanceDetail>();
		private String _active;
		private String _amEditReturnDays;
		private String _description;
		private String _itemGroupCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual Int32? AmMaxReturnDays
		{
			get { return _amMaxReturnDays; }
			set { _amMaxReturnDays = value; }
		}

		[DataMember]
		public virtual Int32? AmReturnDays
		{
			get { return _amReturnDays; }
			set { _amReturnDays = value; }
		}

		[DataMember]
		public virtual Int32? AmReturnMinutes
		{
			get { return _amReturnMinutes; }
			set { _amReturnMinutes = value; }
		}

		[DataMember]
		public virtual Int32? AmReturnYears
		{
			get { return _amReturnYears; }
			set { _amReturnYears = value; }
		}

		[DataMember]
		public virtual ICollection<BillingRate> BillingRates
		{
			get { return _billingRates; }
			set { _billingRates = value; }
		}

		[DataMember]
		public virtual ICollection<InspectionTemplate> InspectionTemplates
		{
			get { return _inspectionTemplates; }
			set { _inspectionTemplates = value; }
		}

		[DataMember]
		public virtual ICollection<Item> Items
		{
			get { return _items; }
			set { _items = value; }
		}

		[DataMember]
		public virtual ICollection<ItemGroupLine> ItemGroupLines
		{
			get { return _itemGroupLines; }
			set { _itemGroupLines = value; }
		}

		[DataMember]
		public virtual ICollection<MaintenanceDetail> MaintenanceDetails
		{
			get { return _maintenanceDetails; }
			set { _maintenanceDetails = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String AmEditReturnDays
		{
			get { return _amEditReturnDays; }
			set { _amEditReturnDays = value; }
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String ItemGroupCode
		{
			get { return _itemGroupCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ItemGroupCode must not be blank or null.");
				else _itemGroupCode = value;
			}
		}


		#endregion
	}
}
