using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ItemGroupSubLine : Entity
	{
		#region Fields

		private ItemGroupLine _itemGroupLine;
		private ICollection<BillingRate> _billingRates = new HashSet<BillingRate>();
		private ICollection<InspectionTemplate> _inspectionTemplates = new HashSet<InspectionTemplate>();
		private ICollection<Item> _items = new HashSet<Item>();
		private String _active;
		private String _description;
		private String _itemGroupSubLineCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ItemGroupLine ItemGroupLine
		{
			get { return _itemGroupLine; }
			set { _itemGroupLine = value; }
		}

		[DataMember]
		public virtual ICollection<BillingRate> BillingRates
		{
			get { return _billingRates; }
			set { _billingRates = value; }
		}

		[DataMember]
		public virtual ICollection<InspectionTemplate> InspectionTemplates
		{
			get { return _inspectionTemplates; }
			set { _inspectionTemplates = value; }
		}

		[DataMember]
		public virtual ICollection<Item> Items
		{
			get { return _items; }
			set { _items = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String ItemGroupSubLineCode
		{
			get { return _itemGroupSubLineCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ItemGroupSubLineCode must not be blank or null.");
				else _itemGroupSubLineCode = value;
			}
		}


		#endregion
	}
}
