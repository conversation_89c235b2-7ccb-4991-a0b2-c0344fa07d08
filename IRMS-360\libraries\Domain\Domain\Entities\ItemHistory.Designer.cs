using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ItemHistory : Entity
	{
		#region Fields

		private CompanyLocationType _companyLocationType;
		private DateTime? _asOf;
		private DateTime? _received;
		private Decimal? _adjustments;
		private Decimal? _beginningBalance;
		private Decimal? _endingBalance;
		private Decimal? _receipts;
		private Decimal? _returns;
		private Decimal? _shipments;
		private Decimal? _unsentShipments;
		private Decimal? _shipmentsUnitCost;
		private Item _item;
        private Int32 _itemId;
        private ReceiptType _receiptType;
		private StatusCode _statusCode;
		private Stratification _stratification;
        private Stratification _oldStratification;
        private String _lotNumber;

		#endregion

		#region Properties

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual DateTime? AsOf
		{
			get { return _asOf; }
			set { _asOf = value; }
		}

		[DataMember]
		public virtual DateTime? Received
		{
			get { return _received; }
			set { _received = value; }
		}

		[DataMember]
		public virtual Decimal? Adjustments
		{
			get { return _adjustments; }
			set { _adjustments = value; }
		}

		[DataMember]
		public virtual Decimal? BeginningBalance
		{
			get { return _beginningBalance; }
			set { _beginningBalance = value; }
		}

		[DataMember]
		public virtual Decimal? EndingBalance
		{
			get { return _endingBalance; }
			set { _endingBalance = value; }
		}

		[DataMember]
		public virtual Decimal? Receipts
		{
			get { return _receipts; }
			set { _receipts = value; }
		}

		[DataMember]
		public virtual Decimal? Returns
		{
			get { return _returns; }
			set { _returns = value; }
		}

		[DataMember]
		public virtual Decimal? Shipments
		{
			get { return _shipments; }
			set { _shipments = value; }
		}

		[DataMember]
		public virtual Decimal? UnsentShipments
		{
			get { return _unsentShipments; }
			set { _unsentShipments = value; }
		}

        [DataMember]
        public virtual Decimal? ShipmentsUnitCost
        {
            get { return _shipmentsUnitCost; }
            set { _shipmentsUnitCost = value; }
        }

        [DataMember]
		public virtual Item Item
		{
			get { return _item; }
			set { _item = value; }
		}

        [DataMember]
        public virtual Int32 ItemId
        {
            get { return _itemId; }
            set { _itemId = value; }
        }

        [DataMember]
		public virtual ReceiptType ReceiptType
		{
			get { return _receiptType; }
			set { _receiptType = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

        [DataMember]
        public virtual Stratification Stratification
        {
            get { return _stratification; }
            set { _stratification = value; }
        }

        [DataMember]
        public virtual Stratification OldStratification
        {
            get { return _oldStratification; }
            set { _oldStratification = value; }
        }

        [DataMember]
		public virtual String LotNumber
		{
			get { return _lotNumber; }
			set { _lotNumber = value; }
		}


		#endregion
	}
}
