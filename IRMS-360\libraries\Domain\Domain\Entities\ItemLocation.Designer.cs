using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ItemLocation : Entity
	{
		#region Fields

		private CompanyLocationZone _itemZone;
		private Decimal? _maximumQuantity;
		private Decimal? _minimumQuantity;
		private Decimal? _replenishmentQuantity;
		private Item _item;
		private Location _location;
		private PickType _pickType;
		private UnitOfMeasure _replenishmentUom;

		#endregion

		#region Properties

		[DataMember]
		public virtual CompanyLocationZone ItemZone
		{
			get { return _itemZone; }
			set { _itemZone = value; }
		}

		[DataMember]
		public virtual Decimal? MaximumQuantity
		{
			get { return _maximumQuantity; }
			set { _maximumQuantity = value; }
		}

		[DataMember]
		public virtual Decimal? MinimumQuantity
		{
			get { return _minimumQuantity; }
			set { _minimumQuantity = value; }
		}

		[DataMember]
		public virtual Decimal? ReplenishmentQuantity
		{
			get { return _replenishmentQuantity; }
			set { _replenishmentQuantity = value; }
		}

		[DataMember]
		public virtual Item Item
		{
			get { return _item; }
			set { _item = value; }
		}

		[DataMember]
		public virtual Location Location
		{
			get { return _location; }
			set { _location = value; }
		}

		[DataMember]
		public virtual PickType PickType
		{
			get { return _pickType; }
			set { _pickType = value; }
		}

		[DataMember]
		public virtual UnitOfMeasure ReplenishmentUom
		{
			get { return _replenishmentUom; }
			set { _replenishmentUom = value; }
		}

		#endregion
	}
}