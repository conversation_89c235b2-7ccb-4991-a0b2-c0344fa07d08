using System;
using System.Runtime.Serialization;

using Upp.Irms.Constants;
using Upp.Irms.Core;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class ItemLocation : Entity
	{
		#region Properties

		[DataMember]
		public virtual Location PrimaryLocation { get; set; }
		[DataMember]
		public virtual String LocationCode { get; set; }
		[DataMember]
		public virtual String PickTypeDescription { get; set; }

		#endregion

		#region Constructors

		public ItemLocation()
		{
			_dateCreated = DateTime.Now;
			_userCreated = "IRMS BL";
		}

		public ItemLocation(Location location)
		{
			_dateCreated = DateTime.Now;
			_id = location.Id;
			_item = location.Item;
			_itemZone = _item.CompanyLocationZone;
			_location = location;
			_maximumQuantity = _location.MaximumQuantity;
			_minimumQuantity = _location.MinimumQuantity;
			_pickType = _location.PickType;
			_replenishmentQuantity = _location.MaximumQuantity - _location.MinimumQuantity;
			_replenishmentUom = _location.ReplenishmentUom;
			_userCreated = "IRMS BL";
			//
			this.LocationCode = _location.LocationCode;
			if (_pickType != null) this.PickTypeDescription = _pickType.Description;
			this.PrimaryLocation = _location;
		}

		#endregion

		#region Methods.Public

		public virtual void ChangeItemZone()
		{
			_item = Repositories.Get<Item>().Retrieve(_item.Id);
			_item.CompanyLocationZone = _itemZone;
			_item.DateModified = DateTime.Now;
			_item.UserModified = Registry.Find<UserAccount>().UserName;
			//
			Repositories.Get<Item>().Update(_item);
		}

		public virtual void ChangeProperties()
		{
			_location = Repositories.Get<Location>().Retrieve(_location.Id);
			_location.DateModified = DateTime.Now;
			_location.MaximumQuantity = _maximumQuantity;
			_location.MinimumQuantity = _minimumQuantity;
			_location.PickType = _pickType;
			_location.ReplenishmentQuantity = _maximumQuantity - _minimumQuantity;
			_location.ReplenishmentUom = _replenishmentUom;
			_location.UserModified = Registry.Find<UserAccount>().UserName;
			//
			Repositories.Get<Location>().Update(_location);
		}

		public virtual void ChangePrimaryLocation()
		{
			this.RemovePrimaryLocation();
			this.CreatePrimaryLocation();
		}

		public virtual void CreatePrimaryLocation()
		{
			this.PrimaryLocation = Repositories.Get<Location>().Retrieve(this.PrimaryLocation.Id);
			this.PrimaryLocation.DateModified = DateTime.Now;
			this.PrimaryLocation.Item = _item;
			this.PrimaryLocation.MaximumQuantity = _maximumQuantity;
			this.PrimaryLocation.MinimumQuantity = _minimumQuantity;
			this.PrimaryLocation.PickType = _pickType;
			this.PrimaryLocation.PrimaryPick = "Y";
			this.PrimaryLocation.ReplenishmentQuantity = _maximumQuantity - _minimumQuantity;
			this.PrimaryLocation.ReplenishmentUom = _replenishmentUom;
			this.PrimaryLocation.UserModified = Registry.Find<UserAccount>().UserName;
			//
			Repositories.Get<Location>().Update(this.PrimaryLocation);
		}

		public virtual void RemovePrimaryLocation()
		{
			_location = Repositories.Get<Location>().Retrieve(_location.Id);
			_location.DateModified = DateTime.Now;
			_location.Item = null;
			_location.MaximumQuantity = null;
			_location.MinimumQuantity = null;
			_location.PickType = null;
			_location.PrimaryPick = "N";
			_location.ReplenishmentQuantity = null;
			_location.ReplenishmentUom = null;
			_location.UserModified = Registry.Find<UserAccount>().UserName;
			//
			Repositories.Get<Location>().Update(_location);
		}

		#endregion

		#region Methods.Public.Transactions

		public virtual void WritePrimaryUpdate()
		{
			TransactionType type = Entity.Retrieve<TransactionType>(InventoryTransactions.PrimaryUpdate);
			ItemTransaction transaction = Entity.Activate<ItemTransaction>();
			transaction.Item = _item;
			transaction.LocationFrom = _location;
			transaction.LocationTo = this.PrimaryLocation;
			transaction.Occurred = DateTime.Now;
			transaction.ParticipantBy = Registry.Find<UserAccount>().OrganizationParticipant;
			transaction.TransactionType = type;
			//
			Repositories.Get<ItemTransaction>().Add(transaction);
		}

		#endregion
	}
}