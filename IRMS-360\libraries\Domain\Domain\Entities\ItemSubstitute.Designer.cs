using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ItemSubstitute : Entity
	{
		#region Fields

		private Int32 _itemSequence;
		private Item _item;
		private ItemUomRelationship _itemUomRelationship;
		private String _active;

		#endregion

		#region Properties

		[DataMember]
		public virtual Int32 ItemSequence
		{
			get { return _itemSequence; }
			set { _itemSequence = value; }
		}

		[DataMember]
		public virtual Item Item
		{
			get { return _item; }
			set { _item = value; }
		}

		[DataMember]
		public virtual ItemUomRelationship ItemUomRelationship
		{
			get { return _itemUomRelationship; }
			set { _itemUomRelationship = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}


		#endregion
	}
}
