using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ItemTransaction : Entity
	{
		#region Fields

		private Agency _agencyFrom;
		private Agency _agencyTo;
		private ApprovalAssignment _approvalAssignment;
		private ClaimTask _claimTask;
		private Customer _customer;
		private DateTime _occurred;
		private DateTime? _shelfLifeExpirationFrom;
		private DateTime? _shelfLifeExpirationTo;
		private Decimal _quantity;
		private Decimal? _catchWeight;
		private Decimal? _quantityFrom;
		private Decimal? _temperature;
		private Decimal? _unitCostFrom;
		private Decimal? _unitCostTo;
		private Int32? _physicalSequence;
		private InventoryAdjustmentCode _inventoryAdjustmentCode;
		private InventoryItem _inventoryItem;
		private InventoryItem _parentInventoryItem;
		private InventoryPick _inventoryPick;
        private InventoryHistory _inventoryHistory;
        private Item _item;
		private LicensePlate _licensePlateFrom;
		private LicensePlate _licensePlateTo;
		private ICollection<InventoryDiscrepancy> _inventoryDiscrepancies = new HashSet<InventoryDiscrepancy>();
		private ICollection<InvoiceDetail> _invoiceDetails = new HashSet<InvoiceDetail>();
		private ICollection<UdfItemTransactionValue> _udfItemTransactionValues = new HashSet<UdfItemTransactionValue>();
		private Location _locationFrom;
		private Location _locationTo;
		private OrderDetail _orderDetail;
		private OrderHeader _orderHeader;
		private OrganizationParticipant _participantBy;
		private OrganizationParticipant _participantFrom;
		private OrganizationParticipant _participantTo;
		private PoolItem _poolItemFrom;
		private PoolItem _poolItemTo;
		private PurchaseOrderDetail _purchaseOrderDetail;
		private ReceiptDetail _receiptDetail;
		private RequisitionDetail _requisitionDetail;
		private ReturnDetail _returnDetail;
		private StatusCode _integrationStatusCode;
		private StatusCode _statusCode;
		private StatusCode _statusCodeFrom;
		private StatusReasonCode _statusReasonCode;
		private String _assetCodeFrom;
		private String _assetCodeTo;
		private String _claimTrackingNumber;
		private String _lotNumber;
		private String _memo;
		private String _orderCode;
		private String _serialNumber;
		private String _truckCode;
		private Task _task;
		private TransactionType _transactionType;
		private UnitOfMeasure _unitOfMeasure;
		private VendorItem _vendorItem;
		private Wave _wave;
		private WorkOrderDetail _workOrderDetail;

		#endregion

		#region Properties

		[DataMember]
		public virtual Agency AgencyFrom
		{
			get { return _agencyFrom; }
			set { _agencyFrom = value; }
		}

		[DataMember]
		public virtual Agency AgencyTo
		{
			get { return _agencyTo; }
			set { _agencyTo = value; }
		}

		[DataMember]
		public virtual ApprovalAssignment ApprovalAssignment
		{
			get { return _approvalAssignment; }
			set { _approvalAssignment = value; }
		}

		[DataMember]
		public virtual ClaimTask ClaimTask
		{
			get { return _claimTask; }
			set { _claimTask = value; }
		}

		[DataMember]
		public virtual Customer Customer
		{
			get { return _customer; }
			set { _customer = value; }
		}

		[DataMember]
		public virtual DateTime Occurred
		{
			get { return _occurred; }
			set { _occurred = value; }
		}

		[DataMember]
		public virtual DateTime? ShelfLifeExpirationFrom
		{
			get { return _shelfLifeExpirationFrom; }
			set { _shelfLifeExpirationFrom = value; }
		}

		[DataMember]
		public virtual DateTime? ShelfLifeExpirationTo
		{
			get { return _shelfLifeExpirationTo; }
			set { _shelfLifeExpirationTo = value; }
		}

		[DataMember]
		public virtual Decimal Quantity
		{
			get { return _quantity; }
			set { _quantity = value; }
		}

		[DataMember]
		public virtual Decimal? QuantityFrom
		{
			get { return _quantityFrom; }
			set { _quantityFrom = value; }
		}

		[DataMember]
		public virtual Decimal? Temperature
		{
			get { return _temperature; }
			set { _temperature = value; }
		}

		[DataMember]
		public virtual Decimal? UnitCostFrom
		{
			get { return _unitCostFrom; }
			set { _unitCostFrom = value; }
		}

		[DataMember]
		public virtual Decimal? UnitCostTo
		{
			get { return _unitCostTo; }
			set { _unitCostTo = value; }
		}

		[DataMember]
		public virtual Int32? PhysicalSequence
		{
			get { return _physicalSequence; }
			set { _physicalSequence = value; }
		}

		[DataMember]
		public virtual InventoryAdjustmentCode InventoryAdjustmentCode
		{
			get { return _inventoryAdjustmentCode; }
			set { _inventoryAdjustmentCode = value; }
		}

		[DataMember]
		public virtual InventoryItem InventoryItem
		{
			get { return _inventoryItem; }
			set { _inventoryItem = value; }
		}

		[DataMember]
		public virtual InventoryItem ParentInventoryItem
		{
			get { return _parentInventoryItem; }
			set { _parentInventoryItem = value; }
		}

		[DataMember]
		public virtual InventoryPick InventoryPick
		{
			get { return _inventoryPick; }
			set { _inventoryPick = value; }
		}

        [DataMember]
        public virtual InventoryHistory InventoryHistory
        {
            get { return _inventoryHistory; }
            set { _inventoryHistory = value; }
        }

        [DataMember]
		public virtual Item Item
		{
			get { return _item; }
			set { _item = value; }
		}

		[DataMember]
		public virtual LicensePlate LicensePlateFrom
		{
			get { return _licensePlateFrom; }
			set { _licensePlateFrom = value; }
		}

		[DataMember]
		public virtual LicensePlate LicensePlateTo
		{
			get { return _licensePlateTo; }
			set { _licensePlateTo = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryDiscrepancy> InventoryDiscrepancies
		{
			get { return _inventoryDiscrepancies; }
			set { _inventoryDiscrepancies = value; }
		}

		[DataMember]
		public virtual ICollection<InvoiceDetail> InvoiceDetails
		{
			get { return _invoiceDetails; }
			set { _invoiceDetails = value; }
		}

		[DataMember]
		public virtual ICollection<UdfItemTransactionValue> UdfItemTransactionValues
		{
			get { return _udfItemTransactionValues; }
			set { _udfItemTransactionValues = value; }
		}

		[DataMember]
		public virtual Location LocationFrom
		{
			get { return _locationFrom; }
			set { _locationFrom = value; }
		}

		[DataMember]
		public virtual Location LocationTo
		{
			get { return _locationTo; }
			set { _locationTo = value; }
		}

		[DataMember]
		public virtual OrderDetail OrderDetail
		{
			get { return _orderDetail; }
			set { _orderDetail = value; }
		}

		[DataMember]
		public virtual OrderHeader OrderHeader
		{
			get { return _orderHeader; }
			set { _orderHeader = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant ParticipantBy
		{
			get { return _participantBy; }
			set { _participantBy = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant ParticipantFrom
		{
			get { return _participantFrom; }
			set { _participantFrom = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant ParticipantTo
		{
			get { return _participantTo; }
			set { _participantTo = value; }
		}

		[DataMember]
		public virtual PoolItem PoolItemFrom
		{
			get { return _poolItemFrom; }
			set { _poolItemFrom = value; }
		}

		[DataMember]
		public virtual PoolItem PoolItemTo
		{
			get { return _poolItemTo; }
			set { _poolItemTo = value; }
		}

		[DataMember]
		public virtual PurchaseOrderDetail PurchaseOrderDetail
		{
			get { return _purchaseOrderDetail; }
			set { _purchaseOrderDetail = value; }
		}

		[DataMember]
		public virtual ReceiptDetail ReceiptDetail
		{
			get { return _receiptDetail; }
			set { _receiptDetail = value; }
		}

		[DataMember]
		public virtual RequisitionDetail RequisitionDetail
		{
			get { return _requisitionDetail; }
			set { _requisitionDetail = value; }
		}

		[DataMember]
		public virtual ReturnDetail ReturnDetail
		{
			get { return _returnDetail; }
			set { _returnDetail = value; }
		}

		[DataMember]
		public virtual StatusCode IntegrationStatusCode
		{
            get { return _integrationStatusCode != null ? _integrationStatusCode : StatusCode.GetOpenIntegrationStatusCode(); }
            set { _integrationStatusCode = value != null ? value : StatusCode.GetOpenIntegrationStatusCode(); }
        }

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCodeFrom
		{
			get { return _statusCodeFrom; }
			set { _statusCodeFrom = value; }
		}

		[DataMember]
		public virtual StatusReasonCode StatusReasonCode
		{
			get { return _statusReasonCode; }
			set { _statusReasonCode = value; }
		}

		[DataMember]
		public virtual String AssetCodeFrom
		{
			get { return _assetCodeFrom; }
			set { _assetCodeFrom = value; }
		}

		[DataMember]
		public virtual String AssetCodeTo
		{
			get { return _assetCodeTo; }
			set { _assetCodeTo = value; }
		}

		[DataMember]
		public virtual String ClaimTrackingNumber
		{
			get { return _claimTrackingNumber; }
			set { _claimTrackingNumber = value; }
		}

		[DataMember]
		public virtual String LotNumber
		{
			get { return _lotNumber; }
			set { _lotNumber = value; }
		}

		[DataMember]
		public virtual String Memo
		{
			get { return _memo; }
			set { _memo = value; }
		}

		[DataMember]
		public virtual String OrderCode
		{
			get { return _orderCode; }
			set { _orderCode = value; }
		}

		[DataMember]
		public virtual String SerialNumber
		{
			get { return _serialNumber; }
			set { _serialNumber = value; }
		}

		[DataMember]
		public virtual Task Task
		{
			get { return _task; }
			set { _task = value; }
		}

		[DataMember]
		public virtual TransactionType TransactionType
		{
			get { return _transactionType; }
			set { _transactionType = value; }
		}

		[DataMember]
		public virtual UnitOfMeasure UnitOfMeasure
		{
			get { return _unitOfMeasure; }
			set { _unitOfMeasure = value; }
		}

		[DataMember]
		public virtual VendorItem VendorItem
		{
			get { return _vendorItem; }
			set { _vendorItem = value; }
		}

		[DataMember]
		public virtual Wave Wave
		{
			get { return _wave; }
			set { _wave = value; }
		}

		[DataMember]
		public virtual WorkOrderDetail WorkOrderDetail
		{
			get { return _workOrderDetail; }
			set { _workOrderDetail = value; }
		}


		#endregion
	}
}
