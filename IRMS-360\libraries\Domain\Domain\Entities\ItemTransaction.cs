using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
    [DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
    public partial class ItemTransaction : Entity
    {
        #region Properties

        [DataMember]
        public virtual String AisleCode { get; set; }
        [DataMember]
        public virtual String AssetCode { get; set; }
        [DataMember]
        public virtual String LocationCode { get; set; }
        [DataMember]
        public virtual String ZoneCode { get; set; }

        #endregion

        #region Properties.Reports
        public virtual Int32? CompanyLocationTypeId { get; set; }
        public virtual Decimal AdjustmentUnits { get; set; }
        public virtual Decimal AdjustmentWeight { get; set; }
        public virtual Decimal BeginningBalanceUnits { get; set; }
        public virtual Decimal BeginningBalanceWeight { get; set; }
        public virtual Decimal? CatchWeight { get; set; }
        public virtual Decimal? ItemWeight { get; set; }
        public virtual Decimal OnHandUnits { get; set; }
        public virtual Decimal OnHandWeight { get; set; }
        public virtual Decimal ReceivedUnits { get; set; }
        public virtual Decimal ReceivedWeight { get; set; }
        public virtual Decimal ShippedUnits { get; set; }
        public virtual Decimal ShippedWeight { get; set; }
        public virtual Decimal ReceivedQuantity { get; set; }
        public virtual int? ReceiptDetailId { get; set; }
        public virtual String CompanyCode { get; set; }
        public virtual String CompanyLocationCode { get; set; }
        public virtual String CompanyName { get; set; }
        public virtual String CustomerCode { get; set; }
        public virtual String CustomerName { get; set; }
        public virtual String EndDate { get; set; }
        public virtual String FirstNameBy { get; set; }
        public virtual string InventoryAdjustmentCodeCode { get; set; }
        public virtual String ItemCode { get; set; }
        public virtual int? ItemId { get; set; }
        public virtual int? InventoryItemId { get; set; }
        public virtual String ItemDescription { get; set; }
        public virtual String ItemGroupDescription { get; set; }
        public virtual String ItemReceivedDate { get; set; }
        public virtual String LastNameBy { get; set; }
        public virtual String BusinessName { get; set; }
        public virtual int? OrderHeaderId { get; set; }
        public virtual String PurchaseOrderCode { get; set; }
        public virtual String PurchaseOrderSuffix { get; set; }
        public virtual String ReceivedBy { get; set; }
        public virtual String ReceivedQuantityUnit { get; set; }
        public virtual String StartDate { get; set; }
        public virtual String StatusCodeCode { get; set; }
        public virtual String TransactionTypeCode { get; set; }
        public virtual String TransactionTypeDescription { get; set; }
        public virtual String TransactionNumber { get; set; }
        public virtual String Lot { get; set; }
        public virtual String Comments { get; set; }
        public virtual DateTime TransactionDate { get; set; }
        public virtual String TruckCode { get; set; }
        public virtual String VendorCode { get; set; }
        public virtual String ToDate { get; set; }
        public virtual String FromDate { get; set; }
        public virtual String OrderSuffix { get; set; }
        public virtual String BOL { get; set; }
        public virtual String PRO { get; set; }
        public virtual String SHIPTO { get; set; }
        public virtual String ItemGroupCode { get; set; }
        public virtual String ItemNumber { get; set; }
        public virtual String FromStatusCodeCode { get; set; }
        public virtual String FromStatusCodeDescription { get; set; }
        public virtual String OrderShipToName { get; set; }
        public virtual String ReceiptCode { get; set; }
        public virtual String UOMCode { get; set; }
        public virtual Decimal ActualInboundQuantity { get; set; }
        public virtual Decimal ActQuantity { get; set; }
        public virtual Decimal OpeningBalance { get; set; }
        public virtual Decimal EndingBalance { get; set; }
        public virtual Decimal Weight { get; set; }
        public virtual String Warehouse { get; set; }
        public virtual String CarrierCode { get; set; }
        public virtual String LicensePlateCode { get; set; }
        public virtual String ParticipantByName { get; set; }
        public virtual String EmployeeCode { get; set; }
        public virtual String LocationDescription { get; set; }
        public virtual Int32? TransactionCount { get; set; }

        #endregion

        #region Constructor

        public ItemTransaction()
		{
			//
		}

        #endregion
    }
}
