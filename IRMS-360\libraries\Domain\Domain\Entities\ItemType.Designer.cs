using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ItemType : Entity
	{
		#region Fields

		private ICollection<CompanyGlobalProfile> _companyGlobalProfiles = new HashSet<CompanyGlobalProfile>();
		private ICollection<Item> _items = new HashSet<Item>();
		private String _active;
		private String _consumable;
		private String _description;
		private String _expendable;
		private String _itemTypeCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<CompanyGlobalProfile> CompanyGlobalProfiles
		{
			get { return _companyGlobalProfiles; }
			set { _companyGlobalProfiles = value; }
		}

		[DataMember]
		public virtual ICollection<Item> Items
		{
			get { return _items; }
			set { _items = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Consumable
		{
			get { return _consumable; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Consumable must not be blank or null.");
				else _consumable = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String Expendable
		{
			get { return _expendable; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Expendable must not be blank or null.");
				else _expendable = value;
			}
		}

		[DataMember]
		public virtual String ItemTypeCode
		{
			get { return _itemTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ItemTypeCode must not be blank or null.");
				else _itemTypeCode = value;
			}
		}


		#endregion
	}
}
