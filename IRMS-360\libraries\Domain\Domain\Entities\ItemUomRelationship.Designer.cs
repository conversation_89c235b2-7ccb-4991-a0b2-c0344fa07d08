using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ItemUomRelationship : Entity
	{
		#region Fields

		private Decimal _factor;
		private Decimal? _cube;
		private Decimal? _height;
		private Decimal? _length;
		private Decimal? _weight;
		private Decimal? _width;
		private Int32? _stackHeight;
		private Item _item;
		private ICollection<ItemSubstitute> _itemSubstitutes = new HashSet<ItemSubstitute>();
		private String _active;
		private String _conveyable;
        private String _gtinCode;
        private String _hazmatUom;
		private String _innerPack;
		private String _operand;
		private UnitOfMeasure _unitOfMeasure;

		#endregion

		#region Properties

		[DataMember]
		public virtual Decimal Factor
		{
			get { return _factor; }
			set { _factor = value; }
		}

		[DataMember]
		public virtual Decimal? Cube
		{
			get { return _cube; }
			set { _cube = value; }
		}

		[DataMember]
		public virtual Decimal? Height
		{
			get { return _height; }
			set { _height = value; }
		}

		[DataMember]
		public virtual Decimal? Length
		{
			get { return _length; }
			set { _length = value; }
		}

		[DataMember]
		public virtual Decimal? Weight
		{
			get { return _weight; }
			set { _weight = value; }
		}

		[DataMember]
		public virtual Decimal? Width
		{
			get { return _width; }
			set { _width = value; }
		}

		[DataMember]
		public virtual Int32? StackHeight
		{
			get { return _stackHeight; }
			set { _stackHeight = value; }
		}

		[DataMember]
		public virtual Item Item
		{
			get { return _item; }
			set { _item = value; }
		}

		[DataMember]
		public virtual ICollection<ItemSubstitute> ItemSubstitutes
		{
			get { return _itemSubstitutes; }
			set { _itemSubstitutes = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Conveyable
		{
			get { return _conveyable; }
			set { _conveyable = value; }
		}

		[DataMember]
		public virtual String GtinCode
		{
			get { return _gtinCode; }
			set { _gtinCode = value; }
		}

        [DataMember]
        public virtual String HazmatUom
        {
            get { return _hazmatUom; }
            set { _hazmatUom = value; }
        }

		[DataMember]
		public virtual String InnerPack
		{
			get { return _innerPack; }
			set { _innerPack = value; }
		}

		[DataMember]
		public virtual String Operand
		{
			get { return _operand; }
			set { _operand = value; }
		}

		[DataMember]
		public virtual UnitOfMeasure UnitOfMeasure
		{
			get { return _unitOfMeasure; }
			set { _unitOfMeasure = value; }
		}


		#endregion
	}
}
