using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class ItemUomRelationship : Entity
	{
		#region Properties

        [DataMember]
        public virtual Int32 ItemId { get; set; }
        [DataMember]
        public virtual Int32 UomId { get; set; }
		[DataMember]
		public virtual String ItemCode { get; set; }
		[DataMember]
		public virtual String UomCode { get; set; }

		#endregion

		#region Constructor

		public ItemUomRelationship()
		{
			//
		}

		#endregion
	}
}
