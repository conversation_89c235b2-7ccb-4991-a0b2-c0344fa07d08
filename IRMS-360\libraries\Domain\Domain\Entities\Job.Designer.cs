using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Job : Entity
	{
		#region Fields

		private Agency _agency;
		private CompanyLocationType _companyLocationType;
		private Int32 _jobVersion;
		private Job _parentJob;
		private ICollection<Job> _childJobs = new HashSet<Job>();
		private ICollection<JobProgram> _jobPrograms = new HashSet<JobProgram>();
		private ICollection<JobSchedule> _jobSchedules = new HashSet<JobSchedule>();
		private ICollection<Task> _tasks = new HashSet<Task>();
		private String _active;
		private String _jobCode;
		private String _name;
		private String _toList;

		#endregion

		#region Properties

		[DataMember]
		public virtual Agency Agency
		{
			get { return _agency; }
			set { _agency = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual Int32 JobVersion
		{
			get { return _jobVersion; }
			set { _jobVersion = value; }
		}

		[DataMember]
		public virtual Job ParentJob
		{
			get { return _parentJob; }
			set { _parentJob = value; }
		}

		[DataMember]
		public virtual ICollection<Job> ChildJobs
		{
			get { return _childJobs; }
			set { _childJobs = value; }
		}

		[DataMember]
		public virtual ICollection<JobProgram> JobPrograms
		{
			get { return _jobPrograms; }
			set { _jobPrograms = value; }
		}

		[DataMember]
		public virtual ICollection<JobSchedule> JobSchedules
		{
			get { return _jobSchedules; }
			set { _jobSchedules = value; }
		}

		[DataMember]
		public virtual ICollection<Task> Tasks
		{
			get { return _tasks; }
			set { _tasks = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String JobCode
		{
			get { return _jobCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("JobCode must not be blank or null.");
				else _jobCode = value;
			}
		}

		[DataMember]
		public virtual String Name
		{
			get { return _name; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Name must not be blank or null.");
				else _name = value;
			}
		}

		[DataMember]
		public virtual String ToList
		{
			get { return _toList; }
			set { _toList = value; }
		}


		#endregion
	}
}
