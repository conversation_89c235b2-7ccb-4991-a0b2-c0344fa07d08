using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class JobDetail : Entity
	{
		#region Fields

		private ICollection<JobDetailParameter> _jobDetailParameters = new HashSet<JobDetailParameter>();
		private ICollection<JobDetailTrigger> _jobDetailTriggers = new HashSet<JobDetailTrigger>();
		private String _active;
		private String _description;
		private String _durable;
		private String _jobClass;
		private String _jobGroup;
		private String _jobName;
		private String _jobType;
		private String _recovery;
		private String _volatile;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<JobDetailParameter> JobDetailParameters
		{
			get { return _jobDetailParameters; }
			set { _jobDetailParameters = value; }
		}

		[DataMember]
		public virtual ICollection<JobDetailTrigger> JobDetailTriggers
		{
			get { return _jobDetailTriggers; }
			set { _jobDetailTriggers = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set { _description = value; }
		}

		[DataMember]
		public virtual String Durable
		{
			get { return _durable; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Durable must not be blank or null.");
				else _durable = value;
			}
		}

		[DataMember]
		public virtual String JobClass
		{
			get { return _jobClass; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("JobClass must not be blank or null.");
				else _jobClass = value;
			}
		}

		[DataMember]
		public virtual String JobGroup
		{
			get { return _jobGroup; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("JobGroup must not be blank or null.");
				else _jobGroup = value;
			}
		}

		[DataMember]
		public virtual String JobName
		{
			get { return _jobName; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("JobName must not be blank or null.");
				else _jobName = value;
			}
		}

		[DataMember]
		public virtual String JobType
		{
			get { return _jobType; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("JobType must not be blank or null.");
				else _jobType = value;
			}
		}

		[DataMember]
		public virtual String Recovery
		{
			get { return _recovery; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Recovery must not be blank or null.");
				else _recovery = value;
			}
		}

		[DataMember]
		public virtual String Volatile
		{
			get { return _volatile; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Volatile must not be blank or null.");
				else _volatile = value;
			}
		}


		#endregion
	}
}
