using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
    [DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
    public partial class JobDetail : Entity
    {
        #region Constructor

        public JobDetail()
        {
            //
        }

        #endregion

        #region Properties.EOD

        [DataMember]
        public virtual String CronExpression { get; set; }
        [DataMember]
        public virtual String MisfireInstruction { get; set; }
        [DataMember]
        public virtual String TriggerGroup { get; set; }
        [DataMember]
        public virtual String TriggerName { get; set; }



        #endregion
    }
}
