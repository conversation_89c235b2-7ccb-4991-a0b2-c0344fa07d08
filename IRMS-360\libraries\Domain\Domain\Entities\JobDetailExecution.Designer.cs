using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class JobDetailExecution : Entity
	{
		#region Fields

		private DateTime _started;
		private DateTime? _completed;
		private String _executionResult;
		private String _jobGroup;
		private String _jobName;
		private String _jobTriggerGroup;
		private String _jobTriggerName;
		private String _status;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime Started
		{
			get { return _started; }
			set { _started = value; }
		}

		[DataMember]
		public virtual DateTime? Completed
		{
			get { return _completed; }
			set { _completed = value; }
		}

		[DataMember]
		public virtual String ExecutionResult
		{
			get { return _executionResult; }
			set { _executionResult = value; }
		}

		[DataMember]
		public virtual String JobGroup
		{
			get { return _jobGroup; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("JobGroup must not be blank or null.");
				else _jobGroup = value;
			}
		}

		[DataMember]
		public virtual String JobName
		{
			get { return _jobName; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("JobName must not be blank or null.");
				else _jobName = value;
			}
		}

		[DataMember]
		public virtual String JobTriggerGroup
		{
			get { return _jobTriggerGroup; }
			set { _jobTriggerGroup = value; }
		}

		[DataMember]
		public virtual String JobTriggerName
		{
			get { return _jobTriggerName; }
			set { _jobTriggerName = value; }
		}

		[DataMember]
		public virtual String Status
		{
			get { return _status; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Status must not be blank or null.");
				else _status = value;
			}
		}


		#endregion
	}
}
