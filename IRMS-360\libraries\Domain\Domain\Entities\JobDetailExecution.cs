using System;
using System.Runtime.Serialization;
using Upp.Irms.Core;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class JobDetailExecution : Entity
	{
		#region Constructor

		public JobDetailExecution()
		{
			//
		}

		#endregion

        #region Methods.Public
        public virtual void Create()
        {
            _dateCreated = DateTime.Now;
            _started = DateTime.Now;
            _status = "Completed";
            _userCreated = Registry.Find<UserAccount>().UserName;
            //
            Repositories.Get<JobDetailExecution>().Add(this);
        }

        #endregion
    }
}
