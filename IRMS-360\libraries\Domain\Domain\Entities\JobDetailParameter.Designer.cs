using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class JobDetailParameter : Entity
	{
		#region Fields

		private JobDetail _jobDetail;
		private String _active;
		private String _description;
		private String _parameterName;
		private String _parameterValue;

		#endregion

		#region Properties

		[DataMember]
		public virtual JobDetail JobDetail
		{
			get { return _jobDetail; }
			set { _jobDetail = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set { _description = value; }
		}

		[DataMember]
		public virtual String ParameterName
		{
			get { return _parameterName; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ParameterName must not be blank or null.");
				else _parameterName = value;
			}
		}

		[DataMember]
		public virtual String ParameterValue
		{
			get { return _parameterValue; }
			set { _parameterValue = value; }
		}


		#endregion
	}
}
