using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class JobDetailTrigger : Entity
	{
		#region Fields

		private JobDetail _jobDetail;
		private String _active;
		private String _cronExpression;
		private String _description;
		private String _misfireInstruction;
		private String _triggerGroup;
		private String _triggerName;
		private String _volatile;

		#endregion

		#region Properties

		[DataMember]
		public virtual JobDetail JobDetail
		{
			get { return _jobDetail; }
			set { _jobDetail = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String CronExpression
		{
			get { return _cronExpression; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("CronExpression must not be blank or null.");
				else _cronExpression = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set { _description = value; }
		}

		[DataMember]
		public virtual String MisfireInstruction
		{
			get { return _misfireInstruction; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("MisfireInstruction must not be blank or null.");
				else _misfireInstruction = value;
			}
		}

		[DataMember]
		public virtual String TriggerGroup
		{
			get { return _triggerGroup; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("TriggerGroup must not be blank or null.");
				else _triggerGroup = value;
			}
		}

		[DataMember]
		public virtual String TriggerName
		{
			get { return _triggerName; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("TriggerName must not be blank or null.");
				else _triggerName = value;
			}
		}

		[DataMember]
		public virtual String Volatile
		{
			get { return _volatile; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Volatile must not be blank or null.");
				else _volatile = value;
			}
		}


		#endregion
	}
}
