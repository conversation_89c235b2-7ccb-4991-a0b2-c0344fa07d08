using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class JobExecution : Entity
	{
		#region Fields

		private DateTime _started;
		private DateTime? _completed;
		private JobProgram _jobProgram;
		private JobSchedule _jobSchedule;
		private String _executionResult;
		private Task _task;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime Started
		{
			get { return _started; }
			set { _started = value; }
		}

		[DataMember]
		public virtual DateTime? Completed
		{
			get { return _completed; }
			set { _completed = value; }
		}

		[DataMember]
		public virtual JobProgram JobProgram
		{
			get { return _jobProgram; }
			set { _jobProgram = value; }
		}

		[DataMember]
		public virtual JobSchedule JobSchedule
		{
			get { return _jobSchedule; }
			set { _jobSchedule = value; }
		}

		[DataMember]
		public virtual String ExecutionResult
		{
			get { return _executionResult; }
			set { _executionResult = value; }
		}

		[DataMember]
		public virtual Task Task
		{
			get { return _task; }
			set { _task = value; }
		}


		#endregion
	}
}
