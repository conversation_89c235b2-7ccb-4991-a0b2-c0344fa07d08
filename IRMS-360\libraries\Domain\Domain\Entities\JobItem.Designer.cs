using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class JobItem : Entity
	{
		#region Fields

		private Decimal _maximumQuantity;
		private Decimal _minimumQuantity;
		private Item _item;
		private JobTitle _jobTitle;
		private String _active;

		#endregion

		#region Properties

		[DataMember]
		public virtual Decimal MaximumQuantity
		{
			get { return _maximumQuantity; }
			set { _maximumQuantity = value; }
		}

		[DataMember]
		public virtual Decimal MinimumQuantity
		{
			get { return _minimumQuantity; }
			set { _minimumQuantity = value; }
		}

		[DataMember]
		public virtual Item Item
		{
			get { return _item; }
			set { _item = value; }
		}

		[DataMember]
		public virtual JobTitle JobTitle
		{
			get { return _jobTitle; }
			set { _jobTitle = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}


		#endregion
	}
}
