using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class JobParameter : Entity
	{
		#region Fields

		private Int32 _sortOrder;
		private JobProgram _jobProgram;
		private Parameter _parameter;
		private String _parameterValue;

		#endregion

		#region Properties

		[DataMember]
		public virtual Int32 SortOrder
		{
			get { return _sortOrder; }
			set { _sortOrder = value; }
		}

		[DataMember]
		public virtual JobProgram JobProgram
		{
			get { return _jobProgram; }
			set { _jobProgram = value; }
		}

		[DataMember]
		public virtual Parameter Parameter
		{
			get { return _parameter; }
			set { _parameter = value; }
		}

		[DataMember]
		public virtual String ParameterValue
		{
			get { return _parameterValue; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ParameterValue must not be blank or null.");
				else _parameterValue = value;
			}
		}


		#endregion
	}
}
