using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class JobProgram : Entity
	{
		#region Fields

		private ApplicationModule _applicationModule;
		private Job _job;
		private JobProgram _parentJobProgram;
		private ICollection<JobExecution> _jobExecutions = new HashSet<JobExecution>();
		private ICollection<JobParameter> _jobParameters = new HashSet<JobParameter>();
		private ICollection<JobProgram> _childJobPrograms = new HashSet<JobProgram>();
		private String _active;
		private String _inputFileName;
		private String _inputIndicator;
		private String _outputFileName;
		private String _outputIndicator;
		private String _stopOnFailure;

		#endregion

		#region Properties

		[DataMember]
		public virtual ApplicationModule ApplicationModule
		{
			get { return _applicationModule; }
			set { _applicationModule = value; }
		}

		[DataMember]
		public virtual Job Job
		{
			get { return _job; }
			set { _job = value; }
		}

		[DataMember]
		public virtual JobProgram ParentJobProgram
		{
			get { return _parentJobProgram; }
			set { _parentJobProgram = value; }
		}

		[DataMember]
		public virtual ICollection<JobExecution> JobExecutions
		{
			get { return _jobExecutions; }
			set { _jobExecutions = value; }
		}

		[DataMember]
		public virtual ICollection<JobParameter> JobParameters
		{
			get { return _jobParameters; }
			set { _jobParameters = value; }
		}

		[DataMember]
		public virtual ICollection<JobProgram> ChildJobPrograms
		{
			get { return _childJobPrograms; }
			set { _childJobPrograms = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String InputFileName
		{
			get { return _inputFileName; }
			set { _inputFileName = value; }
		}

		[DataMember]
		public virtual String InputIndicator
		{
			get { return _inputIndicator; }
			set { _inputIndicator = value; }
		}

		[DataMember]
		public virtual String OutputFileName
		{
			get { return _outputFileName; }
			set { _outputFileName = value; }
		}

		[DataMember]
		public virtual String OutputIndicator
		{
			get { return _outputIndicator; }
			set { _outputIndicator = value; }
		}

		[DataMember]
		public virtual String StopOnFailure
		{
			get { return _stopOnFailure; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("StopOnFailure must not be blank or null.");
				else _stopOnFailure = value;
			}
		}


		#endregion
	}
}
