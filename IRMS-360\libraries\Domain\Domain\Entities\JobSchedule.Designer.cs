using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class JobSchedule : Entity
	{
		#region Fields

		private Frequency _frequency;
		private Int32? _dayOfMonth;
		private Job _job;
		private ICollection<JobExecution> _jobExecutions = new HashSet<JobExecution>();
		private String _active;
		private String _friday;
		private String _monday;
		private String _saturday;
		private String _sunday;
		private String _thursday;
		private String _timeOfDay;
		private String _timeOfMonth;
		private String _tuesday;
		private String _wednesday;

		#endregion

		#region Properties

		[DataMember]
		public virtual Frequency Frequency
		{
			get { return _frequency; }
			set { _frequency = value; }
		}

		[DataMember]
		public virtual Int32? DayOfMonth
		{
			get { return _dayOfMonth; }
			set { _dayOfMonth = value; }
		}

		[DataMember]
		public virtual Job Job
		{
			get { return _job; }
			set { _job = value; }
		}

		[DataMember]
		public virtual ICollection<JobExecution> JobExecutions
		{
			get { return _jobExecutions; }
			set { _jobExecutions = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Friday
		{
			get { return _friday; }
			set { _friday = value; }
		}

		[DataMember]
		public virtual String Monday
		{
			get { return _monday; }
			set { _monday = value; }
		}

		[DataMember]
		public virtual String Saturday
		{
			get { return _saturday; }
			set { _saturday = value; }
		}

		[DataMember]
		public virtual String Sunday
		{
			get { return _sunday; }
			set { _sunday = value; }
		}

		[DataMember]
		public virtual String Thursday
		{
			get { return _thursday; }
			set { _thursday = value; }
		}

		[DataMember]
		public virtual String TimeOfDay
		{
			get { return _timeOfDay; }
			set { _timeOfDay = value; }
		}

		[DataMember]
		public virtual String TimeOfMonth
		{
			get { return _timeOfMonth; }
			set { _timeOfMonth = value; }
		}

		[DataMember]
		public virtual String Tuesday
		{
			get { return _tuesday; }
			set { _tuesday = value; }
		}

		[DataMember]
		public virtual String Wednesday
		{
			get { return _wednesday; }
			set { _wednesday = value; }
		}


		#endregion
	}
}
