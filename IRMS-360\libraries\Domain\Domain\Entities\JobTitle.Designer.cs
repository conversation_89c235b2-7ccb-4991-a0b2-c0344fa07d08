using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class JobTitle : Entity
	{
		#region Fields

		private ICollection<AlternateJobTitle> _alternateJobTitles = new HashSet<AlternateJobTitle>();
		private ICollection<JobItem> _jobItems = new HashSet<JobItem>();
		private ICollection<Participant> _participants = new HashSet<Participant>();
		private String _active;
		private String _description;
		private String _jobTitleCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<AlternateJobTitle> AlternateJobTitles
		{
			get { return _alternateJobTitles; }
			set { _alternateJobTitles = value; }
		}

		[DataMember]
		public virtual ICollection<JobItem> JobItems
		{
			get { return _jobItems; }
			set { _jobItems = value; }
		}

		[DataMember]
		public virtual ICollection<Participant> Participants
		{
			get { return _participants; }
			set { _participants = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String JobTitleCode
		{
			get { return _jobTitleCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("JobTitleCode must not be blank or null.");
				else _jobTitleCode = value;
			}
		}


		#endregion
	}
}
