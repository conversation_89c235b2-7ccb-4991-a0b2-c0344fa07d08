using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class KitDetail : Entity
	{
		#region Fields

		private Decimal? _maximumQuantity;
		private Decimal? _minimumQuantity;
		private Int32? _buildSequence;
		private Decimal? _quantity;
		private Item _item;
		private KitHeader _kitHeader;
		private String _active;
		private String _description;
		private String _kitCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual Decimal? MaximumQuantity
		{
			get { return _maximumQuantity; }
			set { _maximumQuantity = value; }
		}

		[DataMember]
		public virtual Decimal? MinimumQuantity
		{
			get { return _minimumQuantity; }
			set { _minimumQuantity = value; }
		}

		[DataMember]
		public virtual Int32? BuildSequence
		{
			get { return _buildSequence; }
			set { _buildSequence = value; }
		}

		[DataMember]
		public virtual Decimal? Quantity
		{
			get { return _quantity; }
			set { _quantity = value; }
		}

		[DataMember]
		public virtual Item Item
		{
			get { return _item; }
			set { _item = value; }
		}

		[DataMember]
		public virtual KitHeader KitHeader
		{
			get { return _kitHeader; }
			set { _kitHeader = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set { _description = value; }
		}

		[DataMember]
		public virtual String KitCode
		{
			get { return _kitCode; }
			set { _kitCode = value; }
		}


		#endregion
	}
}
