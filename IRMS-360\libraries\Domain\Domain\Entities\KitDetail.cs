using System;
using System.Runtime.Serialization;

using NHibernate.Criterion;

using Upp.Irms.Core;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class KitDetail : Entity
	{
		#region Properties

		[DataMember]
		public virtual Decimal IncludedQuantity { get; set; }
		[DataMember]
		public virtual Decimal? RemainingQuantity { get; set; }
		[DataMember]
		public virtual String ItemCode { get; set; }

		#endregion

		#region Constructor

		public KitDetail()
		{
			//
		}

		#endregion

		#region Methods.Public

		public virtual void CalculateRemainingQuantity(InventoryItem kit)
		{
			if ("Y".Equals(_item.ItemType.Consumable))
			{
				DetachedCriteria quantity = DetachedCriteria.For<InventoryItemDetail>()
					.Add("InventoryItem.Item", _item)
					.Add("ParentInventoryItem", kit)
					.SetProjection(Projections.Sum("Quantity"));
				this.IncludedQuantity = Repositories.Get<InventoryItemDetail>().Function<Int32>(quantity);
			}
			else
			{
				DetachedCriteria quantity = DetachedCriteria.For<InventoryItem>()
					.Add("Item", _item)
					.Add("ParentInventoryItem", kit)
					.SetProjection(Projections.Sum("Quantity"));
				this.IncludedQuantity = Repositories.Get<InventoryItem>().Function<Decimal>(quantity);
			}
			//
			this.RemainingQuantity = _minimumQuantity - this.IncludedQuantity;
			if (this.RemainingQuantity <= 0M) this.RemainingQuantity = 0M;
		}

		#endregion
	}
}
