using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class KitHeader : Entity
	{
		#region Fields

		private Item _item;
		private ICollection<InventoryItem> _inventoryItems = new HashSet<InventoryItem>();
		private ICollection<InventoryItemDetail> _inventoryItemDetails = new HashSet<InventoryItemDetail>();
		private ICollection<KitDetail> _kitDetails = new HashSet<KitDetail>();
		private String _active;
		private String _assemblyRequired;
		private String _description;
		private String _disassemblyRequired;
		private String _kitCode;
		private String _kitTypeCode;
		private String _notes;
		private String _trackSubComponents;

		#endregion

		#region Properties

		[DataMember]
		public virtual Item Item
		{
			get { return _item; }
			set { _item = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryItem> InventoryItems
		{
			get { return _inventoryItems; }
			set { _inventoryItems = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryItemDetail> InventoryItemDetails
		{
			get { return _inventoryItemDetails; }
			set { _inventoryItemDetails = value; }
		}

		[DataMember]
		public virtual ICollection<KitDetail> KitDetails
		{
			get { return _kitDetails; }
			set { _kitDetails = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String AssemblyRequired
		{
			get { return _assemblyRequired; }
			set { _assemblyRequired = value; }
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set { _description = value; }
		}

		[DataMember]
		public virtual String DisassemblyRequired
		{
			get { return _disassemblyRequired; }
			set { _disassemblyRequired = value; }
		}

		[DataMember]
		public virtual String KitCode
		{
			get { return _kitCode; }
			set { _kitCode = value; }
		}

		[DataMember]
		public virtual String KitTypeCode
		{
			get { return _kitTypeCode; }
			set { _kitTypeCode = value; }
		}

		[DataMember]
		public virtual String Notes
		{
			get { return _notes; }
			set { _notes = value; }
		}

		[DataMember]
		public virtual String TrackSubComponents
		{
			get { return _trackSubComponents; }
			set { _trackSubComponents = value; }
		}


		#endregion
	}
}
