using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class LicensePlate : Entity
	{
		#region Fields

		private AgencyLocationType _agencyLocationType;
		private AgencyOrganizationalUnit _agencyOrganizationalUnit;
		private CompanyLocationType _companyLocationType;
		private CustomerLocationType _customerLocationType;
		private Decimal? _weight;
		private ItemFulfillment _itemFulfillment;
		private LicensePlate _parentLicensePlate;
		private LicenseType _licenseType;
		private ICollection<CartonDetail> _cartonDetails = new HashSet<CartonDetail>();
		private ICollection<CartonHeader> _cartonHeaders = new HashSet<CartonHeader>();
		private ICollection<InventoryItem> _inventoryItems = new HashSet<InventoryItem>();
		private ICollection<InventoryPick> _inventoryPicks = new HashSet<InventoryPick>();
		private ICollection<InventoryTask> _inventoryTasks = new HashSet<InventoryTask>();
		private ICollection<Item> _items = new HashSet<Item>();
		private ICollection<LicensePlate> _childLicensePlates = new HashSet<LicensePlate>();
		private ICollection<LicensePlateLocation> _licensePlateLocations = new HashSet<LicensePlateLocation>();
		private ICollection<LicensePlateStatus> _licensePlateStatuses = new HashSet<LicensePlateStatus>();
		private ICollection<Location> _locations = new HashSet<Location>();
		private ICollection<OrderDetail> _orderDetails = new HashSet<OrderDetail>();
		private ICollection<PreReceiptCarton> _preReceiptCartons = new HashSet<PreReceiptCarton>();
		private ICollection<PreReceiptOverage> _preReceiptOverages = new HashSet<PreReceiptOverage>();
		private ICollection<ReceiptDetail> _receiptDetails = new HashSet<ReceiptDetail>();
		private ICollection<ShipmentDetail> _shipmentDetails = new HashSet<ShipmentDetail>();
		private ICollection<ShipmentHeader> _shipmentHeaders = new HashSet<ShipmentHeader>();
		private ICollection<Transaction> _transactions = new HashSet<Transaction>();
		private String _active;
		private String _additionalTrackingCode;
		private String _alternateLicensePlateCode;
		private String _licensePlateCode;
		private String _proNumber;
		private String _trackingCode;
		private String _ucc128;

		#endregion

		#region Properties

		[DataMember]
		public virtual AgencyLocationType AgencyLocationType
		{
			get { return _agencyLocationType; }
			set { _agencyLocationType = value; }
		}

		[DataMember]
		public virtual AgencyOrganizationalUnit AgencyOrganizationalUnit
		{
			get { return _agencyOrganizationalUnit; }
			set { _agencyOrganizationalUnit = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual CustomerLocationType CustomerLocationType
		{
			get { return _customerLocationType; }
			set { _customerLocationType = value; }
		}

		[DataMember]
		public virtual Decimal? Weight
		{
			get { return _weight; }
			set { _weight = value; }
		}

		[DataMember]
		public virtual ItemFulfillment ItemFulfillment
		{
			get { return _itemFulfillment; }
			set { _itemFulfillment = value; }
		}

		[DataMember]
		public virtual LicensePlate ParentLicensePlate
		{
			get { return _parentLicensePlate; }
			set { _parentLicensePlate = value; }
		}

		[DataMember]
		public virtual LicenseType LicenseType
		{
			get { return _licenseType; }
			set { _licenseType = value; }
		}

		[DataMember]
		public virtual ICollection<CartonDetail> CartonDetails
		{
			get { return _cartonDetails; }
			set { _cartonDetails = value; }
		}

		[DataMember]
		public virtual ICollection<CartonHeader> CartonHeaders
		{
			get { return _cartonHeaders; }
			set { _cartonHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryItem> InventoryItems
		{
			get { return _inventoryItems; }
			set { _inventoryItems = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryPick> InventoryPicks
		{
			get { return _inventoryPicks; }
			set { _inventoryPicks = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryTask> InventoryTasks
		{
			get { return _inventoryTasks; }
			set { _inventoryTasks = value; }
		}

		[DataMember]
		public virtual ICollection<Item> Items
		{
			get { return _items; }
			set { _items = value; }
		}

		[DataMember]
		public virtual ICollection<LicensePlate> ChildLicensePlates
		{
			get { return _childLicensePlates; }
			set { _childLicensePlates = value; }
		}

		[DataMember]
		public virtual ICollection<LicensePlateLocation> LicensePlateLocations
		{
			get { return _licensePlateLocations; }
			set { _licensePlateLocations = value; }
		}

		[DataMember]
		public virtual ICollection<LicensePlateStatus> LicensePlateStatuses
		{
			get { return _licensePlateStatuses; }
			set { _licensePlateStatuses = value; }
		}

		[DataMember]
		public virtual ICollection<Location> Locations
		{
			get { return _locations; }
			set { _locations = value; }
		}

		[DataMember]
		public virtual ICollection<OrderDetail> OrderDetails
		{
			get { return _orderDetails; }
			set { _orderDetails = value; }
		}

		[DataMember]
		public virtual ICollection<PreReceiptCarton> PreReceiptCartons
		{
			get { return _preReceiptCartons; }
			set { _preReceiptCartons = value; }
		}

		[DataMember]
		public virtual ICollection<PreReceiptOverage> PreReceiptOverages
		{
			get { return _preReceiptOverages; }
			set { _preReceiptOverages = value; }
		}

		[DataMember]
		public virtual ICollection<ReceiptDetail> ReceiptDetails
		{
			get { return _receiptDetails; }
			set { _receiptDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ShipmentDetail> ShipmentDetails
		{
			get { return _shipmentDetails; }
			set { _shipmentDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ShipmentHeader> ShipmentHeaders
		{
			get { return _shipmentHeaders; }
			set { _shipmentHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<Transaction> Transactions
		{
			get { return _transactions; }
			set { _transactions = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String AdditionalTrackingCode
		{
			get { return _additionalTrackingCode; }
			set { _additionalTrackingCode = value; }
		}

		[DataMember]
		public virtual String AlternateLicensePlateCode
		{
			get { return _alternateLicensePlateCode; }
			set { _alternateLicensePlateCode = value; }
		}

		[DataMember]
		public virtual String LicensePlateCode
		{
			get { return _licensePlateCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("LicensePlateCode must not be blank or null.");
				else _licensePlateCode = value;
			}
		}

		[DataMember]
		public virtual String ProNumber
		{
			get { return _proNumber; }
			set { _proNumber = value; }
		}

		[DataMember]
		public virtual String TrackingCode
		{
			get { return _trackingCode; }
			set { _trackingCode = value; }
		}

		[DataMember]
		public virtual String Ucc128
		{
			get { return _ucc128; }
			set { _ucc128 = value; }
		}


		#endregion
	}
}
