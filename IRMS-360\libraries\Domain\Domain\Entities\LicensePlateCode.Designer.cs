using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class LicensePlateCode : Entity
	{
		#region Fields

		private CompanyLocationType _companyLocationType;
		private Int32 _currentValue;
		private Int32 _incrementValue;
		private Int32 _initialValue;
		private String _code;
		private String _prefix;

		#endregion

		#region Properties

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual Int32 CurrentValue
		{
			get { return _currentValue; }
			set { _currentValue = value; }
		}

		[DataMember]
		public virtual Int32 IncrementValue
		{
			get { return _incrementValue; }
			set { _incrementValue = value; }
		}

		[DataMember]
		public virtual Int32 InitialValue
		{
			get { return _initialValue; }
			set { _initialValue = value; }
		}

		[DataMember]
		public virtual String Code
		{
			get { return _code; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Code must not be blank or null.");
				else _code = value;
			}
		}

		[DataMember]
		public virtual String Prefix
		{
			get { return _prefix; }
			set { _prefix = value; }
		}


		#endregion
	}
}
