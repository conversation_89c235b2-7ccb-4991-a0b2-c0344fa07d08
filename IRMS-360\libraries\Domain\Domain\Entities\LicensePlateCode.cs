using System;
using System.Runtime.Serialization;

using NHibernate.Criterion;

using Upp.Irms.Core;
using Upp.Shared.Utilities;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class LicensePlateCode : Entity
	{
		#region Fields.Static

		private static String[] _prefixes = new String[]
		{
			"P",
			"T",
			"C",
			"L",
			"I",
			"V",
			"SH"
		};

		#endregion

		#region Constructor

		public LicensePlateCode()
		{
			//
		}

		#endregion

		#region Methods.Static

		public static String GetCurrentValue(LicenseType type)
		{
			Int32 index = Array.IndexOf(_prefixes, type.LicensePrefix);
			if (index == -1) throw new Exception("Unknown License Plate prefix type.");
			//
			String rule = (10100 + index).ToString();
			Int32? value = BusinessRule.RetrieveInteger(rule);
			if (value == null) throw new Exception(String.Format("Business Rule {0} is undefined.", rule));
			//
			String length = Converter.ToString(value.Value - 1);
			String format = "{0}{1," + length + ":D" + length + "}";
			//
			Company company = Registry.Find<Company>();
			CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
			//
			DetachedCriteria criteria = DetachedCriteria.For<LicensePlateCode>();
			if (warehouse != null) criteria = criteria.Add("CompanyLocationType", warehouse);
			else criteria = criteria.Add("CompanyLocationType.CompanyLocation.Company", company);
			criteria = criteria.Add("Code", type.LicenseTypeCode);
			//
			LicensePlateCode key = Repositories.Get<LicensePlateCode>().Retrieve(criteria);
			if (key == null) throw new Exception(String.Format("Unable to find LicensePlateCode for {0}.", type.LicenseTypeCode));
			//
			key.CurrentValue += key.IncrementValue;
			key.DateModified = DateTime.Now;
			key.UserModified = "IRMS BL";
			Repositories.Get<LicensePlateCode>().Update(key);
			//
			return String.Format(format, key.Prefix, key.CurrentValue);
		}

		#endregion
	}
}
