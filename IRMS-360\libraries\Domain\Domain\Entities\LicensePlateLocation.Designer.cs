using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class LicensePlateLocation : Entity
	{
		#region Fields

		private DateTime _occurred;
		private LicensePlate _licensePlate;
		private Location _location;
		private String _slotNumber;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime Occurred
		{
			get { return _occurred; }
			set { _occurred = value; }
		}

		[DataMember]
		public virtual LicensePlate LicensePlate
		{
			get { return _licensePlate; }
			set { _licensePlate = value; }
		}

		[DataMember]
		public virtual Location Location
		{
			get { return _location; }
			set { _location = value; }
		}

		[DataMember]
		public virtual String SlotNumber
		{
			get { return _slotNumber; }
			set { _slotNumber = value; }
		}


		#endregion
	}
}
