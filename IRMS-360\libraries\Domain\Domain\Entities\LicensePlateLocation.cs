using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class LicensePlateLocation : Entity
	{
		#region Properties

		[DataMember]
		public virtual Int32 TruckSlotNumber { get; set; }
		[DataMember]
		public virtual String LicensePlateCode { get; set; }
		public virtual String LocationCode { get; set; }

		#endregion

		#region Constructor

		public LicensePlateLocation()
		{
			//
		}

		#endregion
	}
}
