using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class LicensePlateStatus : Entity
	{
		#region Fields

		private DateTime _occurred;
		private LicensePlate _licensePlate;
		private StatusCode _integrationStatusCode;
		private StatusCode _statusCode;

        private ICollection<InventoryPick> _inventoryPicks = new HashSet<InventoryPick>();
		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime Occurred
		{
			get { return _occurred; }
			set { _occurred = value; }
		}

		[DataMember]
		public virtual LicensePlate LicensePlate
		{
			get { return _licensePlate; }
			set { _licensePlate = value; }
		}

		[DataMember]
		public virtual StatusCode IntegrationStatusCode
		{
			get { return _integrationStatusCode != null ? _integrationStatusCode : StatusCode.GetOpenIntegrationStatusCode(); }
			set { _integrationStatusCode = value != null ? value : StatusCode.GetOpenIntegrationStatusCode(); }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

        [DataMember]
        public virtual ICollection<InventoryPick> InventoryPicks
        {
            get { return _inventoryPicks; }
            set { _inventoryPicks = value; }
        }

		#endregion
	}
}
