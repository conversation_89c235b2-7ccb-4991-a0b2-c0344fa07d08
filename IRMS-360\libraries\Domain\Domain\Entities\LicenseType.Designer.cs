using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class LicenseType : Entity
	{
		#region Fields

		private FunctionalAreaCode _functionalAreaCode;
		private ICollection<CompanyGlobalProfile> _companyGlobalProfiles = new HashSet<CompanyGlobalProfile>();
		private ICollection<LicensePlate> _licensePlates = new HashSet<LicensePlate>();
		private ICollection<LocationLicenseType> _locationLicenseTypes = new HashSet<LocationLicenseType>();
		private String _active;
		private String _description;
		private String _licensePrefix;
		private String _licenseTypeCode;
		private String _permanent;

		#endregion

		#region Properties

		[DataMember]
		public virtual FunctionalAreaCode FunctionalAreaCode
		{
			get { return _functionalAreaCode; }
			set { _functionalAreaCode = value; }
		}

		[DataMember]
		public virtual ICollection<CompanyGlobalProfile> CompanyGlobalProfiles
		{
			get { return _companyGlobalProfiles; }
			set { _companyGlobalProfiles = value; }
		}

		[DataMember]
		public virtual ICollection<LicensePlate> LicensePlates
		{
			get { return _licensePlates; }
			set { _licensePlates = value; }
		}

		[DataMember]
		public virtual ICollection<LocationLicenseType> LocationLicenseTypes
		{
			get { return _locationLicenseTypes; }
			set { _locationLicenseTypes = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String LicensePrefix
		{
			get { return _licensePrefix; }
			set { _licensePrefix = value; }
		}

		[DataMember]
		public virtual String LicenseTypeCode
		{
			get { return _licenseTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("LicenseTypeCode must not be blank or null.");
				else _licenseTypeCode = value;
			}
		}

		[DataMember]
		public virtual String Permanent
		{
			get { return _permanent; }
			set { _permanent = value; }
		}


		#endregion
	}
}
