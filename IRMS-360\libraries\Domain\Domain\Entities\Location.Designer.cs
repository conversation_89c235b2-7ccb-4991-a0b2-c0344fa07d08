using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Location : Entity
	{
		#region Fields

		private AgencyLocationType _agencyLocationType;
		private AgencyOrganizationalUnit _agencyOrganizationalUnit;
		private Aisle _aisle;
		private Carrier _carrier;
		private CompanyLocation _companyLocation;
		private CompanyLocationType _companyLocationType;
		private CompanyLocationZone _companyLocationZone;
		private CustomerLocation _customerLocation;
		private DateTime? _lastCycleCount;
		private Decimal? _maximumQuantity;
		private Decimal? _maximumWeight;
		private Decimal? _minimumQuantity;
		private Decimal? _replenishmentQuantity;
		private Decimal? _sizeFactor;
		private Dock _dock;
		private Int32? _cube;
		private Int32? _height;
		private Int32? _length;
		private Int32? _maximumPallets;
		private Int32? _minimumPallets;
		private Int32? _palletFootprint;
		private Int32? _palletStackHeight;
		private Int32? _pickSequence;
		private Int32? _width;
		private Item _item;
		private LicensePlate _licensePlate;
		private ICollection<Alert> _alerts = new HashSet<Alert>();
		private ICollection<CompanyGlobalProfile> _companyGlobalProfiles = new HashSet<CompanyGlobalProfile>();
		private ICollection<Dock> _docks = new HashSet<Dock>();
		private ICollection<InventoryItemDetail> _inventoryItemDetails = new HashSet<InventoryItemDetail>();
		private ICollection<InventoryPick> _inventoryPicks = new HashSet<InventoryPick>();
		private ICollection<InvoiceDetail> _invoiceDetails = new HashSet<InvoiceDetail>();
		private ICollection<Item> _items = new HashSet<Item>();
		private ICollection<ItemFulfillment> _itemFulfillments = new HashSet<ItemFulfillment>();
		private ICollection<LicensePlateLocation> _licensePlateLocations = new HashSet<LicensePlateLocation>();
		private ICollection<Location> _childLocations = new HashSet<Location>();
		private ICollection<LocationCustomer> _locationCustomers = new HashSet<LocationCustomer>();
		private ICollection<MaintenanceRequest> _maintenanceRequests = new HashSet<MaintenanceRequest>();
		private ICollection<PreReceiptCarton> _preReceiptCartons = new HashSet<PreReceiptCarton>();
		private ICollection<PreReceiptOverage> _preReceiptOverages = new HashSet<PreReceiptOverage>();
		private ICollection<ReceiptDetail> _receiptDetails = new HashSet<ReceiptDetail>();
		private ICollection<ShipmentHeader> _shipmentHeaders = new HashSet<ShipmentHeader>();
		private ICollection<UdfLocationValue> _udfLocationValues = new HashSet<UdfLocationValue>();
		private ICollection<WorkOrderHeader> _workOrderHeaders = new HashSet<WorkOrderHeader>();
		private Location _parentLocation;
		private LocationType _locationType;
		private OrganizationParticipant _custodian;
		private PickType _pickType;
		private Stratification _stratification;
		private String _active;
		private String _cycleCount;
		private String _description;
		private String _latitude;
		private String _locationCode;
		private String _locationDelimiterPosition;
		private String _lockCode;
		private String _lockReason;
		private String _longitude;
		private String _physicalAllowed;
		private String _pickBoxSize;
		private String _primaryPick;
		private String _replenishBy;
		private String _shareable;
		private UnitOfMeasure _dimensionUom;
		private UnitOfMeasure _replenishmentUom;
		private UnitOfMeasure _unitOfMeasure;

		#endregion

		#region Properties

		[DataMember]
		public virtual AgencyLocationType AgencyLocationType
		{
			get { return _agencyLocationType; }
			set { _agencyLocationType = value; }
		}

		[DataMember]
		public virtual AgencyOrganizationalUnit AgencyOrganizationalUnit
		{
			get { return _agencyOrganizationalUnit; }
			set { _agencyOrganizationalUnit = value; }
		}

		[DataMember]
		public virtual Aisle Aisle
		{
			get { return _aisle; }
			set { _aisle = value; }
		}

		[DataMember]
		public virtual Carrier Carrier
		{
			get { return _carrier; }
			set { _carrier = value; }
		}

		[DataMember]
		public virtual CompanyLocation CompanyLocation
		{
			get { return _companyLocation; }
			set { _companyLocation = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual CompanyLocationZone CompanyLocationZone
		{
			get { return _companyLocationZone; }
			set { _companyLocationZone = value; }
		}

		[DataMember]
		public virtual CustomerLocation CustomerLocation
		{
			get { return _customerLocation; }
			set { _customerLocation = value; }
		}

		[DataMember]
		public virtual DateTime? LastCycleCount
		{
			get { return _lastCycleCount; }
			set { _lastCycleCount = value; }
		}

		[DataMember]
		public virtual Decimal? MaximumQuantity
		{
			get { return _maximumQuantity; }
			set { _maximumQuantity = value; }
		}

		[DataMember]
		public virtual Decimal? MaximumWeight
		{
			get { return _maximumWeight; }
			set { _maximumWeight = value; }
		}

		[DataMember]
		public virtual Decimal? MinimumQuantity
		{
			get { return _minimumQuantity; }
			set { _minimumQuantity = value; }
		}

		[DataMember]
		public virtual Decimal? ReplenishmentQuantity
		{
			get { return _replenishmentQuantity; }
			set { _replenishmentQuantity = value; }
		}

		[DataMember]
		public virtual Decimal? SizeFactor
		{
			get { return _sizeFactor; }
			set { _sizeFactor = value; }
		}

		[DataMember]
		public virtual Dock Dock
		{
			get { return _dock; }
			set { _dock = value; }
		}

		[DataMember]
		public virtual Int32? Cube
		{
			get { return _cube; }
			set { _cube = value; }
		}

		[DataMember]
		public virtual Int32? Height
		{
			get { return _height; }
			set { _height = value; }
		}

		[DataMember]
		public virtual Int32? Length
		{
			get { return _length; }
			set { _length = value; }
		}

		[DataMember]
		public virtual Int32? MaximumPallets
		{
			get { return _maximumPallets; }
			set { _maximumPallets = value; }
		}

		[DataMember]
		public virtual Int32? MinimumPallets
		{
			get { return _minimumPallets; }
			set { _minimumPallets = value; }
		}

		[DataMember]
		public virtual Int32? PalletFootprint
		{
			get { return _palletFootprint; }
			set { _palletFootprint = value; }
		}

		[DataMember]
		public virtual Int32? PalletStackHeight
		{
			get { return _palletStackHeight; }
			set { _palletStackHeight = value; }
		}

		[DataMember]
		public virtual Int32? PickSequence
		{
			get { return _pickSequence; }
			set { _pickSequence = value; }
		}

		[DataMember]
		public virtual Int32? Width
		{
			get { return _width; }
			set { _width = value; }
		}

		[DataMember]
		public virtual Item Item
		{
			get { return _item; }
			set { _item = value; }
		}

		[DataMember]
		public virtual LicensePlate LicensePlate
		{
			get { return _licensePlate; }
			set { _licensePlate = value; }
		}

		[DataMember]
		public virtual ICollection<Alert> Alerts
		{
			get { return _alerts; }
			set { _alerts = value; }
		}

		[DataMember]
		public virtual ICollection<CompanyGlobalProfile> CompanyGlobalProfiles
		{
			get { return _companyGlobalProfiles; }
			set { _companyGlobalProfiles = value; }
		}

		[DataMember]
		public virtual ICollection<Dock> Docks
		{
			get { return _docks; }
			set { _docks = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryItemDetail> InventoryItemDetails
		{
			get { return _inventoryItemDetails; }
			set { _inventoryItemDetails = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryPick> InventoryPicks
		{
			get { return _inventoryPicks; }
			set { _inventoryPicks = value; }
		}

		[DataMember]
		public virtual ICollection<InvoiceDetail> InvoiceDetails
		{
			get { return _invoiceDetails; }
			set { _invoiceDetails = value; }
		}

		[DataMember]
		public virtual ICollection<Item> Items
		{
			get { return _items; }
			set { _items = value; }
		}

		[DataMember]
		public virtual ICollection<ItemFulfillment> ItemFulfillments
		{
			get { return _itemFulfillments; }
			set { _itemFulfillments = value; }
		}

		[DataMember]
		public virtual ICollection<LicensePlateLocation> LicensePlateLocations
		{
			get { return _licensePlateLocations; }
			set { _licensePlateLocations = value; }
		}

		[DataMember]
		public virtual ICollection<Location> ChildLocations
		{
			get { return _childLocations; }
			set { _childLocations = value; }
		}

		[DataMember]
		public virtual ICollection<LocationCustomer> LocationCustomers
		{
			get { return _locationCustomers; }
			set { _locationCustomers = value; }
		}

		[DataMember]
		public virtual ICollection<MaintenanceRequest> MaintenanceRequests
		{
			get { return _maintenanceRequests; }
			set { _maintenanceRequests = value; }
		}

		[DataMember]
		public virtual ICollection<PreReceiptCarton> PreReceiptCartons
		{
			get { return _preReceiptCartons; }
			set { _preReceiptCartons = value; }
		}

		[DataMember]
		public virtual ICollection<PreReceiptOverage> PreReceiptOverages
		{
			get { return _preReceiptOverages; }
			set { _preReceiptOverages = value; }
		}

		[DataMember]
		public virtual ICollection<ReceiptDetail> ReceiptDetails
		{
			get { return _receiptDetails; }
			set { _receiptDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ShipmentHeader> ShipmentHeaders
		{
			get { return _shipmentHeaders; }
			set { _shipmentHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<UdfLocationValue> UdfLocationValues
		{
			get { return _udfLocationValues; }
			set { _udfLocationValues = value; }
		}

		[DataMember]
		public virtual ICollection<WorkOrderHeader> WorkOrderHeaders
		{
			get { return _workOrderHeaders; }
			set { _workOrderHeaders = value; }
		}

		[DataMember]
		public virtual Location ParentLocation
		{
			get { return _parentLocation; }
			set { _parentLocation = value; }
		}

		[DataMember]
		public virtual LocationType LocationType
		{
			get { return _locationType; }
			set { _locationType = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant Custodian
		{
			get { return _custodian; }
			set { _custodian = value; }
		}

		[DataMember]
		public virtual PickType PickType
		{
			get { return _pickType; }
			set { _pickType = value; }
		}

		[DataMember]
		public virtual Stratification Stratification
		{
			get { return _stratification; }
			set { _stratification = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String CycleCount
		{
			get { return _cycleCount; }
			set { _cycleCount = value; }
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String Latitude
		{
			get { return _latitude; }
			set { _latitude = value; }
		}

		[DataMember]
		public virtual String LocationCode
		{
			get { return _locationCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("LocationCode must not be blank or null.");
				else _locationCode = value;
			}
		}

		[DataMember]
		public virtual String LocationDelimiterPosition
		{
			get { return _locationDelimiterPosition; }
			set { _locationDelimiterPosition = value; }
		}

		[DataMember]
		public virtual String LockCode
		{
			get { return _lockCode; }
			set { _lockCode = value; }
		}

		[DataMember]
		public virtual String LockReason
		{
			get { return _lockReason; }
			set { _lockReason = value; }
		}

		[DataMember]
		public virtual String Longitude
		{
			get { return _longitude; }
			set { _longitude = value; }
		}

		[DataMember]
		public virtual String PhysicalAllowed
		{
			get { return _physicalAllowed; }
			set { _physicalAllowed = value; }
		}

		[DataMember]
		public virtual String PickBoxSize
		{
			get { return _pickBoxSize; }
			set { _pickBoxSize = value; }
		}

		[DataMember]
		public virtual String PrimaryPick
		{
			get { return _primaryPick; }
			set { _primaryPick = value; }
		}

		[DataMember]
		public virtual String ReplenishBy
		{
			get { return _replenishBy; }
			set { _replenishBy = value; }
		}

		[DataMember]
		public virtual String Shareable
		{
			get { return _shareable; }
			set { _shareable = value; }
		}

		[DataMember]
		public virtual UnitOfMeasure DimensionUom
		{
			get { return _dimensionUom; }
			set { _dimensionUom = value; }
		}

		[DataMember]
		public virtual UnitOfMeasure ReplenishmentUom
		{
			get { return _replenishmentUom; }
			set { _replenishmentUom = value; }
		}

		[DataMember]
		public virtual UnitOfMeasure UnitOfMeasure
		{
			get { return _unitOfMeasure; }
			set { _unitOfMeasure = value; }
		}


		#endregion
	}
}
