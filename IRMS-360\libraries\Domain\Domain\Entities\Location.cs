using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;

using NHibernate;
using NHibernate.Criterion;
using NHibernate.SqlCommand;
using NHibernate.Transform;

using Upp.Irms.Constants;
using Upp.Irms.Core;
using Upp.Shared.Utilities;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class Location : Entity
	{
		#region Properties

		[DataMember]
		public virtual Boolean IsPallet { get; set; }
		[DataMember]
		public virtual Boolean IsSlotValid { get; set; }
		[DataMember]
		public virtual Boolean MaxPallet { get; set; }
		[DataMember]
		public virtual Decimal Quantity { get; set; }
		[DataMember]
		public virtual Int32 AvailableQuantity { get; set; }
		[DataMember]
		public virtual Int32 LpnCount { get; set; }
		[DataMember]
		public virtual Int32 PalletCount { get; set; }
		[DataMember]
		public virtual Int32 PendingQuantity { get; set; }
        [DataMember]
        public virtual Item DefaultItem { get; set; }
        [DataMember]
        public virtual LicensePlate DefaultLpn { get; set; }
		[DataMember]
		public virtual String AisleCode { get; set; }
		[DataMember]
		public virtual String FullCaseLocation { get; set; }
		[DataMember]
		public virtual String ItemCode { get; set; }
		[DataMember]
		public virtual String LicensePlateCode { get; set; }
		[DataMember]
		public virtual String LocationAisle { get; set; }
		[DataMember]
		public virtual String LocationBay { get; set; }
		[DataMember]
		public virtual String LocationLevel { get; set; }
		[DataMember]
		public virtual String LocationSlot { get; set; }
		[DataMember]
		public virtual String LocationTypeCode { get; set; }
		[DataMember]
		public virtual String PickTypeCode { get; set; }
		[DataMember]
		public virtual String PickTypeDescription { get; set; }
		[DataMember]
		public virtual String ReserveLocation { get; set; }
		[DataMember]
		public virtual String Slot { get; set; }
		[DataMember]
		public virtual String ZoneCode { get; set; }
		[DataMember]
		public virtual Task Task { get; set; }

		#endregion

        #region Report Properties

        [DataMember]
        public virtual String Arrow { get; set; }

        #endregion

		#region Constructor

		public Location()
		{
			//
		}

		#endregion

		#region Methods.Private.Replenish

        private Decimal AssignQuantity(decimal required)
        {
            if (_replenishmentUom == null) return required;
            //
            ProjectionList projections = Projections.ProjectionList()
                .Add(Projections.Property("Factor"), "Factor");
            DetachedCriteria criteria = DetachedCriteria.For<ItemUomRelationship>()
                .Add(Expression.Eq("Active", "A"))
                .Add(Expression.Eq("Item", _item))
                .Add(Expression.Eq("UnitOfMeasure", _replenishmentUom))
                .SetMaxResults(1)
                .SetProjection(projections)
                .SetResultTransformer(Transformers.AliasToBean<ItemUomRelationship>());
            ItemUomRelationship relationship = Repositories.Get<ItemUomRelationship>().Retrieve(criteria);
            if (relationship == null || relationship.Factor == 0) return required;
            //
            required = required - (required % relationship.Factor);
            //
            Decimal available = this.FindBulkQuantity();
            available += this.FindPrimeQuantity();
            //
            if (available >= required) return required;
            else if (available < relationship.Factor) return 0M;
            else return available - (available % relationship.Factor);
        }

		private Decimal AssignReplenishments(decimal required,
			int start,
			IList<InventoryItem> inventory,
			bool? consolidate,
			IList<InventoryTask> existing)
		{
			CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
			Priority high = Entity.Retrieve<Priority>(Priorities.High);
			StatusCode open = Entity.Retrieve<StatusCode>(TaskStatuses.Open);
			TransactionType replenishment = Entity.Retrieve<TransactionType>(InventoryTransactions.Replenishment);
			//
            Decimal factor = 1;
            ProjectionList projections = Projections.ProjectionList()
                .Add(Projections.Property("Factor"), "Factor");
            DetachedCriteria criteria = DetachedCriteria.For<ItemUomRelationship>()
                .Add(Expression.Eq("Active", "A"))
                .Add(Expression.Eq("Item", _item))
                .Add(Expression.Eq("UnitOfMeasure", _replenishmentUom))
                .SetMaxResults(1)
                .SetProjection(projections)
                .SetResultTransformer(Transformers.AliasToBean<ItemUomRelationship>());
            ItemUomRelationship relationship = Repositories.Get<ItemUomRelationship>().Retrieve(criteria);
            if (relationship != null) factor = relationship.Factor;
            //
			for (int i = start; i < inventory.Count; i++)
			{
				if (required <= 0) return 0;
				//
				if (consolidate.HasValue && consolidate.Value && existing != null)
				{
					InventoryTask match = existing
						.Where(t => t.LocationFrom.LocationCode.Equals(inventory[i].LocationCode))
						.Where(t => t.Quantity < inventory[i].Quantity)
						.FirstOrDefault();
					if (match != null)
					{
						Decimal increment = inventory[i].Quantity - match.Quantity;
						if (increment > required) increment = required;
						//
						match.DateModified = DateTime.Now;
						match.Quantity += Converter.ToInt32(increment);
						match.UserModified = Registry.Find<UserAccount>().UserName;
						Repositories.Get<InventoryTask>().Update(match);
						//
						required -= increment;
						//
						continue;
					}
				}
				//
				if (this.Task == null)
				{
					this.Task = Task.Create(replenishment);
					this.Task.StatusCode = open;
					Repositories.Get<Task>().Add(this.Task);
				}
				//
				Int32 quantity = (required > inventory[i].Quantity) ?
                    Converter.ToInt32(inventory[i].Quantity - (inventory[i].Quantity % factor)) :
					Converter.ToInt32(required);
				//
                if (quantity < factor) continue;
                //
				criteria = DetachedCriteria.For<Location>()
					.Add(Expression.Eq("CompanyLocationType", warehouse))
					.Add(Expression.Eq("LocationCode", inventory[i].LocationCode));
				Location from = Repositories.Get<Location>().Retrieve(criteria);
				//
				InventoryTask child = InventoryTask.Create(this.Task);
				child.CompanyLocationType = warehouse;
				child.CompanyLocationZoneFrom = from.CompanyLocationZone;
				child.CompanyLocationZoneTo = _companyLocationZone;
				child.Item = _item;
				child.LocationFrom = from;
				child.LocationTo = this;
				child.Priority = high;
				child.Quantity = quantity;
				child.StatusCode = open;
				child.Task = this.Task;
				Repositories.Get<InventoryTask>().Add(child);
				//
				required -= child.Quantity;
			}
			//
			return required;
		}

		private Decimal BulkReplenish(decimal required, bool? consolidate)
		{
			CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
			StatusCode available = Entity.Retrieve<StatusCode>(InventoryStatuses.Available);
			StatusCode open = Entity.Retrieve<StatusCode>(TaskStatuses.Open);
			TransactionType replenishment = Entity.Retrieve<TransactionType>(InventoryTransactions.Replenishment);
			//
			String order = BusinessRule.RetrieveString("8");
			if (String.IsNullOrEmpty(order)) order = "FIFO";
			//
			ProjectionList projections = Projections.ProjectionList()
				.Add(Projections.GroupProperty("l.LocationCode"), "LocationCode")
				.Add(Projections.Sum("Quantity"), "Quantity");
			if ("FIFO".Equals(order)) projections = projections.Add(Projections.Min("Received"), "Received");
			DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
				.CreateAlias("Location", "l")
				.CreateAlias("l.CompanyLocationZone", "clz", JoinType.LeftOuterJoin)
				.Add(Expression.Eq("CompanyLocationType", warehouse))
				.Add(Expression.Eq("Item", _item))
				.Add(Expression.Eq("StatusCode", available))
				.Add(Expression.Gt("Quantity", 0M))
				.Add(Expression.Eq("clz.ReplenishFrom", "Y"))
				.Add(Expression.IsNull("l.Item"))
				.Add(Expression.Not(Expression.Eq("l.PrimaryPick", "Y")))
				.SetProjection(projections)
				.SetResultTransformer(Transformers.AliasToBean<InventoryItem>());
			IList<InventoryItem> inventory = Repositories.Get<InventoryItem>().List(criteria);
			//
			if ("FIFO".Equals(order)) inventory = inventory.OrderBy(i => i.Received).ToList();
			else inventory = inventory.OrderBy(i => i.Quantity).ToList();
			for (int i = 0; i < inventory.Count; i++) inventory[i].Id = i;
			//
			IList<InventoryTask> existing = null;
			if (consolidate.HasValue && consolidate.Value)
			{
				LocationType mobile = Entity.Retrieve<LocationType>(InventoryLocationTypes.Mobile);
				criteria = DetachedCriteria.For<InventoryTask>()
				   .CreateAlias("LocationFrom", "lf")
				   .CreateAlias("Task", "t")
				   .Add(Expression.Eq("Item", _item))
				   .Add(Expression.Eq("LocationTo", this))
				   .Add(Expression.Eq("StatusCode", open))
				   .Add(Expression.Eq("t.TransactionType", replenishment))
				   .Add(Expression.Not(Expression.Eq("lf.LocationType", mobile)))
				   .SetFetchMode("LocationFrom", FetchMode.Join)
				   .SetFetchMode("Task", FetchMode.Join);
				existing = Repositories.Get<InventoryTask>().List(criteria);
			}
			//
			Int32 start = 0;
			if ("BF".Equals(order))
			{
				InventoryItem fit = inventory
					.Where(i => i.Quantity >= required)
					.FirstOrDefault();
				if (fit != null) start = fit.Id.Value;
			}
			//
			return this.AssignReplenishments(required, start, inventory, consolidate, existing);
		}

        private Decimal FindBulkQuantity()
        {
            CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
            StatusCode available = Entity.Retrieve<StatusCode>(InventoryStatuses.Available);
            //
            DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
                .CreateAlias("Location", "l")
                .CreateAlias("l.CompanyLocationZone", "clz", JoinType.LeftOuterJoin)
                .Add(Expression.Eq("CompanyLocationType", warehouse))
                .Add(Expression.Eq("Item", _item))
                .Add(Expression.Eq("StatusCode", available))
                .Add(Expression.Gt("Quantity", 0M))
                .Add(Expression.Eq("clz.ReplenishFrom", "Y"))
                .Add(Expression.IsNull("l.Item"))
                .Add(Expression.Not(Expression.Eq("l.PrimaryPick", "Y")))
                .SetProjection(Projections.Sum("Quantity"));
            //
            return Repositories.Get<InventoryItem>().Function<Decimal>(criteria);
        }

        private Decimal FindPrimeQuantity()
        {
            Boolean? primes = BusinessRule.RetrieveBoolean("5760");
            if (!primes.HasValue || !primes.Value) return 0;
            //
            CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
            StatusCode available = Entity.Retrieve<StatusCode>(InventoryStatuses.Available);
            //
            DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
                .CreateAlias("Location", "l")
                .CreateAlias("l.CompanyLocationZone", "clz", JoinType.LeftOuterJoin)
                .Add(Expression.Eq("CompanyLocationType", warehouse))
                .Add(Expression.Eq("Item", _item))
                .Add(Expression.Eq("StatusCode", available))
                .Add(Expression.Gt("Quantity", 0M))
                .Add(Expression.Eq("clz.ReplenishFrom", "Y"))
                .Add(Expression.IsNotNull("l.Item"))
                .Add(Expression.Not(Expression.Eq("Location", this)))
                .SetProjection(Projections.Sum("Quantity"));
            
            if (_pickType != null)
            {
                if (CodeValue.GetCode(PickTypes.Pallet).Equals(_pickType.PickTypeCode))
                    criteria = criteria.Add(Expression.Eq("l.PickType", _pickType));
                else if (CodeValue.GetCode(PickTypes.FullCase).Equals(_pickType.PickTypeCode))
                {
                    PickType each = Entity.Retrieve<PickType>(PickTypes.SplitCase);
                    criteria = criteria.AddOr("l.PickType", _pickType, "l.PickType", each);
                }
                else if (CodeValue.GetCode(PickTypes.SplitCase).Equals(_pickType.PickTypeCode))
                {
                    PickType box = Entity.Retrieve<PickType>(PickTypes.FullCase);
                    PickType pallet = Entity.Retrieve<PickType>(PickTypes.Pallet);
                    //
                    Disjunction ors = Restrictions.Disjunction();
                    ors.Add(Restrictions.Eq("l.PickType.Id", _pickType.Id));
                    ors.Add(Restrictions.Eq("l.PickType.Id", box.Id));
                    ors.Add(Restrictions.Eq("l.PickType.Id", pallet.Id));
                    //
                    criteria = criteria.Add(ors);
                }
            }
            //
            return Repositories.Get<InventoryItem>().Function<Decimal>(criteria);
        }

		private Decimal PickTypeReplenish(decimal required, PickType type, bool? consolidate)
		{
			CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
			StatusCode available = Entity.Retrieve<StatusCode>(InventoryStatuses.Available);
			StatusCode open = Entity.Retrieve<StatusCode>(TaskStatuses.Open);
			TransactionType replenishment = Entity.Retrieve<TransactionType>(InventoryTransactions.Replenishment);
			//
			ProjectionList projections = Projections.ProjectionList()
				.Add(Projections.GroupProperty("l.LocationCode"), "LocationCode")
				.Add(Projections.Sum("Quantity"), "Quantity");
			DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
				.CreateAlias("Location", "l")
				.CreateAlias("l.CompanyLocationZone", "clz", JoinType.LeftOuterJoin)
				.Add(Expression.Eq("CompanyLocationType", warehouse))
				.Add(Expression.Eq("Item", _item))
				.Add(Expression.Eq("StatusCode", available))
				.Add(Expression.Gt("Quantity", 0M))
				.Add(Expression.Eq("clz.ReplenishFrom", "Y"))
				.Add(Expression.IsNotNull("l.Item"))
				.Add(Expression.Not(Expression.Eq("Location", this)))
				.SetProjection(projections)
				.SetResultTransformer(Transformers.AliasToBean<InventoryItem>());
			if (type != null) criteria = criteria.Add(Expression.Eq("l.PickType", type));
			IList<InventoryItem> inventory = Repositories.Get<InventoryItem>().List(criteria);
			//
			IList<InventoryTask> existing = null;
			if (consolidate.HasValue && consolidate.Value)
			{
				criteria = DetachedCriteria.For<InventoryTask>()
				   .CreateAlias("LocationFrom", "lf")
				   .CreateAlias("Task", "t")
				   .Add(Expression.Eq("Item", _item))
				   .Add(Expression.Eq("LocationTo", this))
				   .Add(Expression.Eq("StatusCode", open))
				   .Add(Expression.Eq("t.TransactionType", replenishment))
				   .Add(Expression.Eq("t.CompanyLocationType", warehouse))
				   .Add(Expression.IsNotNull("lf.Item"))
				   .Add(Expression.Not(Expression.Eq("LocationFrom", this)))
				   .SetFetchMode("LocationFrom", FetchMode.Join)
				   .SetFetchMode("Task", FetchMode.Join);
				if (type != null) criteria = criteria.Add(Expression.Eq("lf.PickType", type));
				existing = Repositories.Get<InventoryTask>().List(criteria);
			}
			//
			return this.AssignReplenishments(required, 0, inventory, consolidate, existing); ;
		}

		private Decimal PrimeReplenish(decimal required, bool? consolidate)
		{
			/* Use other primes for replenishment? */
			Boolean? primes = BusinessRule.RetrieveBoolean("5760");
			if (!primes.HasValue || !primes.Value) return required;
			//
			if (_pickType == null) required = this.PickTypeReplenish(required, null, consolidate);
			else if (CodeValue.GetCode(PickTypes.Pallet).Equals(_pickType.PickTypeCode))
				required = this.PickTypeReplenish(required, _pickType, consolidate);
			else if (CodeValue.GetCode(PickTypes.FullCase).Equals(_pickType.PickTypeCode))
			{
				required = this.PickTypeReplenish(required, _pickType, consolidate);
				if (required > 0)
				{
					PickType each = Entity.Retrieve<PickType>(PickTypes.SplitCase);
					required = this.PickTypeReplenish(required, each, consolidate);
				}
			}
			else if (CodeValue.GetCode(PickTypes.SplitCase).Equals(_pickType.PickTypeCode))
			{
				required = this.PickTypeReplenish(required, _pickType, consolidate);
				if (required > 0)
				{
					PickType box = Entity.Retrieve<PickType>(PickTypes.FullCase);
					required = this.PickTypeReplenish(required, box, consolidate);
				}
				//
				if (required > 0)
				{
					PickType pallet = Entity.Retrieve<PickType>(PickTypes.Pallet);
					required = this.PickTypeReplenish(required, pallet, consolidate);
				}
			}
			//
			return required;
		}

		private Decimal SubstituteReplenish(decimal required, bool? consolidate)
		{
            Item coreItem = _item;
            decimal requiredSubtitues = required;
			DetachedCriteria criteria = DetachedCriteria.For<ItemUomRelationship>()
				.Add(Expression.Eq("Active", "A"))
				.Add(Expression.Eq("Item", _item));
			if (this.ReplenishmentUom != null) criteria = criteria.Add(Expression.Eq("UnitOfMeasure", this.ReplenishmentUom));
			IList<ItemUomRelationship> relationships = Repositories.Get<ItemUomRelationship>().List(criteria);
			//
			if (relationships.Count == 0) return required;
			else if (relationships.Count == 1)
			{
				criteria = DetachedCriteria.For<ItemSubstitute>()
					.Add(Expression.Eq("Active", "A"))
					.Add(Expression.Eq("ItemUomRelationship", relationships[0]))
					.AddOrder(Order.Asc("ItemSequence"))
					.SetFetchMode("Item", FetchMode.Eager);
				IList<ItemSubstitute> substitutes = Repositories.Get<ItemSubstitute>().List(criteria);
				//
				requiredSubtitues = Round.Down(required / relationships[0].Factor);
				for (int i = 0; i < substitutes.Count; i++)
				{
					if (requiredSubtitues <= 0) break;
					//
					_item = substitutes[i].Item;
					//
					requiredSubtitues = this.BulkReplenish(requiredSubtitues, consolidate);
					if (requiredSubtitues > 0) this.PrimeReplenish(requiredSubtitues, consolidate);
				}
			}
			else
			{
				criteria = DetachedCriteria.For<ItemSubstitute>()
					.CreateAlias("ItemUomRelationship", "iur")
					.Add(Expression.Eq("Active", "A"))
					.Add(Expression.Eq("iur.Active", "A"))
					.Add(Expression.Eq("iur.Item", _item))
					.AddOrder(Order.Asc("ItemSequence"))
					.SetFetchMode("Item", FetchMode.Join);
				IList<ItemSubstitute> substitutes = Repositories.Get<ItemSubstitute>().List(criteria);
				//
				if (substitutes.Count == 0) return required;
				foreach (ItemSubstitute element in substitutes)
				{
					ProjectionList projections = Projections.ProjectionList()
						.Add(Projections.Property("Received"), "Received");
					criteria = DetachedCriteria.For<InventoryItem>()
						.Add(Expression.Eq("Item", element.Item))
						.Add(Expression.Gt("Quantity", 0M))
						.AddOrder(Order.Asc("Received"))
						.SetMaxResults(1)
						.SetProjection(projections)
						.SetResultTransformer(Transformers.AliasToBean<InventoryItem>());
					InventoryItem inventory = Repositories.Get<InventoryItem>().Retrieve(criteria);
					if (inventory != null) element.DateModified = inventory.Received;
				}
				//
				substitutes = substitutes
					.OrderBy(c => c.ItemSequence)
					.ThenBy(c => c.DateModified)
					.ToList<ItemSubstitute>();
				for (int i = 0; i < substitutes.Count; i++)
				{
                    if (substitutes[i].ItemUomRelationship != null && substitutes[i].ItemUomRelationship.Factor > 0)
                    {
                        requiredSubtitues = Round.Down(required / substitutes[i].ItemUomRelationship.Factor);
                        if (requiredSubtitues > 0)
                        {
                            _item = substitutes[i].Item;
                            //
                            requiredSubtitues = this.BulkReplenish(requiredSubtitues, consolidate);
                            if (requiredSubtitues > 0) requiredSubtitues = this.PrimeReplenish(requiredSubtitues, consolidate);
                            //
                            if (requiredSubtitues > 0) required = Round.Down(requiredSubtitues * substitutes[i].ItemUomRelationship.Factor);
                        }
                        else break;
                    }
                    else continue;
				}
			}
			//
            _item = coreItem;
			return required;
		}

		#endregion

		#region Methods.Public

		public virtual void CalculateAvailableQuantity(string itemCode)
		{
			this.CalculateQuantity(itemCode, CodeValue.GetCode(InventoryStatuses.Available));
		}

		public virtual void CalculatePalletQuantity()
		{
			DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
				.CreateAlias("LicensePlate", "lp")
				.CreateAlias("lp.ParentLicensePlate", "plp")
				.Add("CompanyLocationType", Registry.Find<CompanyLocationType>())
				.Add("Location", this)
				.Add("Quantity", ">", 0M)
				.SetProjection(Projections.Count(Projections.Distinct(Projections.Property("plp.Id"))));
			this.PalletCount = Repositories.Get<LicensePlate>().Function<Int32>(criteria);
		}

		public virtual void CalculatePendingQuantity(Item item)
		{
			StatusCode open = Entity.Retrieve<StatusCode>(TaskStatuses.Open);
			TransactionType replenishment = Entity.Retrieve<TransactionType>(InventoryTransactions.Replenishment);
			//
			Company company = Registry.Find<Company>();
			CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
			DetachedCriteria criteria = DetachedCriteria.For<InventoryTask>()
				.Add("Item", item)
				.Add("LocationTo", this)
				.Add("StatusCode", open)
				.Add("Task.TransactionType", replenishment)
				.SetProjection(Projections.Sum("Quantity"));
			if (warehouse != null) criteria = criteria.Add("CompanyLocationType", warehouse);
			else if (company != null) criteria = criteria.Add("CompanyLocationType.CompanyLocationCompany", company);
			this.PendingQuantity = Repositories.Get<InventoryTask>().Function<Int32>(criteria);
		}

		public virtual void CalculateQuantity(string itemCode, string statusCode)
		{
			DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
				.Add("CompanyLocationType", Registry.Find<CompanyLocationType>())
				.Add("Item.ItemCode", itemCode)
				.Add("Location", this)
				.Add("Quantity", ">", 0M)
				.Add("StatusCode.Code", statusCode)
				.SetProjection(Projections.Sum("Quantity"));
			this.Quantity = Repositories.Get<InventoryItem>().Function<Decimal>(criteria);
		}

		public virtual void CalculateQuantity(string itemCode, LicensePlate plate, string statusCode)
		{
			DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
				.Add("CompanyLocationType", Registry.Find<CompanyLocationType>())
				.Add("Item.ItemCode", itemCode)
				.Add("Location", this)
				.Add("Quantity", ">", 0M)
				.SetProjection(Projections.Sum("Quantity"));
			if (plate != null) criteria = criteria
				.Add("LicensePlate.ParentLicensePlate", plate);
			if (String.IsNullOrEmpty(statusCode)) criteria = criteria
				.Add("StatusCode", Entity.Retrieve<StatusCode>(InventoryStatuses.Available));
			this.Quantity = Repositories.Get<InventoryItem>().Function<Decimal>(criteria);
		}

		public virtual void ChangePrimaryLocation(Location from)
		{
			from = Repositories.Get<Location>().Retrieve(from.Id);
			//
			StatusCode distributed = Entity.Retrieve<StatusCode>(OrderStatuses.Distributed);
			DetachedCriteria criteria = DetachedCriteria.For<ItemFulfillment>()
				.Add("Item", from.Item)
				.Add("Location", from)
				.Add("StatusCode", distributed);
			IList<ItemFulfillment> picks = Repositories.Get<ItemFulfillment>().List(criteria);
			foreach (ItemFulfillment element in picks)
			{
				element.DateModified = DateTime.Now;
				element.Location = this;
				element.UserModified = Registry.Find<UserAccount>().UserName;
				Repositories.Get<ItemFulfillment>().Update(element);
			}
			//
			StatusCode open = Entity.Retrieve<StatusCode>(TaskStatuses.Open);
			TransactionType replenishment = Entity.Retrieve<TransactionType>(InventoryTransactions.Replenishment);
			Company company = Registry.Find<Company>();
			CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
			criteria = DetachedCriteria.For<InventoryTask>()
				.Add("Item", from.Item)
				.Add("LocationTo", from)
				.Add("StatusCode", open)
				.Add("Task.TransactionType", replenishment);
			if (warehouse != null) criteria = criteria.Add("CompanyLocationType", warehouse);
			else if (company != null) criteria = criteria.Add("CompanyLocationType.CompanyLocationCompany", company);
			IList<InventoryTask> tasks = Repositories.Get<InventoryTask>().List(criteria);
			foreach (InventoryTask element in tasks)
			{
				element.DateModified = DateTime.Now;
				element.LocationTo = this;
				element.UserModified = Registry.Find<UserAccount>().UserName;
				Repositories.Get<InventoryTask>().Update(element);
			}
			//
			_dateModified = DateTime.Now;
			_item = from.Item;
			_maximumQuantity = from.MaximumQuantity;
			_minimumQuantity = from.MinimumQuantity;
			_pickType = from.PickType;
			_primaryPick = "Y";
			_replenishmentQuantity = from.ReplenishmentQuantity;
			_replenishmentUom = from.ReplenishmentUom;
			_userModified = Registry.Find<UserAccount>().UserName;
			Repositories.Get<Location>().Update(this);
			//
			from.DateModified = DateTime.Now;
			from.Item = null;
			from.MaximumQuantity = null;
			from.MinimumQuantity = null;
			from.PickType = null;
			from.PrimaryPick = "N";
			from.ReplenishmentQuantity = null;
			from.ReplenishmentUom = null;
			from.UserModified = Registry.Find<UserAccount>().UserName;
			Repositories.Get<Location>().Update(from);
		}

        public virtual void CheckInventory()
        {
            DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()                
                .Add("CompanyLocationType", Registry.Find<CompanyLocationType>())
                .Add("Location", this)
                .Add("Quantity", "!=", 0M)
                .Add("StatusCode.Code", "!=", CodeValue.GetCode(InventoryStatuses.Unavailable));
            IList<InventoryItem> stock = Repositories.Get<InventoryItem>().List(criteria);
            //
            if (stock != null && stock.Count == 1)
            {
                this.DefaultItem = stock[0].Item;
                if (stock[0].LicensePlate != null && stock[0].LicensePlate.ParentLicensePlate != null)
                    this.DefaultLpn = stock[0].LicensePlate.ParentLicensePlate;
            }
        }

		public virtual void CheckIsPallet()
		{
			LicenseType pallet = Entity.Retrieve<LicenseType>(InventoryLicenseTypes.Pallet);
			DetachedCriteria criteria = DetachedCriteria.For<LocationLicenseType>()
				.Add(Expression.Eq("Active", "A"))
				.Add(Expression.Eq("LicenseType", pallet))
				.Add(Expression.Eq("LocationType", _locationType));
			this.IsPallet = (Repositories.Get<LocationLicenseType>().Count(criteria) > 0);
		}

		public virtual void CheckMaxPallet(LicensePlate plate, LicenseType licensetype)
		{
			/*LicenseType pallet = Entity.Retrieve<LicenseType>(InventoryLicenseTypes.Pallet);
			if (!pallet.SameAs(licensetype)) return;

			DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
								.CreateAlias("LicensePlate", "lp")
								.CreateAlias("lp.ParentLicensePlate", "plp")
								.Add("Location", this)
								.Add("lp.ParentLicensePlate", "!=", plate)
								.SetProjection(Projections.Count(Projections.Distinct(Projections.Property("plp.Id"))));
			Int32 LpnCount = Repositories.Get<InventoryItem>().Function<Int32>(criteria);
			//
			this.MaxPallet = (_maximumPallets.HasValue && _maximumPallets.Value < LpnCount + 1);*/
		}

		public virtual void CheckIsSlotValid(String slot)
		{
			DetachedCriteria criteria = DetachedCriteria.For<LicensePlateLocation>()
				.Add("Location.CompanyLocationType", Registry.Find<CompanyLocationType>())
				.Add(Expression.Eq("SlotNumber", slot));
			this.IsSlotValid = (Repositories.Get<LicensePlateLocation>().Count(criteria) > 0);
		}

		public virtual void ClearTruck()
		{
			DetachedCriteria criteria = DetachedCriteria.For<LicensePlateLocation>()
				   .Add(Expression.Eq("Location", this));
			if (String.IsNullOrEmpty(this.Slot)) criteria = criteria.Add(Expression.IsNotNull("SlotNumber"));
			else criteria = criteria.Add(Expression.Eq("SlotNumber", this.Slot));
			IList<LicensePlateLocation> locations = Repositories.Get<LicensePlateLocation>().List(criteria);
			//
			foreach (LicensePlateLocation element in locations)
				Repositories.Get<LicensePlateLocation>().Remove(element);
		}

		public virtual void ReplenishInventory(Item item, Location location, LicensePlate plate, string serialNumbers, bool release = false)
		{
			TransactionType type = release ?
				Entity.Retrieve<TransactionType>(InventoryTransactions.ReleaseReplenishment) :
				Entity.Retrieve<TransactionType>(InventoryTransactions.Replenishment);
			String[] numbers = serialNumbers.Split('|');
			for (int i = 0; i < numbers.Length; i++)
			{
				DetachedCriteria criteria = DetachedCriteria.For<InventoryItemDetail>()
					.Add("InventoryItem.Item", item)
					.Add("SerialNumber", numbers[i])
					.SetMaxResults(1);
				InventoryItemDetail detail = Repositories.Get<InventoryItemDetail>().Retrieve(criteria);
				if (detail != null)
				{
					detail.Move(location, plate);
					detail.InventoryItem.WriteStockMovement(type, location, plate, 1, numbers[i]);
				}
			}
		}

		public virtual void SetCycleflag()
		{
            StatusCode unavailable = Entity.Retrieve<StatusCode>(InventoryStatuses.Unavailable);
			DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
				.CreateAlias("StatusCode", "StatusCode")
				.Add(Expression.Eq("Location", this))
                .Add(Expression.Not(Expression.Eq("CycleCount", "N")))
                .Add(Expression.Not(Expression.Eq("StatusCode", unavailable)))
                .SetProjection(Projections.Count("Id"));
            Int32 count = Repositories.Get<InventoryTask>().Function<Int32>(criteria);
			if (count == 0)
			{
				_cycleCount = "N";
				_dateModified = DateTime.Now;
				_lastCycleCount = DateTime.Now;
				_userModified = Registry.Find<UserAccount>().UserName;
				//
				Repositories.Get<Location>().Update(this);
			}
		}

		public virtual void SetPrimarypick(bool primaryPick)
		{
			_primaryPick = primaryPick ? "Y" : "N";
			_dateModified = DateTime.Now;
			_userModified = Registry.Find<UserAccount>().UserName;
			//
			Repositories.Get<Location>().Update(this);
		}

		public virtual void ValidateActive()
		{
			if (!"A".Equals(_active))
			{
				String error = String.Format("Location {0} is not active.", _locationCode);
				throw new Exception(error);
			}
		}

		public virtual void ValidateInventory()
		{
			String inventory = CodeValue.GetCode(FunctionalAreas.Inventory);
			if (!inventory.Equals(_locationType.FunctionalAreaCode.Code))
			{
				String error = String.Format("Location {0} is an inventory location.", _locationCode);
				throw new Exception(error);
			}
		}

		#endregion

		#region Methods.Public.CycleCount

		public virtual void ClearCycleCount()
		{
			_cycleCount = "N";
			_dateModified = DateTime.Now;
			_lastCycleCount = DateTime.Now;
			_userModified = Registry.Find<UserAccount>().UserName;
			Repositories.Get<Location>().Update(this);
		}

		public virtual void CreateCycleCount()
		{
			TransactionType cycle = Entity.Retrieve<TransactionType>(InventoryTransactions.CycleCount);
			this.Task = Task.Create(cycle);
			if (Registry.Find<CompanyLocationType>() == null)
			{
				this.Task.Started = DateTime.Now;
				this.Task.StatusCode = Entity.Retrieve<StatusCode>(TaskStatuses.Active);
				this.Task.TransactionType = Entity.Retrieve<TransactionType>(InventoryTransactions.CycleCountLocation);
			}
			Repositories.Get<Task>().Add(this.Task);
			//
			if (Registry.Find<CompanyLocationType>() == null)
			{
				InventoryTask main = InventoryTask.Create(this.Task);
				main.LocationFrom = this;
				main.Quantity = 0;
				Repositories.Get<InventoryTask>().Add(main);
			}
			//
			DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
                .Add(Expression.Or(Expression.IsNull("CycleCount"), Expression.Not(Expression.Eq("CycleCount", "Y"))))
				.Add("Location", this)
				.Add("Quantity", ">", 0M);
			if (Registry.Find<CompanyLocationType>() == null)
				criteria = criteria.Add("StatusCode", Entity.Retrieve<StatusCode>(InventoryStatuses.Available));
			IList<InventoryItem> items = Repositories.Get<InventoryItem>().List(criteria);
			foreach (InventoryItem element in items)
			{
				element.CycleCount = "Y";
				element.DateModified = DateTime.Now;
				element.UserModified = Registry.Find<UserAccount>().UserName;
				Repositories.Get<InventoryItem>().Update(element);
				//
				InventoryTask count = InventoryTask.Create(this.Task);
				count.CompanyLocationZoneFrom = element.Item.CompanyLocationZone;
				count.InventoryItem = element;
				count.Item = element.Item;
				if (element.LicensePlate != null) count.LicensePlate = element.LicensePlate.ParentLicensePlate;
				count.LocationFrom = this;
				count.LotNumber = element.LotNumber;
				count.Quantity = Converter.ToInt32(element.Quantity);
				//
				Repositories.Get<InventoryTask>().Add(count);
			}
			//
			this.SetCycleCount();
		}

		public virtual void CreateCycleCount(Item item, string lot)
		{
			CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
			StatusCode complete = Entity.Retrieve<StatusCode>(TaskStatuses.Complete);
			StatusCode open = Entity.Retrieve<StatusCode>(TaskStatuses.Open);
			TransactionType count = Entity.Retrieve<TransactionType>(InventoryTransactions.CycleCount);
			//
			DetachedCriteria criteria = DetachedCriteria.For<InventoryTask>()
				.CreateAlias("Task", "t")
				.Add(Expression.Eq("Item", item))
				.Add(Expression.Eq("LocationFrom", this))
				.Add(Expression.Eq("t.TransactionType", count))
				.Add(Expression.Not(Expression.Eq("StatusCode", complete)))
				.SetProjection(Projections.Count("Id"));
			Int32 existing = Repositories.Get<InventoryTask>().Function<Int32>(criteria);
			if (existing > 0) return;
			//
			Task created = Task.Create(count);
			created.Requested = DateTime.Now;
			created.StatusCode = open;
			Repositories.Get<Task>().Add(created);
			//
			InventoryTask main = InventoryTask.Create(created);
			main.CompanyLocationType = warehouse;
			main.CompanyLocationZoneFrom = item.CompanyLocationZone;
			main.Item = item;
			main.LocationFrom = this;
			main.LotNumber = lot;
			main.Quantity = 0;
			main.StatusCode = open;
			Repositories.Get<InventoryTask>().Add(main);
			//
			criteria = DetachedCriteria.For<InventoryItem>()
				.Add(Expression.Or(Expression.IsNull("CycleCount"), Expression.Not(Expression.Eq("CycleCount", "Y"))))
				.Add(Expression.Eq("Item", item))
				.Add(Expression.Eq("Location", this))
				.Add(Expression.Gt("Quantity", 0M));
			if (!String.IsNullOrEmpty(lot)) criteria = criteria
				.Add(Expression.Eq("LotNumber", lot));
			IList<InventoryItem> stock = Repositories.Get<InventoryItem>().List(criteria);
			foreach (InventoryItem element in stock)
			{
				element.CycleCount = "Y";
				element.DateModified = DateTime.Now;
				element.UserModified = Registry.Find<UserAccount>().UserName;
				Repositories.Get<InventoryItem>().Update(element);
				//
				InventoryTask child = InventoryTask.Create(created);
				child.CompanyLocationType = warehouse;
				child.CompanyLocationZoneFrom = item.CompanyLocationZone;
				child.InventoryItem = element;
				child.Item = item;
				/*child.LicensePlate = element.LicensePlate.ParentLicensePlate;*/
				child.LocationFrom = this;
				child.LotNumber = lot;
				child.Quantity = Converter.ToInt32(element.Quantity);
				child.StatusCode = open;
				Repositories.Get<InventoryTask>().Add(child);
			}
		}

		public virtual void FinishCycleCount() // <-- this is for IRMS|wm solely.
		{
			DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
				.Add(Expression.Eq("CycleCount", "Y"))
				.Add(Expression.Eq("Location", this))
				.Add(Expression.Gt("Quantity", 0M))
				.SetProjection(Projections.Count("Id"));
			Int32 count = Repositories.Get<InventoryItem>().Function<Int32>(criteria);
			if (count == 0) this.ClearCycleCount();
		}

		public virtual void SetCycleCount()
		{
			_cycleCount = "Y";
			_dateModified = DateTime.Now;
			_lastCycleCount = null;
			_userModified = Registry.Find<UserAccount>().UserName;
			Repositories.Get<Location>().Update(this);
		}

		#endregion

		#region Methods.Public.Inventory

		public virtual void DecrementReplenishments(Item item, StatusCode status)
		{
			if (!item.Equals(_item) || !_maximumQuantity.HasValue) return;
			//
			LocationType mobile = Entity.Retrieve<LocationType>(InventoryLocationTypes.Mobile);
			StatusCode complete = Entity.Retrieve<StatusCode>(TaskStatuses.Complete);
			TransactionType replenish = Entity.Retrieve<TransactionType>(InventoryTransactions.Replenishment);
			//
			if (status == null) status = Entity.Retrieve<StatusCode>(InventoryStatuses.Available);
			DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
			   .Add(Expression.Eq("Item", item))
			   .Add(Expression.Eq("Location", this))
			   .Add(Expression.Eq("StatusCode", status))
			   .SetProjection(Projections.Sum("Quantity"));
			Decimal stock = Repositories.Get<InventoryItem>().Function<Decimal>(criteria);
			//
			Decimal missing = _maximumQuantity.Value - stock;
			criteria = DetachedCriteria.For<ItemUomRelationship>()
				.Add(Expression.Eq("Item", item))
				.Add(Expression.Eq("UnitOfMeasure", _replenishmentUom))
				.SetMaxResults(1);
			ItemUomRelationship relationship = Repositories.Get<ItemUomRelationship>().Retrieve(criteria);
			if (relationship != null && relationship.Factor > 1) missing -= missing % relationship.Factor;
			//
			criteria = DetachedCriteria.For<InventoryTask>()
				.CreateAlias("LocationFrom", "lf")
				.Add(Expression.Eq("Item", item))
				.Add(Expression.Eq("LocationTo", this))
				.Add(Expression.Eq("TransactionType", replenish))
				.Add(Expression.Not(Expression.Eq("lf.LocationType", mobile)))
				.Add(Expression.Not(Expression.Eq("StatusCode", complete)))
				.AddOrder(Order.Asc("Quantity"));
			IList<InventoryTask> replenishments = Repositories.Get<InventoryTask>().List(criteria);
			foreach (InventoryTask element in replenishments)
			{
				if (missing >= element.Quantity) missing -= element.Quantity;
				else if (missing > 0)
				{
					element.ChangeQuantity(Converter.ToInt32(missing));
					missing = 0;
				}
				else element.ChangeStatus(complete);
			}
		}

		public virtual void MoveAllocations(Item item, Location to, StatusCode status)
		{
			StatusCode open = Entity.Retrieve<StatusCode>(Upp.Irms.Constants.OrderStatuses.Distributed);
			if (status == null) status = Entity.Retrieve<StatusCode>(Upp.Irms.Constants.InventoryStatuses.Available);
			//
			DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
				.Add(Expression.Eq("Item", item))
				.Add(Expression.Eq("Location", this))
				.Add(Expression.Eq("StatusCode", status))
				.SetProjection(Projections.Sum("Quantity"));
			Decimal stock = Repositories.Get<InventoryItem>().Function<Decimal>(criteria);

			criteria = DetachedCriteria.For<ItemFulfillment>()
				.Add(Expression.Eq("Item", item))
				.Add(Expression.Eq("Location", this))
				.Add(Expression.Eq("StatusCode", open))
				.SetProjection(Projections.Sum("Quantity"));
			Int32 demand = Repositories.Get<ItemFulfillment>().Function<Int32>(criteria);
			//
			if (stock >= demand) return;
			//
			Decimal quantity = demand - stock;
			criteria = DetachedCriteria.For<ItemFulfillment>()
				.Add(Expression.Eq("Item", item))
				.Add(Expression.Eq("Location", this))
				.Add(Expression.Eq("StatusCode", open));
			IList<ItemFulfillment> allocations = Repositories.Get<ItemFulfillment>().List(criteria);
			foreach (ItemFulfillment element in allocations)
			{
				if (quantity <= 0) return;
				//
				if (quantity >= element.Quantity)
				{
					element.ChangeLocation(to);
					//
					quantity -= element.Quantity;
				}
				else
				{
					ItemFulfillment remaining = Entity.Clone<ItemFulfillment>(element);
					remaining.Quantity = Converter.ToInt32(element.Quantity - quantity);
					Repositories.Get<ItemFulfillment>().Add(remaining);
					//
					element.Quantity = Converter.ToInt32(quantity);
					element.ChangeLocation(to);
					//
					return;
				}
			}
		}

		#endregion

		#region Methods.Public.PhysicalInventory

		public virtual void PerformPhysicalCount()
		{
			this.CycleCount = "N";
			//
			CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
			StatusCode complete = Entity.Retrieve<StatusCode>(TaskStatuses.Complete);
			TransactionType physical = Entity.Retrieve<TransactionType>(InventoryTransactions.Physical);

			ProjectionList projections = Projections.ProjectionList()
				   .Add(Projections.Property("Task"), "Task");

			DetachedCriteria criteria = DetachedCriteria.For<InventoryTask>()
										.Add("CompanyLocationType", warehouse)
										.Add("Task.StatusCode", "!=", complete)
										.Add("Task.TransactionType", physical)
										.SetProjection(projections)
										.SetResultTransformer(Transformers.AliasToBean<InventoryTask>())
										.SetMaxResults(1);
			this.Task = Repositories.Get<InventoryTask>().Retrieve(criteria).Task;
			//
			InventoryTask main = InventoryTask.Create(this.Task);
			main.InventoryItem = null;
			main.Quantity = 0;
			Repositories.Get<InventoryTask>().Add(main);
			//
			WritePhyscialInventory(main);

		}
		public virtual void WritePhyscialInventory(InventoryTask task = null)
		{
			TransactionType type = Entity.Retrieve<TransactionType>(InventoryTransactions.PhysicalCount);
			//
			ItemTransaction transaction = Entity.Activate<ItemTransaction>();
			//
			transaction.InventoryItem = null;
			transaction.Item = null;
			transaction.LocationFrom = this;
			transaction.Occurred = DateTime.Now;
			transaction.ParticipantBy = Registry.Find<UserAccount>().OrganizationParticipant;
			transaction.Quantity = 0;
			transaction.QuantityFrom = 0;
			transaction.TransactionType = type;
			transaction.IntegrationStatusCode = Entity.Retrieve<StatusCode>(IntegrationStatuses.Open);
			if (task != null) transaction.Task = task.Task;
			//           
			Repositories.Get<ItemTransaction>().Add(transaction);
		}

		#endregion

		#region Methods.Public.Replenish

		public virtual Boolean Replenish()
		{
			if (_item == null ||
				!_maximumQuantity.HasValue ||
				!_minimumQuantity.HasValue) return false;
			//
			StatusCode available = Entity.Retrieve<StatusCode>(InventoryStatuses.Available);
			StatusCode complete = Entity.Retrieve<StatusCode>(TaskStatuses.Complete);
			TransactionType replenishment = Entity.Retrieve<TransactionType>(InventoryTransactions.Replenishment);
			//
			DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
				.Add(Expression.Eq("Item", _item))
				.Add(Expression.Eq("Location", this))
				.Add(Expression.Eq("StatusCode", available))
				.SetProjection(Projections.Sum("Quantity"));
			Decimal current = Repositories.Get<InventoryItem>().Function<Decimal>(criteria);
			//
			criteria = DetachedCriteria.For<InventoryTask>()
				.CreateAlias("Task", "t")
				.Add(Expression.Eq("Item", _item))
				.Add(Expression.Eq("LocationTo", this))
				.Add(Expression.Eq("t.TransactionType", replenishment))
				.Add(Expression.Not(Expression.Eq("StatusCode", complete)))
				.SetProjection(Projections.Sum("Quantity"));
			Int32 scheduled = Repositories.Get<InventoryTask>().Function<Int32>(criteria);
			//
			String demand = BusinessRule.RetrieveString("2010");
			if (String.IsNullOrEmpty(demand)) return false;
			//
			Decimal allocation = 0;
			if ("D".Equals(demand))
			{
				StatusCode distributed = Entity.Retrieve<StatusCode>(OrderStatuses.Distributed);
				criteria = DetachedCriteria.For<ItemFulfillment>()
					.Add(Expression.Eq("Item", _item))
					.Add(Expression.Eq("Location", this))
					.Add(Expression.Eq("StatusCode", distributed))
					.SetProjection(Projections.Sum("Quantity"));
				allocation = Repositories.Get<ItemFulfillment>().Function<Int32>(criteria);
			}
			//
			if (_minimumQuantity < (current + scheduled) - allocation) return false;
			//
			Boolean? consolidate = BusinessRule.RetrieveBoolean("11057");
			Decimal required = (_maximumQuantity - ((current + scheduled) - allocation)).Value;
			if (required <= 0) return false;
			//
			Boolean? replenishByXItems = BusinessRule.RetrieveBoolean("11120");
			if (replenishByXItems.HasValue && replenishByXItems.Value)
			{
				if ("SAME".Equals(this.ReplenishBy))
				{
                    required = this.AssignQuantity(required);
                    if (required <= 0) return false;
                    //
                    required = this.BulkReplenish(required, consolidate);
					if (required > 0) this.PrimeReplenish(required, consolidate);
				}
				else if ("SUB".Equals(this.ReplenishBy))
				{
					this.SubstituteReplenish(required, consolidate);
				}
				else if ("BOTH".Equals(this.ReplenishBy))
				{
                    required = this.AssignQuantity(required);
                    if (required <= 0) return false;
                    //
                    required = this.BulkReplenish(required, consolidate);
					if (required > 0) required = this.PrimeReplenish(required, consolidate);
					if (required > 0) this.SubstituteReplenish(required, consolidate);
				}
			}
			else
			{
                required = this.AssignQuantity(required);
                if (required <= 0) return false;
                //
                required = this.BulkReplenish(required, consolidate);
				if (required > 0) this.PrimeReplenish(required, consolidate);
			}
			//
			Boolean? message = BusinessRule.RetrieveBoolean("11056");
			return message.HasValue ? message.Value : false;
		}

		public virtual void Replenish(Item item, Location location, LicensePlate plate, decimal quantity, bool replenish = false)
		{
			TransactionType type = replenish ?
				Entity.Retrieve<TransactionType>(InventoryTransactions.Replenishment) :
				Entity.Retrieve<TransactionType>(StockAdjustmentTransactions.StockMovement);
			DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
				.Add(Expression.Eq("Item", item))
				.Add(Expression.Eq("Location", this))
				.Add(Expression.Gt("Quantity", 0M))
				.AddOrder(Order.Desc("Received"));
			IList<InventoryItem> stock = Repositories.Get<InventoryItem>().List(criteria);
			//
			foreach (InventoryItem element in stock)
			{
				if (quantity <= 0) break;
				//
				element.TransactionType = type;
				quantity = element.Move(location, plate, quantity);
			}
		}

		#endregion


	}
}
