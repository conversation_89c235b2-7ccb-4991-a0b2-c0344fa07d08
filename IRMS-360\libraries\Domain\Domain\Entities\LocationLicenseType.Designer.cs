using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class LocationLicenseType : Entity
	{
		#region Fields

		private LicenseType _licenseType;
		private LocationType _locationType;
		private String _active;

		#endregion

		#region Properties

		[DataMember]
		public virtual LicenseType LicenseType
		{
			get { return _licenseType; }
			set { _licenseType = value; }
		}

		[DataMember]
		public virtual LocationType LocationType
		{
			get { return _locationType; }
			set { _locationType = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}


		#endregion
	}
}
