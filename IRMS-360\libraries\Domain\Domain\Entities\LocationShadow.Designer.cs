using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class LocationShadow : Entity
	{
		#region Fields

		private String _columnName;
		private String _locationCode;
		private String _newValue;
		private String _oldValue;
		private String _transactionType;

		#endregion

		#region Properties

		[DataMember]
		public virtual String ColumnName
		{
			get { return _columnName; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ColumnName must not be blank or null.");
				else _columnName = value;
			}
		}

		[DataMember]
		public virtual String LocationCode
		{
			get { return _locationCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("LocationCode must not be blank or null.");
				else _locationCode = value;
			}
		}

		[DataMember]
		public virtual String NewValue
		{
			get { return _newValue; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("NewValue must not be blank or null.");
				else _newValue = value;
			}
		}

		[DataMember]
		public virtual String OldValue
		{
			get { return _oldValue; }
			set { _oldValue = value; }
		}

		[DataMember]
		public virtual String TransactionType
		{
			get { return _transactionType; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("TransactionType must not be blank or null.");
				else _transactionType = value;
			}
		}


		#endregion
	}
}
