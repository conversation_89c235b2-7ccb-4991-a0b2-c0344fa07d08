using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class LocationType : Entity
	{
		#region Fields

		private FunctionalAreaCode _functionalAreaCode;
		private ICollection<AgencyLocationType> _agencyLocationTypes = new HashSet<AgencyLocationType>();
		private ICollection<CarrierLocationType> _carrierLocationTypes = new HashSet<CarrierLocationType>();
		private ICollection<CompanyLocationType> _companyLocationTypes = new HashSet<CompanyLocationType>();
		private ICollection<CustomerLocationType> _customerLocationTypes = new HashSet<CustomerLocationType>();
		private ICollection<Location> _locations = new HashSet<Location>();
		private ICollection<LocationLicenseType> _locationLicenseTypes = new HashSet<LocationLicenseType>();
		private ICollection<ManufacturerLocationType> _manufacturerLocationTypes = new HashSet<ManufacturerLocationType>();
		private ICollection<OrderLocation> _orderLocations = new HashSet<OrderLocation>();
		private ICollection<ParticipantLocationType> _participantLocationTypes = new HashSet<ParticipantLocationType>();
		private ICollection<ProviderLocationType> _providerLocationTypes = new HashSet<ProviderLocationType>();
		private ICollection<PurchaseOrderLocation> _purchaseOrderLocations = new HashSet<PurchaseOrderLocation>();
		private ICollection<RequisitionLocation> _requisitionLocations = new HashSet<RequisitionLocation>();
		private ICollection<ReturnLocation> _returnLocations = new HashSet<ReturnLocation>();
		private ICollection<ShipmentLocation> _shipmentLocations = new HashSet<ShipmentLocation>();
		private ICollection<VendorLocationType> _vendorLocationTypes = new HashSet<VendorLocationType>();
		private String _active;
		private String _description;
		private String _locationTypeCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual FunctionalAreaCode FunctionalAreaCode
		{
			get { return _functionalAreaCode; }
			set { _functionalAreaCode = value; }
		}

		[DataMember]
		public virtual ICollection<AgencyLocationType> AgencyLocationTypes
		{
			get { return _agencyLocationTypes; }
			set { _agencyLocationTypes = value; }
		}

		[DataMember]
		public virtual ICollection<CarrierLocationType> CarrierLocationTypes
		{
			get { return _carrierLocationTypes; }
			set { _carrierLocationTypes = value; }
		}

		[DataMember]
		public virtual ICollection<CompanyLocationType> CompanyLocationTypes
		{
			get { return _companyLocationTypes; }
			set { _companyLocationTypes = value; }
		}

		[DataMember]
		public virtual ICollection<CustomerLocationType> CustomerLocationTypes
		{
			get { return _customerLocationTypes; }
			set { _customerLocationTypes = value; }
		}

		[DataMember]
		public virtual ICollection<Location> Locations
		{
			get { return _locations; }
			set { _locations = value; }
		}

		[DataMember]
		public virtual ICollection<LocationLicenseType> LocationLicenseTypes
		{
			get { return _locationLicenseTypes; }
			set { _locationLicenseTypes = value; }
		}

		[DataMember]
		public virtual ICollection<ManufacturerLocationType> ManufacturerLocationTypes
		{
			get { return _manufacturerLocationTypes; }
			set { _manufacturerLocationTypes = value; }
		}

		[DataMember]
		public virtual ICollection<OrderLocation> OrderLocations
		{
			get { return _orderLocations; }
			set { _orderLocations = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantLocationType> ParticipantLocationTypes
		{
			get { return _participantLocationTypes; }
			set { _participantLocationTypes = value; }
		}

		[DataMember]
		public virtual ICollection<ProviderLocationType> ProviderLocationTypes
		{
			get { return _providerLocationTypes; }
			set { _providerLocationTypes = value; }
		}

		[DataMember]
		public virtual ICollection<PurchaseOrderLocation> PurchaseOrderLocations
		{
			get { return _purchaseOrderLocations; }
			set { _purchaseOrderLocations = value; }
		}

		[DataMember]
		public virtual ICollection<RequisitionLocation> RequisitionLocations
		{
			get { return _requisitionLocations; }
			set { _requisitionLocations = value; }
		}

		[DataMember]
		public virtual ICollection<ReturnLocation> ReturnLocations
		{
			get { return _returnLocations; }
			set { _returnLocations = value; }
		}

		[DataMember]
		public virtual ICollection<ShipmentLocation> ShipmentLocations
		{
			get { return _shipmentLocations; }
			set { _shipmentLocations = value; }
		}

		[DataMember]
		public virtual ICollection<VendorLocationType> VendorLocationTypes
		{
			get { return _vendorLocationTypes; }
			set { _vendorLocationTypes = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String LocationTypeCode
		{
			get { return _locationTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("LocationTypeCode must not be blank or null.");
				else _locationTypeCode = value;
			}
		}


		#endregion
	}
}
