using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class MaintenanceDetail : Entity
	{
		#region Fields

		private Decimal? _maintenanceCost;
		private Int32 _sortOrder;
		private Int32? _frequencyDays;
		private Int32? _frequencyMiles;
		private Item _item;
		private ItemGroup _itemGroup;
		private ICollection<MaintenanceProgramReading> _maintenanceProgramReadings = new HashSet<MaintenanceProgramReading>();
		private ICollection<WorkOrderDetail> _workOrderDetails = new HashSet<WorkOrderDetail>();
		private MaintenanceProgram _maintenanceProgram;
		private String _active;
		private String _maintenanceDescription;
		private String _maintenanceShortDescription;

		#endregion

		#region Properties

		[DataMember]
		public virtual Decimal? MaintenanceCost
		{
			get { return _maintenanceCost; }
			set { _maintenanceCost = value; }
		}

		[DataMember]
		public virtual Int32 SortOrder
		{
			get { return _sortOrder; }
			set { _sortOrder = value; }
		}

		[DataMember]
		public virtual Int32? FrequencyDays
		{
			get { return _frequencyDays; }
			set { _frequencyDays = value; }
		}

		[DataMember]
		public virtual Int32? FrequencyMiles
		{
			get { return _frequencyMiles; }
			set { _frequencyMiles = value; }
		}

		[DataMember]
		public virtual Item Item
		{
			get { return _item; }
			set { _item = value; }
		}

		[DataMember]
		public virtual ItemGroup ItemGroup
		{
			get { return _itemGroup; }
			set { _itemGroup = value; }
		}

		[DataMember]
		public virtual ICollection<MaintenanceProgramReading> MaintenanceProgramReadings
		{
			get { return _maintenanceProgramReadings; }
			set { _maintenanceProgramReadings = value; }
		}

		[DataMember]
		public virtual ICollection<WorkOrderDetail> WorkOrderDetails
		{
			get { return _workOrderDetails; }
			set { _workOrderDetails = value; }
		}

		[DataMember]
		public virtual MaintenanceProgram MaintenanceProgram
		{
			get { return _maintenanceProgram; }
			set { _maintenanceProgram = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String MaintenanceDescription
		{
			get { return _maintenanceDescription; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("MaintenanceDescription must not be blank or null.");
				else _maintenanceDescription = value;
			}
		}

		[DataMember]
		public virtual String MaintenanceShortDescription
		{
			get { return _maintenanceShortDescription; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("MaintenanceShortDescription must not be blank or null.");
				else _maintenanceShortDescription = value;
			}
		}


		#endregion
	}
}
