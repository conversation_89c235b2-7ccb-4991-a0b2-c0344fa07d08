using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class MaintenanceProgram : Entity
	{
		#region Fields

		private Agency _agency;
		private CompanyLocationType _companyLocationType;
		private Item _item;
		private ICollection<MaintenanceDetail> _maintenanceDetails = new HashSet<MaintenanceDetail>();
		private ICollection<MaintenanceProgramReading> _maintenanceProgramReadings = new HashSet<MaintenanceProgramReading>();
		private ICollection<MaintenanceRequest> _maintenanceRequests = new HashSet<MaintenanceRequest>();
		private ICollection<WorkOrderHeader> _workOrderHeaders = new HashSet<WorkOrderHeader>();
		private MaintenanceType _maintenanceType;
		private String _active;
		private String _description;
		private String _programCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual Agency Agency
		{
			get { return _agency; }
			set { _agency = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual Item Item
		{
			get { return _item; }
			set { _item = value; }
		}

		[DataMember]
		public virtual ICollection<MaintenanceDetail> MaintenanceDetails
		{
			get { return _maintenanceDetails; }
			set { _maintenanceDetails = value; }
		}

		[DataMember]
		public virtual ICollection<MaintenanceProgramReading> MaintenanceProgramReadings
		{
			get { return _maintenanceProgramReadings; }
			set { _maintenanceProgramReadings = value; }
		}

		[DataMember]
		public virtual ICollection<MaintenanceRequest> MaintenanceRequests
		{
			get { return _maintenanceRequests; }
			set { _maintenanceRequests = value; }
		}

		[DataMember]
		public virtual ICollection<WorkOrderHeader> WorkOrderHeaders
		{
			get { return _workOrderHeaders; }
			set { _workOrderHeaders = value; }
		}

		[DataMember]
		public virtual MaintenanceType MaintenanceType
		{
			get { return _maintenanceType; }
			set { _maintenanceType = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String ProgramCode
		{
			get { return _programCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ProgramCode must not be blank or null.");
				else _programCode = value;
			}
		}


		#endregion
	}
}
