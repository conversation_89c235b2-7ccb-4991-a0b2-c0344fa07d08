using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class MaintenanceProgramReading : Entity
	{
		#region Fields

		private ICollection<WorkOrderMeterReading> _workOrderMeterReadings = new HashSet<WorkOrderMeterReading>();
		private MaintenanceDetail _maintenanceDetail;
		private MaintenanceProgram _maintenanceProgram;
		private MeterReadingType _meterReadingType;
		private String _active;
		private String _locationDescription;
		private String _notes;
		private String _required;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<WorkOrderMeterReading> WorkOrderMeterReadings
		{
			get { return _workOrderMeterReadings; }
			set { _workOrderMeterReadings = value; }
		}

		[DataMember]
		public virtual MaintenanceDetail MaintenanceDetail
		{
			get { return _maintenanceDetail; }
			set { _maintenanceDetail = value; }
		}

		[DataMember]
		public virtual MaintenanceProgram MaintenanceProgram
		{
			get { return _maintenanceProgram; }
			set { _maintenanceProgram = value; }
		}

		[DataMember]
		public virtual MeterReadingType MeterReadingType
		{
			get { return _meterReadingType; }
			set { _meterReadingType = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String LocationDescription
		{
			get { return _locationDescription; }
			set { _locationDescription = value; }
		}

		[DataMember]
		public virtual String Notes
		{
			get { return _notes; }
			set { _notes = value; }
		}

		[DataMember]
		public virtual String Required
		{
			get { return _required; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Required must not be blank or null.");
				else _required = value;
			}
		}


		#endregion
	}
}
