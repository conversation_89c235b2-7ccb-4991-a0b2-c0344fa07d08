using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class MaintenanceRequest : Entity
	{
		#region Fields

		private DateTime _requested;
		private DateTime _scheduled;
		private DateTime? _completed;
		private DateTime? _requiredBy;
		private InventoryItem _inventoryItem;
		private ICollection<WorkOrderHeader> _workOrderHeaders = new HashSet<WorkOrderHeader>();
		private Location _location;
		private MaintenanceProgram _maintenanceProgram;
		private OrganizationParticipant _assigned;
		private OrganizationParticipant _scheduledBy;
		private StatusCode _statusCode;
		private String _maintenanceRequestCode;
		private String _notes;
		private String _problemDescription;
		private String _requestorEmail;
		private String _requestorFirstName;
		private String _requestorLastName;
		private String _requestorPrimaryPhone;
		private String _requestorSecondaryPhone;
		private VendorLocationType _vendorLocationType;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime Requested
		{
			get { return _requested; }
			set { _requested = value; }
		}

		[DataMember]
		public virtual DateTime Scheduled
		{
			get { return _scheduled; }
			set { _scheduled = value; }
		}

		[DataMember]
		public virtual DateTime? Completed
		{
			get { return _completed; }
			set { _completed = value; }
		}

		[DataMember]
		public virtual DateTime? RequiredBy
		{
			get { return _requiredBy; }
			set { _requiredBy = value; }
		}

		[DataMember]
		public virtual InventoryItem InventoryItem
		{
			get { return _inventoryItem; }
			set { _inventoryItem = value; }
		}

		[DataMember]
		public virtual ICollection<WorkOrderHeader> WorkOrderHeaders
		{
			get { return _workOrderHeaders; }
			set { _workOrderHeaders = value; }
		}

		[DataMember]
		public virtual Location Location
		{
			get { return _location; }
			set { _location = value; }
		}

		[DataMember]
		public virtual MaintenanceProgram MaintenanceProgram
		{
			get { return _maintenanceProgram; }
			set { _maintenanceProgram = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant Assigned
		{
			get { return _assigned; }
			set { _assigned = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant ScheduledBy
		{
			get { return _scheduledBy; }
			set { _scheduledBy = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual String MaintenanceRequestCode
		{
			get { return _maintenanceRequestCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("MaintenanceRequestCode must not be blank or null.");
				else _maintenanceRequestCode = value;
			}
		}

		[DataMember]
		public virtual String Notes
		{
			get { return _notes; }
			set { _notes = value; }
		}

		[DataMember]
		public virtual String ProblemDescription
		{
			get { return _problemDescription; }
			set { _problemDescription = value; }
		}

		[DataMember]
		public virtual String RequestorEmail
		{
			get { return _requestorEmail; }
			set { _requestorEmail = value; }
		}

		[DataMember]
		public virtual String RequestorFirstName
		{
			get { return _requestorFirstName; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("RequestorFirstName must not be blank or null.");
				else _requestorFirstName = value;
			}
		}

		[DataMember]
		public virtual String RequestorLastName
		{
			get { return _requestorLastName; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("RequestorLastName must not be blank or null.");
				else _requestorLastName = value;
			}
		}

		[DataMember]
		public virtual String RequestorPrimaryPhone
		{
			get { return _requestorPrimaryPhone; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("RequestorPrimaryPhone must not be blank or null.");
				else _requestorPrimaryPhone = value;
			}
		}

		[DataMember]
		public virtual String RequestorSecondaryPhone
		{
			get { return _requestorSecondaryPhone; }
			set { _requestorSecondaryPhone = value; }
		}

		[DataMember]
		public virtual VendorLocationType VendorLocationType
		{
			get { return _vendorLocationType; }
			set { _vendorLocationType = value; }
		}


		#endregion
	}
}
