using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class MaintenanceType : Entity
	{
		#region Fields

		private ICollection<MaintenanceProgram> _maintenancePrograms = new HashSet<MaintenanceProgram>();
		private String _active;
		private String _description;
		private String _maintenanceTypeCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<MaintenanceProgram> MaintenancePrograms
		{
			get { return _maintenancePrograms; }
			set { _maintenancePrograms = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String MaintenanceTypeCode
		{
			get { return _maintenanceTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("MaintenanceTypeCode must not be blank or null.");
				else _maintenanceTypeCode = value;
			}
		}


		#endregion
	}
}
