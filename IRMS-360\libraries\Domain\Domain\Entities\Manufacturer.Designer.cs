using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Manufacturer : Entity
	{
		#region Fields

		private Agency _agency;
		private CompanyLocationType _companyLocationType;
		private ICollection<Item> _items = new HashSet<Item>();
		private ICollection<ManufacturerCommunication> _manufacturerCommunications = new HashSet<ManufacturerCommunication>();
		private ICollection<ManufacturerLocation> _manufacturerLocations = new HashSet<ManufacturerLocation>();
		private ICollection<OrganizationParticipant> _organizationParticipants = new HashSet<OrganizationParticipant>();
		private ICollection<Printer> _printers = new HashSet<Printer>();
		private String _active;
		private String _manufacturerCode;
		private String _name;
		private String _url;
		private TimeZone _timeZone;

		#endregion

		#region Properties

		[DataMember]
		public virtual Agency Agency
		{
			get { return _agency; }
			set { _agency = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual ICollection<Item> Items
		{
			get { return _items; }
			set { _items = value; }
		}

		[DataMember]
		public virtual ICollection<ManufacturerCommunication> ManufacturerCommunications
		{
			get { return _manufacturerCommunications; }
			set { _manufacturerCommunications = value; }
		}

		[DataMember]
		public virtual ICollection<ManufacturerLocation> ManufacturerLocations
		{
			get { return _manufacturerLocations; }
			set { _manufacturerLocations = value; }
		}

		[DataMember]
		public virtual ICollection<OrganizationParticipant> OrganizationParticipants
		{
			get { return _organizationParticipants; }
			set { _organizationParticipants = value; }
		}

		[DataMember]
		public virtual ICollection<Printer> Printers
		{
			get { return _printers; }
			set { _printers = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String ManufacturerCode
		{
			get { return _manufacturerCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ManufacturerCode must not be blank or null.");
				else _manufacturerCode = value;
			}
		}

		[DataMember]
		public virtual String Name
		{
			get { return _name; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Name must not be blank or null.");
				else _name = value;
			}
		}

		[DataMember]
		public virtual String Url
		{
			get { return _url; }
			set { _url = value; }
		}

		[DataMember]
		public virtual TimeZone TimeZone
		{
			get { return _timeZone; }
			set { _timeZone = value; }
		}


		#endregion
	}
}
