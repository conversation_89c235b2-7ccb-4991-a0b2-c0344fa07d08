using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ManufacturerCommunication : Entity
	{
		#region Fields

		private CommunicationRole _communicationRole;
		private Manufacturer _manufacturer;
		private ManufacturerLocation _manufacturerLocation;
		private String _active;
		private String _communicationValue;
		private String _primaryCommunication;

		#endregion

		#region Properties

		[DataMember]
		public virtual CommunicationRole CommunicationRole
		{
			get { return _communicationRole; }
			set { _communicationRole = value; }
		}

		[DataMember]
		public virtual Manufacturer Manufacturer
		{
			get { return _manufacturer; }
			set { _manufacturer = value; }
		}

		[DataMember]
		public virtual ManufacturerLocation ManufacturerLocation
		{
			get { return _manufacturerLocation; }
			set { _manufacturerLocation = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String CommunicationValue
		{
			get { return _communicationValue; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("CommunicationValue must not be blank or null.");
				else _communicationValue = value;
			}
		}

		[DataMember]
		public virtual String PrimaryCommunication
		{
			get { return _primaryCommunication; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("PrimaryCommunication must not be blank or null.");
				else _primaryCommunication = value;
			}
		}


		#endregion
	}
}
