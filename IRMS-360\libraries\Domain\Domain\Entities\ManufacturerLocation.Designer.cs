using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ManufacturerLocation : Entity
	{
		#region Fields

		private ICollection<ManufacturerCommunication> _manufacturerCommunications = new HashSet<ManufacturerCommunication>();
		private ICollection<ManufacturerLocationType> _manufacturerLocationTypes = new HashSet<ManufacturerLocationType>();
		private Manufacturer _manufacturer;
		private String _active;
		private String _addressDirection;
		private String _addressLine1;
		private String _addressLine2;
		private String _addressLine3;
		private String _addressNumber;
		private String _addressSuffix;
		private TimeZone _timeZone;
		private ZipCode _zipCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<ManufacturerCommunication> ManufacturerCommunications
		{
			get { return _manufacturerCommunications; }
			set { _manufacturerCommunications = value; }
		}

		[DataMember]
		public virtual ICollection<ManufacturerLocationType> ManufacturerLocationTypes
		{
			get { return _manufacturerLocationTypes; }
			set { _manufacturerLocationTypes = value; }
		}

		[DataMember]
		public virtual Manufacturer Manufacturer
		{
			get { return _manufacturer; }
			set { _manufacturer = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set { _active = value; }
		}

		[DataMember]
		public virtual String AddressDirection
		{
			get { return _addressDirection; }
			set { _addressDirection = value; }
		}

		[DataMember]
		public virtual String AddressLine1
		{
			get { return _addressLine1; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("AddressLine1 must not be blank or null.");
				else _addressLine1 = value;
			}
		}

		[DataMember]
		public virtual String AddressLine2
		{
			get { return _addressLine2; }
			set { _addressLine2 = value; }
		}

		[DataMember]
		public virtual String AddressLine3
		{
			get { return _addressLine3; }
			set { _addressLine3 = value; }
		}

		[DataMember]
		public virtual String AddressNumber
		{
			get { return _addressNumber; }
			set { _addressNumber = value; }
		}

		[DataMember]
		public virtual String AddressSuffix
		{
			get { return _addressSuffix; }
			set { _addressSuffix = value; }
		}

		[DataMember]
		public virtual TimeZone TimeZone
		{
			get { return _timeZone; }
			set { _timeZone = value; }
		}

		[DataMember]
		public virtual ZipCode ZipCode
		{
			get { return _zipCode; }
			set { _zipCode = value; }
		}


		#endregion
	}
}
