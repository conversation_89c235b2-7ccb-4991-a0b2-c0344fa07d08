using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ManufacturerLocationType : Entity
	{
		#region Fields

		private ICollection<OrganizationParticipant> _organizationParticipants = new HashSet<OrganizationParticipant>();
		private LocationType _locationType;
		private ManufacturerLocation _manufacturerLocation;
		private String _active;
		private String _description;
		private String _manufacturerLocationCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<OrganizationParticipant> OrganizationParticipants
		{
			get { return _organizationParticipants; }
			set { _organizationParticipants = value; }
		}

		[DataMember]
		public virtual LocationType LocationType
		{
			get { return _locationType; }
			set { _locationType = value; }
		}

		[DataMember]
		public virtual ManufacturerLocation ManufacturerLocation
		{
			get { return _manufacturerLocation; }
			set { _manufacturerLocation = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String ManufacturerLocationCode
		{
			get { return _manufacturerLocationCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ManufacturerLocationCode must not be blank or null.");
				else _manufacturerLocationCode = value;
			}
		}


		#endregion
	}
}
