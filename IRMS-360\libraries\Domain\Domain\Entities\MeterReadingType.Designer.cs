using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class MeterReadingType : Entity
	{
		#region Fields

		private ICollection<MaintenanceProgramReading> _maintenanceProgramReadings = new HashSet<MaintenanceProgramReading>();
		private ICollection<WorkOrderMeterReading> _workOrderMeterReadings = new HashSet<WorkOrderMeterReading>();
		private String _active;
		private String _description;
		private String _maxRequired;
		private String _meterReadingTypeCode;
		private String _minRequired;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<MaintenanceProgramReading> MaintenanceProgramReadings
		{
			get { return _maintenanceProgramReadings; }
			set { _maintenanceProgramReadings = value; }
		}

		[DataMember]
		public virtual ICollection<WorkOrderMeterReading> WorkOrderMeterReadings
		{
			get { return _workOrderMeterReadings; }
			set { _workOrderMeterReadings = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String MaxRequired
		{
			get { return _maxRequired; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("MaxRequired must not be blank or null.");
				else _maxRequired = value;
			}
		}

		[DataMember]
		public virtual String MeterReadingTypeCode
		{
			get { return _meterReadingTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("MeterReadingTypeCode must not be blank or null.");
				else _meterReadingTypeCode = value;
			}
		}

		[DataMember]
		public virtual String MinRequired
		{
			get { return _minRequired; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("MinRequired must not be blank or null.");
				else _minRequired = value;
			}
		}


		#endregion
	}
}
