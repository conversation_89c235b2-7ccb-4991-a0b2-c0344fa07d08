using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Module : Entity
	{
		#region Fields

		private ICollection<ApplicationModule> _applicationModules = new HashSet<ApplicationModule>();
		private ModuleType _moduleType;
		private String _active;
		private String _moduleCode;
		private String _name;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<ApplicationModule> ApplicationModules
		{
			get { return _applicationModules; }
			set { _applicationModules = value; }
		}

		[DataMember]
		public virtual ModuleType ModuleType
		{
			get { return _moduleType; }
			set { _moduleType = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String ModuleCode
		{
			get { return _moduleCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ModuleCode must not be blank or null.");
				else _moduleCode = value;
			}
		}

		[DataMember]
		public virtual String Name
		{
			get { return _name; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Name must not be blank or null.");
				else _name = value;
			}
		}


		#endregion
	}
}
