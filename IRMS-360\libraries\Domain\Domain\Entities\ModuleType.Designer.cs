using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ModuleType : Entity
	{
		#region Fields

		private ICollection<Module> _modules = new HashSet<Module>();
		private String _active;
		private String _description;
		private String _moduleTypeCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<Module> Modules
		{
			get { return _modules; }
			set { _modules = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String ModuleTypeCode
		{
			get { return _moduleTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ModuleTypeCode must not be blank or null.");
				else _moduleTypeCode = value;
			}
		}


		#endregion
	}
}
