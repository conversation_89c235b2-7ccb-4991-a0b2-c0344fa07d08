using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Need : Entity
	{
		#region Fields

		private NeedType _needType;
		private String _active;
		private String _description;
		private String _needCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual NeedType NeedType
		{
			get { return _needType; }
			set { _needType = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String NeedCode
		{
			get { return _needCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("NeedCode must not be blank or null.");
				else _needCode = value;
			}
		}


		#endregion
	}
}
