using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class NeedType : Entity
	{
		#region Fields

		private ICollection<Need> _needs = new HashSet<Need>();
		private String _active;
		private String _description;
		private String _needTypeCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<Need> Needs
		{
			get { return _needs; }
			set { _needs = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String NeedTypeCode
		{
			get { return _needTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("NeedTypeCode must not be blank or null.");
				else _needTypeCode = value;
			}
		}


		#endregion
	}
}
