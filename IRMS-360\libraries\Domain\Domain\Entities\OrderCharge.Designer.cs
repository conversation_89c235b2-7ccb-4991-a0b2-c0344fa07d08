using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class OrderCharge : Entity
	{
		#region Fields

		private BillingCode _billingCode;
		private Decimal _amount;
		private OrderDetail _orderDetail;
		private OrderHeader _orderHeader;
		private String _comments;

		#endregion

		#region Properties

		[DataMember]
		public virtual BillingCode BillingCode
		{
			get { return _billingCode; }
			set { _billingCode = value; }
		}

		[DataMember]
		public virtual Decimal Amount
		{
			get { return _amount; }
			set { _amount = value; }
		}

		[DataMember]
		public virtual OrderDetail OrderDetail
		{
			get { return _orderDetail; }
			set { _orderDetail = value; }
		}

		[DataMember]
		public virtual OrderHeader OrderHeader
		{
			get { return _orderHeader; }
			set { _orderHeader = value; }
		}

		[DataMember]
		public virtual String Comments
		{
			get { return _comments; }
			set { _comments = value; }
		}


		#endregion
	}
}
