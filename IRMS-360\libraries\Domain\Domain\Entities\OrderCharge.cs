using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class OrderCharge : Entity
	{
		#region Constructor

		public OrderCharge()
		{
			//
		}

		#endregion

		#region Properties.Reports
        public virtual Int32? OrderId { get; set; }
        public virtual Int32? OrderLineId { get; set; }
		public virtual String BillingCategoryCode { get; set; }
		public virtual String BillingCodeCode { get; set; }

		#endregion
	}
}
