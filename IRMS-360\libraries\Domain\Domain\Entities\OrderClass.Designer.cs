using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class OrderClass : Entity
	{
		#region Fields

		private Int32? _priority;
		private ICollection<OrderHeader> _orderHeaders = new HashSet<OrderHeader>();
		private String _active;
		private String _description;
		private String _orderClassCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual Int32? Priority
		{
			get { return _priority; }
			set { _priority = value; }
		}

		[DataMember]
		public virtual ICollection<OrderHeader> OrderHeaders
		{
			get { return _orderHeaders; }
			set { _orderHeaders = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String OrderClassCode
		{
			get { return _orderClassCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("OrderClassCode must not be blank or null.");
				else _orderClassCode = value;
			}
		}


		#endregion
	}
}
