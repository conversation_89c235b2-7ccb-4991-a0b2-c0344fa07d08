using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class OrderComment : Entity
	{
		#region Fields

		private Comment _comment;
		private Int32 _commentLine;
		private OrderDetail _orderDetail;
		private OrderHeader _orderHeader;
		private String _active;
		private String _comments;
		private String _functionalUse;

		#endregion

		#region Properties

		[DataMember]
		public virtual Comment Comment
		{
			get { return _comment; }
			set { _comment = value; }
		}

		[DataMember]
		public virtual Int32 CommentLine
		{
			get { return _commentLine; }
			set { _commentLine = value; }
		}

		[DataMember]
		public virtual OrderDetail OrderDetail
		{
			get { return _orderDetail; }
			set { _orderDetail = value; }
		}

		[DataMember]
		public virtual OrderHeader OrderHeader
		{
			get { return _orderHeader; }
			set { _orderHeader = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Comments
		{
			get { return _comments; }
			set { _comments = value; }
		}

		[DataMember]
		public virtual String FunctionalUse
		{
			get { return _functionalUse; }
			set { _functionalUse = value; }
		}


		#endregion
	}
}
