using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class OrderDetail : Entity
	{
		#region Fields

		private CartonSize _cartonSize;
		private DateTime? _requiredBy;
		private Decimal _salePrice;
		private Decimal? _msrp;
		private Int32 _lineNumber;
		private Decimal _quantity;
		private Decimal? _customerOrderedQuantity;
		private Int32? _lineNumberSequence;
		private Decimal? _originalQuantity;
		private Decimal? _returnQuantity;
		private Item _item;
		private LicensePlate _licensePlate;
		private ICollection<Alert> _alerts = new HashSet<Alert>();
		private ICollection<CartonDetail> _cartonDetails = new HashSet<CartonDetail>();
		private ICollection<InventoryItemDetail> _inventoryItemDetails = new HashSet<InventoryItemDetail>();
		private ICollection<InventoryPick> _inventoryPicks = new HashSet<InventoryPick>();
		private ICollection<ItemFulfillment> _itemFulfillments = new HashSet<ItemFulfillment>();
		private ICollection<OrderCharge> _orderCharges = new HashSet<OrderCharge>();
		private ICollection<OrderComment> _orderComments = new HashSet<OrderComment>();
		private ICollection<OrderReferenceCode> _orderReferenceCodes = new HashSet<OrderReferenceCode>();
		private ICollection<OrderReturnReason> _orderReturnReasons = new HashSet<OrderReturnReason>();
		private ICollection<ReceiptDetail> _receiptDetails = new HashSet<ReceiptDetail>();
		private ICollection<ReturnDetail> _returnDetails = new HashSet<ReturnDetail>();
		private ICollection<ShipmentDetail> _shipmentDetails = new HashSet<ShipmentDetail>();
		private ICollection<WorkOrderHeader> _workOrderHeaders = new HashSet<WorkOrderHeader>();
		private OrderHeader _orderHeader;
		private StatusCode _statusCode;
		private StatusCode _stockStatusCode;
		private String _binLocation;
		private String _comments;
		private String _forceShip;
		private String _invoiceNumber;
		private String _lotNumber;
		private String _pickLine;
		private String _sameLot;
		private String _serialNumber;
		private String _vendorItemCode;
		private UnitOfMeasure _unitOfMeasure;

		#endregion

		#region Properties

		[DataMember]
		public virtual CartonSize CartonSize
		{
			get { return _cartonSize; }
			set { _cartonSize = value; }
		}

		[DataMember]
		public virtual DateTime? RequiredBy
		{
			get { return _requiredBy; }
			set { _requiredBy = value; }
		}

		[DataMember]
		public virtual Decimal SalePrice
		{
			get { return _salePrice; }
			set { _salePrice = value; }
		}

		[DataMember]
		public virtual Decimal? Msrp
		{
			get { return _msrp; }
			set { _msrp = value; }
		}

		[DataMember]
		public virtual Int32 LineNumber
		{
			get { return _lineNumber; }
			set { _lineNumber = value; }
		}

		[DataMember]
		public virtual Decimal Quantity
		{
			get { return _quantity; }
			set { _quantity = value; }
		}

		[DataMember]
		public virtual Decimal? CustomerOrderedQuantity
		{
			get { return _customerOrderedQuantity; }
			set { _customerOrderedQuantity = value; }
		}

		[DataMember]
		public virtual Int32? LineNumberSequence
		{
			get { return _lineNumberSequence; }
			set { _lineNumberSequence = value; }
		}

		[DataMember]
		public virtual Decimal? OriginalQuantity
		{
			get { return _originalQuantity; }
			set { _originalQuantity = value; }
		}

		[DataMember]
		public virtual Decimal? ReturnQuantity
		{
			get { return _returnQuantity; }
			set { _returnQuantity = value; }
		}

		[DataMember]
		public virtual Item Item
		{
			get { return _item; }
			set { _item = value; }
		}

		[DataMember]
		public virtual LicensePlate LicensePlate
		{
			get { return _licensePlate; }
			set { _licensePlate = value; }
		}

		[DataMember]
		public virtual ICollection<Alert> Alerts
		{
			get { return _alerts; }
			set { _alerts = value; }
		}

		[DataMember]
		public virtual ICollection<CartonDetail> CartonDetails
		{
			get { return _cartonDetails; }
			set { _cartonDetails = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryItemDetail> InventoryItemDetails
		{
			get { return _inventoryItemDetails; }
			set { _inventoryItemDetails = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryPick> InventoryPicks
		{
			get { return _inventoryPicks; }
			set { _inventoryPicks = value; }
		}

		[DataMember]
		public virtual ICollection<ItemFulfillment> ItemFulfillments
		{
			get { return _itemFulfillments; }
			set { _itemFulfillments = value; }
		}

		[DataMember]
		public virtual ICollection<OrderCharge> OrderCharges
		{
			get { return _orderCharges; }
			set { _orderCharges = value; }
		}

		[DataMember]
		public virtual ICollection<OrderComment> OrderComments
		{
			get { return _orderComments; }
			set { _orderComments = value; }
		}

		[DataMember]
		public virtual ICollection<OrderReferenceCode> OrderReferenceCodes
		{
			get { return _orderReferenceCodes; }
			set { _orderReferenceCodes = value; }
		}

		[DataMember]
		public virtual ICollection<OrderReturnReason> OrderReturnReasons
		{
			get { return _orderReturnReasons; }
			set { _orderReturnReasons = value; }
		}

		[DataMember]
		public virtual ICollection<ReceiptDetail> ReceiptDetails
		{
			get { return _receiptDetails; }
			set { _receiptDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ReturnDetail> ReturnDetails
		{
			get { return _returnDetails; }
			set { _returnDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ShipmentDetail> ShipmentDetails
		{
			get { return _shipmentDetails; }
			set { _shipmentDetails = value; }
		}

		[DataMember]
		public virtual ICollection<WorkOrderHeader> WorkOrderHeaders
		{
			get { return _workOrderHeaders; }
			set { _workOrderHeaders = value; }
		}

		[DataMember]
		public virtual OrderHeader OrderHeader
		{
			get { return _orderHeader; }
			set { _orderHeader = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual StatusCode StockStatusCode
		{
			get { return _stockStatusCode; }
			set { _stockStatusCode = value; }
		}

		[DataMember]
		public virtual String BinLocation
		{
			get { return _binLocation; }
			set { _binLocation = value; }
		}

		[DataMember]
		public virtual String Comments
		{
			get { return _comments; }
			set { _comments = value; }
		}

		[DataMember]
		public virtual String ForceShip
		{
			get { return _forceShip; }
			set { _forceShip = value; }
		}

		[DataMember]
		public virtual String InvoiceNumber
		{
			get { return _invoiceNumber; }
			set { _invoiceNumber = value; }
		}

		[DataMember]
		public virtual String LotNumber
		{
			get { return _lotNumber; }
			set { _lotNumber = value; }
		}

		[DataMember]
		public virtual String PickLine
		{
			get { return _pickLine; }
			set { _pickLine = value; }
		}

		[DataMember]
		public virtual String SameLot
		{
			get { return _sameLot; }
			set { _sameLot = value; }
		}

		[DataMember]
		public virtual String SerialNumber
		{
			get { return _serialNumber; }
			set { _serialNumber = value; }
		}

		[DataMember]
		public virtual String VendorItemCode
		{
			get { return _vendorItemCode; }
			set { _vendorItemCode = value; }
		}

		[DataMember]
		public virtual UnitOfMeasure UnitOfMeasure
		{
			get { return _unitOfMeasure; }
			set { _unitOfMeasure = value; }
		}


		#endregion
	}
}
