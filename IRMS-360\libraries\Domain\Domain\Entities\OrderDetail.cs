using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

using NHibernate.Criterion;

using Upp.Irms.Constants;
using Upp.Irms.Core;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class OrderDetail : Entity
	{
		#region Properties

		public virtual Decimal Amount { get; set; }
        public virtual Decimal ItemTotal { get; set; }
        public virtual Decimal? ItemWeight { get; set; }
        public virtual Decimal CartQuantity { get; set; }
        public virtual Int32? ItemId { get; set; }
        public virtual int ItemStockStatusId { get; set; }
        public virtual int AvailableStockStatusId { get; set; }
        public virtual Int32? OrderId { get; set; }
		public virtual Decimal ShippedQuantity { get; set; }
        public virtual Decimal ItemCube { get; set; }
		public virtual String DetailComments { get; set; }
        public virtual String DisItemTotal { get; set; }
        public virtual String DisItemUnitCost { get; set; }
        public virtual String PackedQuantity { get; set; }
        public virtual String ItemCountryCodeDescription { get; set; }
		public virtual String UOMDescription { get; set; }
        
        [DataMember]
        public virtual String OrderCode { get; set; }
        [DataMember]
        public virtual String Ordered { get; set; }
        [DataMember]
        public virtual String OrderSuffix { get; set; }
        [DataMember]
        public virtual String CustomerPo { get; set; }
        [DataMember]
        public virtual String ItemCode { get; set; }
        [DataMember]
        public virtual DateTime StatusDate { get; set; }
        [DataMember]
        public virtual String ItemDescription { get; set; }
        [DataMember]
        public virtual String LineStatusDate { get; set; }
        [DataMember]
        public virtual String StatusDescription { get; set; }
        [DataMember]
        public virtual String SnapshotDate { get; set; }
        [DataMember]
        public virtual String SnapshotTime { get; set; }
        [DataMember]
        public virtual String Shipment { get; set; }
        [DataMember]
        public virtual String ShipTo { get; set; }
        [DataMember]
        public virtual String ProNumber { get; set; }
        [DataMember]
        public virtual String CarrierCode { get; set; }
        [DataMember]
        public virtual String UOMCode { get; set; }

		#endregion

		#region Constructor

		public OrderDetail()
		{
			//
		}

		#endregion

		#region Methods.Public

        public virtual String FindComments(string use)
        {
            DetachedCriteria criteria = DetachedCriteria.For<OrderComment>()
                .Add("Active", "A")
                .Add("OrderDetail", this);
            if (!String.IsNullOrEmpty(use)) criteria = criteria.Add("FunctionalUse", use);
            IList<OrderComment> comments = Repositories.Get<OrderComment>().List(criteria);

            IList<String> parts = comments
                .OrderBy(c => c.CommentLine)
                .Select(c => c.Comments)
                .ToList();
            //
            StringBuilder builder = new StringBuilder();
            foreach (String element in parts) builder.Append(element);
            return builder.ToString();
        }

		public virtual void FindOrderChargeAmount()
		{
			// FIXME: use Constants here.
			DetachedCriteria criteria = DetachedCriteria.For<OrderCharge>()
				.Add("BillingCode.Code", "CH")
				.Add("BillingCode.BillingCategoryCode.Code", "ORD")
				.Add("OrderDetail", this)
				.SetProjection(Projections.Property("Amount"));
			this.Amount = Repositories.Get<OrderCharge>().Function<Decimal>(criteria);
		}

		public virtual void UpdateReturnQuantity(Decimal quantity)
		{
			_dateModified = DateTime.Now;
			_returnQuantity = _returnQuantity == null ? quantity : (_returnQuantity + quantity);
			_userModified = Registry.Find<UserAccount>().UserName;
			//
			Repositories.Get<OrderDetail>().Update(this);
		}

		public virtual void UpdateStatus()
		{
			Entity.UpdateStatus<OrderDetail, ItemFulfillment>(this, FunctionalAreas.Order);
		}

		#endregion

        #region Properties.Reports

        public virtual Decimal ChargeAmount { get; set; }
        public virtual Decimal Discount { get; set; }
        public virtual Decimal ExtendedPrice { get; set; }
        public virtual Decimal ListPrice { get; set; }
        public virtual String BuyerPartNumber { get; set; }
        public virtual String CustomerItem { get; set; }
        public virtual String CustomerNumber { get; set; }
        public virtual String CustomerBranch { get; set; }
        public virtual String HarmCode { get; set; }
        public virtual String SalesMan { get; set; }
        public virtual String CounterRepresentative { get; set; }
        public virtual String LocationCode { get; set; }
        public virtual String LPNCode { get; set; }
        public virtual int? CustomerId { get; set; }
        public virtual bool MultiCarton { get; set; }
        public virtual String KitHeaderItem { get; set; }
        public virtual String KitHeaderItemDesc { get; set; }
        public virtual String KitHeaderCustomerItemCode { get; set; }
        public virtual bool KitItem { get; set; }
        public virtual int? StockStatusCodeId { get; set; }
        public virtual Decimal KitHeaderQty { get; set; }
        public virtual Decimal KitHeaderSalePrice { get; set; }
        public virtual String OdpoRef { get; set; }
        #endregion
    }
}
