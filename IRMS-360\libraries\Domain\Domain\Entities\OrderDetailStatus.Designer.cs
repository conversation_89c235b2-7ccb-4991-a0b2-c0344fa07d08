using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	
	partial class OrderDetailStatus : Entity
	{
		#region Fields

		private DateTime _occured;
		private OrderDetail _orderDetail;
		private OrderHeader _orderHeader;
		private StatusCode _statusCode;
		private String _comments;
        private string _statusDescription;
        private string _orderCode;
        private string _orderSuffix;
        private int _lineNumber;
        private int? _lineNumberSequence;
        private int? _statusCodeId;
        private int? _orderHeaderId;
        private int? _orderDetailId;

        #endregion

        #region Properties

       
		public virtual DateTime Occured
		{
			get { return _occured; }
			set { _occured = value; }
		}

		
		public virtual OrderDetail OrderDetail
		{
			get { return _orderDetail; }
			set { _orderDetail = value; }
		}

		
		public virtual OrderHeader OrderHeader
		{
			get { return _orderHeader; }
			set { _orderHeader = value; }
		}

		
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		
		public virtual String Comments
		{
			get { return _comments; }
			set { _comments = value; }
		}

       
        public virtual String StatusDescription
        {
            get { return _statusDescription; }
            set { _statusDescription = value; }
        }

       
        public virtual String OrderCode
        {
            get { return _orderCode; }
            set { _orderCode = value; }
        }

       
        public virtual String OrderSuffix
        {
            get { return _orderSuffix; }
            set { _orderSuffix = value; }
        }

       
        public virtual Int32 LineNumber
        {
            get { return _lineNumber; }
            set { _lineNumber = value; }
        }

        
        public virtual Int32? LineNumberSequence
        {
            get { return _lineNumberSequence; }
            set { _lineNumberSequence = value; }
        }
        
        public virtual Int32? StatusCodeId
        {
            get { return _statusCodeId; }
            set { _statusCodeId = value; }
        }
      
        public virtual Int32? OrderHeaderId
        {
            get { return _orderHeaderId; }
            set { _orderHeaderId = value; }
        }
        
        public virtual Int32? OrderDetailId
        {
            get { return _orderDetailId; }
            set { _orderDetailId = value; }
        }
        #endregion
    }
}
