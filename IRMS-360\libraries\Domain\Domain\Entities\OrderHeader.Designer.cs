using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class OrderHeader : Entity
	{
		#region Fields

		private AgencyLocationType _agencyLocationType;
		private AgencyOrganizationalUnit _agencyOrganizationalUnit;
		private Carrier _carrier;
		private CarrierService _carrierService;
		private CompanyLocationType _companyLocationType;
		private CompanyOrganizationalUnit _companyOrganizationalUnit;
		private CurrencyCode _currencyCode;
		private Customer _customer;
		private CustomerCurrencyCode _customerCurrencyCode;
		private CustomerLocationType _customerLocationType;
		private DateTime _requiredBy;
        private DateTime? _arrivalDate1;
        private DateTime? _arrivalDate2;
        private DateTime? _dueDate;
        private DateTime? _ordered;
		private DateTime? _scheduled;
		private DateTime? _statusDate;
		private Decimal? _totalMiles;
		private Decimal? _totalWeight;
		private Decimal _totalItemWeightQuantity;
		private Decimal _totalCubeQuantity;
		private Decimal _fillRate;
		private FobCode _fobCode;
		private FreightClass _freightClass;
		private ICollection<Alert> _alerts = new HashSet<Alert>();
		private ICollection<CartonHeader> _cartonHeaders = new HashSet<CartonHeader>();
		private ICollection<InvoiceDetail> _invoiceDetails = new HashSet<InvoiceDetail>();
		private ICollection<ItemTransaction> _itemTransactions = new HashSet<ItemTransaction>();
		private ICollection<OrderCharge> _orderCharges = new HashSet<OrderCharge>();
		private ICollection<OrderComment> _orderComments = new HashSet<OrderComment>();
		private ICollection<OrderDetail> _orderDetails = new HashSet<OrderDetail>();
		private ICollection<OrderHeader> _childOrderHeaders = new HashSet<OrderHeader>();
		private ICollection<OrderLocation> _orderLocations = new HashSet<OrderLocation>();
		private ICollection<OrderReferenceCode> _orderReferenceCodes = new HashSet<OrderReferenceCode>();
		private ICollection<OrderReturnReason> _orderReturnReasons = new HashSet<OrderReturnReason>();
		private ICollection<OrderStatus> _orderStatuses = new HashSet<OrderStatus>();
		private ICollection<ReceiptHeader> _receiptHeaders = new HashSet<ReceiptHeader>();
		private ICollection<Transaction> _transactions = new HashSet<Transaction>();
		private OrderClass _orderClass;
		private OrderHeader _parentOrderHeader;
		private OrderType _orderType;
		private OrganizationParticipant _eContact;
		private OrganizationParticipant _iContact;
		private ParentBillOfLading _parentBillOfLading;
		private PaymentMethod _paymentMethod;
		private PaymentTerm _paymentTerm;
		private Priority _priority;
		private ProviderLocationType _providerLocationType;
		private ProviderOrganizationalUnit _providerOrganizationalUnit;
		private StatusCode _statusCode;
		private String _adultSignatureRequired;
		private String _billOfLading;
		private String _branchCode;
        private String _class;
        private String _codFundCode;
        private String _commodityDescription;
		private String _customerCode;
		private String _customerPo;
		private String _deliveryConfirmation;
		private String _directSignature;
		private String _earlyDelivery;
		private String _hostBatch;
		private String _hostSelector;
		private String _insideDelivery;
		private String _international;
		private String _liftgateDelivery;
		private String _military;
        private String _nmfc;
		private String _orderCode;
		private String _orderSuffix;
		private String _packingInstructions;
		private String _priorDeliveryNotification;
		private String _proNumber;
		private String _quantumShipNotification;
		private String _quantumViewNotification;
		private String _rateTypeCode;
		private String _residential;
		private String _shipComplete;
		private String _shipEarly;
		private String _shippingInstructions;
        private String _showPrice;
		private String _signatureRequired;
		private String _storeNumber;
		private String _thirdPartyAccount;

		#endregion

		#region Properties

		[DataMember]
		public virtual AgencyLocationType AgencyLocationType
		{
			get { return _agencyLocationType; }
			set { _agencyLocationType = value; }
		}

		[DataMember]
		public virtual AgencyOrganizationalUnit AgencyOrganizationalUnit
		{
			get { return _agencyOrganizationalUnit; }
			set { _agencyOrganizationalUnit = value; }
		}

		[DataMember]
		public virtual Carrier Carrier
		{
			get { return _carrier; }
			set { _carrier = value; }
		}

		[DataMember]
		public virtual CarrierService CarrierService
		{
			get { return _carrierService; }
			set { _carrierService = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual CompanyOrganizationalUnit CompanyOrganizationalUnit
		{
			get { return _companyOrganizationalUnit; }
			set { _companyOrganizationalUnit = value; }
		}

		[DataMember]
		public virtual CurrencyCode CurrencyCode
		{
			get { return _currencyCode; }
			set { _currencyCode = value; }
		}

		[DataMember]
		public virtual Customer Customer
		{
			get { return _customer; }
			set { _customer = value; }
		}

		[DataMember]
		public virtual CustomerCurrencyCode CustomerCurrencyCode
		{
			get { return _customerCurrencyCode; }
			set { _customerCurrencyCode = value; }
		}

		[DataMember]
		public virtual CustomerLocationType CustomerLocationType
		{
			get { return _customerLocationType; }
			set { _customerLocationType = value; }
		}

		[DataMember]
		public virtual DateTime RequiredBy
		{
			get { return _requiredBy; }
			set { _requiredBy = value; }
		}

        [DataMember]
        public virtual DateTime? ArrivalDate1
        {
            get { return _arrivalDate1; }
            set { _arrivalDate1 = value; }
        }

        [DataMember]
        public virtual DateTime? ArrivalDate2
        {
            get { return _arrivalDate2; }
            set { _arrivalDate2 = value; }
        }

        [DataMember]
		public virtual DateTime? DueDate
		{
			get { return _dueDate; }
			set { _dueDate = value; }
		}

		[DataMember]
		public virtual DateTime? Ordered
		{
			get { return _ordered; }
			set { _ordered = value; }
		}

		[DataMember]
		public virtual DateTime? Scheduled
		{
			get { return _scheduled; }
			set { _scheduled = value; }
		}

		[DataMember]
		public virtual DateTime? StatusDate
		{
			get { return _statusDate; }
			set { _statusDate = value; }
		}

		[DataMember]
		public virtual Decimal? TotalMiles
		{
			get { return _totalMiles; }
			set { _totalMiles = value; }
		}

		[DataMember]
		public virtual Decimal? TotalWeight
		{
			get { return _totalWeight; }
			set { _totalWeight = value; }
		}

		[DataMember]
		public virtual Decimal TotalItemWeightQuantity
		{
			get { return _totalItemWeightQuantity; }
			set { _totalItemWeightQuantity = value; }
		}

		[DataMember]
		public virtual Decimal TotalCubeQuantity
		{
			get { return _totalCubeQuantity; }
			set { _totalCubeQuantity = value; }
		}

		[DataMember]
		public virtual Decimal FillRate
		{
			get { return _fillRate; }
			set { _fillRate = value; }
		}

		[DataMember]
		public virtual FobCode FobCode
		{
			get { return _fobCode; }
			set { _fobCode = value; }
		}

		[DataMember]
		public virtual FreightClass FreightClass
		{
			get { return _freightClass; }
			set { _freightClass = value; }
		}

		[DataMember]
		public virtual ICollection<Alert> Alerts
		{
			get { return _alerts; }
			set { _alerts = value; }
		}

		[DataMember]
		public virtual ICollection<CartonHeader> CartonHeaders
		{
			get { return _cartonHeaders; }
			set { _cartonHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<InvoiceDetail> InvoiceDetails
		{
			get { return _invoiceDetails; }
			set { _invoiceDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ItemTransaction> ItemTransactions
		{
			get { return _itemTransactions; }
			set { _itemTransactions = value; }
		}

		[DataMember]
		public virtual ICollection<OrderCharge> OrderCharges
		{
			get { return _orderCharges; }
			set { _orderCharges = value; }
		}

		[DataMember]
		public virtual ICollection<OrderComment> OrderComments
		{
			get { return _orderComments; }
			set { _orderComments = value; }
		}

		[DataMember]
		public virtual ICollection<OrderDetail> OrderDetails
		{
			get { return _orderDetails; }
			set { _orderDetails = value; }
		}

		[DataMember]
		public virtual ICollection<OrderHeader> ChildOrderHeaders
		{
			get { return _childOrderHeaders; }
			set { _childOrderHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<OrderLocation> OrderLocations
		{
			get { return _orderLocations; }
			set { _orderLocations = value; }
		}

		[DataMember]
		public virtual ICollection<OrderReferenceCode> OrderReferenceCodes
		{
			get { return _orderReferenceCodes; }
			set { _orderReferenceCodes = value; }
		}

		[DataMember]
		public virtual ICollection<OrderReturnReason> OrderReturnReasons
		{
			get { return _orderReturnReasons; }
			set { _orderReturnReasons = value; }
		}

		[DataMember]
		public virtual ICollection<OrderStatus> OrderStatuses
		{
			get { return _orderStatuses; }
			set { _orderStatuses = value; }
		}

		[DataMember]
		public virtual ICollection<ReceiptHeader> ReceiptHeaders
		{
			get { return _receiptHeaders; }
			set { _receiptHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<Transaction> Transactions
		{
			get { return _transactions; }
			set { _transactions = value; }
		}

		[DataMember]
		public virtual OrderClass OrderClass
		{
			get { return _orderClass; }
			set { _orderClass = value; }
		}

		[DataMember]
		public virtual OrderHeader ParentOrderHeader
		{
			get { return _parentOrderHeader; }
			set { _parentOrderHeader = value; }
		}

		[DataMember]
		public virtual OrderType OrderType
		{
			get { return _orderType; }
			set { _orderType = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant EContact
		{
			get { return _eContact; }
			set { _eContact = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant IContact
		{
			get { return _iContact; }
			set { _iContact = value; }
		}

		[DataMember]
		public virtual ParentBillOfLading ParentBillOfLading
		{
			get { return _parentBillOfLading; }
			set { _parentBillOfLading = value; }
		}

		[DataMember]
		public virtual PaymentMethod PaymentMethod
		{
			get { return _paymentMethod; }
			set { _paymentMethod = value; }
		}

		[DataMember]
		public virtual PaymentTerm PaymentTerm
		{
			get { return _paymentTerm; }
			set { _paymentTerm = value; }
		}

		[DataMember]
		public virtual Priority Priority
		{
			get { return _priority; }
			set { _priority = value; }
		}

		[DataMember]
		public virtual ProviderLocationType ProviderLocationType
		{
			get { return _providerLocationType; }
			set { _providerLocationType = value; }
		}

		[DataMember]
		public virtual ProviderOrganizationalUnit ProviderOrganizationalUnit
		{
			get { return _providerOrganizationalUnit; }
			set { _providerOrganizationalUnit = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual String AdultSignatureRequired
		{
			get { return _adultSignatureRequired; }
			set { _adultSignatureRequired = value; }
		}

		[DataMember]
		public virtual String BillOfLading
		{
			get { return _billOfLading; }
			set { _billOfLading = value; }
		}

		[DataMember]
		public virtual String BranchCode
		{
			get { return _branchCode; }
			set { _branchCode = value; }
		}

        [DataMember]
        public virtual String Class
        {
            get { return _class; }
            set { _class = value; }
        }

        [DataMember]
        public virtual String CodFundCode
        {
            get { return _codFundCode; }
            set { _codFundCode = value; }
        }

        [DataMember]
        public virtual String CommodityDescription
        {
            get { return _commodityDescription; }
            set { _commodityDescription = value; }
        }

		[DataMember]
		public virtual String CustomerCode
		{
			get { return _customerCode; }
			set { _customerCode = value; }
		}

		[DataMember]
		public virtual String CustomerPo
		{
			get { return _customerPo; }
			set { _customerPo = value; }
		}

		[DataMember]
		public virtual String DeliveryConfirmation
		{
			get { return _deliveryConfirmation; }
			set { _deliveryConfirmation = value; }
		}

		[DataMember]
		public virtual String DirectSignature
		{
			get { return _directSignature; }
			set { _directSignature = value; }
		}

		[DataMember]
		public virtual String EarlyDelivery
		{
			get { return _earlyDelivery; }
			set { _earlyDelivery = value; }
		}

		[DataMember]
		public virtual String HostBatch
		{
			get { return _hostBatch; }
			set { _hostBatch = value; }
		}

		[DataMember]
		public virtual String HostSelector
		{
			get { return _hostSelector; }
			set { _hostSelector = value; }
		}

		[DataMember]
		public virtual String InsideDelivery
		{
			get { return _insideDelivery; }
			set { _insideDelivery = value; }
		}

		[DataMember]
		public virtual String International
		{
			get { return _international; }
			set { _international = value; }
		}

		[DataMember]
		public virtual String LiftgateDelivery
		{
			get { return _liftgateDelivery; }
			set { _liftgateDelivery = value; }
		}

		[DataMember]
		public virtual String Military
		{
			get { return _military; }
			set { _military = value; }
		}

        [DataMember]
        public virtual String Nmfc
        {
            get { return _nmfc; }
            set { _nmfc = value; }
        }

		[DataMember]
		public virtual String OrderCode
		{
			get { return _orderCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("OrderCode must not be blank or null.");
				else _orderCode = value;
			}
		}

		[DataMember]
		public virtual String OrderSuffix
		{
			get { return _orderSuffix; }
			set { _orderSuffix = value; }
		}

		[DataMember]
		public virtual String PackingInstructions
		{
			get { return _packingInstructions; }
			set { _packingInstructions = value; }
		}

		[DataMember]
		public virtual String PriorDeliveryNotification
		{
			get { return _priorDeliveryNotification; }
			set { _priorDeliveryNotification = value; }
		}

		[DataMember]
		public virtual String ProNumber
		{
			get { return _proNumber; }
			set { _proNumber = value; }
		}

		[DataMember]
		public virtual String QuantumShipNotification
		{
			get { return _quantumShipNotification; }
			set { _quantumShipNotification = value; }
		}

		[DataMember]
		public virtual String QuantumViewNotification
		{
			get { return _quantumViewNotification; }
			set { _quantumViewNotification = value; }
		}

		[DataMember]
		public virtual String RateTypeCode
		{
			get { return _rateTypeCode; }
			set { _rateTypeCode = value; }
		}

		[DataMember]
		public virtual String Residential
		{
			get { return _residential; }
			set { _residential = value; }
		}

		[DataMember]
		public virtual String ShipComplete
		{
			get { return _shipComplete; }
			set { _shipComplete = value; }
		}

		[DataMember]
		public virtual String ShipEarly
		{
			get { return _shipEarly; }
			set { _shipEarly = value; }
		}

		[DataMember]
		public virtual String ShippingInstructions
		{
			get { return _shippingInstructions; }
			set { _shippingInstructions = value; }
		}

		[DataMember]
		public virtual String SignatureRequired
		{
			get { return _signatureRequired; }
			set { _signatureRequired = value; }
		}

		[DataMember]
		public virtual String StoreNumber
		{
			get { return _storeNumber; }
			set { _storeNumber = value; }
		}

		[DataMember]
		public virtual String ThirdPartyAccount
		{
			get { return _thirdPartyAccount; }
			set { _thirdPartyAccount = value; }
		}

        [DataMember]
        public virtual String ShowPrice
        {
            get { return _showPrice; }
            set { _showPrice = value; }
        }
		#endregion
	}
}
