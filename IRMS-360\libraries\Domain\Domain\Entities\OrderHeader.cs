using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;

using System.Text;

using NHibernate;
using NHibernate.Criterion;
using NHibernate.Transform;

using Upp.Irms.Constants;
using Upp.Irms.Core;
using Upp.Shared.Utilities;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class OrderHeader : Entity
	{
        private string _billToBusinessName;
        private string _shipToBusinessName;

		#region Properties

        [DataMember]
        public virtual String BillToBusinessName
        {
            get
            {
                return _billToBusinessName;
            }
            set
            {
                _billToBusinessName = value;
            }
        }

        [DataMember]
        public virtual String ShipToBusinessName
        {
            get
            {
                return _shipToBusinessName;
            }
            set
            {
                _shipToBusinessName = value;
            }
        }
        
        [DataMember]
        public virtual Int32 LineNumber { get; set; }
        [DataMember]
        public virtual Decimal OriginalQuantity { get; set; }
        [DataMember]
        public virtual String ItemCode { get; set; }
        [DataMember]
        public virtual String ItemDescription { get; set; }
        [DataMember]
        public virtual Boolean CartonPackingList { get; set; }
		[DataMember]
		public virtual Boolean CrossDockable { get; set; }
		[DataMember]
		public virtual Boolean HasSuffixes { get; set; }
		[DataMember]
		public virtual Boolean NeedsVerification { get; set; }
		[DataMember]
		public virtual Boolean PopulateSlot { get; set; }
		[DataMember]
		public virtual Dock Dock { get; set; }
		[DataMember]
		public virtual LicensePlate LicensePlateTo { get; set; }
        [DataMember]
        public virtual Int32 CartonCount { get; set; }
        [DataMember]
        public virtual Int32 CaseCount { get; set; }
        [DataMember]
		public virtual Int32 DetailCount { get; set; }
		[DataMember]
		public virtual Int32? DefaultSlotNumber { get; set; }
		[DataMember]
		public virtual Int32? LpnCount { get; set; }
		[DataMember]
		public virtual Int32? SlotNumber { get; set; }
		[DataMember]
		public virtual Location LocationTo { get; set; }
		public virtual OrderLocation OrderLocation { get; set; }
		[DataMember]
		public virtual PrinterTypePrinter PrinterTypePrinter { get; set; }
		[DataMember]
		public virtual StatusCode CurrentStatus { get; set; }
		[DataMember]
		public virtual String CarrierCode { get; set; }
		[DataMember]
		public virtual String CurrentStatusCode { get; set; }
		[DataMember]
		public virtual String CurrentStatusDescription { get; set; }
        [DataMember]
        public virtual String DestinationPostalCode { get; set; }
        [DataMember]
        public virtual String Errors { get; set; }
        [DataMember]
        public virtual String LicensePlateCode { get; set; }
		[DataMember]
		public virtual String OrderClassCode { get; set; }
		[DataMember]
		public virtual String OrderTypeCode { get; set; }
        [DataMember]
        public virtual String OrganizationCode { get; set; }
		[DataMember]
		public virtual String TruckCode { get; set; }
		[DataMember]
		public virtual String WaveCode { get; set; }

        [DataMember]
        public virtual String AddressLine1 { get; set; }
        [DataMember]
        public virtual String AddressLine2 { get; set; }
        [DataMember]
        public virtual String AddressLine3 { get; set; }
        [DataMember]
        public virtual String FreightTerm { get; set; }
        [DataMember]
        public virtual String ShipToName { get; set; }
        [DataMember]
        public virtual String State { get; set; }
        [DataMember]
        public virtual String City { get; set; }
        [DataMember]
        public virtual String CompanyLocTypeCode { get; set; }
        [DataMember]
        public virtual String CompanyCode { get; set; }
        [DataMember]
        public virtual String ZipCode { get; set; }

        [DataMember]
        public virtual String Opened { get; set; }

        [DataMember]
        public virtual String Distribute { get; set; }

        [DataMember]
        public virtual String InPick { get; set; }

        [DataMember]
        public virtual String Picked { get; set; }

        [DataMember]
        public virtual String Packed { get; set; }

        [DataMember]
        public virtual String ShipTime { get; set; }

        [DataMember]
        public virtual String TotalTime { get; set; }
        [DataMember]
        public virtual String Country { get; set; }



        #endregion

        #region Properties.Reports

        public virtual String DropShipperInfo { get; set; }
        public virtual Boolean HasHazardous { get; set; }
        public virtual Boolean HazmatShipment { get; set; }
        public virtual Boolean KitItem { get; set; }
        public virtual Decimal CustomerPalletWeight { get; set; }
        public virtual Decimal OrderPalletWeight { get; set; }
        public virtual String OrderCustomerName { get; set; }        
        public virtual Decimal OrderWeight { get; set; }
		public virtual DateTime? Shipped { get; set; }
		public virtual DateTime? ShipDate { get; set; }
		public virtual Decimal FreightCharges { get; set; }
        public virtual Decimal EstimatedFreight { get; set; }
        public virtual Decimal EstimatedFreightCharges { get; set; }
        public virtual Decimal ShipmentWeight { get; set; }
        public virtual Decimal ShipmentCartonQuantity { get; set; }
        public virtual Decimal ShipmentPalletQuantity { get; set; }
        public virtual Decimal ShipmentPalletWeight { get; set; }
        public virtual Decimal TotalOrderCost { get; set; }
        public virtual Decimal TotalShipmentCartonQuantity { get; set; }
        public virtual Decimal TotalShipmentPalletQuantity { get; set; }
        public virtual Decimal TotalShipmentPalletWeight { get; set; }
        public virtual Decimal TotalUnits { get; set; }
        public virtual Decimal Weight { get; set; }
        public virtual Decimal OutboundHandlingCharges { get; set; }
        public virtual int NonHazmatPallets { get; set; }
        public virtual Int32? OrderCarrierId { get; set; }
        public virtual Int32? OrderCustomerId { get; set; }        
        public virtual Int32? OrderHeaderId { get; set; }
        public virtual Int32? ShipmentHeaderId { get; set; }
        public virtual Int32? ShipmentId { get; set; }
        public virtual Int32 ShipmentPalletCount { get; set; }
        public virtual Int32 OrderLpnCount { get; set; }
        public virtual Int32 OrderPalletCount { get; set; }
        public virtual Int32 ShipmentNonHazmatPallets { get; set; }
        public virtual Int32 PackageCount { get; set; }
        public virtual Int32 PalletCount { get; set; }        
        public virtual Int32 SkidCount { get; set; }
        public virtual Int32 ShipmentCartons { get; set; }
        public virtual Int32 ShipmentPallets { get; set; }
        public virtual Int32 TotalPackageCount { get; set; }
        public virtual Int32? WarehouseId { get; set; }
        public virtual String AlternateCustomerCode { get; set; }
        public virtual String ArnReferenceValue { get; set; }
        public virtual String BillToAddr1 { get; set; }
        public virtual String BillToAddrNo { get; set; }
        public virtual String BillToAddrDirection { get; set; }
        public virtual String BillToAddrSuffix { get; set; }
        public virtual String BillToAddr2 { get; set; }
        public virtual String BillToAddr3 { get; set; }
        public virtual String BillCountyCode { get; set; }
        public virtual String BillToCustomerName { get; set; }
        public virtual String BillToCustomerLocationAddress1 { get; set; }
        public virtual String BillToCustomerLocationAddress2 { get; set; }
        public virtual String BillToCustomerLocationAddress3 { get; set; }
        public virtual String BillToCustomerLocationCity { get; set; }
        public virtual String BillToCustomerLocationState { get; set; }
        public virtual String BillToCustomerLocationCountry { get; set; }
        public virtual String BillToCustomerLocationZipCode { get; set; }
        public virtual String BillToPrimaryPhone { get; set; }
        public virtual String BillToSecondaryPhone { get; set; }
        public virtual String BillToPrimaryEmail { get; set; }
        public virtual String BillToSecondaryEmail { get; set; }
		public virtual String Bol { get; set; }
        public virtual String BolAddress { get; set; }        
        public virtual String BrokerAccountNumber { get; set; }
        public virtual String BrokerAddressLine1 { get; set; }
        public virtual String BrokerAddressLine2 { get; set; }
        public virtual String BrokerCity { get; set; }
        public virtual String BrokerCode { get; set; }
        public virtual String BrokerCountry { get; set; }
        public virtual String BrokerFax { get; set; }
        public virtual String BrokerPhone { get; set; }
        public virtual String BrokerName { get; set; }
        public virtual String BrokerNotes { get; set; }
        public virtual String BrokerState { get; set; }
        public virtual String BrokerZip { get; set; }
        public virtual String BuyerPartNumber { get; set; }
		public virtual String CarrierName { get; set; }
        public virtual String Cartons { get; set; }
        public virtual String CarrierServiceName { get; set; }
        public virtual String Chepout { get; set; }
        public virtual String ClassCode { get; set; }
        public virtual String Commodity { get; set; }
        public virtual String CommodityDesc { get; set; }
        public virtual String CompanyLocAddressNumber { get; set; }
        public virtual String CompanyLocAddressLine1 { get; set; }
        public virtual String CompanyLocAddressLine2 { get; set; }
        public virtual String CompanyLocAddressLine3 { get; set; }
        public virtual String CompanyLocAddressSuffix { get; set; }
        public virtual Int32 CompanyLocTypeId { get; set; }
        public virtual String CompanyLocTypeDesc { get; set; }
        public virtual String ConsiderBOLNumber { get; set; }
        public virtual String CreatedBy { get; set; }
        public virtual String CustomerAddress { get; set; }
        public virtual String CustomerAddressLine2 { get; set; }
        public virtual String CustomerAddressLine3 { get; set; }
        public virtual String CustomerCity { get; set; }
        public virtual String CustomerCountry { get; set; }
        public virtual String CustomerEmail { get; set; }
        public virtual String CustomerFax { get; set; }
        public virtual Byte[] CustomerLogo { get; set; }
        public virtual String CustomerState { get; set; }
        public virtual String CustomerPhone { get; set; }
        public virtual String CustomerWebsite { get; set; }
        public virtual String CustomerZip { get; set; }
		public virtual String CustomerName { get; set; }
        public virtual String CustomerNote { get; set; }
        public virtual String OrderCustomerCode { get; set; }
        public virtual String CustomerReference { get; set; }
        public virtual String CustomerReferenceNumber { get; set; }
        public virtual String CustDEA { get; set; }
        public virtual String CustomerPackingList { get; set; }
        public virtual String CustomerPackingListPath { get; set; }
        public virtual String Department { get; set; }
        public virtual String DepartmentName { get; set; }
        public virtual String DepartmentNumber { get; set; }
        public virtual String DockCode { get; set; }
        public virtual String Email { get; set; }
        public virtual String ExcludeBillTo { get; set; }
        public virtual String ExternalPhone { get; set; }
        public virtual String Fein { get; set; }
        public virtual String FobCodeCode { get; set; }
		public virtual String FobDescription { get; set; }
        public virtual String InternalPhone { get; set; }
        public virtual String Phone { get; set; }
        public virtual String CompanyWebSite { get; set; }
        public virtual String cmpWebSiteAddr { get; set; }
        public virtual String INTComment { get; set; }  
        public virtual String InvShipperInfo { get; set; }
		public virtual String HeaderComment { get; set; } 
		public virtual String ManifestCode { get; set; }
        public virtual String MerchandiseCode { get; set; }
		public virtual String MiddleName { get; set; }
        public virtual String N4de { get; set; }
        public virtual String NmfcCode { get; set; }
        
        public virtual String OrderBol { get; set; }
        public virtual String OrderCodeWithSuffix { get; set; }
        public virtual String OrderComment { get; set; }
        public virtual String FunctionalUse { get; set; }
        public virtual String OrderLineComment { get; set; }
		public virtual String OrderCustomer { get; set; }
        public virtual String OrderPro { get; set; }
        public virtual String OrderRefStoreNumber { get; set; }
        public virtual String OrderSepartor { get; set; }
        public virtual String PackingListCustomerCode { get; set; }
        public virtual String PackinglstRefernceValue { get; set; }
        public virtual String PaymentTermDescription { get; set; }
        public virtual String ParentBillOfLadingBOL { get; set; }
        public virtual String ParentBOLPro { get; set; }
		public virtual String Pro { get; set; }
        public virtual String PubShipperInfo { get; set; }
		public virtual String ReferenceValue { get; set; }
        public virtual String RequestorCode { get; set; }
        public virtual String RefKb { get; set; }
        public virtual String RefLf { get; set; }
        public virtual String ReportGrouping { get; set; }
        public virtual String RouteCode { get; set; }
        public virtual String RGAAmount { get; set; }
        public virtual String RGA { get; set; }
        public virtual String SanNumber { get; set; }
        public virtual String ScacCode { get; set; }
        public virtual String SealNumber { get; set; }
		public virtual String ServiceCode { get; set; }
		public virtual String ServiceDescription { get; set; }
        public virtual String ShipFromName { get; set; }
        public virtual string ShipToContactName { get; set; }
        public virtual String ShipmentBol { get; set; }
        public virtual String ShipmentComments { get; set; }
        public virtual String ShipmentPro { get; set; }
        public virtual String SpecialInstruction { get; set; }
        public virtual String SpsOrderNumber { get; set; }
        public virtual String StatusCodeCode { get; set; }
        public virtual String StatusCodeDescription { get; set; }
        public virtual String Store { get; set; }
        public virtual string ThirdPartyContactName { get; set; }
        public virtual String TrailerCode { get; set; }
        public virtual String TrackingCode { get; set; }
        public virtual Decimal TaxAmount { get; set; }
        public virtual Decimal FreightAmount { get; set; }
        public virtual String OrderStatus { get; set; }
        public virtual Int32 LineCount { get; set; }
        public virtual Decimal TotalItemWeight { get; set; }
        public virtual Boolean HazardousOrder { get; set; }
        public virtual Boolean SingleCartonOrder { get; set; }
        public virtual Boolean OrderLevel { get; set; }
        public virtual Boolean CartonLevel { get; set; }
        public virtual String Container { get; set; }
        public virtual String PaymentMethodDescription { get; set; }
        public virtual String ItemGroupDescription { get; set; }
        public virtual DateTime? CancelBy { get; set; }
        public virtual String ShipFobDescription { get; set; }
        public virtual String ShipmentReferenceNumber { get; set; }
        public virtual String ShipToCustomerCode { get; set; }
        public virtual String SidShipperInfo { get; set; }
        public virtual String ShipToAddress { get; set; }
        public virtual String ShipToCity { get; set; }
        public virtual String ShipToState { get; set; }
        public virtual String ShipToZipCode { get; set; }
        public virtual String ShipToCountry { get; set; }
        public virtual String ShipToAddr1 { get; set; }
        public virtual String ShipToAddrNo { get; set; }
        public virtual String ShipToAddrDirection { get; set; }
        public virtual String ShipToAddrSuffix { get; set; }
        public virtual String ShipToAddr2 { get; set; }
        public virtual String ShipToAddr3 { get; set; }
        public virtual String ShipCountyCode { get; set; }
        public virtual String ShipToPrimaryPhone { get; set; }
        public virtual String ShipToSecondaryPhone { get; set; }
        public virtual String ShipToPrimaryEmail { get; set; }
        public virtual String ShipToSecondaryEmail { get; set; }
        public virtual String ShowTrackingIdBarcode { get; set; }
        public virtual String BillToName { get; set; }
        public virtual String BillToCustomerCode { get; set; }
        public virtual String BillToAddress { get; set; }
        public virtual String BillToCity { get; set; }
        public virtual String BillToState { get; set; }
        public virtual String BillToZipCode { get; set; }
        public virtual String BillToCountry { get; set; }
        public virtual Decimal ActualQuantity { get; set; }
        public virtual String WareHouseCity { get; set; }
        public virtual String WareHouseCountry { get; set; }
        public virtual String WareHouseState { get; set; }
        public virtual String WareHouseZipCode { get; set; }
        public virtual String AsnNotSentText { get; set; }
        public virtual String GiftMessage { get; set; }
        public virtual String WarehouseAddress { get; set; }
        public virtual string ShipIDShipperInfo { get; set; }
        public virtual string CHORDShipperInfo { get; set; }
        public virtual string CONFShipperInfo { get; set; }
        public virtual String PackingListMessage { get; set; }
        public virtual String OdpoRef { get; set; }
        public virtual String PackingComment { get; set; }

        public virtual int? ShipShipToId
        {
            get;
            set;
        }


        public virtual string ShipShipToAddr1
        {
            get;
            set;
        }


        public virtual string ShipShipToAddr2
        {
            get;
            set;
        }

        public virtual string ShipShipToAddr3
        {
            get;
            set;
        }

        public virtual string ShipShipToBusinessName
        {
            get;
            set;
        }

        public virtual string ShipShipToName
        {
            get;
            set;
        }


        public virtual string ShipShipToZipCode
        {
            get;
            set;
        }


        public virtual string ShipShipToCity
        {
            get;
            set;
        }

        public virtual string ShipShipToState
        {
            get;
            set;
        }


        public virtual string ShipShipToCountry
        {
            get;
            set;
        }



        public virtual string ShipShipToAddress
        {
            get;
            set;
        }

        public virtual string ShipShipToPrimaryPhone
        {
            get;
            set;
        }

        

        public virtual int? ThirdPartyBillId
        {
            get;
            set;
        }
       
        public virtual string ThirdPartyBillAddress
        {
            get;
            set;
        }

       
        public virtual string ThirdPartyBillAddr1
        {
            get;
            set;
        }

        
        public virtual string ThirdPartyBillAddr2
        {
            get;
            set;
        }

        public virtual string ThirdPartyBillAddr3
        {
            get;
            set;
        }

        public virtual string ThirdPartyBillBusinessName
        {
            get;
            set;
        }
       
        public virtual string ThirdPartyBillName
        {
            get;
            set;
        }

        
        public virtual string ThirdPartyBillZipCode
        {
            get;
            set;
        }

        
        public virtual string ThirdPartyBillCity
        {
            get;
            set;
        }
        
        public virtual string ThirdPartyBillState
        {
            get;
            set;
        }
        
        public virtual string ThirdPartyBillCountry
        {
            get;
            set;
        }

        public virtual string ThirdPartyBillPrimaryPhone
        {
            get;
            set;
        }

        
        public virtual string LocationTypeCode
        {
            get;
            set;
        }
                        

		#endregion

		#region Constructor

		public OrderHeader()
		{
			//
		}

		#endregion

		#region Methods.Private

		private String FindCustomerCode()
		{
			if (!String.IsNullOrEmpty(_customerCode)) return _customerCode;
			//
			LocationType ship = Entity.Retrieve<LocationType>(OrganizationLocationTypes.ShipTo);
			DetachedCriteria criteria = DetachedCriteria.For<OrderLocation>()
				.Add(Expression.Eq("LocationType", ship))
				.Add(Expression.Eq("OrderHeader", this))
				.SetMaxResults(1);
			OrderLocation location = Repositories.Get<OrderLocation>().Retrieve(criteria);
			return (location == null) ? null : location.CustomerCode;
		}

		#endregion

		#region Methods.Public

		public virtual void AddToTruck(string slotNumber)
		{
			LocationType mobile = Entity.Retrieve<LocationType>(InventoryLocationTypes.Mobile);
			//
			ProjectionList projections = Projections.ProjectionList()
				 .Add(Projections.Property("CartonHeader"), "CartonHeader")
				 .Add(Projections.Property("ch.LicensePlate"), "LicensePlate");
			DetachedCriteria criteria = DetachedCriteria.For<ItemFulfillment>()
				.CreateAlias("CartonHeader", "ch")
				.CreateAlias("OrderDetail", "od")
				.Add("od.OrderHeader", this)
				.Add("OrderDetail", "!=", null)
				.SetProjection(Projections.Distinct(projections))
				.SetResultTransformer(Transformers.AliasToBean<ItemFulfillment>());
			IList<ItemFulfillment> picks = Repositories.Get<ItemFulfillment>().List(criteria);
			//
			foreach (ItemFulfillment element in picks)
			{
				criteria = DetachedCriteria.For<LicensePlateLocation>()
					.Add("LicensePlate", element.LicensePlate)
					.AddOrder("Occurred", false)
					.SetMaxResults(1);
				LicensePlateLocation lpnlocation = Repositories.Get<LicensePlateLocation>().Retrieve(criteria);
				//
				if (lpnlocation != null
					&& lpnlocation.Location.LocationType.LocationTypeCode.Equals(mobile.LocationTypeCode)
					&& !string.IsNullOrEmpty(lpnlocation.SlotNumber))
				{
					lpnlocation.DateModified = DateTime.Now;
					lpnlocation.Location = this.LocationTo;
					lpnlocation.SlotNumber = slotNumber;
					lpnlocation.UserModified = Registry.Find<UserAccount>().UserName;
					//
					Repositories.Get<LicensePlateLocation>().Update(lpnlocation);
				}
				else
				{
					lpnlocation = Entity.Activate<LicensePlateLocation>();
					lpnlocation.LicensePlate = element.LicensePlate;
					lpnlocation.Location = this.LocationTo;
					lpnlocation.Occurred = DateTime.Now;
					lpnlocation.SlotNumber = slotNumber;
					//
					Repositories.Get<LicensePlateLocation>().Add(lpnlocation);
				}
			}
		}

		public virtual void CalculateDetailCount()
		{
			DetachedCriteria criteria = DetachedCriteria.For<OrderDetail>()
				.Add("OrderHeader", this)
				.SetProjection(Projections.Count("Id"));
			this.DetailCount = Repositories.Get<OrderDetail>().Function<Int32>(criteria);
		}

		public virtual void ChangeStatus(StatusCode status)
		{
            _dateModified = DateTime.Now;
            _statusCode = status;
            _userModified = Registry.Find<UserAccount>().UserName;
            //
			OrderStatus created = Entity.Activate<OrderStatus>();
			created.Occurred = DateTime.Now;
			created.OrderHeader = this;
			created.StatusCode = status;
			//
			Repositories.Get<OrderStatus>().Add(created);
		}

		public virtual void CheckCrossDockable(PurchaseOrderHeader header)
		{
			this.CrossDockable = false;
			//
			this.FindCurrentStatus();
			if (this.CurrentStatus == null) return;
			//
			String status = CodeValue.GetCode(Upp.Irms.Constants.OrderStatuses.Open);
			if (!status.Equals(this.CurrentStatus.Code)) return;
			//
			if (header == null) return;
			//
			ProjectionList projections = Projections.ProjectionList()
				.Add(Projections.Sum("Quantity"), "Quantity")
				.Add(Projections.GroupProperty("Item"), "Item")
				.Add(Projections.GroupProperty("StockStatusCode"), "StockStatusCode");
			DetachedCriteria criteria = DetachedCriteria.For<OrderDetail>()
				.Add("OrderHeader", this)
				.SetProjection(projections)
				.SetResultTransformer(Transformers.AliasToBean<OrderDetail>());
			IList<OrderDetail> details = Repositories.Get<OrderDetail>().List(criteria);
			//
			StatusCode available = Entity.Retrieve<StatusCode>(InventoryStatuses.Available);
			foreach (OrderDetail element in details)
			{
				criteria = DetachedCriteria.For<InventoryItem>()
					.Add("Item", element.Item)
					.Add("Quantity", ">", 0M)
					.Add("ReceiptDetail.PurchaseOrderDetail.PurchaseOrderHeader", header)
					.Add("StatusCode", element.StockStatusCode == null ? available : element.StockStatusCode)
					.SetProjection(Projections.Sum("Quantity"));
				Decimal received = Repositories.Get<InventoryItem>().Function<Decimal>(criteria);
				//
				if (received != element.Quantity) return;
			}
			//
			this.CrossDockable = true;
		}

		public virtual void CheckInventory(Location location)
		{
			foreach (CartonHeader header in this.CartonHeaders) header.CheckInventory(location);
		}

		public virtual void CheckNeedsVerification()
		{
			StatusCode verify = Entity.Retrieve<StatusCode>(Upp.Irms.Constants.OrderStatuses.Verify);
			DetachedCriteria criteria = DetachedCriteria.For<CartonHeader>()
				.Add("OrderHeader", this)
				.Add("StatusCode", verify);
			this.NeedsVerification = (Repositories.Get<CartonHeader>().Count(criteria) > 0);
		}

        public virtual void CheckPopulateSlot(string truckCode)
        {
            Boolean? populate = BusinessRule.RetrieveBoolean("11079");
            this.PopulateSlot = populate == null ? false : populate.Value;
            //
            if (this.PopulateSlot)
            {
                CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
                //
                ProjectionList projections = Projections.ProjectionList()
                    .Add(Projections.Max(Projections.Cast(NHibernateUtil.Int32, Projections.Property("SlotNumber"))), "TruckSlotNumber");

                DetachedCriteria criteria = DetachedCriteria.For<LicensePlateLocation>()
                        .Add("LicensePlate.CompanyLocationType", warehouse)
                        .Add("Location.LocationCode", "=", truckCode)
                        .SetProjection(projections)
                        .SetMaxResults(1)
                        .SetResultTransformer(Transformers.AliasToBean<LicensePlateLocation>());
                LicensePlateLocation location = Repositories.Get<LicensePlateLocation>().Retrieve(criteria);
                //
                if (location != null) this.DefaultSlotNumber = location.TruckSlotNumber + 1;
                else this.DefaultSlotNumber = 1;
            }
        }

		public virtual void CreateReference(ReferenceCode reference, object value)
		{
			OrderReferenceCode created = Entity.Activate<OrderReferenceCode>();
			created.OrderHeader = this;
			created.ReferenceCode = reference;
			created.ReferenceValue = Converter.ToString(value);
			//
			Repositories.Get<OrderReferenceCode>().Add(created);
		}

		public virtual void CrossDock(PurchaseOrderHeader header)
		{
			if (header == null) return;
			//
			StatusCode available = Entity.Retrieve<StatusCode>(InventoryStatuses.Available);
			StatusCode distributed = Entity.Retrieve<StatusCode>(Upp.Irms.Constants.OrderStatuses.Distributed);
			StatusCode packed = Entity.Retrieve<StatusCode>(Upp.Irms.Constants.OrderStatuses.Packed);
			StatusCode picked = Entity.Retrieve<StatusCode>(Upp.Irms.Constants.OrderStatuses.Picked);
			StatusCode unavailable = Entity.Retrieve<StatusCode>(InventoryStatuses.Unavailable);
			//
			DetachedCriteria criteria = DetachedCriteria.For<OrderDetail>()
				.Add("OrderHeader", this);
			IList<OrderDetail> details = Repositories.Get<OrderDetail>().List(criteria);
			foreach (OrderDetail element in details)
			{
				criteria = DetachedCriteria.For<InventoryItem>()
					.Add("Item", element.Item)
					.Add("Quantity", ">", 0M)
					.Add("ReceiptDetail.PurchaseOrderDetail.PurchaseOrderHeader", header)
					.Add("StatusCode", available);
				IList<InventoryItem> items = Repositories.Get<InventoryItem>().List(criteria);
				foreach (InventoryItem item in items)
				{
					ItemFulfillment allocation = Entity.Activate<ItemFulfillment>();
					allocation.InventoryItem = item;
					allocation.Item = item.Item;
					allocation.LicensePlate = item.LicensePlate.ParentLicensePlate;
					allocation.Location = item.Location;
					allocation.LotNumber = item.LotNumber;
					allocation.OrderDetail = element;
					allocation.Quantity = Converter.ToInt32(item.Quantity);
					allocation.StatusCode = packed;
					Repositories.Get<ItemFulfillment>().Add(allocation);
					//
					item.DateModified = DateTime.Now;
					item.Quantity = 0;
					item.StatusCode = unavailable;
					item.UserModified = Registry.Find<UserAccount>().UserName;
					Repositories.Get<InventoryItem>().Update(item);
				}
			}
			this.ChangeStatus(distributed);
			//
			foreach (OrderDetail element in details)
			{
				criteria = DetachedCriteria.For<ItemFulfillment>()
					.Add("OrderDetail", element);
				IList<ItemFulfillment> allocations = Repositories.Get<ItemFulfillment>().List(criteria);
				foreach (ItemFulfillment allocation in allocations)
				{
					InventoryPick pick = Entity.Activate<InventoryPick>();
					pick.ItemFulfillment = allocation;
					pick.LicensePlate = allocation.LicensePlate;
					pick.Location = allocation.Location;
					pick.OrderDetail = allocation.OrderDetail;
					pick.Picked = DateTime.Now;
					pick.Picker = Registry.Find<UserAccount>().OrganizationParticipant;
					pick.Quantity = allocation.Quantity;
					pick.StatusCode = packed;
					//
					Repositories.Get<InventoryPick>().Add(pick);
					//
					allocation.WritePicked(pick, allocation.InventoryItem, pick.Quantity, allocation.LotNumber, null);
				}
			}
			this.ChangeStatus(picked);
			//
			foreach (OrderDetail element in details)
			{
				criteria = DetachedCriteria.For<InventoryPick>()
					.Add("OrderDetail", element);
				IList<InventoryPick> picks = Repositories.Get<InventoryPick>().List(criteria);
				foreach (InventoryPick pick in picks)
					pick.ItemFulfillment.WritePacked(pick, pick.Quantity, pick.ItemFulfillment.LotNumber, null);
			}
			this.ChangeStatus(packed);
		}

		public virtual String FindComments(string use)
		{
			DetachedCriteria criteria = DetachedCriteria.For<OrderComment>()
				.Add("Active", "A")
				.Add("OrderDetail", null)
				.Add("OrderHeader", this);
			if (!String.IsNullOrEmpty(use)) criteria = criteria.Add("FunctionalUse", use);
			IList<OrderComment> comments = Repositories.Get<OrderComment>().List(criteria);

			IList<String> parts = comments
				.OrderBy(c => c.CommentLine)
				.Select(c => c.Comments)
				.ToList();
			//
			StringBuilder builder = new StringBuilder();
			foreach (String element in parts) builder.Append(element);
			return builder.ToString();
		}

		public virtual void FindCurrentStatus()
		{
			DetachedCriteria criteria = DetachedCriteria.For<OrderStatus>()
				.Add(Expression.Eq("OrderHeader", this))
				.AddOrder("Occurred", false)
				.AddOrder("StatusCode.SortOrder", false)
				.SetMaxResults(1);
			IList<OrderStatus> results = Repositories.Get<OrderStatus>().List(criteria);
			if (results.Count == 1) this.CurrentStatus = results[0].StatusCode;
		}

        public virtual void FindOpenLpnOnSlot(string slotnumber,string truckCode)
        {
            CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
            LocationType mobile = Entity.Retrieve<LocationType>(InventoryLocationTypes.Mobile);
            StatusCode open = Entity.Retrieve<StatusCode>(Upp.Irms.Constants.LicensePlateStatuses.Open);
            //  
            DetachedCriteria status = DetachedCriteria.For<LicensePlateStatus>("lps")
                .CreateAlias("StatusCode", "StatusCode")
                .Add(Expression.EqProperty("_root.LicensePlate.Id", "lps.LicensePlate"))
                .AddOrder("Occurred", false)
                .AddOrder("StatusCode.SortOrder", false)
                .SetMaxResults(1)
                .SetProjection(Projections.Property("StatusCode.Code"));
            //
            DetachedCriteria criteria = DetachedCriteria.For<LicensePlateLocation>("_root")
                   .Add("LicensePlate.CompanyLocationType", warehouse)
                   .Add("Location.LocationCode", "=", truckCode)
                   .Add("SlotNumber", slotnumber)
                   .Add(Expression.Eq(Projections.SubQuery(status), open.Code))
                   .SetMaxResults(1);
            LicensePlateLocation lpnlocation = Repositories.Get<LicensePlateLocation>().Retrieve(criteria);
            if (lpnlocation != null)
            {
                this.LicensePlateCode = lpnlocation.LicensePlate.LicensePlateCode;
                this.TruckCode = lpnlocation.Location.LocationCode;
            }
        }

		public virtual void FindOrderLocation()
		{
			LocationType type = Entity.Retrieve<LocationType>(OrganizationLocationTypes.ShipTo);
			DetachedCriteria criteria = DetachedCriteria.For<OrderLocation>()
				.Add("LocationType", type)
				.Add("OrderHeader", this);
			this.OrderLocation = Repositories.Get<OrderLocation>().Retrieve(criteria);
		}

		public virtual void FindSuffixes()
		{
			DetachedCriteria criteria = DetachedCriteria.For<OrderHeader>().Add("OrderCode", _orderCode);
			this.HasSuffixes = (Repositories.Get<OrderHeader>().Count(criteria) > 1);
		}

		public virtual String OrderCodeSuffix()
		{
			if (String.IsNullOrEmpty(_orderSuffix)) return _orderCode;
			else return String.Format("{0}-{1}", _orderCode, _orderSuffix);
		}

		public virtual void Pick(Location location)
		{
			foreach (CartonHeader header in this.CartonHeaders) header.Pick(location);
		}

		public virtual void PrintBillOfLading(PrinterTypePrinter printer)
		{
			String report = BusinessRule.RetrieveString("24");
			if (String.IsNullOrEmpty(report)) return;
			//
			this.FindCurrentStatus();
			if (!this.CurrentStatus.SameAs(Entity.Retrieve<StatusCode>(Upp.Irms.Constants.OrderStatuses.Shipped))) return;
			//
			Dictionary<String, String> parameters = new Dictionary<String, String>();
			parameters.Add("OrderCode", _orderCode);
			ReportRequestHeader request = ReportRequestHeader.Create(report, parameters, printer, 1);
			Repositories.Get<ReportRequestHeader>().Add(request);
			//
			Task task = Task.Create(request);
			Repositories.Get<Task>().Add(task);
		}

		public virtual void PrintPackingList(PrinterTypePrinter printer, string level)
		{
			if (printer == null) return;
			//
			Dictionary<String, String> parameters = new Dictionary<String, String>();
			parameters.Add("Company", Registry.Find<Company>().CompanyCode);
			parameters.Add("OrderCode", this.OrderCodeSuffix());
			parameters.Add("Warehouse", Registry.Find<CompanyLocationType>().CompanyLocationCode);
			//
			String customer = this.FindCustomerCode();
			String report = null;
			if (!String.IsNullOrEmpty(customer))
			{
				parameters.Add("CustomerCode", customer);
				//
				DetachedCriteria criteria = DetachedCriteria.For<ReportFormat>()
					.CreateAlias("Customer", "c")
					.CreateAlias("ReportType", "rt")
					.Add(Expression.Eq("Active", "A"))
					.Add(Expression.Eq("c.CustomerCode", customer))
					.Add(Expression.Eq("rt.ReportTypeCode", CodeValue.GetCode(ReportTypes.PackingList)))
					.SetMaxResults(1);
				ReportFormat format = Repositories.Get<ReportFormat>().Retrieve(criteria);
				if (format != null) report = format.Report.ApplicationModule.Module.ModuleCode;
			}
			//
			if (String.IsNullOrEmpty(report)) report = BusinessRule.RetrieveString("1049");
			if (String.IsNullOrEmpty(report)) report = CodeValue.GetCode(Reports.PackingList);
			//
			ReportRequestHeader request = ReportRequestHeader.Create(report, parameters, printer, 1);
			Repositories.Get<Task>().Add(Task.Create(request));
		}

		public virtual void Ship()
		{
			if (this.Dock == null) return;
			//
			this.Dock.FindShipmentHeader();
			if (this.Dock.ShipmentHeader == null) return;
			//
			LicenseType carton = Entity.Retrieve<LicenseType>(InventoryLicenseTypes.Carton);
			StatusCode closed = Entity.Retrieve<StatusCode>(ShippingStatuses.Closed);
			StatusCode packed = Entity.Retrieve<StatusCode>(Upp.Irms.Constants.OrderStatuses.Packed);
			StatusCode shipped = Entity.Retrieve<StatusCode>(Upp.Irms.Constants.OrderStatuses.Shipped);
			this.ChangeStatus(shipped);
			//
			DetachedCriteria criteria = DetachedCriteria.For<OrderDetail>()
				.Add("OrderHeader", this);
			IList<OrderDetail> details = Repositories.Get<OrderDetail>().List(criteria);
			foreach (OrderDetail element in details)
			{
                StatusCode statusBeforeUpdate = element.StatusCode;

                element.DateModified = DateTime.Now;
				element.StatusCode = shipped;
				element.UserModified = Registry.Find<UserAccount>().UserName;
				Repositories.Get<OrderDetail>().Update(element);
                
                //Insert into orde_detail_status table
                Boolean WriteOrderDetailStatus = Converter.ToBoolean(BusinessRule.RetrieveBoolean("11558").Value);
                if (WriteOrderDetailStatus && statusBeforeUpdate != element.StatusCode)
                {
                    OrderDetailStatus created = Entity.Activate<OrderDetailStatus>();
                    created.OrderHeader = this;
                    created.OrderDetail = element;
                    created.StatusCode = element.StatusCode;
                    created.Occured = DateTime.Now;
                    created.Version = 1;
                    created.UserCreated = Registry.Find<UserAccount>().UserName;
                    Repositories.Get<OrderDetailStatus>().Add(created);

                }
            }
			//
			criteria = DetachedCriteria.For<ItemFulfillment>()
				.Add("OrderDetail.OrderHeader", this);
			IList<ItemFulfillment> allocations = Repositories.Get<ItemFulfillment>().List(criteria);
			foreach (ItemFulfillment element in allocations)
			{
				element.DateModified = DateTime.Now;
				element.StatusCode = shipped;
				element.UserModified = Registry.Find<UserAccount>().UserName;
				Repositories.Get<ItemFulfillment>().Update(element);
			}
			if (allocations.Count > 0 && allocations[0].Wave != null) allocations[0].Wave.Close();
			//
			criteria = DetachedCriteria.For<InventoryPick>()
				.Add("ItemFulfillment.OrderDetail.OrderHeader", this)
				.SetFetchMode("LicensePlate", FetchMode.Join)
				.SetFetchMode("LicensePlate.LicenseType", FetchMode.Join);
			IList<InventoryPick> picks = Repositories.Get<InventoryPick>().List(criteria);
			foreach (InventoryPick element in picks)
			{
				element.DateModified = DateTime.Now;
				element.StatusCode = shipped;
				element.UserModified = Registry.Find<UserAccount>().UserName;
				Repositories.Get<InventoryPick>().Update(element);
			}
			//
			shipped = Entity.Retrieve<StatusCode>(LicensePlateStatuses.Shipped);
			foreach (InventoryPick element in picks)
			{
				if (element.LicensePlate == null) continue;
				element.LicensePlate.ChangeStatus(shipped);
				//
				ShipmentDetail created = Entity.Activate<ShipmentDetail>();
				created.LicensePlate = element.LicensePlate;
				created.Quantity = 1;
				created.ShipmentHeader = this.Dock.ShipmentHeader;
				created.StatusCode = closed;
				//
				if (carton.SameAs(element.LicensePlate.LicenseType))
				{
					criteria = DetachedCriteria.For<CartonHeader>()
						.Add("LicensePlate", element.LicensePlate)
						.SetMaxResults(1);
					CartonHeader header = Repositories.Get<CartonHeader>().Retrieve(criteria);
					if (header != null)
					{
						header.ChangeStatus(shipped);
						created.CartonHeader = header;
					}
				}
				//
				Repositories.Get<ShipmentDetail>().Add(created);
			}
			//
			if (this.LpnCount.HasValue)
			{
                Boolean? chepCount = BusinessRule.RetrieveBoolean("11026");
                if (chepCount.HasValue && chepCount.Value)
                {
                    ReferenceCode chep = Entity.Retrieve<ReferenceCode>(OrderReferences.Chep);
                    this.CreateReference(chep, this.LpnCount.Value);
                }
                else
                {
                    Boolean? pecoCount = BusinessRule.RetrieveBoolean("11261");
                    if (pecoCount.HasValue && pecoCount.Value)
                    {
                        ReferenceCode peco = Entity.Retrieve<ReferenceCode>(OrderReferences.Peco);
                        this.CreateReference(peco, this.LpnCount.Value);
                    }
                }
			}
		}

		public virtual Boolean ShouldUpdateStatus()
		{
			StatusCode current = Entity.RetrieveStatus<OrderHeader, OrderDetail>(this, FunctionalAreas.Order);
			if (current == null) return false;
			//
			this.FindCurrentStatus();
			return (this.CurrentStatus == null || current.SortOrder != this.CurrentStatus.SortOrder);
		}

		public virtual void UndoPick(Location location, LicensePlate plate)
		{
			DetachedCriteria criteria = DetachedCriteria.For<InventoryPick>()
				.Add("OrderDetail.OrderHeader.OrderCode", _orderCode)
				.Add("Quantity", ">", 0);
			if (!String.IsNullOrEmpty(_orderSuffix)) criteria = criteria.Add("OrderDetail.OrderHeader.OrderSuffix", _orderSuffix);
			IList<InventoryPick> picks = Repositories.Get<InventoryPick>().List(criteria);
			foreach (InventoryPick element in picks) element.UndoPick(location, plate, element.Quantity);
			if (picks.Count > 0 && picks[0].ItemFulfillment.Wave != null) picks[0].ItemFulfillment.Wave.Open();
			/*
			this.ChangeStatus(Entity.Retrieve<StatusCode>(Upp.Irms.Constants.OrderStatuses.Distributed));*/
		}

		public virtual void UpdateStatus()
		{
			Entity.UpdateStatus<OrderHeader, OrderDetail>(this, FunctionalAreas.Order);
		}

		#endregion

        #region Public.ThirdPartyLogistics

        public virtual void FindOrderInfo()
        {
            DetachedCriteria criteria = DetachedCriteria.For<CartonHeader>()
                .Add("OrderHeader", this);
            this.CartonCount = Repositories.Get<CartonHeader>().Count(criteria);
            //
            LocationType ship = Entity.Retrieve<LocationType>(OrganizationLocationTypes.ShipTo);
            criteria = DetachedCriteria.For<OrderLocation>()
                .Add(Expression.Eq("LocationType", ship))
                .Add(Expression.Eq("OrderHeader", this))
                .SetMaxResults(1);
            OrderLocation location = Repositories.Get<OrderLocation>().Retrieve(criteria);
            if (location != null) this.DestinationPostalCode = (location.ZipCode != null ? location.ZipCode.Code : location.AddressZipCode);
        }

        public virtual void UpdateCarrier()
        {
            DetachedCriteria criteria = DetachedCriteria.For<Carrier>()
                .Add(Expression.Eq("CarrierCode", this.CarrierCode))
                .Add(Expression.Eq("CompanyLocationType", Registry.Find<CompanyLocationType>()))
                .SetMaxResults(1);
            _carrier = Repositories.Get<Carrier>().Retrieve(criteria);
            //
            if (_carrier == null) throw new Exception("Carrier not found with Carrier Code:" + this.CarrierCode);
        }

        public virtual void CreateOrderCharges(decimal charges, decimal customerCharges)
        {
            OrderCharge customerOrderCharges = Entity.Activate<OrderCharge>();
            customerOrderCharges.Amount = customerCharges;
            customerOrderCharges.BillingCode = Entity.Retrieve<BillingCode>(BillingCodes.CustomerCharges);
            customerOrderCharges.OrderHeader = this;
            //
            Repositories.Get<OrderCharge>().Add(customerOrderCharges);

            OrderCharge orderCharges = Entity.Activate<OrderCharge>();
            orderCharges.Amount = charges;
            orderCharges.BillingCode = Entity.Retrieve<BillingCode>(BillingCodes.Charges);
            orderCharges.OrderHeader = this;
            //
            Repositories.Get<OrderCharge>().Add(orderCharges);
        }
        
        #endregion
    }
}
