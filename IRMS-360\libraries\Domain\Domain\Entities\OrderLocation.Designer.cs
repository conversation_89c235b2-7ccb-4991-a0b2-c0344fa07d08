using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class OrderLocation : Entity
	{
		#region Fields

		private CountryCode _countryCode;
		private LocationType _locationType;
		private OrderHeader _orderHeader;
		private String _addressDirection;
		private String _addressLine1;
		private String _addressLine2;
		private String _addressLine3;
		private String _addressNumber;
		private String _addressSuffix;
		private String _addressZipCode;
		private String _businessName;
		private String _city;
		private String _contactEmailPrimary;
		private String _contactEmailSecondary;
		private String _contactName;
		private String _contactPhonePrimary;
		private String _contactPhoneSecondary;
		private String _country;
		private String _customerCode;
		private String _firstName;
		private String _lastName;
        private String _san;
		private String _stateCode;
		private ZipCode _zipCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual CountryCode CountryCode
		{
			get { return _countryCode; }
			set { _countryCode = value; }
		}

		[DataMember]
		public virtual LocationType LocationType
		{
			get { return _locationType; }
			set { _locationType = value; }
		}

		[DataMember]
		public virtual OrderHeader OrderHeader
		{
			get { return _orderHeader; }
			set { _orderHeader = value; }
		}

		[DataMember]
		public virtual String AddressDirection
		{
			get { return _addressDirection; }
			set { _addressDirection = value; }
		}

		[DataMember]
		public virtual String AddressLine1
		{
			get { return _addressLine1; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("AddressLine1 must not be blank or null.");
				else _addressLine1 = value;
			}
		}

		[DataMember]
		public virtual String AddressLine2
		{
			get { return _addressLine2; }
			set { _addressLine2 = value; }
		}

		[DataMember]
		public virtual String AddressLine3
		{
			get { return _addressLine3; }
			set { _addressLine3 = value; }
		}

		[DataMember]
		public virtual String AddressNumber
		{
			get { return _addressNumber; }
			set { _addressNumber = value; }
		}

		[DataMember]
		public virtual String AddressSuffix
		{
			get { return _addressSuffix; }
			set { _addressSuffix = value; }
		}

		[DataMember]
		public virtual String AddressZipCode
		{
			get { return _addressZipCode; }
			set { _addressZipCode = value; }
		}

		[DataMember]
		public virtual String BusinessName
		{
			get { return _businessName; }
			set { _businessName = value; }
		}

		[DataMember]
		public virtual String City
		{
			get { return _city; }
			set { _city = value; }
		}

		[DataMember]
		public virtual String ContactEmailPrimary
		{
			get { return _contactEmailPrimary; }
			set { _contactEmailPrimary = value; }
		}

		[DataMember]
		public virtual String ContactEmailSecondary
		{
			get { return _contactEmailSecondary; }
			set { _contactEmailSecondary = value; }
		}

		[DataMember]
		public virtual String ContactName
		{
			get { return _contactName; }
			set { _contactName = value; }
		}

		[DataMember]
		public virtual String ContactPhonePrimary
		{
			get { return _contactPhonePrimary; }
			set { _contactPhonePrimary = value; }
		}

		[DataMember]
		public virtual String ContactPhoneSecondary
		{
			get { return _contactPhoneSecondary; }
			set { _contactPhoneSecondary = value; }
		}

		[DataMember]
		public virtual String Country
		{
			get { return _country; }
			set { _country = value; }
		}

		[DataMember]
		public virtual String CustomerCode
		{
			get { return _customerCode; }
			set { _customerCode = value; }
		}

		[DataMember]
		public virtual String FirstName
		{
			get { return _firstName; }
			set { _firstName = value; }
		}

		[DataMember]
		public virtual String LastName
		{
			get { return _lastName; }
			set { _lastName = value; }
		}

        [DataMember]
        public virtual String San
        {
            get { return _san; }
            set { _san = value; }
        }

		[DataMember]
		public virtual String StateCode
		{
			get { return _stateCode; }
			set { _stateCode = value; }
		}

		[DataMember]
		public virtual ZipCode ZipCode
		{
			get { return _zipCode; }
			set { _zipCode = value; }
		}


		#endregion
	}
}
