using System;
using System.Runtime.Serialization;

using NHibernate;
using NHibernate.Criterion;

using Upp.Irms.Constants;
using Upp.Irms.Core;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class OrderLocation : Entity
	{
		#region Properties.Reports
        public virtual Int32? OrderId { get; set; }
        public virtual Int32? OrderHeaderId { get; set; }
        public virtual String AddressCity { get; set; }
		public virtual String AddressCountry { get; set; }
		public virtual String AddressStateCode { get; set; }
		public virtual String CustomerName { get; set; }
		public virtual String FunctionalAreaCode { get; set; }
		public virtual String LocationTypeCode { get; set; }
		public virtual String UpsHubCode { get; set; }
        public virtual Int32? ZipCodeId { get; set; }
        public virtual String ZipCodeCity { get; set; }
        public virtual String ZipCodeState { get; set; }
        public virtual String ZipCodeCode { get; set; }
        public virtual String LocationZipCode { get; set; }
        public virtual String ZipCodeCountry { get; set; }

		#endregion

		#region Constructor

		public OrderLocation()
		{
			//
		}

		#endregion

		#region Methods.Private

		private void FindUpsHubCode(string code)
		{
			DetachedCriteria criteria = DetachedCriteria.For<UpsHubCode>()
				.Add("ZipBegin", "<=", code)
				.Add("ZipEnd", ">=", code)
				.Add(Expression.Sql("len(zip_begin) = ?", code.Length, NHibernateUtil.Int32))
				.SetProjection(Projections.Property("HubCode"));
			this.UpsHubCode = Repositories.Get<UpsHubCode>().Function<String>(criteria);
		}      

		#endregion

		#region Methods.Public

		public virtual void FindUpsHubCode()
		{
			if (String.IsNullOrEmpty(_addressZipCode) && _zipCode == null) return;
			//
			LocationType ship = Entity.Retrieve<LocationType>(OrganizationLocationTypes.ShipTo);
			if (!ship.Equals(_locationType)) return;
			//
			String code = String.IsNullOrEmpty(_addressZipCode) ? _zipCode.Code : _addressZipCode.Substring(0, 5);
			this.FindUpsHubCode(code);
			//
			if (String.IsNullOrEmpty(this.UpsHubCode))
			{
				code = code.Substring(0, 4);
				this.FindUpsHubCode(code);
				if (String.IsNullOrEmpty(this.UpsHubCode))
				{
					code = code.Substring(0, 3);
					this.FindUpsHubCode(code);
				}
			}
		}

		public virtual String Name()
		{
			if (!String.IsNullOrEmpty(_businessName)) return _businessName;
			else return String.Format("{0} {1}", _firstName, _lastName);
		}

		#endregion
	}
}
