using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class OrderReferenceCode : Entity
	{
		#region Fields

		private CustomerShipmentReference _customerShipmentReference;
		private OrderDetail _orderDetail;
		private OrderHeader _orderHeader;
		private ReferenceCode _referenceCode;
		private String _comments;
		private String _referenceValue;

		#endregion

		#region Properties

		[DataMember]
		public virtual CustomerShipmentReference CustomerShipmentReference
		{
			get { return _customerShipmentReference; }
			set { _customerShipmentReference = value; }
		}

		[DataMember]
		public virtual OrderDetail OrderDetail
		{
			get { return _orderDetail; }
			set { _orderDetail = value; }
		}

		[DataMember]
		public virtual OrderHeader OrderHeader
		{
			get { return _orderHeader; }
			set { _orderHeader = value; }
		}

		[DataMember]
		public virtual ReferenceCode ReferenceCode
		{
			get { return _referenceCode; }
			set { _referenceCode = value; }
		}

		[DataMember]
		public virtual String Comments
		{
			get { return _comments; }
			set { _comments = value; }
		}

		[DataMember]
		public virtual String ReferenceValue
		{
			get { return _referenceValue; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ReferenceValue must not be blank or null.");
				else _referenceValue = value;
			}
		}


		#endregion
	}
}
