using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class OrderReferenceCode : Entity
	{
		#region Constructor

		public OrderReferenceCode()
		{
			//
		}

		#endregion
        #region Properties.Reports
        public virtual Int32? ItemsId { get; set; }
        public virtual Int32? OrderId { get; set; }
        public virtual Int32? OrderLineId { get; set; }
        public virtual String ReferenceCodeCode { get; set; }
        public virtual String ReferenceCodeFAC { get; set; }
        #endregion
    }
}
