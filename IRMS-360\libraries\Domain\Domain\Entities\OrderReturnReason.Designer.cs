using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class OrderReturnReason : Entity
	{
		#region Fields

		private OrderDetail _orderDetail;
		private OrderHeader _orderHeader;
		private ReturnReason _returnReason;

		#endregion

		#region Properties

		[DataMember]
		public virtual OrderDetail OrderDetail
		{
			get { return _orderDetail; }
			set { _orderDetail = value; }
		}

		[DataMember]
		public virtual OrderHeader OrderHeader
		{
			get { return _orderHeader; }
			set { _orderHeader = value; }
		}

		[DataMember]
		public virtual ReturnReason ReturnReason
		{
			get { return _returnReason; }
			set { _returnReason = value; }
		}


		#endregion
	}
}
