using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class OrderService : Entity
	{
		#region Fields

		private BillingCode _billingCode;
		private BillingRate _billingRate;
		private DateTime _occurred;
		private Decimal _quantity;
		private Decimal _rate;
		private ICollection<InvoiceDetail> _invoiceDetails = new HashSet<InvoiceDetail>();
		private OrderDetail _orderDetail;
		private OrderHeader _orderHeader;
		private StatusCode _statusCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual BillingCode BillingCode
		{
			get { return _billingCode; }
			set { _billingCode = value; }
		}

		[DataMember]
		public virtual BillingRate BillingRate
		{
			get { return _billingRate; }
			set { _billingRate = value; }
		}

		[DataMember]
		public virtual DateTime Occurred
		{
			get { return _occurred; }
			set { _occurred = value; }
		}

		[DataMember]
		public virtual Decimal Quantity
		{
			get { return _quantity; }
			set { _quantity = value; }
		}

		[DataMember]
		public virtual Decimal Rate
		{
			get { return _rate; }
			set { _rate = value; }
		}

		[DataMember]
		public virtual ICollection<InvoiceDetail> InvoiceDetails
		{
			get { return _invoiceDetails; }
			set { _invoiceDetails = value; }
		}

		[DataMember]
		public virtual OrderDetail OrderDetail
		{
			get { return _orderDetail; }
			set { _orderDetail = value; }
		}

		[DataMember]
		public virtual OrderHeader OrderHeader
		{
			get { return _orderHeader; }
			set { _orderHeader = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
            get { return _statusCode != null ? _statusCode : StatusCode.GetOpenIntegrationStatusCode(); }
            set { _statusCode = value != null ? value : StatusCode.GetOpenIntegrationStatusCode(); }
        }


		#endregion
	}
}
