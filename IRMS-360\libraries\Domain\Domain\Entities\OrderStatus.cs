using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class OrderStatus : Entity
	{
		#region Constructor

		public OrderStatus()
		{
			//
		}

		#endregion

        #region Properties.Reports
        public virtual Int32? SortOrder { get; set; }
        public virtual String StatusCodeCode { get; set; }
        #endregion
    }
}
