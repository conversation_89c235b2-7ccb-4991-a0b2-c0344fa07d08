using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class OrganizationParticipant : Entity
	{
		#region Fields

		private Agency _agency;
		private AgencyLocationType _agencyLocationType;
		private AgencyOrganizationalUnit _agencyOrganizationalUnit;
		private Carrier _carrier;
		private CarrierLocationType _carrierLocationType;
		private Company _company;
		private CompanyLocationType _companyLocationType;
		private CompanyOrganizationalUnit _companyOrganizationalUnit;
		private Customer _customer;
		private CustomerLocationType _customerLocationType;
		private ICollection<Alert> _alerts = new HashSet<Alert>();
		private ICollection<EdiBatchControl> _ediBatchControls = new HashSet<EdiBatchControl>();
		private ICollection<InventoryPick> _inventoryPicks = new HashSet<InventoryPick>();
		private ICollection<InventoryTask> _inventoryTasks = new HashSet<InventoryTask>();
		private ICollection<Location> _locations = new HashSet<Location>();
		private ICollection<ParticipantAsset> _participantAssets = new HashSet<ParticipantAsset>();
		private ICollection<ParticipantEncounter> _participantEncounters = new HashSet<ParticipantEncounter>();
		private ICollection<ParticipantImmunization> _participantImmunizations = new HashSet<ParticipantImmunization>();
		private ICollection<ParticipantZone> _participantZones = new HashSet<ParticipantZone>();
		private ICollection<PurchaseOrderDetail> _purchaseOrderDetails = new HashSet<PurchaseOrderDetail>();
		private ICollection<ReceiptHeader> _receiptHeaders = new HashSet<ReceiptHeader>();
		private ICollection<RequisitionLocation> _requisitionLocations = new HashSet<RequisitionLocation>();
		private ICollection<Service> _services = new HashSet<Service>();
		private ICollection<ShipmentHeader> _shipmentHeaders = new HashSet<ShipmentHeader>();
		private ICollection<UserAccount> _userAccounts = new HashSet<UserAccount>();
		private ICollection<WaveAssignment> _waveAssignments = new HashSet<WaveAssignment>();
		private Manufacturer _manufacturer;
		private ManufacturerLocationType _manufacturerLocationType;
		private ParticipantRole _participantRole;
		private Provider _provider;
		private ProviderLocationType _providerLocationType;
		private ProviderOrganizationalUnit _providerOrganizationalUnit;
		private Shift _shift;
		private String _active;
		private String _organizationNpi;
		private Vendor _vendor;
		private VendorLocationType _vendorLocationType;

		#endregion

		#region Properties

		[DataMember]
		public virtual Agency Agency
		{
			get { return _agency; }
			set { _agency = value; }
		}

		[DataMember]
		public virtual AgencyLocationType AgencyLocationType
		{
			get { return _agencyLocationType; }
			set { _agencyLocationType = value; }
		}

		[DataMember]
		public virtual AgencyOrganizationalUnit AgencyOrganizationalUnit
		{
			get { return _agencyOrganizationalUnit; }
			set { _agencyOrganizationalUnit = value; }
		}

		[DataMember]
		public virtual Carrier Carrier
		{
			get { return _carrier; }
			set { _carrier = value; }
		}

		[DataMember]
		public virtual CarrierLocationType CarrierLocationType
		{
			get { return _carrierLocationType; }
			set { _carrierLocationType = value; }
		}

		[DataMember]
		public virtual Company Company
		{
			get { return _company; }
			set { _company = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual CompanyOrganizationalUnit CompanyOrganizationalUnit
		{
			get { return _companyOrganizationalUnit; }
			set { _companyOrganizationalUnit = value; }
		}

		[DataMember]
		public virtual Customer Customer
		{
			get { return _customer; }
			set { _customer = value; }
		}

		[DataMember]
		public virtual CustomerLocationType CustomerLocationType
		{
			get { return _customerLocationType; }
			set { _customerLocationType = value; }
		}

		[DataMember]
		public virtual ICollection<Alert> Alerts
		{
			get { return _alerts; }
			set { _alerts = value; }
		}

		[DataMember]
		public virtual ICollection<EdiBatchControl> EdiBatchControls
		{
			get { return _ediBatchControls; }
			set { _ediBatchControls = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryPick> InventoryPicks
		{
			get { return _inventoryPicks; }
			set { _inventoryPicks = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryTask> InventoryTasks
		{
			get { return _inventoryTasks; }
			set { _inventoryTasks = value; }
		}

		[DataMember]
		public virtual ICollection<Location> Locations
		{
			get { return _locations; }
			set { _locations = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantAsset> ParticipantAssets
		{
			get { return _participantAssets; }
			set { _participantAssets = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantEncounter> ParticipantEncounters
		{
			get { return _participantEncounters; }
			set { _participantEncounters = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantImmunization> ParticipantImmunizations
		{
			get { return _participantImmunizations; }
			set { _participantImmunizations = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantZone> ParticipantZones
		{
			get { return _participantZones; }
			set { _participantZones = value; }
		}

		[DataMember]
		public virtual ICollection<PurchaseOrderDetail> PurchaseOrderDetails
		{
			get { return _purchaseOrderDetails; }
			set { _purchaseOrderDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ReceiptHeader> ReceiptHeaders
		{
			get { return _receiptHeaders; }
			set { _receiptHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<RequisitionLocation> RequisitionLocations
		{
			get { return _requisitionLocations; }
			set { _requisitionLocations = value; }
		}

		[DataMember]
		public virtual ICollection<Service> Services
		{
			get { return _services; }
			set { _services = value; }
		}

		[DataMember]
		public virtual ICollection<ShipmentHeader> ShipmentHeaders
		{
			get { return _shipmentHeaders; }
			set { _shipmentHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<UserAccount> UserAccounts
		{
			get { return _userAccounts; }
			set { _userAccounts = value; }
		}

		[DataMember]
		public virtual ICollection<WaveAssignment> WaveAssignments
		{
			get { return _waveAssignments; }
			set { _waveAssignments = value; }
		}

		[DataMember]
		public virtual Manufacturer Manufacturer
		{
			get { return _manufacturer; }
			set { _manufacturer = value; }
		}

		[DataMember]
		public virtual ManufacturerLocationType ManufacturerLocationType
		{
			get { return _manufacturerLocationType; }
			set { _manufacturerLocationType = value; }
		}

		[DataMember]
		public virtual ParticipantRole ParticipantRole
		{
			get { return _participantRole; }
			set { _participantRole = value; }
		}

		[DataMember]
		public virtual Provider Provider
		{
			get { return _provider; }
			set { _provider = value; }
		}

		[DataMember]
		public virtual ProviderLocationType ProviderLocationType
		{
			get { return _providerLocationType; }
			set { _providerLocationType = value; }
		}

		[DataMember]
		public virtual ProviderOrganizationalUnit ProviderOrganizationalUnit
		{
			get { return _providerOrganizationalUnit; }
			set { _providerOrganizationalUnit = value; }
		}

		[DataMember]
		public virtual Shift Shift
		{
			get { return _shift; }
			set { _shift = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String OrganizationNpi
		{
			get { return _organizationNpi; }
			set { _organizationNpi = value; }
		}

		[DataMember]
		public virtual Vendor Vendor
		{
			get { return _vendor; }
			set { _vendor = value; }
		}

		[DataMember]
		public virtual VendorLocationType VendorLocationType
		{
			get { return _vendorLocationType; }
			set { _vendorLocationType = value; }
		}


		#endregion
	}
}
