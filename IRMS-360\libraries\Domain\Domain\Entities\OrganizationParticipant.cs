using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using NHibernate.Criterion;

using Upp.Irms.Constants;
using Upp.Irms.Core;
using Upp.Shared.Utilities;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class OrganizationParticipant : Entity
	{
		#region Properties

		[DataMember]
		public virtual DateTime? ReturnDate { get; set; }
		[DataMember]
		public virtual Decimal? IssuedQuantity { get; set; }
		[DataMember]
		public virtual String FirstName { get; set; }
		[DataMember]
		public virtual String FullName { get; set; }
		[DataMember]
		public virtual String LastName { get; set; }
		[DataMember]
		public virtual String MiddleName { get; set; }
		[DataMember]
		public virtual String OrganizationDescription { get; set; }
		[DataMember]
		public virtual String RoleCode { get; set; }
		[DataMember]
		public virtual String RoleDescription { get; set; }
		[DataMember]
		public virtual Task Task { get; set; }

		#endregion

		#region Constructor

		public OrganizationParticipant()
		{
			//
		}

		#endregion

		#region Methods.Public.Assets

		public virtual void CreateCycleCount()
		{
			TransactionType cycle = Entity.Retrieve<TransactionType>(InventoryTransactions.CycleCountEmployee);
			this.Task = Task.Create(cycle);
			this.Task.Started = DateTime.Now;
			this.Task.StatusCode = Entity.Retrieve<StatusCode>(TaskStatuses.Active);
			Repositories.Get<Task>().Add(this.Task);
			//
			InventoryTask main = InventoryTask.Create(this.Task);
			main.OrganizationParticipant = this;
			main.Quantity = 0;
			Repositories.Get<InventoryTask>().Add(main);
			//
			DetachedCriteria criteria = DetachedCriteria.For<ParticipantAsset>()
				.Add("OrganizationParticipant", this)
				.Add("Quantity", ">", 0);
			if (Registry.Find<AgencyOrganizationalUnit>() != null) criteria = criteria.Add("InventoryItem.AgencyOrganizationalUnit", Registry.Find<AgencyOrganizationalUnit>());
			else if (Registry.Find<Agency>() != null)
			{
				Agency agency = Registry.Find<Agency>();
				criteria = criteria.AddOr("InventoryItem.AgencyOrganizationalUnit.Agency", agency,
					"InventoryItem.AgencyOrganizationalUnit.AgencyLocation.Agency", agency);
			}
			IList<ParticipantAsset> issuances = Repositories.Get<ParticipantAsset>().List(criteria);
			foreach (ParticipantAsset element in issuances) element.SetCycleCount(this.Task);
		}

		public virtual void FindIssuedQuantity(InventoryItem asset)
		{
			DetachedCriteria criteria = DetachedCriteria.For<ParticipantAsset>()
				.Add("InventoryItem", asset)
				.Add("OrganizationParticipant", this)
				.Add("Quantity", ">", 0);
			IList<ParticipantAsset> issuances = Repositories.Get<ParticipantAsset>().List(criteria);
			if (issuances.Count == 1)
			{
				this.IssuedQuantity = issuances[0].Quantity;
				this.ReturnDate = issuances[0].ExpectedReturn;
			}
			else throw new Exception(String.Format("{0} is not checked out to {1}.", asset.AssetCode, this.FullName));

		}
		#endregion
	}
}
