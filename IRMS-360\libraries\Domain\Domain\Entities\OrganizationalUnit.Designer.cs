using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class OrganizationalUnit : Entity
	{
		#region Fields

		private ICollection<AgencyOrganizationalUnit> _agencyOrganizationalUnits = new HashSet<AgencyOrganizationalUnit>();
		private ICollection<CompanyOrganizationalUnit> _companyOrganizationalUnits = new HashSet<CompanyOrganizationalUnit>();
		private ICollection<ProviderOrganizationalUnit> _providerOrganizationalUnits = new HashSet<ProviderOrganizationalUnit>();
		private String _active;
		private String _description;
		private String _organizationalUnitCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<AgencyOrganizationalUnit> AgencyOrganizationalUnits
		{
			get { return _agencyOrganizationalUnits; }
			set { _agencyOrganizationalUnits = value; }
		}

		[DataMember]
		public virtual ICollection<CompanyOrganizationalUnit> CompanyOrganizationalUnits
		{
			get { return _companyOrganizationalUnits; }
			set { _companyOrganizationalUnits = value; }
		}

		[DataMember]
		public virtual ICollection<ProviderOrganizationalUnit> ProviderOrganizationalUnits
		{
			get { return _providerOrganizationalUnits; }
			set { _providerOrganizationalUnits = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String OrganizationalUnitCode
		{
			get { return _organizationalUnitCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("OrganizationalUnitCode must not be blank or null.");
				else _organizationalUnitCode = value;
			}
		}


		#endregion
	}
}
