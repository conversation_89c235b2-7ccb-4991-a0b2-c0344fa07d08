using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class OrganizationalUnitType : Entity
	{
		#region Fields

		private ICollection<CompanyOrganizationalUnit> _companyOrganizationalUnits = new HashSet<CompanyOrganizationalUnit>();
		private String _active;
		private String _description;
		private String _organizationalUnitTypeCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<CompanyOrganizationalUnit> CompanyOrganizationalUnits
		{
			get { return _companyOrganizationalUnits; }
			set { _companyOrganizationalUnits = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String OrganizationalUnitTypeCode
		{
			get { return _organizationalUnitTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("OrganizationalUnitTypeCode must not be blank or null.");
				else _organizationalUnitTypeCode = value;
			}
		}


		#endregion
	}
}
