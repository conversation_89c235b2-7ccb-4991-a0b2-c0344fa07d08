using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Parameter : Entity
	{
		#region Fields

		private FunctionalAreaCode _functionalAreaCode;
		private ICollection<BusinessRuleParameterValue> _businessRuleParameterValues = new HashSet<BusinessRuleParameterValue>();
		private ICollection<JobParameter> _jobParameters = new HashSet<JobParameter>();
		private ICollection<ParameterValue> _parameterValues = new HashSet<ParameterValue>();
		private ICollection<ReportParameter> _reportParameters = new HashSet<ReportParameter>();
		private String _active;
		private String _dataType;
		private String _description;
		private String _label;
		private String _parameterCode;
		private String _presentationType;

		#endregion

		#region Properties

		[DataMember]
		public virtual FunctionalAreaCode FunctionalAreaCode
		{
			get { return _functionalAreaCode; }
			set { _functionalAreaCode = value; }
		}

		[DataMember]
		public virtual ICollection<BusinessRuleParameterValue> BusinessRuleParameterValues
		{
			get { return _businessRuleParameterValues; }
			set { _businessRuleParameterValues = value; }
		}

		[DataMember]
		public virtual ICollection<JobParameter> JobParameters
		{
			get { return _jobParameters; }
			set { _jobParameters = value; }
		}

		[DataMember]
		public virtual ICollection<ParameterValue> ParameterValues
		{
			get { return _parameterValues; }
			set { _parameterValues = value; }
		}

		[DataMember]
		public virtual ICollection<ReportParameter> ReportParameters
		{
			get { return _reportParameters; }
			set { _reportParameters = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String DataType
		{
			get { return _dataType; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("DataType must not be blank or null.");
				else _dataType = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String Label
		{
			get { return _label; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Label must not be blank or null.");
				else _label = value;
			}
		}

		[DataMember]
		public virtual String ParameterCode
		{
			get { return _parameterCode; }
			set { _parameterCode = value; }
		}

		[DataMember]
		public virtual String PresentationType
		{
			get { return _presentationType; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("PresentationType must not be blank or null.");
				else _presentationType = value;
			}
		}


		#endregion
	}
}
