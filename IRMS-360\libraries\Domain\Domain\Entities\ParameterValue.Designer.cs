using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ParameterValue : Entity
	{
		#region Fields

		private ICollection<ReportRequestDetail> _reportRequestDetails = new HashSet<ReportRequestDetail>();
		private Parameter _parameter;
		private String _active;
		private String _value;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<ReportRequestDetail> ReportRequestDetails
		{
			get { return _reportRequestDetails; }
			set { _reportRequestDetails = value; }
		}

		[DataMember]
		public virtual Parameter Parameter
		{
			get { return _parameter; }
			set { _parameter = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Value
		{
			get { return _value; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Value must not be blank or null.");
				else _value = value;
			}
		}


		#endregion
	}
}
