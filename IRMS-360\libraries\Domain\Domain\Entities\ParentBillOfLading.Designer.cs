using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ParentBillOfLading : Entity
	{
		#region Fields

		private Carrier _carrier;
		private CarrierService _carrierService;
		private ICollection<OrderHeader> _orderHeaders = new HashSet<OrderHeader>();
		private String _addressLine1;
		private String _addressLine2;
		private String _addressLine3;
		private String _billOfLading;
		private String _city;
		private String _countryCode;
		private String _proNumber;
		private String _shipToName;
		private String _stateCode;
		private String _zip;
		private ZipCode _zipCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual Carrier Carrier
		{
			get { return _carrier; }
			set { _carrier = value; }
		}

		[DataMember]
		public virtual CarrierService CarrierService
		{
			get { return _carrierService; }
			set { _carrierService = value; }
		}

		[DataMember]
		public virtual ICollection<OrderHeader> OrderHeaders
		{
			get { return _orderHeaders; }
			set { _orderHeaders = value; }
		}

		[DataMember]
		public virtual String AddressLine1
		{
			get { return _addressLine1; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("AddressLine1 must not be blank or null.");
				else _addressLine1 = value;
			}
		}

		[DataMember]
		public virtual String AddressLine2
		{
			get { return _addressLine2; }
			set { _addressLine2 = value; }
		}

		[DataMember]
		public virtual String AddressLine3
		{
			get { return _addressLine3; }
			set { _addressLine3 = value; }
		}

		[DataMember]
		public virtual String BillOfLading
		{
			get { return _billOfLading; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("BillOfLading must not be blank or null.");
				else _billOfLading = value;
			}
		}

		[DataMember]
		public virtual String City
		{
			get { return _city; }
			set { _city = value; }
		}

		[DataMember]
		public virtual String CountryCode
		{
			get { return _countryCode; }
			set { _countryCode = value; }
		}

		[DataMember]
		public virtual String ProNumber
		{
			get { return _proNumber; }
			set { _proNumber = value; }
		}

		[DataMember]
		public virtual String ShipToName
		{
			get { return _shipToName; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ShipToName must not be blank or null.");
				else _shipToName = value;
			}
		}

		[DataMember]
		public virtual String StateCode
		{
			get { return _stateCode; }
			set { _stateCode = value; }
		}

		[DataMember]
		public virtual String Zip
		{
			get { return _zip; }
			set { _zip = value; }
		}

		[DataMember]
		public virtual ZipCode ZipCode
		{
			get { return _zipCode; }
			set { _zipCode = value; }
		}


		#endregion
	}
}
