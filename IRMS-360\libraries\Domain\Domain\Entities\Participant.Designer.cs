using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Participant : Entity
	{
		#region Fields

		private BloodType _bloodType;
		private ColorCode _eyeColorCode;
		private ColorCode _hairColorCode;
        private CompanyOrganizationalUnit _companyOrganizationalUnit;
        private DateTime? _dob;
		private DateTime? _terminated;
		private Decimal? _heightMajor;
		private Decimal? _householdIncome;
		private Decimal? _weightMajor;
		private Ethnicity _ethnicity;
		private Gender _gender;
		private Int32? _chestSize;
		private Int32? _ezemrxPatientid;
		private Int32? _heightMinor;
		private Int32? _householdSize;
		private Int32? _inseamSize;
		private Int32? _neckSize;
		private Int32? _waistSize;
		private Decimal? _weightMinor;
		private JobTitle _jobTitle;
		private Language _language;
		private ICollection<AlternateJobTitle> _alternateJobTitles = new HashSet<AlternateJobTitle>();
		private ICollection<Participant> _childParticipants = new HashSet<Participant>();
		private ICollection<ParticipantCommunication> _participantCommunications = new HashSet<ParticipantCommunication>();
		private ICollection<ParticipantEncounter> _participantEncounters = new HashSet<ParticipantEncounter>();
		private ICollection<ParticipantEncounterNote> _participantEncounterNotes = new HashSet<ParticipantEncounterNote>();
		private ICollection<ParticipantInsurance> _participantInsurances = new HashSet<ParticipantInsurance>();
		private ICollection<ParticipantInventoryItem> _participantInventoryItems = new HashSet<ParticipantInventoryItem>();
		private ICollection<ParticipantLocation> _participantLocations = new HashSet<ParticipantLocation>();
		private ICollection<ParticipantRole> _participantRoles = new HashSet<ParticipantRole>();
		private Participant _parentParticipant;
		private Race _race;
		private RelationshipCode _relationshipCode;
		private Religion _religion;
		private StatusCode _statusCode;
		private String _active;
		private String _budgetPositionNumber;
		private String _contaminated;
		private String _driversLicense;
		private String _employeeCode;
		private String _employerName;
		private String _employerPhone;
		private String _firstName;
		private String _lastName;
		private String _maritalStatus;
		private String _medicaidNumber;
		private String _middleName;
		private String _notes;
		private String _npi;
		private String _reportingExempt;
        private String _shift;
        private String _shoeSize;
		private String _ssn;
		private String _udfField1;
		private String _udfField2;
		private String _udfField3;
		private UnitOfMeasure _heightMajorUom;
		private UnitOfMeasure _heightMinorUom;
		private UnitOfMeasure _sizeUom;
		private UnitOfMeasure _weightMajorUom;
		private UnitOfMeasure _weightMinorUom;

		#endregion

		#region Properties

		[DataMember]
		public virtual BloodType BloodType
		{
			get { return _bloodType; }
			set { _bloodType = value; }
		}

		[DataMember]
		public virtual ColorCode EyeColorCode
		{
			get { return _eyeColorCode; }
			set { _eyeColorCode = value; }
		}

		[DataMember]
		public virtual ColorCode HairColorCode
		{
			get { return _hairColorCode; }
			set { _hairColorCode = value; }
		}

        [DataMember]
        public virtual CompanyOrganizationalUnit CompanyOrganizationalUnit
        {
            get { return _companyOrganizationalUnit; }
            set { _companyOrganizationalUnit = value; }
        }

        [DataMember]
		public virtual DateTime? Dob
		{
			get { return _dob; }
			set { _dob = value; }
		}

		[DataMember]
		public virtual DateTime? Terminated
		{
			get { return _terminated; }
			set { _terminated = value; }
		}

		[DataMember]
		public virtual Decimal? HeightMajor
		{
			get { return _heightMajor; }
			set { _heightMajor = value; }
		}

		[DataMember]
		public virtual Decimal? HouseholdIncome
		{
			get { return _householdIncome; }
			set { _householdIncome = value; }
		}


		[DataMember]
		public virtual Decimal? WeightMajor
		{
			get { return _weightMajor; }
			set { _weightMajor = value; }
		}

		[DataMember]
		public virtual Ethnicity Ethnicity
		{
			get { return _ethnicity; }
			set { _ethnicity = value; }
		}

		[DataMember]
		public virtual Gender Gender
		{
			get { return _gender; }
			set { _gender = value; }
		}

		[DataMember]
		public virtual Int32? ChestSize
		{
			get { return _chestSize; }
			set { _chestSize = value; }
		}

		[DataMember]
		public virtual Int32? EzemrxPatientid
		{
			get { return _ezemrxPatientid; }
			set { _ezemrxPatientid = value; }
		}

		[DataMember]
		public virtual Int32? HeightMinor
		{
			get { return _heightMinor; }
			set { _heightMinor = value; }
		}

		[DataMember]
		public virtual Int32? HouseholdSize
		{
			get { return _householdSize; }
			set { _householdSize = value; }
		}

		[DataMember]
		public virtual Int32? InseamSize
		{
			get { return _inseamSize; }
			set { _inseamSize = value; }
		}

		[DataMember]
		public virtual Int32? NeckSize
		{
			get { return _neckSize; }
			set { _neckSize = value; }
		}

		[DataMember]
		public virtual Int32? WaistSize
		{
			get { return _waistSize; }
			set { _waistSize = value; }
		}

		[DataMember]
		public virtual Decimal? WeightMinor
		{
			get { return _weightMinor; }
			set { _weightMinor = value; }
		}

		[DataMember]
		public virtual JobTitle JobTitle
		{
			get { return _jobTitle; }
			set { _jobTitle = value; }
		}

		[DataMember]
		public virtual Language Language
		{
			get { return _language; }
			set { _language = value; }
		}

		[DataMember]
		public virtual ICollection<AlternateJobTitle> AlternateJobTitles
		{
			get { return _alternateJobTitles; }
			set { _alternateJobTitles = value; }
		}

		[DataMember]
		public virtual ICollection<Participant> ChildParticipants
		{
			get { return _childParticipants; }
			set { _childParticipants = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantCommunication> ParticipantCommunications
		{
			get { return _participantCommunications; }
			set { _participantCommunications = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantEncounter> ParticipantEncounters
		{
			get { return _participantEncounters; }
			set { _participantEncounters = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantEncounterNote> ParticipantEncounterNotes
		{
			get { return _participantEncounterNotes; }
			set { _participantEncounterNotes = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantInsurance> ParticipantInsurances
		{
			get { return _participantInsurances; }
			set { _participantInsurances = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantInventoryItem> ParticipantInventoryItems
		{
			get { return _participantInventoryItems; }
			set { _participantInventoryItems = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantLocation> ParticipantLocations
		{
			get { return _participantLocations; }
			set { _participantLocations = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantRole> ParticipantRoles
		{
			get { return _participantRoles; }
			set { _participantRoles = value; }
		}

		[DataMember]
		public virtual Participant ParentParticipant
		{
			get { return _parentParticipant; }
			set { _parentParticipant = value; }
		}

		[DataMember]
		public virtual Race Race
		{
			get { return _race; }
			set { _race = value; }
		}

		[DataMember]
		public virtual RelationshipCode RelationshipCode
		{
			get { return _relationshipCode; }
			set { _relationshipCode = value; }
		}

		[DataMember]
		public virtual Religion Religion
		{
			get { return _religion; }
			set { _religion = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String BudgetPositionNumber
		{
			get { return _budgetPositionNumber; }
			set { _budgetPositionNumber = value; }
		}

		[DataMember]
		public virtual String Contaminated
		{
			get { return _contaminated; }
			set { _contaminated = value; }
		}

		[DataMember]
		public virtual String DriversLicense
		{
			get { return _driversLicense; }
			set { _driversLicense = value; }
		}

		[DataMember]
		public virtual String EmployeeCode
		{
			get { return _employeeCode; }
			set { _employeeCode = value; }
		}

		[DataMember]
		public virtual String EmployerName
		{
			get { return _employerName; }
			set { _employerName = value; }
		}

		[DataMember]
		public virtual String EmployerPhone
		{
			get { return _employerPhone; }
			set { _employerPhone = value; }
		}

		[DataMember]
		public virtual String FirstName
		{
			get { return _firstName; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("FirstName must not be blank or null.");
				else _firstName = value;
			}
		}

		[DataMember]
		public virtual String LastName
		{
			get { return _lastName; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("LastName must not be blank or null.");
				else _lastName = value;
			}
		}

		[DataMember]
		public virtual String MaritalStatus
		{
			get { return _maritalStatus; }
			set { _maritalStatus = value; }
		}

		[DataMember]
		public virtual String MedicaidNumber
		{
			get { return _medicaidNumber; }
			set { _medicaidNumber = value; }
		}

		[DataMember]
		public virtual String MiddleName
		{
			get { return _middleName; }
			set { _middleName = value; }
		}

		[DataMember]
		public virtual String Notes
		{
			get { return _notes; }
			set { _notes = value; }
		}

		[DataMember]
		public virtual String Npi
		{
			get { return _npi; }
			set { _npi = value; }
		}

		[DataMember]
		public virtual String ReportingExempt
		{
			get { return _reportingExempt; }
			set { _reportingExempt = value; }
		}

        [DataMember]
        public virtual String Shift
        {
            get { return _shift; }
            set { _shift = value; }
        }

        [DataMember]
		public virtual String ShoeSize
		{
			get { return _shoeSize; }
			set { _shoeSize = value; }
		}

		[DataMember]
		public virtual String Ssn
		{
			get { return _ssn; }
			set { _ssn = value; }
		}

		[DataMember]
		public virtual String UdfField1
		{
			get { return _udfField1; }
			set { _udfField1 = value; }
		}

		[DataMember]
		public virtual String UdfField2
		{
			get { return _udfField2; }
			set { _udfField2 = value; }
		}

		[DataMember]
		public virtual String UdfField3
		{
			get { return _udfField3; }
			set { _udfField3 = value; }
		}

		[DataMember]
		public virtual UnitOfMeasure HeightMajorUom
		{
			get { return _heightMajorUom; }
			set { _heightMajorUom = value; }
		}

		[DataMember]
		public virtual UnitOfMeasure HeightMinorUom
		{
			get { return _heightMinorUom; }
			set { _heightMinorUom = value; }
		}

		[DataMember]
		public virtual UnitOfMeasure SizeUom
		{
			get { return _sizeUom; }
			set { _sizeUom = value; }
		}

		[DataMember]
		public virtual UnitOfMeasure WeightMajorUom
		{
			get { return _weightMajorUom; }
			set { _weightMajorUom = value; }
		}

		[DataMember]
		public virtual UnitOfMeasure WeightMinorUom
		{
			get { return _weightMinorUom; }
			set { _weightMinorUom = value; }
		}


		#endregion
	}
}
