using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class Participant : Entity
	{
		#region Properties

		[DataMember]
		public virtual String FullName { get; set; }

        #endregion

        #region Properties.Reports
        public virtual String LanguageDescription { get; set; }
        public virtual String OrgProviderName { get; set; }
        public virtual String UserName { get; set; }

        #endregion
        #region Constructor

        public Participant()
		{
			//
		}

		#endregion
	}
}
