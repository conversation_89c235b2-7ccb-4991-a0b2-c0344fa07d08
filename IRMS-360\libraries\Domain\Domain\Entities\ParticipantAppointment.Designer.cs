using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ParticipantAppointment : Entity
	{
		#region Fields

		private DateTime _scheduled;
		private DateTime? _emailSentDate;
		private DateTime? _scheduledEnd;
		private ICollection<ParticipantAppointment> _childParticipantAppointments = new HashSet<ParticipantAppointment>();
		private OrganizationParticipant _byParticipant;
		private OrganizationParticipant _organizationParticipant;
		private ParticipantAppointment _parentParticipantAppointment;
		private ParticipantRole _participantRole;
		private ServiceType _serviceType;
		private StatusCode _statusCode;
		private String _emailAlert;
		private String _notes;
		private String _recurringDetails;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime Scheduled
		{
			get { return _scheduled; }
			set { _scheduled = value; }
		}

		[DataMember]
		public virtual DateTime? EmailSentDate
		{
			get { return _emailSentDate; }
			set { _emailSentDate = value; }
		}

		[DataMember]
		public virtual DateTime? ScheduledEnd
		{
			get { return _scheduledEnd; }
			set { _scheduledEnd = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantAppointment> ChildParticipantAppointments
		{
			get { return _childParticipantAppointments; }
			set { _childParticipantAppointments = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant ByParticipant
		{
			get { return _byParticipant; }
			set { _byParticipant = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant OrganizationParticipant
		{
			get { return _organizationParticipant; }
			set { _organizationParticipant = value; }
		}

		[DataMember]
		public virtual ParticipantAppointment ParentParticipantAppointment
		{
			get { return _parentParticipantAppointment; }
			set { _parentParticipantAppointment = value; }
		}

		[DataMember]
		public virtual ParticipantRole ParticipantRole
		{
			get { return _participantRole; }
			set { _participantRole = value; }
		}

		[DataMember]
		public virtual ServiceType ServiceType
		{
			get { return _serviceType; }
			set { _serviceType = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual String EmailAlert
		{
			get { return _emailAlert; }
			set { _emailAlert = value; }
		}

		[DataMember]
		public virtual String Notes
		{
			get { return _notes; }
			set { _notes = value; }
		}

		[DataMember]
		public virtual String RecurringDetails
		{
			get { return _recurringDetails; }
			set { _recurringDetails = value; }
		}


		#endregion
	}
}
