using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class ParticipantAppointment : Entity
	{
        #region Properties

        [DataMember]
        public virtual String OrganizationProviderName { get; set; }
        [DataMember]
        public virtual String OrganizationProviderPhone { get; set; }
        [DataMember]
        public virtual String Patient { get; set; }
        [DataMember]
        public virtual String ProviderName { get; set; }
        [DataMember]
        public virtual String ProviderPhone { get; set; }
        [DataMember]
        public virtual String ScheduledDate { get; set; }
        [DataMember]
        public virtual String ScheduledTime { get; set; }

        #endregion

		#region Constructor

		public ParticipantAppointment()
		{
			//
		}

		#endregion
	}
}
