using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ParticipantAsset : Entity
	{
		#region Fields

		private DateTime _occurred;
		private DateTime? _actualReturn;
		private DateTime? _expectedReturn;
		private Decimal? _quantity;
		private InventoryItem _inventoryItem;
		private ICollection<Alert> _alerts = new HashSet<Alert>();
		private ICollection<AlertDetail> _alertDetails = new HashSet<AlertDetail>();
		private OrganizationParticipant _organizationParticipant;
		private String _cycleCount;
		private String _notes;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime Occurred
		{
			get { return _occurred; }
			set { _occurred = value; }
		}

		[DataMember]
		public virtual DateTime? ActualReturn
		{
			get { return _actualReturn; }
			set { _actualReturn = value; }
		}

		[DataMember]
		public virtual DateTime? ExpectedReturn
		{
			get { return _expectedReturn; }
			set { _expectedReturn = value; }
		}

		[DataMember]
		public virtual Decimal? Quantity
		{
			get { return _quantity; }
			set { _quantity = value; }
		}

		[DataMember]
		public virtual InventoryItem InventoryItem
		{
			get { return _inventoryItem; }
			set { _inventoryItem = value; }
		}

		[DataMember]
		public virtual ICollection<Alert> Alerts
		{
			get { return _alerts; }
			set { _alerts = value; }
		}

		[DataMember]
		public virtual ICollection<AlertDetail> AlertDetails
		{
			get { return _alertDetails; }
			set { _alertDetails = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant OrganizationParticipant
		{
			get { return _organizationParticipant; }
			set { _organizationParticipant = value; }
		}

		[DataMember]
		public virtual String CycleCount
		{
			get { return _cycleCount; }
			set { _cycleCount = value; }
		}

		[DataMember]
		public virtual String Notes
		{
			get { return _notes; }
			set { _notes = value; }
		}


		#endregion
	}
}
