using System;
using System.Runtime.Serialization;

using Upp.Irms.Core;
using Upp.Shared.Utilities;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class ParticipantAsset : Entity
	{
		#region Properties

		[DataMember]
		public virtual String AssetCode { get; set; }
		[DataMember]
		public virtual String ItemDescription { get; set; }
		[DataMember]
		public virtual String LocationCode { get; set; }

		#endregion

		#region Constructor

		public ParticipantAsset()
		{
			//
		}

		#endregion

		#region Methods.Public.Assets

		public virtual void ClearCycleCount()
		{
			_cycleCount = "N";
			_dateModified = DateTime.Now;
			_userModified = Registry.Find<UserAccount>().UserName;
			Repositories.Get<ParticipantAsset>().Update(this);
		}

		public virtual void SetCycleCount(Task task)
		{
			_cycleCount = "Y";
			_dateModified = DateTime.Now;
			_userModified = Registry.Find<UserAccount>().UserName;
			Repositories.Get<ParticipantAsset>().Update(this);
			//
			InventoryTask count = InventoryTask.Create(task);
			count.InventoryItem = _inventoryItem;
			count.Item = _inventoryItem.Item;
			count.LotNumber = _inventoryItem.LotNumber;
			count.Quantity = _quantity.Value;
			Repositories.Get<InventoryTask>().Add(count);
		}

		#endregion

		#region Methods.Static

		public static ParticipantAsset Create(InventoryItem asset)
		{
			ParticipantAsset created = Entity.Activate<ParticipantAsset>();
			created.ActualReturn = null;
			created.ExpectedReturn = asset.ReturnDate;
			created.InventoryItem = asset;
			created.Occurred = DateTime.Now;
			created.OrganizationParticipant = asset.Participant;
			if ("N".Equals(asset.Item.ItemType.Consumable)) created.Quantity = 1;
			else created.Quantity = Converter.ToInt32(asset.MovementQuantity);
			//
			return created;
		}

		#endregion
	}
}
