using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ParticipantCommunication : Entity
	{
		#region Fields

		private CommunicationRole _communicationRole;
		private ICollection<AlertRecipient> _alertRecipients = new HashSet<AlertRecipient>();
		private Participant _participant;
		private ParticipantLocation _participantLocation;
		private ParticipantRole _participantRole;
		private String _active;
		private String _communicationValue;
		private String _primaryCommunication;

		#endregion

		#region Properties

		[DataMember]
		public virtual CommunicationRole CommunicationRole
		{
			get { return _communicationRole; }
			set { _communicationRole = value; }
		}

		[DataMember]
		public virtual ICollection<AlertRecipient> AlertRecipients
		{
			get { return _alertRecipients; }
			set { _alertRecipients = value; }
		}

		[DataMember]
		public virtual Participant Participant
		{
			get { return _participant; }
			set { _participant = value; }
		}

		[DataMember]
		public virtual ParticipantLocation ParticipantLocation
		{
			get { return _participantLocation; }
			set { _participantLocation = value; }
		}

		[DataMember]
		public virtual ParticipantRole ParticipantRole
		{
			get { return _participantRole; }
			set { _participantRole = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String CommunicationValue
		{
			get { return _communicationValue; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("CommunicationValue must not be blank or null.");
				else _communicationValue = value;
			}
		}

		[DataMember]
		public virtual String PrimaryCommunication
		{
			get { return _primaryCommunication; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("PrimaryCommunication must not be blank or null.");
				else _primaryCommunication = value;
			}
		}


		#endregion
	}
}
