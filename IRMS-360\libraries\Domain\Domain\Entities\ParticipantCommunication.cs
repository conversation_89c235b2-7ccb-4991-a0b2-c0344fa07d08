using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class ParticipantCommunication : Entity
	{
		#region Constructor

		public ParticipantCommunication()
		{
			//
		}

		#endregion

        #region Properties.Reports

        public virtual Int32? AlertDefinitionId { get; set; }

        #endregion


    }
}
