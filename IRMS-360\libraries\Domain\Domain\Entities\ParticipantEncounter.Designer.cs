using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ParticipantEncounter : Entity
	{
		#region Fields

		private DateTime _encountered;
		private DateTime? _concluded;
		private DateTime? _consulted;
		private DateTime? _dateSubmitted;
		private ICollection<EdiBatchDetail> _ediBatchDetails = new HashSet<EdiBatchDetail>();
		private ICollection<EraClaimPosting> _eraClaimPostings = new HashSet<EraClaimPosting>();
		private ICollection<ParticipantEncounterMessage> _participantEncounterMessages = new HashSet<ParticipantEncounterMessage>();
		private ICollection<ParticipantImmunization> _participantImmunizations = new HashSet<ParticipantImmunization>();
		private ICollection<Transaction> _transactions = new HashSet<Transaction>();
		private OrganizationParticipant _organizationPartici1pant;
		private OrganizationParticipant _organizationParticipant;
		private Participant _participant;
		private ParticipantInsurance _participantInsurance;
		private Pool _pool;
		private ProviderFinancialClass _providerFinancialClass;
		private ProviderLocation _providerLocation;
		private ProviderOrganizationalUnit _providerOrganizationalUnit;
		private ServiceType _serviceType;
		private StatusCode _integrationStatusCode;
		private StatusCode _statusCode;
		private String _alternateEncounterCode;
		private String _consentsToHl7;
		private String _consentsToTreatment;
		private String _encounterCode;
		private String _notes;
		private String _priorAuthorizationCode;
		private String _userSubmitted;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime Encountered
		{
			get { return _encountered; }
			set { _encountered = value; }
		}

		[DataMember]
		public virtual DateTime? Concluded
		{
			get { return _concluded; }
			set { _concluded = value; }
		}

		[DataMember]
		public virtual DateTime? Consulted
		{
			get { return _consulted; }
			set { _consulted = value; }
		}

		[DataMember]
		public virtual DateTime? DateSubmitted
		{
			get { return _dateSubmitted; }
			set { _dateSubmitted = value; }
		}

		[DataMember]
		public virtual ICollection<EdiBatchDetail> EdiBatchDetails
		{
			get { return _ediBatchDetails; }
			set { _ediBatchDetails = value; }
		}

		[DataMember]
		public virtual ICollection<EraClaimPosting> EraClaimPostings
		{
			get { return _eraClaimPostings; }
			set { _eraClaimPostings = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantEncounterMessage> ParticipantEncounterMessages
		{
			get { return _participantEncounterMessages; }
			set { _participantEncounterMessages = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantImmunization> ParticipantImmunizations
		{
			get { return _participantImmunizations; }
			set { _participantImmunizations = value; }
		}

		[DataMember]
		public virtual ICollection<Transaction> Transactions
		{
			get { return _transactions; }
			set { _transactions = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant OrganizationPartici1pant
		{
			get { return _organizationPartici1pant; }
			set { _organizationPartici1pant = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant OrganizationParticipant
		{
			get { return _organizationParticipant; }
			set { _organizationParticipant = value; }
		}

		[DataMember]
		public virtual Participant Participant
		{
			get { return _participant; }
			set { _participant = value; }
		}

		[DataMember]
		public virtual ParticipantInsurance ParticipantInsurance
		{
			get { return _participantInsurance; }
			set { _participantInsurance = value; }
		}

		[DataMember]
		public virtual Pool Pool
		{
			get { return _pool; }
			set { _pool = value; }
		}

		[DataMember]
		public virtual ProviderFinancialClass ProviderFinancialClass
		{
			get { return _providerFinancialClass; }
			set { _providerFinancialClass = value; }
		}

		[DataMember]
		public virtual ProviderLocation ProviderLocation
		{
			get { return _providerLocation; }
			set { _providerLocation = value; }
		}

		[DataMember]
		public virtual ProviderOrganizationalUnit ProviderOrganizationalUnit
		{
			get { return _providerOrganizationalUnit; }
			set { _providerOrganizationalUnit = value; }
		}

		[DataMember]
		public virtual ServiceType ServiceType
		{
			get { return _serviceType; }
			set { _serviceType = value; }
		}

		[DataMember]
		public virtual StatusCode IntegrationStatusCode
		{
            get { return _integrationStatusCode != null ? _integrationStatusCode : StatusCode.GetOpenIntegrationStatusCode(); }
            set { _integrationStatusCode = value != null ? value : StatusCode.GetOpenIntegrationStatusCode(); }
        }

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual String AlternateEncounterCode
		{
			get { return _alternateEncounterCode; }
			set { _alternateEncounterCode = value; }
		}

		[DataMember]
		public virtual String ConsentsToHl7
		{
			get { return _consentsToHl7; }
			set { _consentsToHl7 = value; }
		}

		[DataMember]
		public virtual String ConsentsToTreatment
		{
			get { return _consentsToTreatment; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ConsentsToTreatment must not be blank or null.");
				else _consentsToTreatment = value;
			}
		}

		[DataMember]
		public virtual String EncounterCode
		{
			get { return _encounterCode; }
			set { _encounterCode = value; }
		}

		[DataMember]
		public virtual String Notes
		{
			get { return _notes; }
			set { _notes = value; }
		}

		[DataMember]
		public virtual String PriorAuthorizationCode
		{
			get { return _priorAuthorizationCode; }
			set { _priorAuthorizationCode = value; }
		}

		[DataMember]
		public virtual String UserSubmitted
		{
			get { return _userSubmitted; }
			set { _userSubmitted = value; }
		}


		#endregion
	}
}
