using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class ParticipantEncounter : Entity
	{
		#region Constructor

		public ParticipantEncounter()
		{
			//
		}

		#endregion

        #region Properties

        [DataMember]
        public virtual String FirstName { get; set; }

        [DataMember]
        public virtual String LastName { get; set; }

        #endregion
    }
}
