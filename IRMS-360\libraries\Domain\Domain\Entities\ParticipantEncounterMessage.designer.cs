using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ParticipantEncounterMessage : Entity
	{
		#region Fields

		private CdrMessage _cdrMessage;
		private CdrResponsis _cdrResponsis;
		private DateTime? _dateClosed;
		private ParticipantEncounter _participantEncounter;
		private ParticipantInsurance _participantInsurance;
		private StatusCode _statusCode;
		private String _active;
		private String _description;
		private String _fieldName;
		private String _fieldValue;
		private String _refid;
		private String _userClosed;

		#endregion

		#region Properties

		[DataMember]
		public virtual CdrMessage CdrMessage
		{
			get { return _cdrMessage; }
			set { _cdrMessage = value; }
		}

		[DataMember]
		public virtual CdrResponsis CdrResponsis
		{
			get { return _cdrResponsis; }
			set { _cdrResponsis = value; }
		}

		[DataMember]
		public virtual DateTime? DateClosed
		{
			get { return _dateClosed; }
			set { _dateClosed = value; }
		}

		[DataMember]
		public virtual ParticipantEncounter ParticipantEncounter
		{
			get { return _participantEncounter; }
			set { _participantEncounter = value; }
		}

		[DataMember]
		public virtual ParticipantInsurance ParticipantInsurance
		{
			get { return _participantInsurance; }
			set { _participantInsurance = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set { _description = value; }
		}

		[DataMember]
		public virtual String FieldName
		{
			get { return _fieldName; }
			set { _fieldName = value; }
		}

		[DataMember]
		public virtual String FieldValue
		{
			get { return _fieldValue; }
			set { _fieldValue = value; }
		}

		[DataMember]
		public virtual String Refid
		{
			get { return _refid; }
			set { _refid = value; }
		}

		[DataMember]
		public virtual String UserClosed
		{
			get { return _userClosed; }
			set { _userClosed = value; }
		}


		#endregion
	}
}
