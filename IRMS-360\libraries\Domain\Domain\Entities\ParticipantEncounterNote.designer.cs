using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ParticipantEncounterNote : Entity
	{
		#region Fields

		private Participant _participant;
		private String _active;
		private String _encounterCode;
		private String _notes;
		private String _readNotes;
		private String _setAlert;

		#endregion

		#region Properties

		[DataMember]
		public virtual Participant Participant
		{
			get { return _participant; }
			set { _participant = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String EncounterCode
		{
			get { return _encounterCode; }
			set { _encounterCode = value; }
		}

		[DataMember]
		public virtual String Notes
		{
			get { return _notes; }
			set { _notes = value; }
		}

		[DataMember]
		public virtual String ReadNotes
		{
			get { return _readNotes; }
			set { _readNotes = value; }
		}

		[DataMember]
		public virtual String SetAlert
		{
			get { return _setAlert; }
			set { _setAlert = value; }
		}


		#endregion
	}
}
