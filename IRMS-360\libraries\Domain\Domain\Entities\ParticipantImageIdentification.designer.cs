using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ParticipantImageIdentification : Entity
	{
		#region Fields

		private Participant _participant;
		private String _active;

		#endregion

		#region Properties

		[DataMember]
		public virtual Participant Participant
		{
			get { return _participant; }
			set { _participant = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}


		#endregion
	}
}
