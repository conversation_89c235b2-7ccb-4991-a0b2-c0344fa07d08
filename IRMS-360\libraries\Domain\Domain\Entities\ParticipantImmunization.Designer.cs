using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ParticipantImmunization : Entity
	{
		#region Fields

		private DateTime _occurred;
		private DateTime? _vaccineExpiration;
		private DateTime? _visitDate;
		private Decimal? _dosage;
		private IcdCode _icdCode;
		private Immunization _immunization;
		private InventoryItem _inventoryItem;
		private Item _item;
		private ICollection<Transaction> _transactions = new HashSet<Transaction>();
		private OrganizationParticipant _organizationParticipant;
		private ParticipantEncounter _participantEncounter;
		private Service _service;
		private ServiceDetail _serviceDetail;
		private String _epsdtIndicator;
		private String _famplanIndicator;
		private String _immunizationLocation;
		private String _vaccineLotNumber;
		private UnitOfMeasure _unitOfMeasure;
		private VaccinationRoute _vaccinationRoute;
		private VaccinationSite _vaccinationSite;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime Occurred
		{
			get { return _occurred; }
			set { _occurred = value; }
		}

		[DataMember]
		public virtual DateTime? VaccineExpiration
		{
			get { return _vaccineExpiration; }
			set { _vaccineExpiration = value; }
		}

		[DataMember]
		public virtual DateTime? VisitDate
		{
			get { return _visitDate; }
			set { _visitDate = value; }
		}

		[DataMember]
		public virtual Decimal? Dosage
		{
			get { return _dosage; }
			set { _dosage = value; }
		}

		[DataMember]
		public virtual IcdCode IcdCode
		{
			get { return _icdCode; }
			set { _icdCode = value; }
		}

		[DataMember]
		public virtual Immunization Immunization
		{
			get { return _immunization; }
			set { _immunization = value; }
		}

		[DataMember]
		public virtual InventoryItem InventoryItem
		{
			get { return _inventoryItem; }
			set { _inventoryItem = value; }
		}

		[DataMember]
		public virtual Item Item
		{
			get { return _item; }
			set { _item = value; }
		}

		[DataMember]
		public virtual ICollection<Transaction> Transactions
		{
			get { return _transactions; }
			set { _transactions = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant OrganizationParticipant
		{
			get { return _organizationParticipant; }
			set { _organizationParticipant = value; }
		}

		[DataMember]
		public virtual ParticipantEncounter ParticipantEncounter
		{
			get { return _participantEncounter; }
			set { _participantEncounter = value; }
		}

		[DataMember]
		public virtual Service Service
		{
			get { return _service; }
			set { _service = value; }
		}

		[DataMember]
		public virtual ServiceDetail ServiceDetail
		{
			get { return _serviceDetail; }
			set { _serviceDetail = value; }
		}

		[DataMember]
		public virtual String EpsdtIndicator
		{
			get { return _epsdtIndicator; }
			set { _epsdtIndicator = value; }
		}

		[DataMember]
		public virtual String FamplanIndicator
		{
			get { return _famplanIndicator; }
			set { _famplanIndicator = value; }
		}

		[DataMember]
		public virtual String ImmunizationLocation
		{
			get { return _immunizationLocation; }
			set { _immunizationLocation = value; }
		}

		[DataMember]
		public virtual String VaccineLotNumber
		{
			get { return _vaccineLotNumber; }
			set { _vaccineLotNumber = value; }
		}

		[DataMember]
		public virtual UnitOfMeasure UnitOfMeasure
		{
			get { return _unitOfMeasure; }
			set { _unitOfMeasure = value; }
		}

		[DataMember]
		public virtual VaccinationRoute VaccinationRoute
		{
			get { return _vaccinationRoute; }
			set { _vaccinationRoute = value; }
		}

		[DataMember]
		public virtual VaccinationSite VaccinationSite
		{
			get { return _vaccinationSite; }
			set { _vaccinationSite = value; }
		}


		#endregion
	}
}
