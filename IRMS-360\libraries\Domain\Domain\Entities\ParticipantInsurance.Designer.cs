using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ParticipantInsurance : Entity
	{
		#region Fields

		private DateTime? _effective;
		private DateTime? _expiration;
		private DateTime? _subscriberDob;
		private DateTime? _terminatationDate;
		private Decimal? _copayAmount;
		private InsurancePayer _insurancePayer;
		private InsuranceType _insuranceType;
		private ICollection<ParticipantEncounter> _participantEncounters = new HashSet<ParticipantEncounter>();
		private ICollection<ParticipantEncounterMessage> _participantEncounterMessages = new HashSet<ParticipantEncounterMessage>();
		private Participant _participant;
		private ProviderInsurancePayerAlias _providerInsurancePayerAlias;
		private String _active;
		private String _groupCode;
		private String _groupName;
		private String _insuranceDesignation;
		private String _notes;
		private String _policyCode;
		private String _subscriberCode;
		private String _subscriberFirstName;
		private String _subscriberLastName;
		private VendorLocationType _vendorLocationType;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime? Effective
		{
			get { return _effective; }
			set { _effective = value; }
		}

		[DataMember]
		public virtual DateTime? Expiration
		{
			get { return _expiration; }
			set { _expiration = value; }
		}

		[DataMember]
		public virtual DateTime? SubscriberDob
		{
			get { return _subscriberDob; }
			set { _subscriberDob = value; }
		}

		[DataMember]
		public virtual DateTime? TerminatationDate
		{
			get { return _terminatationDate; }
			set { _terminatationDate = value; }
		}

		[DataMember]
		public virtual Decimal? CopayAmount
		{
			get { return _copayAmount; }
			set { _copayAmount = value; }
		}

		[DataMember]
		public virtual InsurancePayer InsurancePayer
		{
			get { return _insurancePayer; }
			set { _insurancePayer = value; }
		}

		[DataMember]
		public virtual InsuranceType InsuranceType
		{
			get { return _insuranceType; }
			set { _insuranceType = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantEncounter> ParticipantEncounters
		{
			get { return _participantEncounters; }
			set { _participantEncounters = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantEncounterMessage> ParticipantEncounterMessages
		{
			get { return _participantEncounterMessages; }
			set { _participantEncounterMessages = value; }
		}

		[DataMember]
		public virtual Participant Participant
		{
			get { return _participant; }
			set { _participant = value; }
		}

		[DataMember]
		public virtual ProviderInsurancePayerAlias ProviderInsurancePayerAlias
		{
			get { return _providerInsurancePayerAlias; }
			set { _providerInsurancePayerAlias = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set { _active = value; }
		}

		[DataMember]
		public virtual String GroupCode
		{
			get { return _groupCode; }
			set { _groupCode = value; }
		}

		[DataMember]
		public virtual String GroupName
		{
			get { return _groupName; }
			set { _groupName = value; }
		}

		[DataMember]
		public virtual String InsuranceDesignation
		{
			get { return _insuranceDesignation; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("InsuranceDesignation must not be blank or null.");
				else _insuranceDesignation = value;
			}
		}

		[DataMember]
		public virtual String Notes
		{
			get { return _notes; }
			set { _notes = value; }
		}

		[DataMember]
		public virtual String PolicyCode
		{
			get { return _policyCode; }
			set { _policyCode = value; }
		}

		[DataMember]
		public virtual String SubscriberCode
		{
			get { return _subscriberCode; }
			set { _subscriberCode = value; }
		}

		[DataMember]
		public virtual String SubscriberFirstName
		{
			get { return _subscriberFirstName; }
			set { _subscriberFirstName = value; }
		}

		[DataMember]
		public virtual String SubscriberLastName
		{
			get { return _subscriberLastName; }
			set { _subscriberLastName = value; }
		}

		[DataMember]
		public virtual VendorLocationType VendorLocationType
		{
			get { return _vendorLocationType; }
			set { _vendorLocationType = value; }
		}


		#endregion
	}
}
