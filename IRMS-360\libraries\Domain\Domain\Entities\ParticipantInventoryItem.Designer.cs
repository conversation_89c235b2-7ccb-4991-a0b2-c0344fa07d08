using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ParticipantInventoryItem : Entity
	{
		#region Fields

		private DateTime _occurred;
		private ICollection<InspectionDetail> _inspectionDetails = new HashSet<InspectionDetail>();
		private OrganizationParticipant _custodian;
		private OrganizationParticipant _inspectedBy;
		private OrganizationParticipant _issuedBy;
		private OrganizationParticipant _issuedTo;
		private Participant _participant;
		private TransactionType _transactionType;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime Occurred
		{
			get { return _occurred; }
			set { _occurred = value; }
		}

		[DataMember]
		public virtual ICollection<InspectionDetail> InspectionDetails
		{
			get { return _inspectionDetails; }
			set { _inspectionDetails = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant Custodian
		{
			get { return _custodian; }
			set { _custodian = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant InspectedBy
		{
			get { return _inspectedBy; }
			set { _inspectedBy = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant IssuedBy
		{
			get { return _issuedBy; }
			set { _issuedBy = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant IssuedTo
		{
			get { return _issuedTo; }
			set { _issuedTo = value; }
		}

		[DataMember]
		public virtual Participant Participant
		{
			get { return _participant; }
			set { _participant = value; }
		}

		[DataMember]
		public virtual TransactionType TransactionType
		{
			get { return _transactionType; }
			set { _transactionType = value; }
		}


		#endregion
	}
}
