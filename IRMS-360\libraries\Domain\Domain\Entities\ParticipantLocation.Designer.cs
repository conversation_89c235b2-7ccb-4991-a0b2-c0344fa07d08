using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ParticipantLocation : Entity
	{
		#region Fields

		private ICollection<ParticipantCommunication> _participantCommunications = new HashSet<ParticipantCommunication>();
		private ICollection<ParticipantLocationType> _participantLocationTypes = new HashSet<ParticipantLocationType>();
		private Participant _participant;
		private ParticipantRole _participantRole;
		private String _active;
		private String _addressDirection;
		private String _addressLine1;
		private String _addressLine2;
		private String _addressLine3;
		private String _addressNumber;
		private String _addressSuffix;
		private ZipCode _zipCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<ParticipantCommunication> ParticipantCommunications
		{
			get { return _participantCommunications; }
			set { _participantCommunications = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantLocationType> ParticipantLocationTypes
		{
			get { return _participantLocationTypes; }
			set { _participantLocationTypes = value; }
		}

		[DataMember]
		public virtual Participant Participant
		{
			get { return _participant; }
			set { _participant = value; }
		}

		[DataMember]
		public virtual ParticipantRole ParticipantRole
		{
			get { return _participantRole; }
			set { _participantRole = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set { _active = value; }
		}

		[DataMember]
		public virtual String AddressDirection
		{
			get { return _addressDirection; }
			set { _addressDirection = value; }
		}

		[DataMember]
		public virtual String AddressLine1
		{
			get { return _addressLine1; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("AddressLine1 must not be blank or null.");
				else _addressLine1 = value;
			}
		}

		[DataMember]
		public virtual String AddressLine2
		{
			get { return _addressLine2; }
			set { _addressLine2 = value; }
		}

		[DataMember]
		public virtual String AddressLine3
		{
			get { return _addressLine3; }
			set { _addressLine3 = value; }
		}

		[DataMember]
		public virtual String AddressNumber
		{
			get { return _addressNumber; }
			set { _addressNumber = value; }
		}

		[DataMember]
		public virtual String AddressSuffix
		{
			get { return _addressSuffix; }
			set { _addressSuffix = value; }
		}

		[DataMember]
		public virtual ZipCode ZipCode
		{
			get { return _zipCode; }
			set { _zipCode = value; }
		}


		#endregion
	}
}
