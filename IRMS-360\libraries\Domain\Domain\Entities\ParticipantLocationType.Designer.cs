using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ParticipantLocationType : Entity
	{
		#region Fields

		private LocationType _locationType;
		private ParticipantLocation _participantLocation;
		private String _active;
		private String _description;
		private String _participantLocationCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual LocationType LocationType
		{
			get { return _locationType; }
			set { _locationType = value; }
		}

		[DataMember]
		public virtual ParticipantLocation ParticipantLocation
		{
			get { return _participantLocation; }
			set { _participantLocation = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set { _description = value; }
		}

		[DataMember]
		public virtual String ParticipantLocationCode
		{
			get { return _participantLocationCode; }
			set { _participantLocationCode = value; }
		}


		#endregion
	}
}
