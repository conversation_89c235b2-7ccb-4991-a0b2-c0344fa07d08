using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ParticipantRelationship : Entity
	{
		#region Fields

		private Participant _participant;
		private Participant _relationshipParticipant;
		private RelationshipCode _relationshipCode;
		private String _emergencyContact;
		private String _firstName;
		private String _guarantor;
		private String _lastName;
		private String _middleName;

		#endregion

		#region Properties

		[DataMember]
		public virtual Participant Participant
		{
			get { return _participant; }
			set { _participant = value; }
		}

		[DataMember]
		public virtual Participant RelationshipParticipant
		{
			get { return _relationshipParticipant; }
			set { _relationshipParticipant = value; }
		}

		[DataMember]
		public virtual RelationshipCode RelationshipCode
		{
			get { return _relationshipCode; }
			set { _relationshipCode = value; }
		}

		[DataMember]
		public virtual String EmergencyContact
		{
			get { return _emergencyContact; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("EmergencyContact must not be blank or null.");
				else _emergencyContact = value;
			}
		}

		[DataMember]
		public virtual String FirstName
		{
			get { return _firstName; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("FirstName must not be blank or null.");
				else _firstName = value;
			}
		}

		[DataMember]
		public virtual String Guarantor
		{
			get { return _guarantor; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Guarantor must not be blank or null.");
				else _guarantor = value;
			}
		}

		[DataMember]
		public virtual String LastName
		{
			get { return _lastName; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("LastName must not be blank or null.");
				else _lastName = value;
			}
		}

		[DataMember]
		public virtual String MiddleName
		{
			get { return _middleName; }
			set { _middleName = value; }
		}


		#endregion
	}
}
