using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ParticipantRole : Entity
	{
		#region Fields

		private DateTime? _effective;
		private DateTime? _expiration;
		private DateTime? _pickVerificationBegin;
		private DateTime? _pickVerificationEnd;
		private ICollection<OrganizationParticipant> _organizationParticipants = new HashSet<OrganizationParticipant>();
		private ICollection<ParticipantAppointment> _participantAppointments = new HashSet<ParticipantAppointment>();
		private ICollection<ParticipantCommunication> _participantCommunications = new HashSet<ParticipantCommunication>();
		private ICollection<ParticipantLocation> _participantLocations = new HashSet<ParticipantLocation>();
		private ICollection<TaskAssignment> _taskAssignments = new HashSet<TaskAssignment>();
		private Participant _participant;
		private RoleCode _roleCode;
		private String _active;
		private String _pickVerification;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime? Effective
		{
			get { return _effective; }
			set { _effective = value; }
		}

		[DataMember]
		public virtual DateTime? Expiration
		{
			get { return _expiration; }
			set { _expiration = value; }
		}

		[DataMember]
		public virtual DateTime? PickVerificationBegin
		{
			get { return _pickVerificationBegin; }
			set { _pickVerificationBegin = value; }
		}

		[DataMember]
		public virtual DateTime? PickVerificationEnd
		{
			get { return _pickVerificationEnd; }
			set { _pickVerificationEnd = value; }
		}

		[DataMember]
		public virtual ICollection<OrganizationParticipant> OrganizationParticipants
		{
			get { return _organizationParticipants; }
			set { _organizationParticipants = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantAppointment> ParticipantAppointments
		{
			get { return _participantAppointments; }
			set { _participantAppointments = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantCommunication> ParticipantCommunications
		{
			get { return _participantCommunications; }
			set { _participantCommunications = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantLocation> ParticipantLocations
		{
			get { return _participantLocations; }
			set { _participantLocations = value; }
		}

		[DataMember]
		public virtual ICollection<TaskAssignment> TaskAssignments
		{
			get { return _taskAssignments; }
			set { _taskAssignments = value; }
		}

		[DataMember]
		public virtual Participant Participant
		{
			get { return _participant; }
			set { _participant = value; }
		}

		[DataMember]
		public virtual RoleCode RoleCode
		{
			get { return _roleCode; }
			set { _roleCode = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String PickVerification
		{
			get { return _pickVerification; }
			set { _pickVerification = value; }
		}


		#endregion
	}
}
