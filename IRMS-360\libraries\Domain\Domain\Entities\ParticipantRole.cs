using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class ParticipantRole : Entity
	{
		#region Constructor

		public ParticipantRole()
		{
			//
		}

        [DataMember]
        public virtual string FirstName { get; set; }
        [DataMember]
        public virtual string LastName { get; set; }
        [DataMember]
        public virtual string FullName { get; set; }
        [DataMember]
        public virtual string RoleDescription { get; set; }

        #endregion
    }
}
