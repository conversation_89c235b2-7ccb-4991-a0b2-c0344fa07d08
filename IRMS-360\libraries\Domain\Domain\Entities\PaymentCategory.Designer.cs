using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class PaymentCategory : Entity
	{
		#region Fields

		private ICollection<Transaction> _transactions = new HashSet<Transaction>();
		private String _active;
		private String _description;
		private String _paymentCategoryCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<Transaction> Transactions
		{
			get { return _transactions; }
			set { _transactions = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String PaymentCategoryCode
		{
			get { return _paymentCategoryCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("PaymentCategoryCode must not be blank or null.");
				else _paymentCategoryCode = value;
			}
		}


		#endregion
	}
}
