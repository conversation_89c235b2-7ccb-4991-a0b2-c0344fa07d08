using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class PaymentMethod : Entity
	{
		#region Fields

		private ICollection<CustomerPaymentMethod> _customerPaymentMethods = new HashSet<CustomerPaymentMethod>();
		private ICollection<OrderHeader> _orderHeaders = new HashSet<OrderHeader>();
		private ICollection<Transaction> _transactions = new HashSet<Transaction>();
		private String _active;
		private String _description;
		private String _paymentMethodCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<CustomerPaymentMethod> CustomerPaymentMethods
		{
			get { return _customerPaymentMethods; }
			set { _customerPaymentMethods = value; }
		}

		[DataMember]
		public virtual ICollection<OrderHeader> OrderHeaders
		{
			get { return _orderHeaders; }
			set { _orderHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<Transaction> Transactions
		{
			get { return _transactions; }
			set { _transactions = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String PaymentMethodCode
		{
			get { return _paymentMethodCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("PaymentMethodCode must not be blank or null.");
				else _paymentMethodCode = value;
			}
		}


		#endregion
	}
}
