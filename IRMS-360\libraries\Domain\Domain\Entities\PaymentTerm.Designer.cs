using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class PaymentTerm : Entity
	{
		#region Fields

		private Decimal? _percentDiscount;
		private Int32? _discountDueDays;
		private Int32? _dueDays;
		private ICollection<OrderHeader> _orderHeaders = new HashSet<OrderHeader>();
		private ICollection<PurchaseOrderHeader> _purchaseOrderHeaders = new HashSet<PurchaseOrderHeader>();
		private String _active;
		private String _description;
		private String _paymentTermCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual Decimal? PercentDiscount
		{
			get { return _percentDiscount; }
			set { _percentDiscount = value; }
		}

		[DataMember]
		public virtual Int32? DiscountDueDays
		{
			get { return _discountDueDays; }
			set { _discountDueDays = value; }
		}

		[DataMember]
		public virtual Int32? DueDays
		{
			get { return _dueDays; }
			set { _dueDays = value; }
		}

		[DataMember]
		public virtual ICollection<OrderHeader> OrderHeaders
		{
			get { return _orderHeaders; }
			set { _orderHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<PurchaseOrderHeader> PurchaseOrderHeaders
		{
			get { return _purchaseOrderHeaders; }
			set { _purchaseOrderHeaders = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String PaymentTermCode
		{
			get { return _paymentTermCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("PaymentTermCode must not be blank or null.");
				else _paymentTermCode = value;
			}
		}


		#endregion
	}
}
