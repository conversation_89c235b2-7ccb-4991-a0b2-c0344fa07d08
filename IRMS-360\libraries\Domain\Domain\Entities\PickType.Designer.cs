using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class PickType : Entity
	{
		#region Fields

		private ICollection<Location> _locations = new HashSet<Location>();
		private String _active;
		private String _description;
		private String _pickTypeCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<Location> Locations
		{
			get { return _locations; }
			set { _locations = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String PickTypeCode
		{
			get { return _pickTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("PickTypeCode must not be blank or null.");
				else _pickTypeCode = value;
			}
		}


		#endregion
	}
}
