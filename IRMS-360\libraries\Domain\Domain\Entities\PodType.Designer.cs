using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class PodType : Entity
	{
		#region Fields

		private ICollection<Questionnaire> _questionnaires = new HashSet<Questionnaire>();
		private String _active;
		private String _description;
		private String _podTypeCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<Questionnaire> Questionnaires
		{
			get { return _questionnaires; }
			set { _questionnaires = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String PodTypeCode
		{
			get { return _podTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("PodTypeCode must not be blank or null.");
				else _podTypeCode = value;
			}
		}


		#endregion
	}
}
