using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Pool : Entity
	{
		#region Fields

		private Agency _agency;
		private CompanyLocationType _companyLocationType;
		private CustomerLocationType _customerLocationType;
		private DateTime _effective;
		private DateTime? _expiration;
		private Decimal? _poolAmount;
		private ICollection<ParticipantEncounter> _participantEncounters = new HashSet<ParticipantEncounter>();
		private ICollection<PoolItem> _poolItems = new HashSet<PoolItem>();
		private ICollection<PurchaseOrderFunding> _purchaseOrderFundings = new HashSet<PurchaseOrderFunding>();
		private ICollection<WorkOrderDetail> _workOrderDetails = new HashSet<WorkOrderDetail>();
		private ICollection<WorkOrderHeader> _workOrderHeaders = new HashSet<WorkOrderHeader>();
		private String _description;
		private String _notes;
		private String _poolName;
		private VendorLocationType _vendorLocationType;

		#endregion

		#region Properties

		[DataMember]
		public virtual Agency Agency
		{
			get { return _agency; }
			set { _agency = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual CustomerLocationType CustomerLocationType
		{
			get { return _customerLocationType; }
			set { _customerLocationType = value; }
		}

		[DataMember]
		public virtual DateTime Effective
		{
			get { return _effective; }
			set { _effective = value; }
		}

		[DataMember]
		public virtual DateTime? Expiration
		{
			get { return _expiration; }
			set { _expiration = value; }
		}

		[DataMember]
		public virtual Decimal? PoolAmount
		{
			get { return _poolAmount; }
			set { _poolAmount = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantEncounter> ParticipantEncounters
		{
			get { return _participantEncounters; }
			set { _participantEncounters = value; }
		}

		[DataMember]
		public virtual ICollection<PoolItem> PoolItems
		{
			get { return _poolItems; }
			set { _poolItems = value; }
		}

		[DataMember]
		public virtual ICollection<PurchaseOrderFunding> PurchaseOrderFundings
		{
			get { return _purchaseOrderFundings; }
			set { _purchaseOrderFundings = value; }
		}

		[DataMember]
		public virtual ICollection<WorkOrderDetail> WorkOrderDetails
		{
			get { return _workOrderDetails; }
			set { _workOrderDetails = value; }
		}

		[DataMember]
		public virtual ICollection<WorkOrderHeader> WorkOrderHeaders
		{
			get { return _workOrderHeaders; }
			set { _workOrderHeaders = value; }
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set { _description = value; }
		}

		[DataMember]
		public virtual String Notes
		{
			get { return _notes; }
			set { _notes = value; }
		}

		[DataMember]
		public virtual String PoolName
		{
			get { return _poolName; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("PoolName must not be blank or null.");
				else _poolName = value;
			}
		}

		[DataMember]
		public virtual VendorLocationType VendorLocationType
		{
			get { return _vendorLocationType; }
			set { _vendorLocationType = value; }
		}


		#endregion
	}
}
