using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class PreReceiptCarton : Entity
	{
		#region Fields

		private Int32 _quantity;
		private LicensePlate _licensePlate;
		private Location _location;
		private PreReceiptDetail _preReceiptDetail;
		private StatusCode _statusCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual Int32 Quantity
		{
			get { return _quantity; }
			set { _quantity = value; }
		}

		[DataMember]
		public virtual LicensePlate LicensePlate
		{
			get { return _licensePlate; }
			set { _licensePlate = value; }
		}

		[DataMember]
		public virtual Location Location
		{
			get { return _location; }
			set { _location = value; }
		}

		[DataMember]
		public virtual PreReceiptDetail PreReceiptDetail
		{
			get { return _preReceiptDetail; }
			set { _preReceiptDetail = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}


		#endregion
	}
}
