using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class PreReceiptCharge : Entity
	{
		#region Fields

		private BillingRate _billingRate;
		private Decimal _amount;
		private PreReceiptDetail _preReceiptDetail;
		private PreReceiptHeader _preReceiptHeader;
		private String _notes;
		private String _overridden;

		#endregion

		#region Properties

		[DataMember]
		public virtual BillingRate BillingRate
		{
			get { return _billingRate; }
			set { _billingRate = value; }
		}

		[DataMember]
		public virtual Decimal Amount
		{
			get { return _amount; }
			set { _amount = value; }
		}

		[DataMember]
		public virtual PreReceiptDetail PreReceiptDetail
		{
			get { return _preReceiptDetail; }
			set { _preReceiptDetail = value; }
		}

		[DataMember]
		public virtual PreReceiptHeader PreReceiptHeader
		{
			get { return _preReceiptHeader; }
			set { _preReceiptHeader = value; }
		}

		[DataMember]
		public virtual String Notes
		{
			get { return _notes; }
			set { _notes = value; }
		}

		[DataMember]
		public virtual String Overridden
		{
			get { return _overridden; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Overridden must not be blank or null.");
				else _overridden = value;
			}
		}


		#endregion
	}
}
