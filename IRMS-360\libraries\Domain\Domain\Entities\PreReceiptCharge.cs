using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class PreReceiptCharge : Entity
	{
		#region Constructor

		public PreReceiptCharge()
		{
			//
		}

        #endregion

        #region Properties
        [DataMember]
        public virtual String BillingCode { get; set; }
        [DataMember]
        public virtual String CompanyCode { get; set; }
        [DataMember]
        public virtual String CompanyLocationCode { get; set; }
        [DataMember]
        public virtual String LineNumber { get; set; }
        [DataMember]
        public virtual String PreReceiptCode { get; set; }

        #endregion

    }
}
