using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class PreReceiptComment : Entity
	{
		#region Fields

		private Byte[] _commentFile;
		private Int32 _commentLine;
		private PreReceiptDetail _preReceiptDetail;
		private PreReceiptHeader _preReceiptHeader;
		private String _comments;

		#endregion

		#region Properties

		[DataMember]
		public virtual Byte[] CommentFile
		{
			get { return _commentFile; }
			set { _commentFile = value; }
		}

		[DataMember]
		public virtual Int32 CommentLine
		{
			get { return _commentLine; }
			set { _commentLine = value; }
		}

		[DataMember]
		public virtual PreReceiptDetail PreReceiptDetail
		{
			get { return _preReceiptDetail; }
			set { _preReceiptDetail = value; }
		}

		[DataMember]
		public virtual PreReceiptHeader PreReceiptHeader
		{
			get { return _preReceiptHeader; }
			set { _preReceiptHeader = value; }
		}

		[DataMember]
		public virtual String Comments
		{
			get { return _comments; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Comments must not be blank or null.");
				else _comments = value;
			}
		}


		#endregion
	}
}
