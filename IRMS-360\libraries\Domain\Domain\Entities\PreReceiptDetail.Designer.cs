using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class PreReceiptDetail : Entity
	{
		#region Fields

		private Decimal _expectedQuantity;
		private Decimal _weight;
		private Decimal? _actualQuantity;
		private Decimal? _itemCube;
		private Decimal? _itemHeight;
		private Decimal? _itemLength;
		private Decimal? _itemWidth;
		private Int32 _lineNumber;		
		private Int32? _seatRate;
		private Item _item;
		private LicensePlate _licensePlate;
		private LicensePlate _osdLicensePlate;
		private ICollection<ItemFulfillment> _itemFulfillments = new HashSet<ItemFulfillment>();
		private ICollection<PreReceiptCarton> _preReceiptCartons = new HashSet<PreReceiptCarton>();
		private ICollection<PreReceiptCharge> _preReceiptCharges = new HashSet<PreReceiptCharge>();
		private ICollection<PreReceiptComment> _preReceiptComments = new HashSet<PreReceiptComment>();
		private ICollection<ShipmentDetail> _shipmentDetails = new HashSet<ShipmentDetail>();
		private PreReceiptHeader _preReceiptHeader;
        private StatusCode _itemStatusCode;
		private StatusCode _statusCode;
		private String _acknowledgement;
		private String _billOfLading;
        private String _customerSegement;
        private String _noLineItemCode;
		private String _itemDescription;
		private String _outboundTrailer;
		private String _productCode;
		private String _purchaseOrderCode;
		private String _trailerLocationCode;
		private TripHeader _tripHeader;

		#endregion

		#region Properties

		[DataMember]
		public virtual Decimal ExpectedQuantity
		{
			get { return _expectedQuantity; }
			set { _expectedQuantity = value; }
		}

		[DataMember]
		public virtual Decimal Weight
		{
			get { return _weight; }
			set { _weight = value; }
		}

		[DataMember]
		public virtual Decimal? ActualQuantity
		{
			get { return _actualQuantity; }
			set { _actualQuantity = value; }
		}

		[DataMember]
		public virtual Decimal? ItemCube
		{
			get { return _itemCube; }
			set { _itemCube = value; }
		}

		[DataMember]
		public virtual Decimal? ItemHeight
		{
			get { return _itemHeight; }
			set { _itemHeight = value; }
		}

		[DataMember]
		public virtual Decimal? ItemLength
		{
			get { return _itemLength; }
			set { _itemLength = value; }
		}

		[DataMember]
		public virtual Decimal? ItemWidth
		{
			get { return _itemWidth; }
			set { _itemWidth = value; }
		}

		[DataMember]
		public virtual Int32 LineNumber
		{
			get { return _lineNumber; }
			set { _lineNumber = value; }
		}		

		[DataMember]
		public virtual Int32? SeatRate
		{
			get { return _seatRate; }
			set { _seatRate = value; }
		}

		[DataMember]
		public virtual Item Item
		{
			get { return _item; }
			set { _item = value; }
		}

		[DataMember]
		public virtual LicensePlate LicensePlate
		{
			get { return _licensePlate; }
			set { _licensePlate = value; }
		}

		[DataMember]
		public virtual LicensePlate OsdLicensePlate
		{
			get { return _osdLicensePlate; }
			set { _osdLicensePlate = value; }
		}

		[DataMember]
		public virtual ICollection<ItemFulfillment> ItemFulfillments
		{
			get { return _itemFulfillments; }
			set { _itemFulfillments = value; }
		}

		[DataMember]
		public virtual ICollection<PreReceiptCarton> PreReceiptCartons
		{
			get { return _preReceiptCartons; }
			set { _preReceiptCartons = value; }
		}

		[DataMember]
		public virtual ICollection<PreReceiptCharge> PreReceiptCharges
		{
			get { return _preReceiptCharges; }
			set { _preReceiptCharges = value; }
		}

		[DataMember]
		public virtual ICollection<PreReceiptComment> PreReceiptComments
		{
			get { return _preReceiptComments; }
			set { _preReceiptComments = value; }
		}

		[DataMember]
		public virtual ICollection<ShipmentDetail> ShipmentDetails
		{
			get { return _shipmentDetails; }
			set { _shipmentDetails = value; }
		}

		[DataMember]
		public virtual PreReceiptHeader PreReceiptHeader
		{
			get { return _preReceiptHeader; }
			set { _preReceiptHeader = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

        [DataMember]
        public virtual StatusCode ItemStatusCode
        {
            get { return _itemStatusCode; }
            set { _itemStatusCode = value; }
        }

		[DataMember]
		public virtual String Acknowledgement
		{
			get { return _acknowledgement; }
			set { _acknowledgement = value; }
		}

		[DataMember]
		public virtual String BillOfLading
		{
			get { return _billOfLading; }
			set { _billOfLading = value; }
		}

        [DataMember]
        public virtual String CustomerSegement
        {
            get { return _customerSegement; }
            set { _customerSegement = value; }
        }

        [DataMember]
		public virtual String NoLineItemCode
		{
			get { return _noLineItemCode; }
			set { _noLineItemCode = value; }
		}

		[DataMember]
		public virtual String ItemDescription
		{
			get { return _itemDescription; }
			set { _itemDescription = value; }
		}

		[DataMember]
		public virtual String OutboundTrailer
		{
			get { return _outboundTrailer; }
			set { _outboundTrailer = value; }
		}

		[DataMember]
		public virtual String ProductCode
		{
			get { return _productCode; }
			set { _productCode = value; }
		}

		[DataMember]
		public virtual String PurchaseOrderCode
		{
			get { return _purchaseOrderCode; }
			set { _purchaseOrderCode = value; }
		}

		[DataMember]
		public virtual String TrailerLocationCode
		{
			get { return _trailerLocationCode; }
			set { _trailerLocationCode = value; }
		}

		[DataMember]
		public virtual TripHeader TripHeader
		{
			get { return _tripHeader; }
			set { _tripHeader = value; }
		}


		#endregion
	}
}
