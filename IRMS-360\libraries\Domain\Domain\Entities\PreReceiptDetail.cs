using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class PreReceiptDetail : Entity
	{
		#region Constructor

		public PreReceiptDetail()
		{
			//
		}

        #endregion

        #region Properties
        [DataMember]
        public virtual Decimal Amount { get; set; }
        [DataMember]
        public virtual Decimal Rate { get; set; }       
        [DataMember]
        public virtual Int32 CartonCount { get; set; }
        [DataMember]
        public virtual Int32 CartonSequence { get; set; }
        [DataMember]
        public virtual Int32? ConsigneeCustomerId { get; set; }
        [DataMember]
        public virtual Int32? LicensePlateId { get; set; }
        [DataMember]
        public virtual Int32? PreReceiptHeaderId { get; set; }
        [DataMember]
        public virtual Int32? ShipperCustomerId { get; set; }
        [DataMember]
        public virtual String AddressLine1 { get; set; }       
        [DataMember]
        public virtual String AisleCode { get; set; }      
        [DataMember]
        public virtual String BillToAddressLine1 { get; set; }
        [DataMember]
        public virtual String BillToCity { get; set; }
        [DataMember]
        public virtual String BillToCustomerName { get; set; }
        [DataMember]
        public virtual String BillToState { get; set; }
        [DataMember]
        public virtual String BillToZip { get; set; }
        [DataMember]
        public virtual String City { get; set; }
        [DataMember]
        public virtual String CompanyCode { get; set; }
        [DataMember]
        public virtual String CompanyLocationCode { get; set; }
        [DataMember]
        public virtual String GlAccountNumber { get; set; }
        [DataMember]
        public virtual String ItemCode { get; set; }
        [DataMember]
        public virtual String LicensePlateCode { get; set; }       
        [DataMember]
        public virtual String PreReceiptCode { get; set; }
        [DataMember]
        public virtual String ProNumber { get; set; }
        [DataMember]
        public virtual String ShipperCustomerName { get; set; }
        [DataMember]
        public virtual String ShipToAddressLine1 { get; set; }
        [DataMember]
        public virtual String ShipToCity { get; set; }
        [DataMember]
        public virtual String ShipToState { get; set; }
        [DataMember]
        public virtual String ShipToZip { get; set; }       
        [DataMember]
        public virtual String State { get; set; }               
        [DataMember]
        public virtual String StatusCodeDescription { get; set; }       
        [DataMember]
        public virtual String RateType { get; set; }      
        [DataMember]
        public virtual String WarehouseName { get; set; }
        [DataMember]
        public virtual String Zip { get; set; }

        #endregion
    }
}
