using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class PreReceiptHeader : Entity
	{
		#region Fields

		private Carrier _carrier;
		private CompanyLocationType _companyLocationType;
		private Customer _billToCustomer;
		private Customer _consigneeCustomer;
		private Customer _shipperCustomer;
		private DateTime? _pickedUp;
		private Decimal? _totalMileage;
		private Dock _dock;
		private Int32? _customerSegement;
		private ICollection<PreReceiptCharge> _preReceiptCharges = new HashSet<PreReceiptCharge>();
		private ICollection<PreReceiptComment> _preReceiptComments = new HashSet<PreReceiptComment>();
		private ICollection<PreReceiptDetail> _preReceiptDetails = new HashSet<PreReceiptDetail>();
		private StatusCode _statusCode;
		private String _bolNumber;
		private String _comments;
		private String _driverAssist;
		private String _manifestCode;
		private String _nonItem;
		private String _preReceiptCode;
		private String _productCode;
		private String _proNumber;
		private String _referenceNumber;
		private String _termCode;
		private String _trailerCode;
		private Vendor _vendor;

		#endregion

		#region Properties

		[DataMember]
		public virtual Carrier Carrier
		{
			get { return _carrier; }
			set { _carrier = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual Customer BillToCustomer
		{
			get { return _billToCustomer; }
			set { _billToCustomer = value; }
		}

		[DataMember]
		public virtual Customer ConsigneeCustomer
		{
			get { return _consigneeCustomer; }
			set { _consigneeCustomer = value; }
		}

		[DataMember]
		public virtual Customer ShipperCustomer
		{
			get { return _shipperCustomer; }
			set { _shipperCustomer = value; }
		}

		[DataMember]
		public virtual DateTime? PickedUp
		{
			get { return _pickedUp; }
			set { _pickedUp = value; }
		}

		[DataMember]
		public virtual Decimal? TotalMileage
		{
			get { return _totalMileage; }
			set { _totalMileage = value; }
		}

		[DataMember]
		public virtual Dock Dock
		{
			get { return _dock; }
			set { _dock = value; }
		}

		[DataMember]
		public virtual Int32? CustomerSegement
		{
			get { return _customerSegement; }
			set { _customerSegement = value; }
		}

		[DataMember]
		public virtual ICollection<PreReceiptCharge> PreReceiptCharges
		{
			get { return _preReceiptCharges; }
			set { _preReceiptCharges = value; }
		}

		[DataMember]
		public virtual ICollection<PreReceiptComment> PreReceiptComments
		{
			get { return _preReceiptComments; }
			set { _preReceiptComments = value; }
		}

		[DataMember]
		public virtual ICollection<PreReceiptDetail> PreReceiptDetails
		{
			get { return _preReceiptDetails; }
			set { _preReceiptDetails = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual String BolNumber
		{
			get { return _bolNumber; }
			set { _bolNumber = value; }
		}

		[DataMember]
		public virtual String Comments
		{
			get { return _comments; }
			set { _comments = value; }
		}

		[DataMember]
		public virtual String DriverAssist
		{
			get { return _driverAssist; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("DriverAssist must not be blank or null.");
				else _driverAssist = value;
			}
		}

		[DataMember]
		public virtual String ManifestCode
		{
			get { return _manifestCode; }
			set { _manifestCode = value; }
		}

		[DataMember]
		public virtual String NonItem
		{
			get { return _nonItem; }
			set { _nonItem = value; }
		}

		[DataMember]
		public virtual String PreReceiptCode
		{
			get { return _preReceiptCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("PreReceiptCode must not be blank or null.");
				else _preReceiptCode = value;
			}
		}

		[DataMember]
		public virtual String ProductCode
		{
			get { return _productCode; }
			set { _productCode = value; }
		}

		[DataMember]
		public virtual String ProNumber
		{
			get { return _proNumber; }
			set { _proNumber = value; }
		}

		[DataMember]
		public virtual String ReferenceNumber
		{
			get { return _referenceNumber; }
			set { _referenceNumber = value; }
		}

		[DataMember]
		public virtual String TermCode
		{
			get { return _termCode; }
			set { _termCode = value; }
		}

		[DataMember]
		public virtual String TrailerCode
		{
			get { return _trailerCode; }
			set { _trailerCode = value; }
		}

		[DataMember]
		public virtual Vendor Vendor
		{
			get { return _vendor; }
			set { _vendor = value; }
		}


		#endregion
	}
}
