using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class PreReceiptHeader : Entity
	{
		#region Constructor

		public PreReceiptHeader()
		{
			//
		}

        #endregion

        #region Report Properties

        [DataMember]
        public virtual DateTime? DeliveryDate { get; set; }
        [DataMember]
        public virtual DateTime? Scanned { get; set; }
        [DataMember]
        public virtual DateTime? Unloaded { get; set; }
        [DataMember]
        public virtual Decimal Amount { get; set; }
        [DataMember]
        public virtual Decimal BillingRateCode { get; set; }
        [DataMember]
        public virtual Decimal CaseGood { get; set; }             
        [DataMember]
        public virtual Decimal Charge { get; set; }
        [DataMember]
        public virtual Decimal ConsolidationCharge { get; set; }
        [DataMember]
        public virtual Decimal DriverAssistCharge { get; set; }
        [DataMember]
        public virtual Decimal ExpectedQuantity { get; set; }
        [DataMember]
        public virtual Decimal FuelSurcharge { get; set; }
        [DataMember]
        public virtual Decimal MinimumChargeOverride { get; set; }
        [DataMember]
        public virtual Decimal MiscellaneousCharge { get; set; }
        [DataMember]
        public virtual Decimal RatePerMile { get; set; }
        [DataMember]
        public virtual Decimal RevenuePercentage { get; set; }
        [DataMember]
        public virtual Decimal StopoffCharge { get; set; }      
        [DataMember]
        public virtual Decimal TruckLoadCharge { get; set; }
        [DataMember]
        public virtual Decimal TruckLoadRate { get; set; }
        [DataMember]
        public virtual Decimal Upholstery { get; set; }
        [DataMember]
        public virtual Decimal Weight { get; set; }
        [DataMember]
        public virtual Decimal? ItemCube { get; set; }        
        [DataMember]
        public virtual Int32 CartonCount { get; set; }
        [DataMember]
        public virtual Int32 CompanyId { get; set; }
        [DataMember]
        public virtual Int32 CompanyLocationTypeId { get; set; }
        [DataMember]
        public virtual Int32 Peices { get; set; }
        [DataMember]
        public virtual Int32? BillToCustomerId { get; set; }
        [DataMember]
        public virtual Int32? ConsigneeCustomerId { get; set; }
        [DataMember]
        public virtual Int32? PreReceiptDetailId { get; set; }
        [DataMember]
        public virtual Int32? ShipperCustomerId { get; set; }
        [DataMember]
        public virtual String Acknowledgement { get; set; }       
        [DataMember]
        public virtual String AddressDirection { get; set; }       
        [DataMember]
        public virtual String AddressLine1 { get; set; }
        [DataMember]
        public virtual String AddressLine2 { get; set; }
        [DataMember]
        public virtual String AddressLine3 { get; set; }
        [DataMember]
        public virtual String AddressNumber { get; set; }
        [DataMember]
        public virtual String AddressSuffix { get; set; }
        [DataMember]
        public virtual String AisleCode { get; set; }
        [DataMember]
        public virtual String BillingCodeCode { get; set; }
        [DataMember]
        public virtual String BillOfLading { get; set; }
        [DataMember]
        public virtual String BillToAddress { get; set; }
        [DataMember]
        public virtual String BillToAddressLine1 { get; set; }
        [DataMember]
        public virtual String BillToAddressLine2 { get; set; }
        [DataMember]
        public virtual String BillToAddressLine3 { get; set; }
        [DataMember]
        public virtual String BillToCity { get; set; }
        [DataMember]
        public virtual String BillToCustomerCode { get; set; }
        [DataMember]
        public virtual String BillToState { get; set; }
        [DataMember]
        public virtual String BillToZip { get; set; }
        [DataMember]
        public virtual String City { get; set; }
        [DataMember]
        public virtual String CompanyCode { get; set; }
        [DataMember]
        public virtual String CompanyLocationCode { get; set; }
        [DataMember]
        public virtual String ConsigneeAddressLine1 { get; set; }
        [DataMember]
        public virtual String ConsigneeAddressLine2 { get; set; }
        [DataMember]
        public virtual String ConsigneeCity { get; set; }
        [DataMember]
        public virtual String ConsigneeCountry { get; set; }
        [DataMember]
        public virtual String ConsigneeCustomercode { get; set; }
        [DataMember]
        public virtual String ConsigneeCustomerName { get; set; }
        [DataMember]
        public virtual String ConsigneeState { get; set; }
        [DataMember]
        public virtual String ConsigneeZip { get; set; }
        [DataMember]
        public virtual String Country { get; set; }
        [DataMember]
        public virtual String DeliveryDay { get; set; }
        [DataMember]
        public virtual String DeliveryTime { get; set; }
        [DataMember]
        public virtual String DeliverToAddress { get; set; }
        [DataMember]
        public virtual String DockLocation { get; set; }
        [DataMember]
        public virtual String FootageUsage { get; set; }        
        [DataMember]
        public virtual String HeaderStatus { get; set; }
        [DataMember]
        public virtual String ItemCode { get; set; }
        [DataMember]
        public virtual String ItemDescription { get; set; }
        [DataMember]
        public virtual String ItemStatus { get; set; }
        [DataMember]
        public virtual String LicensePlateCode { get; set; }
        [DataMember]
        public virtual String LineStatus { get; set; }
        [DataMember]
        public virtual String OsdType { get; set; }
        [DataMember]
        public virtual String OutboundTrailerCode { get; set; }
        [DataMember]
        public virtual String PhysicalAddress { get; set; }
        [DataMember]
        public virtual String PurchaseOrderCode { get; set; }
        [DataMember]
        public virtual String RemitToAddress { get; set; }
        [DataMember]
        public virtual String ScheduledWith { get; set; }
        [DataMember]
        public virtual String ScheduledBy { get; set; }
        [DataMember]
        public virtual String SerialNumber { get; set; }
        [DataMember]
        public virtual String ShipperAddress { get; set; }
        [DataMember]
        public virtual String ShipperAddressLine1 { get; set; }
        [DataMember]
        public virtual String ShipperAddressLine2 { get; set; }
        [DataMember]
        public virtual String ShipperCity { get; set; }
        [DataMember]
        public virtual String ShipperCountry { get; set; }
        [DataMember]
        public virtual String ShipperCustomerCode { get; set; }
        [DataMember]
        public virtual String ShipperCustomerName { get; set; }
        [DataMember]
        public virtual String ShipperState { get; set; }
        [DataMember]
        public virtual String ShipperZip { get; set; }
        [DataMember]
        public virtual String ShipToAddressLine1 { get; set; }
        [DataMember]
        public virtual String ShipToAddressLine2 { get; set; }
        [DataMember]
        public virtual String ShipToAddressLine3 { get; set; }
        [DataMember]
        public virtual String ShipToCity { get; set; }
        [DataMember]
        public virtual String ShipToState { get; set; }
        [DataMember]
        public virtual String ShipToZip { get; set; }
        [DataMember]
        public virtual String State { get; set; }
        [DataMember]
        public virtual String StateCode { get; set; }
        [DataMember]
        public virtual String StopNumber { get; set; }
        [DataMember]
        public virtual String TripCode { get; set; }
        [DataMember]
        public virtual String Type { get; set; }
        [DataMember]
        public virtual String WarehouseName { get; set; }
        [DataMember]
        public virtual String Zip { get; set; }
        [DataMember]
        public virtual String ZipCode { get; set; }
        [DataMember]
        public virtual String ZipCodeCode { get; set; } 

        #endregion Report Properties
    }
}
