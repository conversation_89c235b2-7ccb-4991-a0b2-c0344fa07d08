using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class PreReceiptOverage : Entity
	{
		#region Fields

		private CompanyLocationType _companyLocationType;
		private LicensePlate _licensePlate;
		private Location _location;
		private String _itemCode;
		private String _shipperManifest;
		private String _shipperName;

		#endregion

		#region Properties

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual LicensePlate LicensePlate
		{
			get { return _licensePlate; }
			set { _licensePlate = value; }
		}

		[DataMember]
		public virtual Location Location
		{
			get { return _location; }
			set { _location = value; }
		}

		[DataMember]
		public virtual String ItemCode
		{
			get { return _itemCode; }
			set { _itemCode = value; }
		}

		[DataMember]
		public virtual String ShipperManifest
		{
			get { return _shipperManifest; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ShipperManifest must not be blank or null.");
				else _shipperManifest = value;
			}
		}

		[DataMember]
		public virtual String ShipperName
		{
			get { return _shipperName; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ShipperName must not be blank or null.");
				else _shipperName = value;
			}
		}


		#endregion
	}
}
