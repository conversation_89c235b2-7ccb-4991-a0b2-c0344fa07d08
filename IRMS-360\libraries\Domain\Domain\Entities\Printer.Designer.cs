using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Printer : Entity
	{
		#region Fields

		private Agency _agency;
		private CompanyLocationType _companyLocationType;
		private ICollection<DockPrinter> _dockPrinters = new HashSet<DockPrinter>();
		private ICollection<PrinterTypePrinter> _printerTypePrinters = new HashSet<PrinterTypePrinter>();
		private Manufacturer _manufacturer;
		private String _active;
		private String _defaultTray;
		private String _description;
		private String _externalIpAddress;
		private String _ipAddress;
		private String _printerCode;
		private String _queueName;
		private Vendor _vendor;

		#endregion

		#region Properties

		[DataMember]
		public virtual Agency Agency
		{
			get { return _agency; }
			set { _agency = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual ICollection<DockPrinter> DockPrinters
		{
			get { return _dockPrinters; }
			set { _dockPrinters = value; }
		}

		[DataMember]
		public virtual ICollection<PrinterTypePrinter> PrinterTypePrinters
		{
			get { return _printerTypePrinters; }
			set { _printerTypePrinters = value; }
		}

		[DataMember]
		public virtual Manufacturer Manufacturer
		{
			get { return _manufacturer; }
			set { _manufacturer = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String DefaultTray
		{
			get { return _defaultTray; }
			set { _defaultTray = value; }
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String ExternalIpAddress
		{
			get { return _externalIpAddress; }
			set { _externalIpAddress = value; }
		}

		[DataMember]
		public virtual String IpAddress
		{
			get { return _ipAddress; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("IpAddress must not be blank or null.");
				else _ipAddress = value;
			}
		}

		[DataMember]
		public virtual String PrinterCode
		{
			get { return _printerCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("PrinterCode must not be blank or null.");
				else _printerCode = value;
			}
		}

		[DataMember]
		public virtual String QueueName
		{
			get { return _queueName; }
			set { _queueName = value; }
		}

		[DataMember]
		public virtual Vendor Vendor
		{
			get { return _vendor; }
			set { _vendor = value; }
		}


		#endregion
	}
}
