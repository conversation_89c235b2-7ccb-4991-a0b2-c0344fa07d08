using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class PrinterTray : Entity
	{
		#region Fields

		private Int32 _number;
		private String _name;

		#endregion

		#region Properties

		[DataMember]
		public virtual Int32 Number
		{
			get { return _number; }
			set { _number = value; }
		}

		[DataMember]
		public virtual String Name
		{
			get { return _name; }
			set { _name = value; }
		}

		#endregion
	}
}
