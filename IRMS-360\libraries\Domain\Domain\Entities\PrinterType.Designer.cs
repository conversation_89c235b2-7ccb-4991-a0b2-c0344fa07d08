using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class PrinterType : Entity
	{
		#region Fields

		private ICollection<PrinterTypePrinter> _printerTypePrinters = new HashSet<PrinterTypePrinter>();
		private String _active;
		private String _description;
		private String _printerTypeCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<PrinterTypePrinter> PrinterTypePrinters
		{
			get { return _printerTypePrinters; }
			set { _printerTypePrinters = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String PrinterTypeCode
		{
			get { return _printerTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("PrinterTypeCode must not be blank or null.");
				else _printerTypeCode = value;
			}
		}


		#endregion
	}
}
