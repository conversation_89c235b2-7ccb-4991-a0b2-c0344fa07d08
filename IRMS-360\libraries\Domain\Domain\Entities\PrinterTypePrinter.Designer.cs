using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class PrinterTypePrinter : Entity
	{
		#region Fields

		private ICollection<ReportRequestDestination> _reportRequestDestinations = new HashSet<ReportRequestDestination>();
		private ICollection<ZonePrinter> _zonePrinters = new HashSet<ZonePrinter>();
		private Printer _printer;
		private PrinterType _printerType;
		private String _active;
		private String _paperTray;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<ReportRequestDestination> ReportRequestDestinations
		{
			get { return _reportRequestDestinations; }
			set { _reportRequestDestinations = value; }
		}

		[DataMember]
		public virtual ICollection<ZonePrinter> ZonePrinters
		{
			get { return _zonePrinters; }
			set { _zonePrinters = value; }
		}

		[DataMember]
		public virtual Printer Printer
		{
			get { return _printer; }
			set { _printer = value; }
		}

		[DataMember]
		public virtual PrinterType PrinterType
		{
			get { return _printerType; }
			set { _printerType = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String PaperTray
		{
			get { return _paperTray; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("PaperTray must not be blank or null.");
				else _paperTray = value;
			}
		}


		#endregion
	}
}
