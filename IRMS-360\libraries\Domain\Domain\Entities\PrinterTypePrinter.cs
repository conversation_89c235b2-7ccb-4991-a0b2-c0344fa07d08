using System;
using System.Drawing.Printing;
using System.Linq;
using System.Runtime.Serialization;

using Upp.Shared.Utilities;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class PrinterTypePrinter : Entity
	{
		#region Properties

		[DataMember]
		public virtual PrinterTray PrinterTray { get; set; }
		[DataMember]
		public virtual String IpAddress { get; set; }
		[DataMember]
		public virtual String PrinterCode { get; set; }
		[DataMember]
		public virtual String PrinterDescription { get; set; }
		[DataMember]
		public virtual String PrinterTypeCode { get; set; }
		[DataMember]
		public virtual String PrinterTypeDescription { get; set; }
		[DataMember]
		public virtual String QueueName { get; set; }

		#endregion

		#region Constructor

		public PrinterTypePrinter()
		{
			//
		}

		#endregion

		#region Methods.Public

		public virtual void FindPrinterTray()
		{
			using (PrintDocument document = new PrintDocument())
			{
				Int32 number = Converter.ToInt32(_paperTray);
				//
				document.PrinterSettings.PrinterName = _printer.QueueName;
				var trays = from PaperSource item in document.PrinterSettings.PaperSources
							where item.RawKind.Equals(number)
							select item;
				if (trays.Count() == 1)
				{
					PaperSource tray = trays.First();
					//
					this.PrinterTray = new PrinterTray();
					this.PrinterTray.Name = tray.SourceName;
					this.PrinterTray.Number = tray.RawKind;
				}
			}
			//
			if (this.PrinterTray == null)
			{
				String error = String.Format("Unable to find tray {0} in {1}.", _paperTray, _printer.Description);
				if (Log.Instance != null) Log.Instance.WriteError(error, "FindPrinterTray()");
			}
		}

		#endregion
	}
}
