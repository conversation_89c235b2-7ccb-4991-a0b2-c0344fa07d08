using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Priority : Entity
	{
		#region Fields

		private FunctionalAreaCode _functionalAreaCode;
		private Int32 _sortOrder;
		private ICollection<InventoryTask> _inventoryTasks = new HashSet<InventoryTask>();
		private ICollection<OrderHeader> _orderHeaders = new HashSet<OrderHeader>();
		private ICollection<PurchaseOrderHeader> _purchaseOrderHeaders = new HashSet<PurchaseOrderHeader>();
		private ICollection<RequisitionHeader> _requisitionHeaders = new HashSet<RequisitionHeader>();
		private String _active;
		private String _description;
		private String _priorityCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual FunctionalAreaCode FunctionalAreaCode
		{
			get { return _functionalAreaCode; }
			set { _functionalAreaCode = value; }
		}

		[DataMember]
		public virtual Int32 SortOrder
		{
			get { return _sortOrder; }
			set { _sortOrder = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryTask> InventoryTasks
		{
			get { return _inventoryTasks; }
			set { _inventoryTasks = value; }
		}

		[DataMember]
		public virtual ICollection<OrderHeader> OrderHeaders
		{
			get { return _orderHeaders; }
			set { _orderHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<PurchaseOrderHeader> PurchaseOrderHeaders
		{
			get { return _purchaseOrderHeaders; }
			set { _purchaseOrderHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<RequisitionHeader> RequisitionHeaders
		{
			get { return _requisitionHeaders; }
			set { _requisitionHeaders = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String PriorityCode
		{
			get { return _priorityCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("PriorityCode must not be blank or null.");
				else _priorityCode = value;
			}
		}


		#endregion
	}
}
