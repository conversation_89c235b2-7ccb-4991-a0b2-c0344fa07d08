using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ProcessStatus : Entity
	{
		#region Fields

		private ICollection<ClaimHeader> _claimHeaders = new HashSet<ClaimHeader>();
		private String _active;
		private String _description;
		private String _processStatusCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<ClaimHeader> ClaimHeaders
		{
			get { return _claimHeaders; }
			set { _claimHeaders = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String ProcessStatusCode
		{
			get { return _processStatusCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ProcessStatusCode must not be blank or null.");
				else _processStatusCode = value;
			}
		}


		#endregion
	}
}
