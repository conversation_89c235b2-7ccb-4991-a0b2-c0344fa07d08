using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Program : Entity
	{
		#region Fields

		private ProgramType _programType;
		private String _programDirection;
		private String _programInputFile;
		private String _programLocation;
		private String _programName;
		private String _programOutputFile;

		#endregion

		#region Properties

		[DataMember]
		public virtual ProgramType ProgramType
		{
			get { return _programType; }
			set { _programType = value; }
		}

		[DataMember]
		public virtual String ProgramDirection
		{
			get { return _programDirection; }
			set { _programDirection = value; }
		}

		[DataMember]
		public virtual String ProgramInputFile
		{
			get { return _programInputFile; }
			set { _programInputFile = value; }
		}

		[DataMember]
		public virtual String ProgramLocation
		{
			get { return _programLocation; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ProgramLocation must not be blank or null.");
				else _programLocation = value;
			}
		}

		[DataMember]
		public virtual String ProgramName
		{
			get { return _programName; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ProgramName must not be blank or null.");
				else _programName = value;
			}
		}

		[DataMember]
		public virtual String ProgramOutputFile
		{
			get { return _programOutputFile; }
			set { _programOutputFile = value; }
		}


		#endregion
	}
}
