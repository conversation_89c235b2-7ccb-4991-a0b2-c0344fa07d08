using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ProgramType : Entity
	{
		#region Fields

		private FunctionalAreaCode _functionalAreaCode;
		private ICollection<Program> _programs = new HashSet<Program>();
		private ICollection<ProviderProgram> _providerPrograms = new HashSet<ProviderProgram>();
		private String _active;
		private String _description;
		private String _programTypeCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual FunctionalAreaCode FunctionalAreaCode
		{
			get { return _functionalAreaCode; }
			set { _functionalAreaCode = value; }
		}

		[DataMember]
		public virtual ICollection<Program> Programs
		{
			get { return _programs; }
			set { _programs = value; }
		}

		[DataMember]
		public virtual ICollection<ProviderProgram> ProviderPrograms
		{
			get { return _providerPrograms; }
			set { _providerPrograms = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String ProgramTypeCode
		{
			get { return _programTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ProgramTypeCode must not be blank or null.");
				else _programTypeCode = value;
			}
		}


		#endregion
	}
}
