using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Provider : Entity
	{
		#region Fields

		private Byte[] _ftpUserPassword;
		private InsuranceServiceType _insuranceServiceType;
		private ICollection<BusinessRuleDetail> _businessRuleDetails = new HashSet<BusinessRuleDetail>();
		private ICollection<Comment> _comments = new HashSet<Comment>();
		private ICollection<EntityCode> _entityCodes = new HashSet<EntityCode>();
		private ICollection<EraProviderReasonCode> _eraProviderReasonCodes = new HashSet<EraProviderReasonCode>();
		private ICollection<EraService> _eraServices = new HashSet<EraService>();
		private ICollection<Immunization> _immunizations = new HashSet<Immunization>();
		private ICollection<InsuranceType> _insuranceTypes = new HashSet<InsuranceType>();
		private ICollection<InterfaceHeader> _interfaceHeaders = new HashSet<InterfaceHeader>();
		private ICollection<OrganizationParticipant> _organizationParticipants = new HashSet<OrganizationParticipant>();
		private ICollection<Provider> _childProviders = new HashSet<Provider>();
		private ICollection<ProviderCommunication> _providerCommunications = new HashSet<ProviderCommunication>();
		private ICollection<ProviderFinancialClass> _providerFinancialClasses = new HashSet<ProviderFinancialClass>();
		private ICollection<ProviderInsurancePayer> _providerInsurancePayers = new HashSet<ProviderInsurancePayer>();
		private ICollection<ProviderLocation> _providerLocations = new HashSet<ProviderLocation>();
		private ICollection<ProviderOrganizationalUnit> _providerOrganizationalUnits = new HashSet<ProviderOrganizationalUnit>();
		private ICollection<ProviderProgram> _providerPrograms = new HashSet<ProviderProgram>();
		private ICollection<Service> _services = new HashSet<Service>();
		private ICollection<Task> _tasks = new HashSet<Task>();
		private ICollection<UserAccountProvider> _userAccountProviders = new HashSet<UserAccountProvider>();
		private PosCode _posCode;
		private Provider _parentProvider;
		private ProviderType _providerType;
		private String _active;
		private String _alternateProviderNumber;
		private String _apptDuration;
		private String _ftpUserName;
		private String _name;
		private String _nationalProviderIdentifier;
		private String _providerAccount;
		private String _providerCode;
		private String _providerNumber;
		private String _taxId;
		private String _url;
		private String _useHl7;
		private TimeZone _timeZone;

		#endregion

		#region Properties

		[DataMember]
		public virtual Byte[] FtpUserPassword
		{
			get { return _ftpUserPassword; }
			set { _ftpUserPassword = value; }
		}

		[DataMember]
		public virtual InsuranceServiceType InsuranceServiceType
		{
			get { return _insuranceServiceType; }
			set { _insuranceServiceType = value; }
		}

		[DataMember]
		public virtual ICollection<BusinessRuleDetail> BusinessRuleDetails
		{
			get { return _businessRuleDetails; }
			set { _businessRuleDetails = value; }
		}

		[DataMember]
		public virtual ICollection<Comment> Comments
		{
			get { return _comments; }
			set { _comments = value; }
		}

		[DataMember]
		public virtual ICollection<EntityCode> EntityCodes
		{
			get { return _entityCodes; }
			set { _entityCodes = value; }
		}

		[DataMember]
		public virtual ICollection<EraProviderReasonCode> EraProviderReasonCodes
		{
			get { return _eraProviderReasonCodes; }
			set { _eraProviderReasonCodes = value; }
		}

		[DataMember]
		public virtual ICollection<EraService> EraServices
		{
			get { return _eraServices; }
			set { _eraServices = value; }
		}

		[DataMember]
		public virtual ICollection<Immunization> Immunizations
		{
			get { return _immunizations; }
			set { _immunizations = value; }
		}

		[DataMember]
		public virtual ICollection<InsuranceType> InsuranceTypes
		{
			get { return _insuranceTypes; }
			set { _insuranceTypes = value; }
		}

		[DataMember]
		public virtual ICollection<InterfaceHeader> InterfaceHeaders
		{
			get { return _interfaceHeaders; }
			set { _interfaceHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<OrganizationParticipant> OrganizationParticipants
		{
			get { return _organizationParticipants; }
			set { _organizationParticipants = value; }
		}

		[DataMember]
		public virtual ICollection<Provider> ChildProviders
		{
			get { return _childProviders; }
			set { _childProviders = value; }
		}

		[DataMember]
		public virtual ICollection<ProviderCommunication> ProviderCommunications
		{
			get { return _providerCommunications; }
			set { _providerCommunications = value; }
		}

		[DataMember]
		public virtual ICollection<ProviderFinancialClass> ProviderFinancialClasses
		{
			get { return _providerFinancialClasses; }
			set { _providerFinancialClasses = value; }
		}

		[DataMember]
		public virtual ICollection<ProviderInsurancePayer> ProviderInsurancePayers
		{
			get { return _providerInsurancePayers; }
			set { _providerInsurancePayers = value; }
		}

		[DataMember]
		public virtual ICollection<ProviderLocation> ProviderLocations
		{
			get { return _providerLocations; }
			set { _providerLocations = value; }
		}

		[DataMember]
		public virtual ICollection<ProviderOrganizationalUnit> ProviderOrganizationalUnits
		{
			get { return _providerOrganizationalUnits; }
			set { _providerOrganizationalUnits = value; }
		}

		[DataMember]
		public virtual ICollection<ProviderProgram> ProviderPrograms
		{
			get { return _providerPrograms; }
			set { _providerPrograms = value; }
		}

		[DataMember]
		public virtual ICollection<Service> Services
		{
			get { return _services; }
			set { _services = value; }
		}

		[DataMember]
		public virtual ICollection<Task> Tasks
		{
			get { return _tasks; }
			set { _tasks = value; }
		}

		[DataMember]
		public virtual ICollection<UserAccountProvider> UserAccountProviders
		{
			get { return _userAccountProviders; }
			set { _userAccountProviders = value; }
		}

		[DataMember]
		public virtual PosCode PosCode
		{
			get { return _posCode; }
			set { _posCode = value; }
		}

		[DataMember]
		public virtual Provider ParentProvider
		{
			get { return _parentProvider; }
			set { _parentProvider = value; }
		}

		[DataMember]
		public virtual ProviderType ProviderType
		{
			get { return _providerType; }
			set { _providerType = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String AlternateProviderNumber
		{
			get { return _alternateProviderNumber; }
			set { _alternateProviderNumber = value; }
		}

		[DataMember]
		public virtual String ApptDuration
		{
			get { return _apptDuration; }
			set { _apptDuration = value; }
		}

		[DataMember]
		public virtual String FtpUserName
		{
			get { return _ftpUserName; }
			set { _ftpUserName = value; }
		}

		[DataMember]
		public virtual String Name
		{
			get { return _name; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Name must not be blank or null.");
				else _name = value;
			}
		}

		[DataMember]
		public virtual String NationalProviderIdentifier
		{
			get { return _nationalProviderIdentifier; }
			set { _nationalProviderIdentifier = value; }
		}

		[DataMember]
		public virtual String ProviderAccount
		{
			get { return _providerAccount; }
			set { _providerAccount = value; }
		}

		[DataMember]
		public virtual String ProviderCode
		{
			get { return _providerCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ProviderCode must not be blank or null.");
				else _providerCode = value;
			}
		}

		[DataMember]
		public virtual String ProviderNumber
		{
			get { return _providerNumber; }
			set { _providerNumber = value; }
		}

		[DataMember]
		public virtual String TaxId
		{
			get { return _taxId; }
			set { _taxId = value; }
		}

		[DataMember]
		public virtual String Url
		{
			get { return _url; }
			set { _url = value; }
		}

		[DataMember]
		public virtual String UseHl7
		{
			get { return _useHl7; }
			set { _useHl7 = value; }
		}

		[DataMember]
		public virtual TimeZone TimeZone
		{
			get { return _timeZone; }
			set { _timeZone = value; }
		}


		#endregion
	}
}
