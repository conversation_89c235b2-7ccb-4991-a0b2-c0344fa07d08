using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ProviderCommunication : Entity
	{
		#region Fields

		private CommunicationRole _communicationRole;
		private Provider _provider;
		private ProviderLocation _providerLocation;
		private ProviderOrganizationalUnit _providerOrganizationalUnit;
		private String _active;
		private String _communicationValue;
		private String _primaryCommunication;

		#endregion

		#region Properties

		[DataMember]
		public virtual CommunicationRole CommunicationRole
		{
			get { return _communicationRole; }
			set { _communicationRole = value; }
		}

		[DataMember]
		public virtual Provider Provider
		{
			get { return _provider; }
			set { _provider = value; }
		}

		[DataMember]
		public virtual ProviderLocation ProviderLocation
		{
			get { return _providerLocation; }
			set { _providerLocation = value; }
		}

		[DataMember]
		public virtual ProviderOrganizationalUnit ProviderOrganizationalUnit
		{
			get { return _providerOrganizationalUnit; }
			set { _providerOrganizationalUnit = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String CommunicationValue
		{
			get { return _communicationValue; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("CommunicationValue must not be blank or null.");
				else _communicationValue = value;
			}
		}

		[DataMember]
		public virtual String PrimaryCommunication
		{
			get { return _primaryCommunication; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("PrimaryCommunication must not be blank or null.");
				else _primaryCommunication = value;
			}
		}


		#endregion
	}
}
