using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ProviderFinancialClass : Entity
	{
		#region Fields

		private FinancialClass _financialClass;
		private ICollection<ParticipantEncounter> _participantEncounters = new HashSet<ParticipantEncounter>();
		private ICollection<ProviderInsurancePayerAlias> _providerInsurancePayerAliases = new HashSet<ProviderInsurancePayerAlias>();
		private Provider _provider;
		private String _active;

		#endregion

		#region Properties

		[DataMember]
		public virtual FinancialClass FinancialClass
		{
			get { return _financialClass; }
			set { _financialClass = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantEncounter> ParticipantEncounters
		{
			get { return _participantEncounters; }
			set { _participantEncounters = value; }
		}

		[DataMember]
		public virtual ICollection<ProviderInsurancePayerAlias> ProviderInsurancePayerAliases
		{
			get { return _providerInsurancePayerAliases; }
			set { _providerInsurancePayerAliases = value; }
		}

		[DataMember]
		public virtual Provider Provider
		{
			get { return _provider; }
			set { _provider = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}


		#endregion
	}
}
