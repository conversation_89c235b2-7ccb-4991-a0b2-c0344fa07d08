using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ProviderInsurancePayer : Entity
	{
		#region Fields

		private InsurancePayer _insurancePayer;
		private ICollection<EraVoucher> _eraVouchers = new HashSet<EraVoucher>();
		private ICollection<ProviderInsurancePayerAlias> _providerInsurancePayerAliases = new HashSet<ProviderInsurancePayerAlias>();
		private Provider _provider;
		private ProviderLocation _providerLocation;
		private String _active;
		private String _payerType;

		#endregion

		#region Properties

		[DataMember]
		public virtual InsurancePayer InsurancePayer
		{
			get { return _insurancePayer; }
			set { _insurancePayer = value; }
		}

		[DataMember]
		public virtual ICollection<EraVoucher> EraVouchers
		{
			get { return _eraVouchers; }
			set { _eraVouchers = value; }
		}

		[DataMember]
		public virtual ICollection<ProviderInsurancePayerAlias> ProviderInsurancePayerAliases
		{
			get { return _providerInsurancePayerAliases; }
			set { _providerInsurancePayerAliases = value; }
		}

		[DataMember]
		public virtual Provider Provider
		{
			get { return _provider; }
			set { _provider = value; }
		}

		[DataMember]
		public virtual ProviderLocation ProviderLocation
		{
			get { return _providerLocation; }
			set { _providerLocation = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String PayerType
		{
			get { return _payerType; }
			set { _payerType = value; }
		}


		#endregion
	}
}
