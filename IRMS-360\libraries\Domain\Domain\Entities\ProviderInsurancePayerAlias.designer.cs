using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ProviderInsurancePayerAlias : Entity
	{
		#region Fields

		private ICollection<ParticipantInsurance> _participantInsurances = new HashSet<ParticipantInsurance>();
		private ProviderFinancialClass _providerFinancialClass;
		private ProviderInsurancePayer _providerInsurancePayer;
		private String _active;
		private String _alias;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<ParticipantInsurance> ParticipantInsurances
		{
			get { return _participantInsurances; }
			set { _participantInsurances = value; }
		}

		[DataMember]
		public virtual ProviderFinancialClass ProviderFinancialClass
		{
			get { return _providerFinancialClass; }
			set { _providerFinancialClass = value; }
		}

		[DataMember]
		public virtual ProviderInsurancePayer ProviderInsurancePayer
		{
			get { return _providerInsurancePayer; }
			set { _providerInsurancePayer = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Alias
		{
			get { return _alias; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Alias must not be blank or null.");
				else _alias = value;
			}
		}


		#endregion
	}
}
