using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ProviderLocation : Entity
	{
		#region Fields

		private ICollection<EdiBatchControl> _ediBatchControls = new HashSet<EdiBatchControl>();
		private ICollection<Immunization> _immunizations = new HashSet<Immunization>();
		private ICollection<InsuranceType> _insuranceTypes = new HashSet<InsuranceType>();
		private ICollection<InterfaceCommunication> _interfaceCommunications = new HashSet<InterfaceCommunication>();
		private ICollection<InterfaceDetailDefault> _interfaceDetailDefaults = new HashSet<InterfaceDetailDefault>();
		private ICollection<ParticipantEncounter> _participantEncounters = new HashSet<ParticipantEncounter>();
		private ICollection<ProviderCommunication> _providerCommunications = new HashSet<ProviderCommunication>();
		private ICollection<ProviderInsurancePayer> _providerInsurancePayers = new HashSet<ProviderInsurancePayer>();
		private ICollection<ProviderLocationType> _providerLocationTypes = new HashSet<ProviderLocationType>();
		private ICollection<ProviderOrganizationalUnit> _providerOrganizationalUnits = new HashSet<ProviderOrganizationalUnit>();
		private ICollection<Service> _services = new HashSet<Service>();
		private ICollection<UserAccountProvider> _userAccountProviders = new HashSet<UserAccountProvider>();
		private Provider _provider;
		private String _active;
		private String _addressDirection;
		private String _addressLine1;
		private String _addressLine2;
		private String _addressLine3;
		private String _addressNumber;
		private String _addressSuffix;
		private String _alternateProviderNumber;
		private String _billingPhoneNumber;
		private String _nationalProviderIdentifier;
		private String _providerNumber;
		private String _taxId;
		private TimeZone _timeZone;
		private ZipCode _zipCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<EdiBatchControl> EdiBatchControls
		{
			get { return _ediBatchControls; }
			set { _ediBatchControls = value; }
		}

		[DataMember]
		public virtual ICollection<Immunization> Immunizations
		{
			get { return _immunizations; }
			set { _immunizations = value; }
		}

		[DataMember]
		public virtual ICollection<InsuranceType> InsuranceTypes
		{
			get { return _insuranceTypes; }
			set { _insuranceTypes = value; }
		}

		[DataMember]
		public virtual ICollection<InterfaceCommunication> InterfaceCommunications
		{
			get { return _interfaceCommunications; }
			set { _interfaceCommunications = value; }
		}

		[DataMember]
		public virtual ICollection<InterfaceDetailDefault> InterfaceDetailDefaults
		{
			get { return _interfaceDetailDefaults; }
			set { _interfaceDetailDefaults = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantEncounter> ParticipantEncounters
		{
			get { return _participantEncounters; }
			set { _participantEncounters = value; }
		}

		[DataMember]
		public virtual ICollection<ProviderCommunication> ProviderCommunications
		{
			get { return _providerCommunications; }
			set { _providerCommunications = value; }
		}

		[DataMember]
		public virtual ICollection<ProviderInsurancePayer> ProviderInsurancePayers
		{
			get { return _providerInsurancePayers; }
			set { _providerInsurancePayers = value; }
		}

		[DataMember]
		public virtual ICollection<ProviderLocationType> ProviderLocationTypes
		{
			get { return _providerLocationTypes; }
			set { _providerLocationTypes = value; }
		}

		[DataMember]
		public virtual ICollection<ProviderOrganizationalUnit> ProviderOrganizationalUnits
		{
			get { return _providerOrganizationalUnits; }
			set { _providerOrganizationalUnits = value; }
		}

		[DataMember]
		public virtual ICollection<Service> Services
		{
			get { return _services; }
			set { _services = value; }
		}

		[DataMember]
		public virtual ICollection<UserAccountProvider> UserAccountProviders
		{
			get { return _userAccountProviders; }
			set { _userAccountProviders = value; }
		}

		[DataMember]
		public virtual Provider Provider
		{
			get { return _provider; }
			set { _provider = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set { _active = value; }
		}

		[DataMember]
		public virtual String AddressDirection
		{
			get { return _addressDirection; }
			set { _addressDirection = value; }
		}

		[DataMember]
		public virtual String AddressLine1
		{
			get { return _addressLine1; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("AddressLine1 must not be blank or null.");
				else _addressLine1 = value;
			}
		}

		[DataMember]
		public virtual String AddressLine2
		{
			get { return _addressLine2; }
			set { _addressLine2 = value; }
		}

		[DataMember]
		public virtual String AddressLine3
		{
			get { return _addressLine3; }
			set { _addressLine3 = value; }
		}

		[DataMember]
		public virtual String AddressNumber
		{
			get { return _addressNumber; }
			set { _addressNumber = value; }
		}

		[DataMember]
		public virtual String AddressSuffix
		{
			get { return _addressSuffix; }
			set { _addressSuffix = value; }
		}

		[DataMember]
		public virtual String AlternateProviderNumber
		{
			get { return _alternateProviderNumber; }
			set { _alternateProviderNumber = value; }
		}

		[DataMember]
		public virtual String BillingPhoneNumber
		{
			get { return _billingPhoneNumber; }
			set { _billingPhoneNumber = value; }
		}

		[DataMember]
		public virtual String NationalProviderIdentifier
		{
			get { return _nationalProviderIdentifier; }
			set { _nationalProviderIdentifier = value; }
		}

		[DataMember]
		public virtual String ProviderNumber
		{
			get { return _providerNumber; }
			set { _providerNumber = value; }
		}

		[DataMember]
		public virtual String TaxId
		{
			get { return _taxId; }
			set { _taxId = value; }
		}

		[DataMember]
		public virtual TimeZone TimeZone
		{
			get { return _timeZone; }
			set { _timeZone = value; }
		}

		[DataMember]
		public virtual ZipCode ZipCode
		{
			get { return _zipCode; }
			set { _zipCode = value; }
		}


		#endregion
	}
}
