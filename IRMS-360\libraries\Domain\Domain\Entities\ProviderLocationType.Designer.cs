using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ProviderLocationType : Entity
	{
		#region Fields

		private ICollection<OrderHeader> _orderHeaders = new HashSet<OrderHeader>();
		private ICollection<OrganizationParticipant> _organizationParticipants = new HashSet<OrganizationParticipant>();
		private LocationType _locationType;
		private ProviderLocation _providerLocation;
		private String _active;
		private String _description;
		private String _providerLocationCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<OrderHeader> OrderHeaders
		{
			get { return _orderHeaders; }
			set { _orderHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<OrganizationParticipant> OrganizationParticipants
		{
			get { return _organizationParticipants; }
			set { _organizationParticipants = value; }
		}

		[DataMember]
		public virtual LocationType LocationType
		{
			get { return _locationType; }
			set { _locationType = value; }
		}

		[DataMember]
		public virtual ProviderLocation ProviderLocation
		{
			get { return _providerLocation; }
			set { _providerLocation = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set { _description = value; }
		}

		[DataMember]
		public virtual String ProviderLocationCode
		{
			get { return _providerLocationCode; }
			set { _providerLocationCode = value; }
		}


		#endregion
	}
}
