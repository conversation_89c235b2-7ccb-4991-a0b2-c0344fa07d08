using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ProviderOrganizationalUnit : Entity
	{
		#region Fields

		private ICollection<EdiBatchControl> _ediBatchControls = new HashSet<EdiBatchControl>();
		private ICollection<Immunization> _immunizations = new HashSet<Immunization>();
		private ICollection<InsuranceType> _insuranceTypes = new HashSet<InsuranceType>();
		private ICollection<InterfaceCommunication> _interfaceCommunications = new HashSet<InterfaceCommunication>();
		private ICollection<InterfaceDetailDefault> _interfaceDetailDefaults = new HashSet<InterfaceDetailDefault>();
		private ICollection<InventoryItem> _inventoryItems = new HashSet<InventoryItem>();
		private ICollection<OrderHeader> _orderHeaders = new HashSet<OrderHeader>();
		private ICollection<OrganizationParticipant> _organizationParticipants = new HashSet<OrganizationParticipant>();
		private ICollection<ParticipantEncounter> _participantEncounters = new HashSet<ParticipantEncounter>();
		private ICollection<ProviderCommunication> _providerCommunications = new HashSet<ProviderCommunication>();
		private ICollection<ProviderOrganizationalUnit> _childProviderOrganizationalUnits = new HashSet<ProviderOrganizationalUnit>();
		private ICollection<Service> _services = new HashSet<Service>();
		private ICollection<UserAccountProvider> _userAccountProviders = new HashSet<UserAccountProvider>();
		private OrganizationalUnit _organizationalUnit;
		private Provider _provider;
		private ProviderLocation _providerLocation;
		private ProviderOrganizationalUnit _parentProviderOrganizationalUnit;
		private String _active;
		private String _description;
		private String _organizationUnitCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<EdiBatchControl> EdiBatchControls
		{
			get { return _ediBatchControls; }
			set { _ediBatchControls = value; }
		}

		[DataMember]
		public virtual ICollection<Immunization> Immunizations
		{
			get { return _immunizations; }
			set { _immunizations = value; }
		}

		[DataMember]
		public virtual ICollection<InsuranceType> InsuranceTypes
		{
			get { return _insuranceTypes; }
			set { _insuranceTypes = value; }
		}

		[DataMember]
		public virtual ICollection<InterfaceCommunication> InterfaceCommunications
		{
			get { return _interfaceCommunications; }
			set { _interfaceCommunications = value; }
		}

		[DataMember]
		public virtual ICollection<InterfaceDetailDefault> InterfaceDetailDefaults
		{
			get { return _interfaceDetailDefaults; }
			set { _interfaceDetailDefaults = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryItem> InventoryItems
		{
			get { return _inventoryItems; }
			set { _inventoryItems = value; }
		}

		[DataMember]
		public virtual ICollection<OrderHeader> OrderHeaders
		{
			get { return _orderHeaders; }
			set { _orderHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<OrganizationParticipant> OrganizationParticipants
		{
			get { return _organizationParticipants; }
			set { _organizationParticipants = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantEncounter> ParticipantEncounters
		{
			get { return _participantEncounters; }
			set { _participantEncounters = value; }
		}

		[DataMember]
		public virtual ICollection<ProviderCommunication> ProviderCommunications
		{
			get { return _providerCommunications; }
			set { _providerCommunications = value; }
		}

		[DataMember]
		public virtual ICollection<ProviderOrganizationalUnit> ChildProviderOrganizationalUnits
		{
			get { return _childProviderOrganizationalUnits; }
			set { _childProviderOrganizationalUnits = value; }
		}

		[DataMember]
		public virtual ICollection<Service> Services
		{
			get { return _services; }
			set { _services = value; }
		}

		[DataMember]
		public virtual ICollection<UserAccountProvider> UserAccountProviders
		{
			get { return _userAccountProviders; }
			set { _userAccountProviders = value; }
		}

		[DataMember]
		public virtual OrganizationalUnit OrganizationalUnit
		{
			get { return _organizationalUnit; }
			set { _organizationalUnit = value; }
		}

		[DataMember]
		public virtual Provider Provider
		{
			get { return _provider; }
			set { _provider = value; }
		}

		[DataMember]
		public virtual ProviderLocation ProviderLocation
		{
			get { return _providerLocation; }
			set { _providerLocation = value; }
		}

		[DataMember]
		public virtual ProviderOrganizationalUnit ParentProviderOrganizationalUnit
		{
			get { return _parentProviderOrganizationalUnit; }
			set { _parentProviderOrganizationalUnit = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String OrganizationUnitCode
		{
			get { return _organizationUnitCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("OrganizationUnitCode must not be blank or null.");
				else _organizationUnitCode = value;
			}
		}


		#endregion
	}
}
