using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ProviderProgram : Entity
	{
		#region Fields

		private DateTime _effective;
		private DateTime? _expiration;
		private ProgramType _programType;
		private Provider _provider;
		private String _notes;
		private String _programLicense;
		private String _providerProgramCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime Effective
		{
			get { return _effective; }
			set { _effective = value; }
		}

		[DataMember]
		public virtual DateTime? Expiration
		{
			get { return _expiration; }
			set { _expiration = value; }
		}

		[DataMember]
		public virtual ProgramType ProgramType
		{
			get { return _programType; }
			set { _programType = value; }
		}

		[DataMember]
		public virtual Provider Provider
		{
			get { return _provider; }
			set { _provider = value; }
		}

		[DataMember]
		public virtual String Notes
		{
			get { return _notes; }
			set { _notes = value; }
		}

		[DataMember]
		public virtual String ProgramLicense
		{
			get { return _programLicense; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ProgramLicense must not be blank or null.");
				else _programLicense = value;
			}
		}

		[DataMember]
		public virtual String ProviderProgramCode
		{
			get { return _providerProgramCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ProviderProgramCode must not be blank or null.");
				else _providerProgramCode = value;
			}
		}


		#endregion
	}
}
