using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ProviderType : Entity
	{
		#region Fields

		private ICollection<Provider> _providers = new HashSet<Provider>();
		private ICollection<ProviderType> _childProviderTypes = new HashSet<ProviderType>();
		private ProviderType _parentProviderType;
		private String _active;
		private String _description;
		private String _providerTypeCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<Provider> Providers
		{
			get { return _providers; }
			set { _providers = value; }
		}

		[DataMember]
		public virtual ICollection<ProviderType> ChildProviderTypes
		{
			get { return _childProviderTypes; }
			set { _childProviderTypes = value; }
		}

		[DataMember]
		public virtual ProviderType ParentProviderType
		{
			get { return _parentProviderType; }
			set { _parentProviderType = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String ProviderTypeCode
		{
			get { return _providerTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ProviderTypeCode must not be blank or null.");
				else _providerTypeCode = value;
			}
		}


		#endregion
	}
}
