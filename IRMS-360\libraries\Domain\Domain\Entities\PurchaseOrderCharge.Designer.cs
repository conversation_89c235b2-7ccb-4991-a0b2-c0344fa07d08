using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class PurchaseOrderCharge : Entity
	{
		#region Fields

		private BillingCode _billingCode;
		private Decimal _amount;
		private PurchaseOrderDetail _purchaseOrderDetail;
		private PurchaseOrderHeader _purchaseOrderHeader;
		private String _comments;

		#endregion

		#region Properties

		[DataMember]
		public virtual BillingCode BillingCode
		{
			get { return _billingCode; }
			set { _billingCode = value; }
		}

		[DataMember]
		public virtual Decimal Amount
		{
			get { return _amount; }
			set { _amount = value; }
		}

		[DataMember]
		public virtual PurchaseOrderDetail PurchaseOrderDetail
		{
			get { return _purchaseOrderDetail; }
			set { _purchaseOrderDetail = value; }
		}

		[DataMember]
		public virtual PurchaseOrderHeader PurchaseOrderHeader
		{
			get { return _purchaseOrderHeader; }
			set { _purchaseOrderHeader = value; }
		}

		[DataMember]
		public virtual String Comments
		{
			get { return _comments; }
			set { _comments = value; }
		}


		#endregion
	}
}
