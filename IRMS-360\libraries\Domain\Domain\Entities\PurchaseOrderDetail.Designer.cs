using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class PurchaseOrderDetail : Entity
	{
		#region Fields

		private CompanyGlobalLocation _companyGlobalLocation;
		private DateTime? _lotExpiration;
		private DateTime? _manufactureDate;
		private DateTime? _required;
        private Decimal? _catchWeight;
        private Decimal _quantity;
        private Decimal _unitCost;
		private Int32 _lineNumber;
		private Int32 _lineNumberSequence;
		private Item _item;
		private ICollection<Alert> _alerts = new HashSet<Alert>();
		private ICollection<ItemFulfillment> _itemFulfillments = new HashSet<ItemFulfillment>();
		private ICollection<PurchaseOrderFunding> _purchaseOrderFundings = new HashSet<PurchaseOrderFunding>();
		private ICollection<ReceiptDetail> _receiptDetails = new HashSet<ReceiptDetail>();
		private ICollection<ReturnDetail> _returnDetails = new HashSet<ReturnDetail>();
		private ICollection<ReturnReasonDetail> _returnReasonDetails = new HashSet<ReturnReasonDetail>();
		private OrganizationParticipant _approver;
		private PurchaseOrderHeader _purchaseOrderHeader;
		private RequisitionDetail _requisitionDetail;
		private StatusCode _statusCode;
		private String _comments;
		private String _itemCode;
		private String _itemDescription;
		private String _lotNumber;
		private UnitOfMeasure _unitOfMeasure;
		private VendorItem _vendorItem;

		#endregion

		#region Properties

		[DataMember]
		public virtual CompanyGlobalLocation CompanyGlobalLocation
		{
			get { return _companyGlobalLocation; }
			set { _companyGlobalLocation = value; }
		}

		[DataMember]
		public virtual DateTime? LotExpiration
		{
			get { return _lotExpiration; }
			set { _lotExpiration = value; }
		}

		[DataMember]
		public virtual DateTime? ManufactureDate
		{
			get { return _manufactureDate; }
			set { _manufactureDate = value; }
		}

		[DataMember]
		public virtual DateTime? Required
		{
			get { return _required; }
			set { _required = value; }
		}

        [DataMember]
        public virtual Decimal? CatchWeight
        {
            get { return _catchWeight; }
            set { _catchWeight = value; }
        }

        [DataMember]
		public virtual Decimal Quantity
		{
			get { return _quantity; }
			set { _quantity = value; }
		}

		[DataMember]
		public virtual Decimal UnitCost
		{
			get { return _unitCost; }
			set { _unitCost = value; }
		}

		[DataMember]
		public virtual Int32 LineNumber
		{
			get { return _lineNumber; }
			set { _lineNumber = value; }
		}

		[DataMember]
		public virtual Int32 LineNumberSequence
		{
			get { return _lineNumberSequence; }
			set { _lineNumberSequence = value; }
		}

		[DataMember]
		public virtual Item Item
		{
			get { return _item; }
			set { _item = value; }
		}

		[DataMember]
		public virtual ICollection<Alert> Alerts
		{
			get { return _alerts; }
			set { _alerts = value; }
		}

		[DataMember]
		public virtual ICollection<ItemFulfillment> ItemFulfillments
		{
			get { return _itemFulfillments; }
			set { _itemFulfillments = value; }
		}

		[DataMember]
		public virtual ICollection<PurchaseOrderFunding> PurchaseOrderFundings
		{
			get { return _purchaseOrderFundings; }
			set { _purchaseOrderFundings = value; }
		}

		[DataMember]
		public virtual ICollection<ReceiptDetail> ReceiptDetails
		{
			get { return _receiptDetails; }
			set { _receiptDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ReturnDetail> ReturnDetails
		{
			get { return _returnDetails; }
			set { _returnDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ReturnReasonDetail> ReturnReasonDetails
		{
			get { return _returnReasonDetails; }
			set { _returnReasonDetails = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant Approver
		{
			get { return _approver; }
			set { _approver = value; }
		}

		[DataMember]
		public virtual PurchaseOrderHeader PurchaseOrderHeader
		{
			get { return _purchaseOrderHeader; }
			set { _purchaseOrderHeader = value; }
		}

		[DataMember]
		public virtual RequisitionDetail RequisitionDetail
		{
			get { return _requisitionDetail; }
			set { _requisitionDetail = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual String Comments
		{
			get { return _comments; }
			set { _comments = value; }
		}

		[DataMember]
		public virtual String ItemCode
		{
			get { return _itemCode; }
			set { _itemCode = value; }
		}

		[DataMember]
		public virtual String ItemDescription
		{
			get { return _itemDescription; }
			set { _itemDescription = value; }
		}

		[DataMember]
		public virtual String LotNumber
		{
			get { return _lotNumber; }
			set { _lotNumber = value; }
		}

		[DataMember]
		public virtual UnitOfMeasure UnitOfMeasure
		{
			get { return _unitOfMeasure; }
			set { _unitOfMeasure = value; }
		}

		[DataMember]
		public virtual VendorItem VendorItem
		{
			get { return _vendorItem; }
			set { _vendorItem = value; }
		}


		#endregion
	}
}
