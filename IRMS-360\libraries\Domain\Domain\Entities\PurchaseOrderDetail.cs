using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;

using NHibernate.Criterion;
using NHibernate.Transform;

using Upp.Irms.Constants;
using Upp.Irms.Core;
using Upp.Shared.Utilities;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class PurchaseOrderDetail : Entity
	{
		#region Properties

		[DataMember]
		public virtual Boolean FullLpn { get; set; }
		[DataMember]
		public virtual Boolean HideQuantity { get; set; }
		[DataMember]
		public virtual Boolean SkipUomQuantity { get; set; }
		[DataMember]
		public virtual CartonSize CartonSize { get; set; }
        [DataMember]
        public virtual DateTime ReturnDate { get; set; }
		[DataMember]
		public virtual Decimal ExpectedQuantity { get; set; }
		[DataMember]
		public virtual Decimal ReceivedQuantity { get; set; }
		[DataMember]
		public virtual Decimal RequiredQuantity { get; set; }
		[DataMember]
		public virtual Decimal? OriginalQuantity { get; set; }
		[DataMember]
		public virtual Int32 Copies { get; set; }
		[DataMember]
		public virtual Int32 Count { get; set; }
        [DataMember]
        public virtual Int32 ItemId { get; set; }
		[DataMember]
		public virtual Int32 ReturnedQuantity { get; set; }
		[DataMember]
		public virtual Int32? Overages { get; set; }
		[DataMember]
		public virtual Int32? UomQuantity { get; set; }
        [DataMember]
        public virtual Int32 UnitOfMeasureId { get; set; }
        [DataMember]
        public virtual Int32? VendorItemId { get; set; }
		[DataMember]
		public virtual ICollection<InspectionHeader> InspectionHeaders { get; set; }
		[DataMember]
		public virtual ICollection<InventoryAttribute> InventoryAttributes { get; set; }
		[DataMember]
		public virtual ICollection<UdfItemValue> UdfItemValues { get; set; }
		[DataMember]
		public virtual LicensePlate LicensePlate { get; set; }
		[DataMember]
		public virtual List<String> DetailComments { get; set; }
		[DataMember]
		public virtual Location Location { get; set; }
		[DataMember]
		public virtual PrinterTypePrinter PrinterTypePrinter { get; set; }
		[DataMember]
		public virtual StatusCode StockStatusCode { get; set; }
		[DataMember]
		public virtual String Default { get; set; }
        [DataMember]
        public virtual String GLNCity { get; set; }
        [DataMember]
        public virtual String GtinCode { get; set; }
        [DataMember]
		public virtual String ItemLongDescription { get; set; }
        [DataMember]
        public virtual String LicensePlateCode { get; set; }
        [DataMember]
        public virtual String LocationGLNCode { get; set; }
        [DataMember]
        public virtual String OrderClass { get; set; }
        [DataMember]
        public virtual String OrderHeaderType { get; set; }
        [DataMember]
		public virtual String PurchaseOrderCode { get; set; }
		[DataMember]
		public virtual String PurchaseOrderSuffix { get; set; }
		[DataMember]
		public virtual String ReceiptCode { get; set; }
		[DataMember]
		public virtual String SerialNumbers { get; set; }
		[DataMember]
		public virtual String Status { get; set; }
		[DataMember]
		public virtual String UnitDescription { get; set; }
		[DataMember]
		public virtual String UomCode { get; set; }
		[DataMember]
		public virtual String UomDescription { get; set; }
		[DataMember]
		public virtual String UpcCode { get; set; }
		[DataMember]
		public virtual String VendorItemCode { get; set; }
		[DataMember]
		public virtual String ZoneCode { get; set; }
        [DataMember]
        public virtual string TiedOrder { get; set; }
        #endregion

        #region Constructor

        public PurchaseOrderDetail()
		{
			//
		}

		#endregion

		#region Methods.Private

		private void ValidateAssetCodes(string[] assets)
		{
			DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
				.Add("Item.AssetIndicator", "Y")
				.Add(Restrictions.In("AssetCode", assets))
				.SetProjection(Projections.Count("Id"));
			//
			Agency agency = Registry.Find<Agency>();
			CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
			if (warehouse != null) criteria = criteria.Add("CompanyLocationType", warehouse);
			else if (agency == null)
			{
				agency = Registry.Find<AgencyOrganizationalUnit>().Agency;
				if (agency == null) agency = Registry.Find<AgencyOrganizationalUnit>().AgencyLocation.Agency;
				criteria = criteria.AddOr("AgencyOrganizationalUnit.Agency", agency,
					"AgencyOrganizationalUnit.AgencyLocation.Agency", agency);
			}
			//
			Int32 count = Repositories.Get<InventoryItem>().Function<Int32>(criteria);
			if (count > 0) throw new Exception("Some of the specified Asset Codes already exist in the system.");
		}

		private void ValidateSerialNumbers(string[] numbers)
		{
			DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
				.Add("Item", _item)
				.Add("Item.Serialized", "Y")
				.Add(Restrictions.In("SerialNumber", numbers))
				.SetProjection(Projections.Count("Id"));
			//
			Agency agency = Registry.Find<Agency>();
			CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
			if (warehouse != null) criteria = criteria.Add("CompanyLocationType", warehouse);
			else if (agency == null)
			{
				agency = Registry.Find<AgencyOrganizationalUnit>().Agency;
				if (agency == null) agency = Registry.Find<AgencyOrganizationalUnit>().AgencyLocation.Agency;
				criteria = criteria.AddOr("AgencyOrganizationalUnit.Agency", agency,
					"AgencyOrganizationalUnit.AgencyLocation.Agency", agency);
			}
			//
			Int32 count = Repositories.Get<InventoryItem>().Function<Int32>(criteria);
			if (count > 0) throw new Exception("Some of the specified Serial Numbers already exist in the system.");
		}

		#endregion

		#region Methods.Private.DomaineReceive

		private void Receive(ReceiptHeader receipt,
			decimal quantity, bool match,
			IList<UdfMetadataValue> metadata, ICollection<InspectionHeader> inspectionHeaders, String serailNumber = null)
		{
			Decimal received = match ? quantity : (quantity * this.UomQuantity).Value;
			//
			ReceiptDetail detail = ReceiptDetail.Create(receipt, this);
			detail.ExpectedQuantity = detail.Quantity = quantity;
			detail.LicensePlate = this.LicensePlate;
			detail.LotNumber = this.LotNumber;
			detail.Location = this.Location;
			detail.PurchaseOrderDetail = this;
			detail.StatusCode = Entity.Retrieve<StatusCode>(OrderStatuses.Received);
			detail.StockStatusCode = this.StockStatusCode;
			detail.UnitOfMeasure = _unitOfMeasure;
			detail.UomQuantity = this.UomQuantity;
			Repositories.Get<ReceiptDetail>().Add(detail);
			//
			InventoryItem created = InventoryItem.Create(_item, this.Location, this.StockStatusCode, received);
			created.Customer = _purchaseOrderHeader.Customer;
			created.LotExpiration = this.LotExpiration;
			created.LotNumber = this.LotNumber;
			created.ReceiptDetail = detail;
			created.Received = DateTime.Now;
			if (!String.IsNullOrEmpty(serailNumber)) created.SerialNumber = serailNumber;
			created.UnitCost = _unitCost;
			created.UnitOfMeasure = _item.UnitOfMeasure;
			created.UomQuantity = this.UomQuantity;
			Repositories.Get<InventoryItem>().Add(created);
			//
			created.LicensePlate.ChangeParent(this.LicensePlate);
			created.WriteReceived(detail);
			created.WriteUdfValues(metadata, this.UdfItemValues);
			//
			if (inspectionHeaders != null && inspectionHeaders.Count > 0)
			{
				if (!String.IsNullOrEmpty(serailNumber)) inspectionHeaders = inspectionHeaders.Where(c => c.InventoryItem != null && serailNumber.Equals(c.InventoryItem.SerialNumber)).ToList();
				foreach (InspectionHeader inspection in inspectionHeaders) inspection.RecordInspection(created);
			}
		}

		#endregion

		#region Methods.Private.Receive

		private void Receive(ReceiptHeader receipt,
			decimal quantity, bool match,
			IList<UdfMetadataValue> metadata)
		{
			Decimal received = match ? quantity : (quantity * this.UomQuantity).Value;
			//
			ReceiptDetail detail = ReceiptDetail.Create(receipt, this);
			detail.ExpectedQuantity = detail.Quantity = quantity;
			detail.LicensePlate = this.LicensePlate;
			detail.LotNumber = this.LotNumber;
			detail.Location = this.Location;
			detail.PurchaseOrderDetail = this;
			detail.StatusCode = Entity.Retrieve<StatusCode>(OrderStatuses.Received);
			detail.StockStatusCode = this.StockStatusCode;
			detail.UnitOfMeasure = _unitOfMeasure;
			detail.UomQuantity = this.UomQuantity;
			Repositories.Get<ReceiptDetail>().Add(detail);
			//
			InventoryItem created = InventoryItem.Create(_item, this.Location, this.StockStatusCode, received);
			created.LotExpiration = this.LotExpiration;
			created.LotNumber = this.LotNumber;
			created.ReceiptDetail = detail;
			created.Received = DateTime.Now;
			created.UnitCost = _unitCost;
			created.UnitOfMeasure = _item.UnitOfMeasure;
			created.UomQuantity = this.UomQuantity;
			Repositories.Get<InventoryItem>().Add(created);
			//
			created.LicensePlate.ChangeParent(this.LicensePlate);
			created.WriteReceived(detail);
			created.WriteUdfValues(metadata, this.UdfItemValues);
		}

		#endregion

		#region Methods.Public

		public virtual void ChangeStatus(StatusCode status)
		{
			_dateModified = DateTime.Now;
			_statusCode = status;
			_userModified = Registry.Find<UserAccount>().UserName;
			Repositories.Get<PurchaseOrderDetail>().Update(this);
		}

		#endregion

		#region Methods.Public.Bulk

		public virtual void PrepareBulk()
		{
			CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
			UnitOfMeasure each = Entity.Retrieve<UnitOfMeasure>(InventoryUoms.Each);
			UnitOfMeasure pallet = Entity.Retrieve<UnitOfMeasure>(InventoryUoms.Pallet);
			//
			DetachedCriteria criteria = DetachedCriteria.For<Item>()
				.Add(Expression.Eq("CompanyLocationType", warehouse))
				.Add(Expression.Eq("ItemCode", this.ItemCode))
				.SetMaxResults(1);
			_item = Repositories.Get<Item>().Retrieve(criteria);
			if (_item.UnitOfMeasure == null)
			{
				String error = String.Format("Missing standard UOM for item {0}.", _item.ItemCode);
				throw new Exception(error);
			}
			//
			criteria = DetachedCriteria.For<UnitOfMeasure>()
				.CreateAlias("FunctionalAreaCode", "fac")
				.Add(Expression.Eq("UomCode", this.UomCode))
				.Add(Expression.Eq("fac.Code", CodeValue.GetCode(FunctionalAreas.Inventory)))
				.SetMaxResults(1);
			_unitOfMeasure = Repositories.Get<UnitOfMeasure>().Retrieve(criteria);
			//
			if (_item.UnitOfMeasure.Equals(each) || _item.UnitOfMeasure.Equals(pallet)) this.UnitDescription = " Each =";
			else this.UnitDescription = String.Format(" {0} =", Inflector.Pluralize(_item.UnitOfMeasure.Description));
			//
			this.UomDescription = " LPNs";
			if (pallet.Equals(_item.UnitOfMeasure)) this.UomQuantity = 1;
			else
			{
				criteria = DetachedCriteria.For<ItemUomRelationship>()
					.Add(Expression.Eq("Active", "A"))
					.Add(Expression.Eq("Item", _item))
					.Add(Expression.Eq("UnitOfMeasure", pallet))
					.SetMaxResults(1);
				ItemUomRelationship relationship = Repositories.Get<ItemUomRelationship>().Retrieve(criteria);
				Decimal? factor = (relationship == null) ? _unitOfMeasure.Factor : relationship.Factor;
				this.UomQuantity = factor.HasValue ? Converter.ToInt32(factor) : 1;
			}
			//
			PurchaseOrderHeader po = Repositories.Get<PurchaseOrderHeader>().Retrieve(_purchaseOrderHeader.Id);
			po.FindReceiptHeader(true);
			/* Default full pallet value. */
			Boolean? full = BusinessRule.RetrieveBoolean("1041");
			this.FullLpn = full.HasValue ? full.Value : false;
			this.ReceiptCode = po.ReceiptHeader.ReceiptCode;
			this.StockStatusCode = "Y".Equals(_item.QualityAssurance) ?
				Entity.Retrieve<StatusCode>(InventoryStatuses.QaHold) :
				Entity.Retrieve<StatusCode>(InventoryStatuses.Available);
			this.UdfItemValues = _item.FindUdfItemValues();
		}

		public virtual void ReceiveBulk()
		{
			if (this.InventoryAttributes == null || this.InventoryAttributes.Count == 0) throw new Exception("Missing LPNs.");
			//
			_purchaseOrderHeader.FindReceiptHeader(true);
			//
			DateTime? manufacture = InventoryItem.CalculateManufactureDate(this.LotNumber);
			this.StockStatusCode = InventoryItem.UpdateStatus(_item, this.StockStatusCode, manufacture);
			//
			IList<UdfMetadataValue> metadata = null;
			if (this.UdfItemValues != null && this.UdfItemValues.Count > 0)
			{
				String[] labels = this.UdfItemValues.Select(e => e.Label).ToArray();
				DetachedCriteria criteria = DetachedCriteria.For<UdfMetadataValue>()
					.Add(Expression.Eq("Active", "A"))
					.Add(Expression.In("Label", labels))
					.Add(Expression.Eq("TableName", "ITEMS"));
				metadata = Repositories.Get<UdfMetadataValue>().List(criteria);
			}
			//
			LicenseType ilpn = Entity.Retrieve<LicenseType>(InventoryLicenseTypes.InventoryItem);
			LicenseType plpn = Entity.Retrieve<LicenseType>(InventoryLicenseTypes.Pallet);
			ReceiptHeader receipt = _purchaseOrderHeader.ReceiptHeader;
			UnitOfMeasure pallet = Entity.Retrieve<UnitOfMeasure>(InventoryUoms.Pallet);
			//
			Decimal portion = pallet.Equals(_unitOfMeasure) ? 1 : this.UomQuantity.Value;
			foreach (InventoryAttribute element in this.InventoryAttributes)
			{
				if (!element.LicensePlate.Id.HasValue)
				{
					element.LicensePlate.CompanyLocationType = Registry.Find<CompanyLocationType>();
					element.LicensePlate.LicenseType = plpn;
					Repositories.Get<LicensePlate>().Add(element.LicensePlate);
				}
				if (this.FullLpn) element.LicensePlate.ChangeStatus(Entity.Retrieve<StatusCode>(LicensePlateStatuses.Complete));
				else element.LicensePlate.ChangeStatus(Entity.Retrieve<StatusCode>(LicensePlateStatuses.Open));
				//
				ReceiptDetail detail = ReceiptDetail.Create(receipt, this);
				detail.ExpectedQuantity = detail.Quantity = portion;
				detail.LicensePlate = element.LicensePlate;
				detail.LotNumber = this.LotNumber;
				detail.Location = this.Location;
				detail.StatusCode = Entity.Retrieve<StatusCode>(OrderStatuses.Received);
				detail.StockStatusCode = this.StockStatusCode;
				detail.UnitOfMeasure = _unitOfMeasure;
				detail.UomQuantity = this.UomQuantity;
				Repositories.Get<ReceiptDetail>().Add(detail);
				//
				InventoryItem created = InventoryItem.Create(_item, this.Location, this.StockStatusCode, this.UomQuantity.Value);
				created.LotExpiration = this.LotExpiration;
				created.LotNumber = this.LotNumber;
				created.ManufactureDate = manufacture;
				created.ReceiptDetail = detail;
				created.UnitCost = _unitCost;
				created.UnitOfMeasure = _item.UnitOfMeasure;
				created.UomQuantity = this.UomQuantity;
				Repositories.Get<InventoryItem>().Add(created);
				//
				created.LicensePlate.ChangeParent(element.LicensePlate);
				created.WriteReceived(detail);
				created.WriteUdfValues(metadata, this.UdfItemValues);
				//
				element.LicensePlate.ChangeLocation(this.Location);
			}
			//
			Decimal received = (this.InventoryAttributes.Count * this.UomQuantity).Value;
			if (received >= this.RequiredQuantity)
			{
				/* Close PO|RT manually? */
				Boolean? manual = BusinessRule.RetrieveBoolean("1012");
				_statusCode = (manual.HasValue && manual.Value) ?
					Entity.Retrieve<StatusCode>(PurchaseStatuses.Active) :
					Entity.Retrieve<StatusCode>(PurchaseStatuses.Received);
			}
			else _statusCode = Entity.Retrieve<StatusCode>(PurchaseStatuses.Active);
			// Hack to avoid Id-less InventoryAttributes serialization issues.
			this.InventoryAttributes = null;
		}

		#endregion

		#region Methods.Public.DomaineReceive

		public virtual void DomaineReceive()
		{
			DetachedCriteria criteria = null;
			_purchaseOrderHeader.FindReceiptHeader(true);
			//
			Boolean match = _item.UnitOfMeasure.Equals(_unitOfMeasure);
			/* Close PO|RT manually? */
			Boolean? manual = BusinessRule.RetrieveBoolean("1012");
			Decimal received = match ? (_quantity * this.UomQuantity).Value : _quantity;
			//
			StatusCode active = Entity.Retrieve<StatusCode>(PurchaseStatuses.Active);
			StatusCode closed = Entity.Retrieve<StatusCode>(PurchaseStatuses.Received);
			StatusCode open = Entity.Retrieve<StatusCode>(PurchaseStatuses.Open);
			//
			IList<UdfMetadataValue> metadata = null;
			if (this.UdfItemValues != null && this.UdfItemValues.Count > 0)
			{
				String[] labels = this.UdfItemValues.Select(e => e.Label).ToArray();
				criteria = DetachedCriteria.For<UdfMetadataValue>()
					.Add(Expression.Eq("Active", "A"))
					.Add(Expression.In("Label", labels))
					.Add(Expression.Eq("TableName", "ITEMS"));
				metadata = Repositories.Get<UdfMetadataValue>().List(criteria);
			}
			//
			if (this.LicensePlate != null)
			{
				if (!this.LicensePlate.Id.HasValue)
				{
					this.LicensePlate.CompanyLocationType = Registry.Find<CompanyLocationType>();
					this.LicensePlate.LicenseType = Entity.Retrieve<LicenseType>(InventoryLicenseTypes.Carton);
					Repositories.Get<LicensePlate>().Add(this.LicensePlate);
					//
					CartonHeader carton = Entity.Activate<CartonHeader>();
					carton.LicensePlate = this.LicensePlate;
					carton.CartonSize = this.CartonSize;
					Repositories.Get<CartonHeader>().Add(carton);
				}
				//else this.LicensePlate = Repositories.Get<LicensePlate>().Retrieve(this.LicensePlate.Id);
				this.LicensePlate.ChangeLocation(this.Location);
				if (this.FullLpn) this.LicensePlate.ChangeStatus(Entity.Retrieve<StatusCode>(LicensePlateStatuses.Complete));
				else this.LicensePlate.ChangeStatus(Entity.Retrieve<StatusCode>(LicensePlateStatuses.Open));
				if (this.CartonSize != null) this.LicensePlate.AssignCartonSize(this.CartonSize);
			}
			//
			criteria = DetachedCriteria.For<PurchaseOrderDetail>()
				.Add(Expression.Eq("Item", _item))
				.Add(Expression.Eq("PurchaseOrderHeader", _purchaseOrderHeader))
				.Add(Expression.Or(Expression.Eq("StatusCode", active), Expression.Eq("StatusCode", open)))
				.Add(Expression.Eq("UnitOfMeasure", _unitOfMeasure))
				.AddOrder(Order.Asc("LineNumber"));
			IList<PurchaseOrderDetail> details = Repositories.Get<PurchaseOrderDetail>().List(criteria);
			//
            Decimal? receivedQuantity = 0;
            foreach (PurchaseOrderDetail poDetail in details) foreach (ReceiptDetail rtDetail in poDetail.ReceiptDetails) receivedQuantity += rtDetail.Quantity;
            Decimal? poQuantity = details.Sum(c => c.Quantity);
            if (poQuantity.HasValue && received > (poQuantity - receivedQuantity))
            {
                if (this.Overages == 1 || this.Overages == 4)
                {
                    String message = String.Format("Cannot receive more than the expected quantity {0}. seems to be somebody received the same item", poQuantity - receivedQuantity);
                    throw new Exception(message);
                }
            }
            //
			for (int i = 0; i < details.Count; i++)
			{
				if (received <= 0) break;
				//
				if (received < details[i].Quantity)
				{
					details[i].LicensePlate = this.LicensePlate;
					details[i].Location = this.Location;
					details[i].LotExpiration = this.LotExpiration;
					details[i].LotNumber = this.LotNumber;
					details[i].StockStatusCode = this.StockStatusCode;
					details[i].UomQuantity = this.UomQuantity;
					details[i].ChangeStatus(active);
					details[i].Receive(_purchaseOrderHeader.ReceiptHeader, received, match, metadata, this.InspectionHeaders);
					//
					break;
				}
				else
				{
					Decimal portion = (i == details.Count - 1) ? received : details[i].Quantity;
					//
					details[i].LicensePlate = this.LicensePlate;
					details[i].Location = this.Location;
					details[i].LotExpiration = this.LotExpiration;
					details[i].LotNumber = this.LotNumber;
					details[i].StockStatusCode = this.StockStatusCode;
					details[i].UomQuantity = this.UomQuantity;
					details[i].ChangeStatus((manual.HasValue && !manual.Value) ? closed : active);
					details[i].Receive(_purchaseOrderHeader.ReceiptHeader, portion, match, metadata, this.InspectionHeaders);
					//
					received -= portion;
				}
			}
			//
			_purchaseOrderHeader.UpdateStatus();
			//
			this.InspectionHeaders = null;
		}

		public virtual void DomaineReceive(string serialNumbers)
		{
			DetachedCriteria criteria = null;
			_purchaseOrderHeader.FindReceiptHeader(true);
			//
			Boolean match = _item.UnitOfMeasure.Equals(_unitOfMeasure);
			/* Close PO|RT manually? */
			Boolean? manual = BusinessRule.RetrieveBoolean("1012");
			Decimal received = match ? (_quantity * this.UomQuantity).Value : _quantity;
			//
			List<InventoryItem> _numbers = new List<InventoryItem>();
			String[] numbers = serialNumbers.Split('|');
			for (int i = 0; i < numbers.Length; i++)
			{
				InventoryItem item = new InventoryItem();
				item.SerialNumber = numbers[i];
				_numbers.Add(item);
			}
			//
			StatusCode active = Entity.Retrieve<StatusCode>(PurchaseStatuses.Active);
			StatusCode closed = Entity.Retrieve<StatusCode>(PurchaseStatuses.Received);
			StatusCode open = Entity.Retrieve<StatusCode>(PurchaseStatuses.Open);
			//
			IList<UdfMetadataValue> metadata = null;
			if (this.UdfItemValues != null && this.UdfItemValues.Count > 0)
			{
				String[] labels = this.UdfItemValues.Select(e => e.Label).ToArray();
				criteria = DetachedCriteria.For<UdfMetadataValue>()
					.Add(Expression.Eq("Active", "A"))
					.Add(Expression.In("Label", labels))
					.Add(Expression.Eq("TableName", "ITEMS"));
				metadata = Repositories.Get<UdfMetadataValue>().List(criteria);
			}
			//
			if (this.LicensePlate != null)
			{
				if (!this.LicensePlate.Id.HasValue)
				{
					this.LicensePlate.CompanyLocationType = Registry.Find<CompanyLocationType>();
					this.LicensePlate.LicenseType = Entity.Retrieve<LicenseType>(InventoryLicenseTypes.Carton);
					Repositories.Get<LicensePlate>().Add(this.LicensePlate);
					//
					CartonHeader carton = Entity.Activate<CartonHeader>();
					carton.LicensePlate = this.LicensePlate;
					carton.CartonSize = this.CartonSize;
					Repositories.Get<CartonHeader>().Add(carton);
				}
				//else this.LicensePlate = Repositories.Get<LicensePlate>().Retrieve(this.LicensePlate.Id);
				this.LicensePlate.ChangeLocation(this.Location);
				if (this.FullLpn) this.LicensePlate.ChangeStatus(Entity.Retrieve<StatusCode>(LicensePlateStatuses.Complete));
				else this.LicensePlate.ChangeStatus(Entity.Retrieve<StatusCode>(LicensePlateStatuses.Open));
				if (this.CartonSize != null) this.LicensePlate.AssignCartonSize(this.CartonSize);
			}
			//
			criteria = DetachedCriteria.For<PurchaseOrderDetail>()
				.Add(Expression.Eq("Item", _item))
				.Add(Expression.Eq("PurchaseOrderHeader", _purchaseOrderHeader))
				.Add(Expression.Or(Expression.Eq("StatusCode", active), Expression.Eq("StatusCode", open)))
				.Add(Expression.Eq("UnitOfMeasure", _unitOfMeasure))
				.AddOrder(Order.Asc("LineNumber"));
			IList<PurchaseOrderDetail> details = Repositories.Get<PurchaseOrderDetail>().List(criteria);
			//
			for (int i = 0; i < details.Count; i++)
			{
				if (received <= 0) break;
				//
				if (received < details[i].Quantity)
				{
					details[i].LicensePlate = this.LicensePlate;
					details[i].Location = this.Location;
					details[i].LotExpiration = this.LotExpiration;
					details[i].LotNumber = this.LotNumber;
					details[i].StockStatusCode = this.StockStatusCode;
					details[i].UomQuantity = this.UomQuantity;
					details[i].ChangeStatus(active);
					//
					for (int j = 0; j < received; j++)
					{
						details[i].Receive(_purchaseOrderHeader.ReceiptHeader, 1, match, metadata, this.InspectionHeaders, _numbers[0].SerialNumber);
						_numbers.Remove(_numbers.Where(c => c.SerialNumber.Equals(_numbers[0].SerialNumber)).SingleOrDefault());
					}
					//
					break;
				}
				else
				{
					Decimal portion = (i == details.Count - 1) ? received : details[i].Quantity;
					//
					details[i].LicensePlate = this.LicensePlate;
					details[i].Location = this.Location;
					details[i].LotExpiration = this.LotExpiration;
					details[i].LotNumber = this.LotNumber;
					details[i].StockStatusCode = this.StockStatusCode;
					details[i].UomQuantity = this.UomQuantity;
					details[i].ChangeStatus((manual.HasValue && !manual.Value) ? closed : active);
					//
					for (int j = 0; j < portion; j++)
					{
						details[i].Receive(_purchaseOrderHeader.ReceiptHeader, 1, match, metadata, this.InspectionHeaders, _numbers[0].SerialNumber);
						_numbers.Remove(_numbers.Where(c => c.SerialNumber.Equals(_numbers[0].SerialNumber)).SingleOrDefault());
					}
					//
					received -= portion;
				}
			}
			//
			_purchaseOrderHeader.UpdateStatus();
			//
			this.InspectionHeaders = null;
		}

		#endregion

		#region Methods.Public.Receive

		public virtual void FindUomInfo()
		{
			CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
			String inventory = CodeValue.GetCode(FunctionalAreas.Inventory);
			UnitOfMeasure each = Entity.Retrieve<UnitOfMeasure>(InventoryUoms.Each);
			//
			DetachedCriteria criteria = DetachedCriteria.For<Item>()
				.Add(Expression.Eq("CompanyLocationType", warehouse))
				.Add(Expression.Eq("ItemCode", this.ItemCode))
				.SetMaxResults(1);
			_item = Repositories.Get<Item>().Retrieve(criteria);
			if (_item.UnitOfMeasure == null)
			{
				String error = String.Format("Missing standard UOM for item {0}.", _item.ItemCode);
				throw new Exception(error);
			}
			//
			criteria = DetachedCriteria.For<UnitOfMeasure>()
				.CreateAlias("FunctionalAreaCode", "fac")
				.Add(Expression.Eq("fac.Code", inventory))
				.Add(Expression.Eq("UomCode", this.UomCode))
				.SetMaxResults(1);
			_unitOfMeasure = Repositories.Get<UnitOfMeasure>().Retrieve(criteria);
			//
			if (_item.UnitOfMeasure.Equals(each)) this.UnitDescription = " Each =";
			else this.UnitDescription = String.Format(" {0} =", Inflector.Pluralize(_item.UnitOfMeasure.Description));
			//
			UnitOfMeasure unit = _unitOfMeasure;
			if (_item.UnitOfMeasure.Equals(_unitOfMeasure))
			{
				/* Default receiving UOM. */
				String code = BusinessRule.RetrieveString("11065");
				if (String.IsNullOrEmpty(code)) unit = Entity.Retrieve<UnitOfMeasure>(InventoryUoms.Case);
				else
				{
					criteria = DetachedCriteria.For<UnitOfMeasure>()
						.CreateAlias("FunctionalAreaCode", "fac")
						.Add(Expression.Eq("fac.Code", inventory))
						.Add(Expression.Eq("UomCode", code))
						.SetMaxResults(1);
					unit = Repositories.Get<UnitOfMeasure>().Retrieve(criteria);
					if (unit == null) unit = Entity.Retrieve<UnitOfMeasure>(InventoryUoms.Case);
				}
				//
				this.UomDescription = String.Format(" {0}", Inflector.Pluralize(unit.Description));
				criteria = DetachedCriteria.For<ItemUomRelationship>()
					.Add(Expression.Eq("Active", "A"))
					.Add(Expression.Eq("Item", _item))
					.Add(Expression.Eq("UnitOfMeasure", unit))
					.SetMaxResults(1);
				ItemUomRelationship relationship = Repositories.Get<ItemUomRelationship>().Retrieve(criteria);
				Decimal? factor = (relationship == null) ? _unitOfMeasure.Factor : relationship.Factor;
				this.UomQuantity = factor.HasValue ? Converter.ToInt32(factor) : 1;
			}
			else
			{
				this.UomDescription = String.Format(" {0}", Inflector.Pluralize(_unitOfMeasure.Description));
				//
				criteria = DetachedCriteria.For<ItemUomRelationship>()
					.Add(Expression.Eq("Active", "A"))
					.Add(Expression.Eq("Item", _item))
					.Add(Expression.Eq("UnitOfMeasure", _unitOfMeasure))
					.SetMaxResults(1);
				ItemUomRelationship relationship = Repositories.Get<ItemUomRelationship>().Retrieve(criteria);
				Decimal? factor = (relationship == null) ? _unitOfMeasure.Factor : relationship.Factor;
				this.UomQuantity = factor.HasValue ? Converter.ToInt32(factor) : 1;
			}
		}

		public virtual void Prepare(Location location)
		{
			PurchaseOrderHeader po = Repositories.Get<PurchaseOrderHeader>().Retrieve(_purchaseOrderHeader.Id);
			po.FindReceiptHeader(true);
			/* Show PO Comments */
			Boolean? comments = BusinessRule.RetrieveBoolean("6260");
			if (comments.HasValue && comments.Value)
			{
				StatusCode active = Entity.Retrieve<StatusCode>(PurchaseStatuses.Active);
				StatusCode open = Entity.Retrieve<StatusCode>(PurchaseStatuses.Open);
				//
				DetachedCriteria criteria = DetachedCriteria.For<PurchaseOrderDetail>()
					.Add(Expression.IsNotNull("Comments"))
					.Add(Expression.Eq("Item", _item))
					.Add(Expression.Eq("PurchaseOrderHeader", _purchaseOrderHeader))
					.Add(Expression.Or(Expression.Eq("StatusCode", active), Expression.Eq("StatusCode", open)))
					.Add(Expression.Eq("UnitOfMeasure", _unitOfMeasure))
					.SetProjection(Projections.Property("Comments"))
					.SetResultTransformer(Transformers.AliasToBean<PurchaseOrderDetail>());
				IList<PurchaseOrderDetail> details = Repositories.Get<PurchaseOrderDetail>().List(criteria);
				this.DetailComments = details.Select(e => e.Comments).ToList();
			}
			/* Default quantity. */
			this.Default = BusinessRule.RetrieveString("6252");
			/* Default full pallet value. */
			Boolean? full = BusinessRule.RetrieveBoolean("1041");
			this.FullLpn = full.HasValue ? full.Value : false;
			this.ReceiptCode = po.ReceiptHeader.ReceiptCode;
			this.StockStatusCode = "Y".Equals(_item.QualityAssurance) ?
				Entity.Retrieve<StatusCode>(InventoryStatuses.QaHold) :
				Entity.Retrieve<StatusCode>(InventoryStatuses.Available);
			this.StockStatusCode.SuggestLpn(_item.CompanyLocationZone, location);
			this.UdfItemValues = _item.FindUdfItemValues();
		}

		public virtual void Receive()
		{
			DetachedCriteria criteria = null;
			_purchaseOrderHeader.FindReceiptHeader(true);
			/* Close PO|RT manually? */
			Boolean? manual = BusinessRule.RetrieveBoolean("1012");
			Boolean match = _item.UnitOfMeasure.Equals(_unitOfMeasure);
			Decimal received = match ? (_quantity * this.UomQuantity).Value : _quantity;
			//
			StatusCode active = Entity.Retrieve<StatusCode>(PurchaseStatuses.Active);
			StatusCode closed = Entity.Retrieve<StatusCode>(PurchaseStatuses.Received);
			StatusCode open = Entity.Retrieve<StatusCode>(PurchaseStatuses.Open);
			//
			IList<UdfMetadataValue> metadata = null;
			if (this.UdfItemValues != null && this.UdfItemValues.Count > 0)
			{
				String[] labels = this.UdfItemValues.Select(e => e.Label).ToArray();
				criteria = DetachedCriteria.For<UdfMetadataValue>()
					.Add(Expression.Eq("Active", "A"))
					.Add(Expression.In("Label", labels))
					.Add(Expression.Eq("TableName", "ITEMS"));
				metadata = Repositories.Get<UdfMetadataValue>().List(criteria);
			}
			//
			if (this.LicensePlate != null)
			{
				if (!this.LicensePlate.Id.HasValue)
				{
					this.LicensePlate.CompanyLocationType = Registry.Find<CompanyLocationType>();
					this.LicensePlate.LicenseType = Entity.Retrieve<LicenseType>(InventoryLicenseTypes.Pallet);
					Repositories.Get<LicensePlate>().Add(this.LicensePlate);
				}
				this.LicensePlate.ChangeLocation(this.Location);
				if (this.FullLpn) this.LicensePlate.ChangeStatus(Entity.Retrieve<StatusCode>(LicensePlateStatuses.Complete));
				else this.LicensePlate.ChangeStatus(Entity.Retrieve<StatusCode>(LicensePlateStatuses.Open));
			}
			//
			criteria = DetachedCriteria.For<PurchaseOrderDetail>()
				.Add(Expression.Eq("Item", _item))
				.Add(Expression.Eq("PurchaseOrderHeader", _purchaseOrderHeader))
				.Add(Expression.Or(Expression.Eq("StatusCode", active), Expression.Eq("StatusCode", open)))
				.Add(Expression.Eq("UnitOfMeasure", _unitOfMeasure))
				.AddOrder(Order.Asc("LineNumber"));
			IList<PurchaseOrderDetail> details = Repositories.Get<PurchaseOrderDetail>().List(criteria);
			//
            Decimal? receivedQuantity = 0;
            foreach (PurchaseOrderDetail poDetail in details) foreach (ReceiptDetail rtDetail in poDetail.ReceiptDetails) receivedQuantity += rtDetail.Quantity;
            Decimal? poQuantity = details.Sum(c => c.Quantity);
            if (poQuantity.HasValue && received > (poQuantity - receivedQuantity))
            {
                if (this.Overages == 1 || this.Overages == 4)
                {
                    String message = String.Format("Cannot receive more than the expected quantity {0}. seems to be somebody received the same item", poQuantity - receivedQuantity);
                    throw new Exception(message);
                }
            }
            //
			for (int i = 0; i < details.Count; i++)
			{
				if (received <= 0) break;
				//
				if (received < details[i].Quantity)
				{
					details[i].LicensePlate = this.LicensePlate;
					details[i].Location = this.Location;
					details[i].LotExpiration = this.LotExpiration;
					details[i].LotNumber = this.LotNumber;
					details[i].StockStatusCode = this.StockStatusCode;
					details[i].UomQuantity = this.UomQuantity;
					details[i].ChangeStatus(active);
					details[i].Receive(_purchaseOrderHeader.ReceiptHeader, received, match, metadata);
					//
					break;
				}
				else
				{
					Decimal portion = (i == details.Count - 1) ? received : details[i].Quantity;
					//
					details[i].LicensePlate = this.LicensePlate;
					details[i].Location = this.Location;
					details[i].LotExpiration = this.LotExpiration;
					details[i].LotNumber = this.LotNumber;
					details[i].StockStatusCode = this.StockStatusCode;
					details[i].UomQuantity = this.UomQuantity;
					details[i].ChangeStatus((manual.HasValue && !manual.Value) ? closed : active);
					details[i].Receive(_purchaseOrderHeader.ReceiptHeader, portion, match, metadata);
					//
					received -= portion;
				}
			}
			//
			_purchaseOrderHeader.UpdateStatus();
		}

		#endregion

		#region Methods.Public.Return

		public virtual void PrepareReturn(Location location)
		{
			/* Show PO Comments */
			Boolean? comments = BusinessRule.RetrieveBoolean("6260");
			if (comments.HasValue && comments.Value && !String.IsNullOrEmpty(_comments))
				this.DetailComments = new List<String>() { _comments };
			/* Default quantity. */
			this.Default = BusinessRule.RetrieveString("6252");
			/* Default full pallet value. */
			Boolean? full = BusinessRule.RetrieveBoolean("1041");
			this.FullLpn = full.HasValue ? full.Value : false;
			this.StockStatusCode = "Y".Equals(_item.QualityAssurance) ?
				Entity.Retrieve<StatusCode>(InventoryStatuses.QaHold) :
				Entity.Retrieve<StatusCode>(InventoryStatuses.Available);
			this.StockStatusCode.SuggestLpn(_item.CompanyLocationZone, location);
			this.UdfItemValues = _item.FindUdfItemValues();
			if (!String.IsNullOrEmpty(_purchaseOrderHeader.VendorCode))
			{
				CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
				DetachedCriteria criteria = DetachedCriteria.For<Vendor>()
					.Add(Expression.Eq("Active", "A"))
					.Add(Expression.Eq("CompanyLocationType", warehouse))
					.Add(Expression.Eq("VendorCode", _purchaseOrderHeader.VendorCode))
					.SetMaxResults(1);
				_purchaseOrderHeader.Vendor = Repositories.Get<Vendor>().Retrieve(criteria);
			}
		}

		public virtual void Return(ActionCode action, DispositionCode disposition, ReturnReason reason)
		{
			Boolean match = _item.UnitOfMeasure.Equals(_unitOfMeasure);
			Decimal received = match ? (this.ReturnedQuantity * this.UomQuantity).Value : this.ReturnedQuantity;
			//
			IList<UdfMetadataValue> metadata = null;
			if (this.UdfItemValues != null && this.UdfItemValues.Count > 0)
			{
				String[] labels = this.UdfItemValues.Select(e => e.Label).ToArray();
				DetachedCriteria criteria = DetachedCriteria.For<UdfMetadataValue>()
					.Add(Expression.Eq("Active", "A"))
					.Add(Expression.In("Label", labels))
					.Add(Expression.Eq("TableName", "ITEMS"));
				metadata = Repositories.Get<UdfMetadataValue>().List(criteria);
			}
			//
			if (this.LicensePlate != null)
			{
				if (!this.LicensePlate.Id.HasValue)
				{
					this.LicensePlate.CompanyLocationType = Registry.Find<CompanyLocationType>();
					this.LicensePlate.LicenseType = Entity.Retrieve<LicenseType>(InventoryLicenseTypes.Pallet);
					Repositories.Get<LicensePlate>().Add(this.LicensePlate);
				}
				this.LicensePlate.ChangeLocation(this.Location);
				if (this.FullLpn) this.LicensePlate.ChangeStatus(Entity.Retrieve<StatusCode>(LicensePlateStatuses.Complete));
				else this.LicensePlate.ChangeStatus(Entity.Retrieve<StatusCode>(LicensePlateStatuses.Open));
			}
			//
			ReturnHeader header = ReturnHeader.Create();
			header.Vendor = _purchaseOrderHeader.Vendor;
			Repositories.Get<ReturnHeader>().Add(header);
			//
			InventoryItem created = InventoryItem.Create(_item, this.Location, this.StockStatusCode, received);
			created.LotExpiration = this.LotExpiration;
			created.LotNumber = this.LotNumber;
			created.Received = DateTime.Now;
			created.UnitCost = _unitCost;
			created.UnitOfMeasure = _unitOfMeasure;
			created.UomQuantity = this.UomQuantity;
			Repositories.Get<InventoryItem>().Add(created);
			//
			ReturnDetail detail = ReturnDetail.Create(header, this, created);
			detail.ActionCode = action;
			detail.DispositionCode = disposition;
			detail.Quantity = Converter.ToInt32(received);
			detail.ReturnReason = reason;
			detail.StatusCode = Entity.Retrieve<StatusCode>(ReturnStatuses.Complete);
			Repositories.Get<ReturnDetail>().Add(detail);
			//
			created.LicensePlate.ChangeParent(this.LicensePlate);
			created.WriteReturned(detail);
			created.WriteUdfValues(metadata, this.UdfItemValues);
			//
			header.ChangeStatus(detail.StatusCode);
			//
			this.ChangeStatus(Entity.Retrieve<StatusCode>(PurchaseStatuses.Active));
			_purchaseOrderHeader.UpdateStatus();
		}

		#endregion
	}
}
