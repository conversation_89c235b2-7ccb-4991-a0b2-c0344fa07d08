using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class PurchaseOrderFunding : Entity
	{
		#region Fields

		private Decimal _fundingPercent;
		private Pool _pool;
		private PurchaseOrderDetail _purchaseOrderDetail;
		private String _active;

		#endregion

		#region Properties

		[DataMember]
		public virtual Decimal FundingPercent
		{
			get { return _fundingPercent; }
			set { _fundingPercent = value; }
		}

		[DataMember]
		public virtual Pool Pool
		{
			get { return _pool; }
			set { _pool = value; }
		}

		[DataMember]
		public virtual PurchaseOrderDetail PurchaseOrderDetail
		{
			get { return _purchaseOrderDetail; }
			set { _purchaseOrderDetail = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}


		#endregion
	}
}
