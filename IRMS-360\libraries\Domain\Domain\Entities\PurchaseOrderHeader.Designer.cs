using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class PurchaseOrderHeader : Entity
	{
		#region Fields

		private Agency _agency;
		private AgencyLocation _agencyLocation;
		private AgencyOrganizationalUnit _agencyOrganizationalUnit;
		private Carrier _carrier;
		private CarrierService _carrierService;
		private CompanyLocation _companyLocation;
		private CompanyLocationType _companyLocationType;
		private CompanyOrganizationalUnit _companyOrganizationalUnit;
		private Contract _contract;
		private CurrencyCode _currencyCode;
		private Customer _customer;
		private DateTime _required;
		private DateTime? _purchaseOrderPrinted;
		private DateTime? _requested;
		private ICollection<Alert> _alerts = new HashSet<Alert>();
		private ICollection<ApprovalRequest> _approvalRequests = new HashSet<ApprovalRequest>();
		private ICollection<CarrierAppointment> _carrierAppointments = new HashSet<CarrierAppointment>();
		private ICollection<InvoiceDetail> _invoiceDetails = new HashSet<InvoiceDetail>();
		private ICollection<InvoiceHeader> _invoiceHeaders = new HashSet<InvoiceHeader>();
		private ICollection<PurchaseOrderCharge> _purchaseOrderCharges = new HashSet<PurchaseOrderCharge>();
		private ICollection<PurchaseOrderDetail> _purchaseOrderDetails = new HashSet<PurchaseOrderDetail>();
		private ICollection<PurchaseOrderLocation> _purchaseOrderLocations = new HashSet<PurchaseOrderLocation>();
		private ICollection<PurchaseOrderReference> _purchaseOrderReferences = new HashSet<PurchaseOrderReference>();
		private ICollection<PurchaseOrderService> _purchaseOrderServices = new HashSet<PurchaseOrderService>();
		private ICollection<PurchaseOrderStatus> _purchaseOrderStatuses = new HashSet<PurchaseOrderStatus>();
		private ICollection<ReceiptHeader> _receiptHeaders = new HashSet<ReceiptHeader>();
		private OrderType _orderType;
		private OrganizationParticipant _approver;
		private OrganizationParticipant _buyer;
		private OrganizationParticipant _contact;
		private OrganizationParticipant _requestor;
		private PaymentTerm _paymentTerm;
		private PrefixCode _prefixCode;
		private Priority _priority;
		private RequisitionHeader _requisitionHeader;
		private String _billOfLading;
		private String _comments;
		private String _purchaseOrderCode;
		private String _purchaseOrderSuffix;
		private String _sealNumber;
		private String _sendPoEmail;
		private String _trailerCode;
		private String _vendorCode;
		private Vendor _vendor;

		#endregion

		#region Properties

		[DataMember]
		public virtual Agency Agency
		{
			get { return _agency; }
			set { _agency = value; }
		}

		[DataMember]
		public virtual AgencyLocation AgencyLocation
		{
			get { return _agencyLocation; }
			set { _agencyLocation = value; }
		}

		[DataMember]
		public virtual AgencyOrganizationalUnit AgencyOrganizationalUnit
		{
			get { return _agencyOrganizationalUnit; }
			set { _agencyOrganizationalUnit = value; }
		}

		[DataMember]
		public virtual Carrier Carrier
		{
			get { return _carrier; }
			set { _carrier = value; }
		}

		[DataMember]
		public virtual CarrierService CarrierService
		{
			get { return _carrierService; }
			set { _carrierService = value; }
		}

		[DataMember]
		public virtual CompanyLocation CompanyLocation
		{
			get { return _companyLocation; }
			set { _companyLocation = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual CompanyOrganizationalUnit CompanyOrganizationalUnit
		{
			get { return _companyOrganizationalUnit; }
			set { _companyOrganizationalUnit = value; }
		}

		[DataMember]
		public virtual Contract Contract
		{
			get { return _contract; }
			set { _contract = value; }
		}

		[DataMember]
		public virtual CurrencyCode CurrencyCode
		{
			get { return _currencyCode; }
			set { _currencyCode = value; }
		}

		[DataMember]
		public virtual Customer Customer
		{
			get { return _customer; }
			set { _customer = value; }
		}

		[DataMember]
		public virtual DateTime Required
		{
			get { return _required; }
			set { _required = value; }
		}

		[DataMember]
		public virtual DateTime? PurchaseOrderPrinted
		{
			get { return _purchaseOrderPrinted; }
			set { _purchaseOrderPrinted = value; }
		}

		[DataMember]
		public virtual DateTime? Requested
		{
			get { return _requested; }
			set { _requested = value; }
		}

		[DataMember]
		public virtual ICollection<Alert> Alerts
		{
			get { return _alerts; }
			set { _alerts = value; }
		}

		[DataMember]
		public virtual ICollection<ApprovalRequest> ApprovalRequests
		{
			get { return _approvalRequests; }
			set { _approvalRequests = value; }
		}

		[DataMember]
		public virtual ICollection<CarrierAppointment> CarrierAppointments
		{
			get { return _carrierAppointments; }
			set { _carrierAppointments = value; }
		}

		[DataMember]
		public virtual ICollection<InvoiceDetail> InvoiceDetails
		{
			get { return _invoiceDetails; }
			set { _invoiceDetails = value; }
		}

		[DataMember]
		public virtual ICollection<InvoiceHeader> InvoiceHeaders
		{
			get { return _invoiceHeaders; }
			set { _invoiceHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<PurchaseOrderCharge> PurchaseOrderCharges
		{
			get { return _purchaseOrderCharges; }
			set { _purchaseOrderCharges = value; }
		}

		[DataMember]
		public virtual ICollection<PurchaseOrderDetail> PurchaseOrderDetails
		{
			get { return _purchaseOrderDetails; }
			set { _purchaseOrderDetails = value; }
		}

		[DataMember]
		public virtual ICollection<PurchaseOrderLocation> PurchaseOrderLocations
		{
			get { return _purchaseOrderLocations; }
			set { _purchaseOrderLocations = value; }
		}

		[DataMember]
		public virtual ICollection<PurchaseOrderReference> PurchaseOrderReferences
		{
			get { return _purchaseOrderReferences; }
			set { _purchaseOrderReferences = value; }
		}

		[DataMember]
		public virtual ICollection<PurchaseOrderService> PurchaseOrderServices
		{
			get { return _purchaseOrderServices; }
			set { _purchaseOrderServices = value; }
		}

		[DataMember]
		public virtual ICollection<PurchaseOrderStatus> PurchaseOrderStatuses
		{
			get { return _purchaseOrderStatuses; }
			set { _purchaseOrderStatuses = value; }
		}

		[DataMember]
		public virtual ICollection<ReceiptHeader> ReceiptHeaders
		{
			get { return _receiptHeaders; }
			set { _receiptHeaders = value; }
		}

		[DataMember]
		public virtual OrderType OrderType
		{
			get { return _orderType; }
			set { _orderType = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant Approver
		{
			get { return _approver; }
			set { _approver = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant Buyer
		{
			get { return _buyer; }
			set { _buyer = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant Contact
		{
			get { return _contact; }
			set { _contact = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant Requestor
		{
			get { return _requestor; }
			set { _requestor = value; }
		}

		[DataMember]
		public virtual PaymentTerm PaymentTerm
		{
			get { return _paymentTerm; }
			set { _paymentTerm = value; }
		}

		[DataMember]
		public virtual PrefixCode PrefixCode
		{
			get { return _prefixCode; }
			set { _prefixCode = value; }
		}

		[DataMember]
		public virtual Priority Priority
		{
			get { return _priority; }
			set { _priority = value; }
		}

		[DataMember]
		public virtual RequisitionHeader RequisitionHeader
		{
			get { return _requisitionHeader; }
			set { _requisitionHeader = value; }
		}

		[DataMember]
		public virtual String BillOfLading
		{
			get { return _billOfLading; }
			set { _billOfLading = value; }
		}

		[DataMember]
		public virtual String Comments
		{
			get { return _comments; }
			set { _comments = value; }
		}

		[DataMember]
		public virtual String PurchaseOrderCode
		{
			get { return _purchaseOrderCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("PurchaseOrderCode must not be blank or null.");
				else _purchaseOrderCode = value;
			}
		}

		[DataMember]
		public virtual String PurchaseOrderSuffix
		{
			get { return _purchaseOrderSuffix; }
			set { _purchaseOrderSuffix = value; }
		}

		[DataMember]
		public virtual String SealNumber
		{
			get { return _sealNumber; }
			set { _sealNumber = value; }
		}

		[DataMember]
		public virtual String SendPoEmail
		{
			get { return _sendPoEmail; }
			set { _sendPoEmail = value; }
		}

		[DataMember]
		public virtual String TrailerCode
		{
			get { return _trailerCode; }
			set { _trailerCode = value; }
		}

		[DataMember]
		public virtual String VendorCode
		{
			get { return _vendorCode; }
			set { _vendorCode = value; }
		}

		[DataMember]
		public virtual Vendor Vendor
		{
			get { return _vendor; }
			set { _vendor = value; }
		}


		#endregion
	}
}
