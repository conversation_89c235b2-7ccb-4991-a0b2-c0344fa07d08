using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;

using NHibernate;
using NHibernate.Criterion;
using NHibernate.SqlCommand;
using NHibernate.Transform;

using Upp.Irms.Constants;
using Upp.Irms.Core;

namespace Upp.Irms.Domain
{
    [DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
    public partial class PurchaseOrderHeader : Entity
    {
        #region Properties

        [DataMember]
        public virtual Boolean InProgress { get; set; }
        [DataMember]
        public virtual Int32 DetailCount { get; set; }
        [DataMember]
        public virtual Int32 TransactionCount { get; set; }
        [DataMember]
        public virtual Int32? CloseQuestion { get; set; }
        [DataMember]
        public virtual ReceiptHeader ReceiptHeader { get; set; }
        [DataMember]
        public virtual StatusCode CurrentStatus { get; set; }
        [DataMember]
        public virtual String ReceiptCode { get; set; }
        [DataMember]
        public virtual String RmaCode { get; set; }
        [DataMember]
        public virtual String StatusCodeCode { get; set; }
        [DataMember]
        public virtual String CarrierCode { get; set; }

        [DataMember]
        public virtual String CarrierName { get; set; }
        [DataMember]
        public virtual String CustomerCode { get; set; }
        [DataMember]
        public virtual String CustomerName { get; set; }
        [DataMember]
        public virtual String VendorName { get; set; }
        [DataMember]
        public virtual String ItemGroupCode { get; set; }
        [DataMember]
        public virtual String ItemCode { get; set; }
        [DataMember]
        public virtual Decimal ActualQuantity { get; set; }
        [DataMember]
        public virtual Decimal ActualWeight { get; set; }
        [DataMember]
        public virtual Decimal Qty { get; set; }
        [DataMember]
        public virtual Decimal ItemWeight { get; set; }
        [DataMember]
        public virtual Decimal TotalWeight { get; set; }

        [DataMember]
        public virtual int PurchaseOrderHeaderId { get; set; }

        #endregion

        #region Constructor

        public PurchaseOrderHeader()
        {
            //
        }

        #endregion

        #region Methods.Public

        public virtual void CalculateDetailCount()
        {
            StatusCode active = Entity.Retrieve<StatusCode>(PurchaseStatuses.Active);
            StatusCode open = Entity.Retrieve<StatusCode>(PurchaseStatuses.Open);
            DetachedCriteria criteria = DetachedCriteria.For<PurchaseOrderDetail>()
                .Add("PurchaseOrderHeader", this)
                .AddOr("StatusCode", active, "StatusCode", open)
                .SetProjection(Projections.Count("Id"));
            this.DetailCount = Repositories.Get<PurchaseOrderDetail>().Function<Int32>(criteria);
        }

        public virtual void ChangeStatus(StatusCode status)
        {
            PurchaseOrderStatus created = Entity.Activate<PurchaseOrderStatus>();
            created.Occurred = DateTime.Now;
            created.PurchaseOrderHeader = this;
            created.StatusCode = status;
            Repositories.Get<PurchaseOrderStatus>().Add(created);
        }

        public virtual void FindCurrentStatus()
        {
            DetachedCriteria criteria = DetachedCriteria.For<PurchaseOrderStatus>()
                .Add("PurchaseOrderHeader", this)
                .AddOrder("Occurred", false)
                .AddOrder("StatusCode.SortOrder", false)
                .SetMaxResults(1);
            IList<PurchaseOrderStatus> results = Repositories.Get<PurchaseOrderStatus>().List(criteria);
            if (results.Count == 1) this.CurrentStatus = results[0].StatusCode;
        }

        public virtual StatusCode FindDetailStatus()
        {
            DetachedCriteria criteria = DetachedCriteria.For<PurchaseOrderDetail>()
                .Add(Expression.Eq("PurchaseOrderHeader", this))
                .SetProjection(Projections.Count("Id"));
            Int32 details = Repositories.Get<PurchaseOrderDetail>().Function<Int32>(criteria);
            if (details == 0) return Entity.Retrieve<StatusCode>(PurchaseStatuses.Pending);
            //
            ProjectionList projections = Projections.ProjectionList()
                .Add(Projections.Count("Id"), "Count")
                .Add(Projections.GroupProperty("sc.Code"), "Status");
            criteria = DetachedCriteria.For<PurchaseOrderDetail>()
                .CreateAlias("StatusCode", "sc")
                .Add(Expression.Eq("PurchaseOrderHeader", this))
                .SetProjection(projections)
                .SetResultTransformer(Transformers.AliasToBean<PurchaseOrderDetail>());
            IList<PurchaseOrderDetail> distribution = Repositories.Get<PurchaseOrderDetail>().List(criteria);
            //
            PurchaseStatuses[] statuses = new PurchaseStatuses[]
            {
                PurchaseStatuses.Complete,
                PurchaseStatuses.Open,
                PurchaseStatuses.Received,
            };
            foreach (PurchaseStatuses element in statuses)
            {
                PurchaseOrderDetail match = distribution
                    .Where(e => e.Status.Equals(CodeValue.GetCode(element)))
                    .FirstOrDefault();
                if (match != null && match.Count == details) return Entity.Retrieve<StatusCode>(element);
            }
            //
            return Entity.Retrieve<StatusCode>(PurchaseStatuses.Active);
        }

        public virtual String FindFullCode()
        {
            if (String.IsNullOrEmpty(_purchaseOrderSuffix)) return _purchaseOrderCode;
            else return String.Format("{0}-{1}", _purchaseOrderCode, _purchaseOrderSuffix);
        }

        public virtual void FindReceiptHeader(bool create)
        {
            CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
            DetachedCriteria criteria = DetachedCriteria.For<ReceiptHeader>()
                .Add("CompanyLocationType", warehouse)
                .Add("PurchaseOrderHeader", this)
                .SetMaxResults(1);
            this.ReceiptHeader = Repositories.Get<ReceiptHeader>().Retrieve(criteria);
            if (create && this.ReceiptHeader == null)
            {
                ReceiptHeader header = ReceiptHeader.Create(this);
                header.ReceivedBy = Registry.Find<UserAccount>().OrganizationParticipant;
                Repositories.Get<ReceiptHeader>().Add(header);
                //
                this.ReceiptHeader = header;
            }
        }

        public virtual void UpdateStatus()
        {
            this.FindCurrentStatus();
            //
            StatusCode detail = this.FindDetailStatus();
            if (this.CurrentStatus == null) this.ChangeStatus(detail);
            else if (detail.SortOrder > this.CurrentStatus.SortOrder) this.ChangeStatus(detail);
        }

        public virtual void ValidateActive()
        {
            if (this.CurrentStatus == null)
            {
                String error = String.Format("PO {0} does not have a status.", this.FindFullCode());
                throw new Exception(error);
            }
            //
            StatusCode active = Entity.Retrieve<StatusCode>(PurchaseStatuses.Active);
            StatusCode open = Entity.Retrieve<StatusCode>(PurchaseStatuses.Active);
            if (!active.SameAs(this.CurrentStatus) && !open.SameAs(this.CurrentStatus))
            {
                String error = String.Format("PO {0} status is {1}.", this.FindFullCode(), this.CurrentStatus.Description);
                throw new Exception(error);
            }
        }

        #endregion

        #region Methods.Public.SendClose

        public virtual void CountTransactions()
        {
            /* Prompt user to close the PO? */
            this.CloseQuestion = BusinessRule.RetrieveInteger("1011");
            /* Use PO|RT send|close screen? */
            Boolean? send = BusinessRule.RetrieveBoolean("1012");
            if (!send.HasValue || !send.Value) return;
            //
            StatusCode open = Entity.Retrieve<StatusCode>(IntegrationStatuses.Open);
            TransactionType received = Entity.Retrieve<TransactionType>(OrderTransactions.Received);
            DetachedCriteria criteria = DetachedCriteria.For<ItemTransaction>()
                .CreateAlias("ReceiptDetail", "rd")
                    .CreateAlias("rd.PurchaseOrderDetail", "pod", JoinType.LeftOuterJoin)
                    .CreateAlias("rd.ReceiptHeader", "rh")
                .Add(Expression.Or(Expression.Eq("pod.PurchaseOrderHeader", this), Expression.Eq("rh.PurchaseOrderHeader", this)))
                .Add(Expression.Eq("TransactionType", received))
                .Add(Expression.Eq("IntegrationStatusCode", open))
                .Add(Expression.IsNull("Task"))
                .SetProjection(Projections.Count("Id"));
            this.TransactionCount = Repositories.Get<ItemTransaction>().Function<Int32>(criteria);
        }

        public virtual void Send(bool close)
        {
            TransactionType send = Entity.Retrieve<TransactionType>(InventoryTransactions.ReceiptSend);
            Task created = Task.Create(send);
            Repositories.Get<Task>().Add(created);
            //
            StatusCode open = Entity.Retrieve<StatusCode>(IntegrationStatuses.Open);
            TransactionType received = Entity.Retrieve<TransactionType>(OrderTransactions.Received);
            DetachedCriteria criteria = DetachedCriteria.For<ItemTransaction>()
                .CreateAlias("ReceiptDetail", "rd")
                    .CreateAlias("rd.PurchaseOrderDetail", "pod", JoinType.LeftOuterJoin)
                    .CreateAlias("rd.ReceiptHeader", "rh")
                .Add(Expression.Eq("TransactionType", received))
                .Add(Expression.Eq("IntegrationStatusCode", open))
                .Add(Expression.IsNull("Task"))
                .Add(Expression.Or(Expression.Eq("pod.PurchaseOrderHeader", this), Expression.Eq("rh.PurchaseOrderHeader", this)));
            IList<ItemTransaction> transactions = Repositories.Get<ItemTransaction>().List(criteria);
            foreach (ItemTransaction element in transactions)
            {
                element.DateModified = DateTime.Now;
                element.IntegrationStatusCode = open;
                element.Task = created;
                element.UserModified = Registry.Find<UserAccount>().UserName;
                Repositories.Get<ItemTransaction>().Update(element);
            }
            //
            if (!close) return;
            //
            StatusCode oreceived = Entity.Retrieve<StatusCode>(OrderStatuses.Received);
            criteria = DetachedCriteria.For<ReceiptDetail>()
                .CreateAlias("PurchaseOrderDetail", "pod", JoinType.LeftOuterJoin)
                .CreateAlias("ReceiptHeader", "rh")
                .Add(Expression.Not(Expression.Eq("StatusCode", oreceived)))
                .Add(Expression.Or(Expression.Eq("pod.PurchaseOrderHeader", this), Expression.Eq("rh.PurchaseOrderHeader", this)))
                .SetFetchMode("ReceiptHeader", FetchMode.Join);
            IList<ReceiptDetail> details = Repositories.Get<ReceiptDetail>().List(criteria);
            foreach (ReceiptDetail element in details)
            {
                element.ChangeStatus(oreceived);
                element.ReceiptHeader.ReceivedBy = Registry.Find<UserAccount>().OrganizationParticipant;
                element.ReceiptHeader.ChangeStatus(oreceived);
            }
            //
            StatusCode complete = Entity.Retrieve<StatusCode>(PurchaseStatuses.Complete);
            criteria = DetachedCriteria.For<PurchaseOrderDetail>().Add(Expression.Eq("PurchaseOrderHeader", this));
            IList<PurchaseOrderDetail> pdetails = Repositories.Get<PurchaseOrderDetail>().List(criteria);
            foreach (PurchaseOrderDetail element in pdetails) element.ChangeStatus(complete);
            //
            this.ChangeStatus(complete);
        }

        #endregion
    }
}
