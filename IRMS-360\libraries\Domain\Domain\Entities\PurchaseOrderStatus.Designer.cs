using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class PurchaseOrderStatus : Entity
	{
		#region Fields

		private DateTime _occurred;
		private PurchaseOrderHeader _purchaseOrderHeader;
		private StatusCode _integrationStatusCode;
		private StatusCode _statusCode;
		private String _comments;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime Occurred
		{
			get { return _occurred; }
			set { _occurred = value; }
		}

		[DataMember]
		public virtual PurchaseOrderHeader PurchaseOrderHeader
		{
			get { return _purchaseOrderHeader; }
			set { _purchaseOrderHeader = value; }
		}

		[DataMember]
		public virtual StatusCode IntegrationStatusCode
		{
            get { return _integrationStatusCode != null ? _integrationStatusCode : StatusCode.GetOpenIntegrationStatusCode(); }
            set { _integrationStatusCode = value != null ? value : StatusCode.GetOpenIntegrationStatusCode(); }
        }

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual String Comments
		{
			get { return _comments; }
			set { _comments = value; }
		}


		#endregion
	}
}
