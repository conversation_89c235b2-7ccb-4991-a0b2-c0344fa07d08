using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Question : Entity
	{
		#region Fields

		private FunctionalAreaCode _functionalAreaCode;
		private ICollection<QuestionnaireQuestion> _questionnaireQuestions = new HashSet<QuestionnaireQuestion>();
		private ICollection<UserAccountQuestion> _userAccountQuestions = new HashSet<UserAccountQuestion>();
		private String _active;
		private String _description;
		private String _questionCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual FunctionalAreaCode FunctionalAreaCode
		{
			get { return _functionalAreaCode; }
			set { _functionalAreaCode = value; }
		}

		[DataMember]
		public virtual ICollection<QuestionnaireQuestion> QuestionnaireQuestions
		{
			get { return _questionnaireQuestions; }
			set { _questionnaireQuestions = value; }
		}

		[DataMember]
		public virtual ICollection<UserAccountQuestion> UserAccountQuestions
		{
			get { return _userAccountQuestions; }
			set { _userAccountQuestions = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String QuestionCode
		{
			get { return _questionCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("QuestionCode must not be blank or null.");
				else _questionCode = value;
			}
		}


		#endregion
	}
}
