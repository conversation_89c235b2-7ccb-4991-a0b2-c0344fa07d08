using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Questionnaire : Entity
	{
		#region Fields

		private FunctionalAreaCode _functionalAreaCode;
		private ICollection<InspectionProcedure> _inspectionProcedures = new HashSet<InspectionProcedure>();
		private ICollection<QuestionnaireQuestion> _questionnaireQuestions = new HashSet<QuestionnaireQuestion>();
		private PodType _podType;
		private String _active;
		private String _description;
		private String _questionnaireCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual FunctionalAreaCode FunctionalAreaCode
		{
			get { return _functionalAreaCode; }
			set { _functionalAreaCode = value; }
		}

		[DataMember]
		public virtual ICollection<InspectionProcedure> InspectionProcedures
		{
			get { return _inspectionProcedures; }
			set { _inspectionProcedures = value; }
		}

		[DataMember]
		public virtual ICollection<QuestionnaireQuestion> QuestionnaireQuestions
		{
			get { return _questionnaireQuestions; }
			set { _questionnaireQuestions = value; }
		}

		[DataMember]
		public virtual PodType PodType
		{
			get { return _podType; }
			set { _podType = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String QuestionnaireCode
		{
			get { return _questionnaireCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("QuestionnaireCode must not be blank or null.");
				else _questionnaireCode = value;
			}
		}


		#endregion
	}
}
