using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class QuestionnaireAnswer : Entity
	{
		#region Fields

		private Answer _answer;
		private Int32 _sortOrder;
		private ICollection<QuestionnaireDirective> _questionnaireDirectives = new HashSet<QuestionnaireDirective>();
		private QuestionnaireQuestion _nextQuestionnaireQuestion;
		private QuestionnaireQuestion _questionnaireQuestion;
		private StandardDirective _standardDirective;
		private String _active;
		private String _answerText;
		private String _notes;

		#endregion

		#region Properties

		[DataMember]
		public virtual Answer Answer
		{
			get { return _answer; }
			set { _answer = value; }
		}

		[DataMember]
		public virtual Int32 SortOrder
		{
			get { return _sortOrder; }
			set { _sortOrder = value; }
		}

		[DataMember]
		public virtual ICollection<QuestionnaireDirective> QuestionnaireDirectives
		{
			get { return _questionnaireDirectives; }
			set { _questionnaireDirectives = value; }
		}

		[DataMember]
		public virtual QuestionnaireQuestion NextQuestionnaireQuestion
		{
			get { return _nextQuestionnaireQuestion; }
			set { _nextQuestionnaireQuestion = value; }
		}

		[DataMember]
		public virtual QuestionnaireQuestion QuestionnaireQuestion
		{
			get { return _questionnaireQuestion; }
			set { _questionnaireQuestion = value; }
		}

		[DataMember]
		public virtual StandardDirective StandardDirective
		{
			get { return _standardDirective; }
			set { _standardDirective = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String AnswerText
		{
			get { return _answerText; }
			set { _answerText = value; }
		}

		[DataMember]
		public virtual String Notes
		{
			get { return _notes; }
			set { _notes = value; }
		}


		#endregion
	}
}
