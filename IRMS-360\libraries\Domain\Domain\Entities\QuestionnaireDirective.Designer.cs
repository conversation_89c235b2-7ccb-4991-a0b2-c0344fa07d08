using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class QuestionnaireDirective : Entity
	{
		#region Fields

		private QuestionnaireAnswer _questionnaireAnswer;
		private StandardDirective _standardDirective;

		#endregion

		#region Properties

		[DataMember]
		public virtual QuestionnaireAnswer QuestionnaireAnswer
		{
			get { return _questionnaireAnswer; }
			set { _questionnaireAnswer = value; }
		}

		[DataMember]
		public virtual StandardDirective StandardDirective
		{
			get { return _standardDirective; }
			set { _standardDirective = value; }
		}


		#endregion
	}
}
