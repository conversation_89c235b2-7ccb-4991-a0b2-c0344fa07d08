using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class QuestionnaireQuestion : Entity
	{
		#region Fields

		private Int32 _sortOrder;
		private Question _question;
		private Questionnaire _questionnaire;
		private String _active;

		#endregion

		#region Properties

		[DataMember]
		public virtual Int32 SortOrder
		{
			get { return _sortOrder; }
			set { _sortOrder = value; }
		}

		[DataMember]
		public virtual Question Question
		{
			get { return _question; }
			set { _question = value; }
		}

		[DataMember]
		public virtual Questionnaire Questionnaire
		{
			get { return _questionnaire; }
			set { _questionnaire = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}


		#endregion
	}
}
