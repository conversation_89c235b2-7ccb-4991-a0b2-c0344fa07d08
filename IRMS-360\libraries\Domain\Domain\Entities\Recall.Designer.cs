using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Recall : Entity
	{
		#region Fields

		private Agency _agency;
		private CompanyLocationType _companyLocationType;
		private ICollection<RecallCommunication> _recallCommunications = new HashSet<RecallCommunication>();
		private ICollection<RecallDocument> _recallDocuments = new HashSet<RecallDocument>();
		private ICollection<RecallItem> _recallItems = new HashSet<RecallItem>();
		private StatusCode _statusCode;
		private String _comments;
		private String _description;
		private String _recallCode;
		private String _recallLevel;
		private String _recallMethod;
		private String _recallTypeCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual Agency Agency
		{
			get { return _agency; }
			set { _agency = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual ICollection<RecallCommunication> RecallCommunications
		{
			get { return _recallCommunications; }
			set { _recallCommunications = value; }
		}

		[DataMember]
		public virtual ICollection<RecallDocument> RecallDocuments
		{
			get { return _recallDocuments; }
			set { _recallDocuments = value; }
		}

		[DataMember]
		public virtual ICollection<RecallItem> RecallItems
		{
			get { return _recallItems; }
			set { _recallItems = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual String Comments
		{
			get { return _comments; }
			set { _comments = value; }
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String RecallCode
		{
			get { return _recallCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("RecallCode must not be blank or null.");
				else _recallCode = value;
			}
		}

		[DataMember]
		public virtual String RecallLevel
		{
			get { return _recallLevel; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("RecallLevel must not be blank or null.");
				else _recallLevel = value;
			}
		}

		[DataMember]
		public virtual String RecallMethod
		{
			get { return _recallMethod; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("RecallMethod must not be blank or null.");
				else _recallMethod = value;
			}
		}

		[DataMember]
		public virtual String RecallTypeCode
		{
			get { return _recallTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("RecallTypeCode must not be blank or null.");
				else _recallTypeCode = value;
			}
		}


		#endregion
	}
}
