using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class RecallCommunication : Entity
	{
		#region Fields

		private CommunicationRole _communicationRole;
		private Recall _recall;
		private String _communicationValue;

		#endregion

		#region Properties

		[DataMember]
		public virtual CommunicationRole CommunicationRole
		{
			get { return _communicationRole; }
			set { _communicationRole = value; }
		}

		[DataMember]
		public virtual Recall Recall
		{
			get { return _recall; }
			set { _recall = value; }
		}

		[DataMember]
		public virtual String CommunicationValue
		{
			get { return _communicationValue; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("CommunicationValue must not be blank or null.");
				else _communicationValue = value;
			}
		}


		#endregion
	}
}
