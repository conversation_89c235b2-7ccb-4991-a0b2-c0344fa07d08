using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class RecallDocument : Entity
	{
		#region Fields

		private Byte[] _document;
		private DocumentType _documentType;
		private Recall _recall;
		private String _documentLocation;

		#endregion

		#region Properties

		[DataMember]
		public virtual Byte[] Document
		{
			get { return _document; }
			set { _document = value; }
		}

		[DataMember]
		public virtual DocumentType DocumentType
		{
			get { return _documentType; }
			set { _documentType = value; }
		}

		[DataMember]
		public virtual Recall Recall
		{
			get { return _recall; }
			set { _recall = value; }
		}

		[DataMember]
		public virtual String DocumentLocation
		{
			get { return _documentLocation; }
			set { _documentLocation = value; }
		}


		#endregion
	}
}
