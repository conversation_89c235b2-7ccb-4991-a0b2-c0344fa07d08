using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class RecallItem : Entity
	{
		#region Fields

		private InventoryItem _inventoryItem;
		private Recall _recall;
		private ShipmentDetail _shipmentDetail;

		#endregion

		#region Properties

		[DataMember]
		public virtual InventoryItem InventoryItem
		{
			get { return _inventoryItem; }
			set { _inventoryItem = value; }
		}

		[DataMember]
		public virtual Recall Recall
		{
			get { return _recall; }
			set { _recall = value; }
		}

		[DataMember]
		public virtual ShipmentDetail ShipmentDetail
		{
			get { return _shipmentDetail; }
			set { _shipmentDetail = value; }
		}


		#endregion
	}
}
