using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ReceiptDetail : Entity
	{
		#region Fields

		private DateTime? _lotExpiration;
		private DateTime? _manufactureDate;
		private Decimal _quantity;
		private Decimal? _expectedQuantity;
		private Decimal? _unitCost;
		private Decimal? _uomQuantity;
		private Item _item;
		private LicensePlate _licensePlate;
		private ICollection<InventoryItem> _inventoryItems = new HashSet<InventoryItem>();
		private ICollection<InvoiceDetail> _invoiceDetails = new HashSet<InvoiceDetail>();
		private ICollection<ItemTransaction> _itemTransactions = new HashSet<ItemTransaction>();
		private ICollection<ReceiptReturnDetail> _receiptReturnDetails = new HashSet<ReceiptReturnDetail>();
		private Location _location;
		private OrderDetail _orderDetail;
		private PurchaseOrderDetail _purchaseOrderDetail;
		private ReceiptHeader _receiptHeader;
		private StatusCode _statusCode;
		private StatusCode _stockStatusCode;
		private String _comments;
		private String _itemCode;
		private String _itemDescription;
		private String _lotNumber;
		private String _serialNumber;
		private UnitOfMeasure _unitOfMeasure;
		private VendorItem _vendorItem;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime? LotExpiration
		{
			get { return _lotExpiration; }
			set { _lotExpiration = value; }
		}

		[DataMember]
		public virtual DateTime? ManufactureDate
		{
			get { return _manufactureDate; }
			set { _manufactureDate = value; }
		}

		[DataMember]
		public virtual Decimal Quantity
		{
			get { return _quantity; }
			set { _quantity = value; }
		}

		[DataMember]
		public virtual Decimal? ExpectedQuantity
		{
			get { return _expectedQuantity; }
			set { _expectedQuantity = value; }
		}

		[DataMember]
		public virtual Decimal? UnitCost
		{
			get { return _unitCost; }
			set { _unitCost = value; }
		}

		[DataMember]
		public virtual Decimal? UomQuantity
		{
			get { return _uomQuantity; }
			set { _uomQuantity = value; }
		}

		[DataMember]
		public virtual Item Item
		{
			get { return _item; }
			set { _item = value; }
		}

		[DataMember]
		public virtual LicensePlate LicensePlate
		{
			get { return _licensePlate; }
			set { _licensePlate = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryItem> InventoryItems
		{
			get { return _inventoryItems; }
			set { _inventoryItems = value; }
		}

		[DataMember]
		public virtual ICollection<InvoiceDetail> InvoiceDetails
		{
			get { return _invoiceDetails; }
			set { _invoiceDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ItemTransaction> ItemTransactions
		{
			get { return _itemTransactions; }
			set { _itemTransactions = value; }
		}

		[DataMember]
		public virtual ICollection<ReceiptReturnDetail> ReceiptReturnDetails
		{
			get { return _receiptReturnDetails; }
			set { _receiptReturnDetails = value; }
		}

		[DataMember]
		public virtual Location Location
		{
			get { return _location; }
			set { _location = value; }
		}

		[DataMember]
		public virtual OrderDetail OrderDetail
		{
			get { return _orderDetail; }
			set { _orderDetail = value; }
		}

		[DataMember]
		public virtual PurchaseOrderDetail PurchaseOrderDetail
		{
			get { return _purchaseOrderDetail; }
			set { _purchaseOrderDetail = value; }
		}

		[DataMember]
		public virtual ReceiptHeader ReceiptHeader
		{
			get { return _receiptHeader; }
			set { _receiptHeader = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual StatusCode StockStatusCode
		{
			get { return _stockStatusCode; }
			set { _stockStatusCode = value; }
		}

		[DataMember]
		public virtual String Comments
		{
			get { return _comments; }
			set { _comments = value; }
		}

		[DataMember]
		public virtual String ItemCode
		{
			get { return _itemCode; }
			set { _itemCode = value; }
		}

		[DataMember]
		public virtual String ItemDescription
		{
			get { return _itemDescription; }
			set { _itemDescription = value; }
		}

		[DataMember]
		public virtual String LotNumber
		{
			get { return _lotNumber; }
			set { _lotNumber = value; }
		}

		[DataMember]
		public virtual String SerialNumber
		{
			get { return _serialNumber; }
			set { _serialNumber = value; }
		}

		[DataMember]
		public virtual UnitOfMeasure UnitOfMeasure
		{
			get { return _unitOfMeasure; }
			set { _unitOfMeasure = value; }
		}

		[DataMember]
		public virtual VendorItem VendorItem
		{
			get { return _vendorItem; }
			set { _vendorItem = value; }
		}


		#endregion
	}
}
