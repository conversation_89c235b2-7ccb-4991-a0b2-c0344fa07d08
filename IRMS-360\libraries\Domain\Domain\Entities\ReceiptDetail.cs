using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;

using NHibernate;
using NHibernate.Criterion;
using NHibernate.SqlCommand;
using NHibernate.Transform;

using Upp.Irms.Constants;
using Upp.Irms.Core;
using Upp.Shared.Utilities;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class ReceiptDetail : Entity
	{
		#region Properties

		[DataMember]
		public virtual Boolean FullLpn { get; set; }
		[DataMember]
		public virtual Boolean HideQuantity { get; set; }
		[DataMember]
		public virtual Boolean SkipUomQuantity { get; set; }
		[DataMember]
		public virtual Decimal ReceivedQuantity { get; set; }
		[DataMember]
		public virtual Decimal RequiredQuantity { get; set; }
        [DataMember]
        public virtual Decimal PODetailQuantity { get; set; }
		[DataMember]
		public virtual Decimal? OriginalQuantity { get; set; }
		[DataMember]
		public virtual Int32 Copies { get; set; }
		[DataMember]
		public virtual Int32 Count { get; set; }
		[DataMember]
		public virtual Int32 LineNumber { get; set; }
		[DataMember]
		public virtual Int32? Overages { get; set; }
		[DataMember]
		public virtual ICollection<UdfItemValue> UdfItemValues { get; set; }
		[DataMember]
		public virtual List<String> DetailComments { get; set; }
		[DataMember]
		public virtual PrinterTypePrinter PrinterTypePrinter { get; set; }
		[DataMember]
		public virtual String Default { get; set; }
		[DataMember]
		public virtual String DefectCode { get; set; }
		[DataMember]
		public virtual String ItemLongDescription { get; set; }
		[DataMember]
		public virtual String LicensePlateCode { get; set; }
		[DataMember]
		public virtual String PurchaseOrderCode { get; set; }
		[DataMember]
		public virtual String PurchaseOrderSuffix { get; set; }
		[DataMember]
		public virtual String ReceiptCode { get; set; }
		[DataMember]
		public virtual String SerialNumbers { get; set; }
		[DataMember]
		public virtual String Status { get; set; }
		[DataMember]
		public virtual String StockStatus { get; set; }
		[DataMember]
		public virtual String UdfLabels { get; set; }
		[DataMember]
		public virtual String UdfValues { get; set; }
		[DataMember]
		public virtual String UnitDescription { get; set; }
		[DataMember]
		public virtual String UomCode { get; set; }
		[DataMember]
		public virtual String UomDescription { get; set; }
		[DataMember]
		public virtual String UpcCode { get; set; }
		[DataMember]
		public virtual String VendorItemCode { get; set; }
		[DataMember]
		public virtual String ZoneCode { get; set; }
        [DataMember]
        public virtual String POTrailer { get; set; }
        [DataMember]
        public virtual String POStatus { get; set; }
        [DataMember]
        public virtual String StatusCodeDescription { get; set; }
        [DataMember]
        public virtual String POTypeCode { get; set; }
        [DataMember]
        public virtual String Lot { get; set; }
        [DataMember]
        public virtual Decimal Expected { get; set; }
        [DataMember]
        public virtual Decimal Open { get; set; }
        [DataMember]
        public virtual Decimal Actual { get; set; }
        [DataMember]
        public virtual Int32 PurchaseOrderHeaderId { get; set; }
        [DataMember]
        public virtual String FilteredCustomerCode { get; set; }
        [DataMember]
        public virtual String FilteredPoCode { get; set; }
        [DataMember]
        public virtual String Warehouse { get; set; }
        [DataMember]
        public virtual String Company { get; set; }
        [DataMember]
        public virtual String VendorName { get; set; }
        [DataMember]
        public virtual string FilteredToDate { get; set; }
        [DataMember]
        public virtual string FilteredFromDate { get; set; }
        [DataMember]
        public virtual DateTime? PODate { get; set; }
        [DataMember]
        public virtual int ItemId { get; set; }
        [DataMember]
        public virtual int? LicensePlateId { get; set; }
        [DataMember]
        public virtual string TiedOrder { get; set; }
        [DataMember]
        public virtual Int32 PurchaseOrderDetailId { get; set; }
        #endregion

        #region Constructor

        public ReceiptDetail()
		{
			//
		}

		#endregion

		#region Methods.Private.Receive

		private void ClosePo(bool? manual)
		{
			StatusCode active = Entity.Retrieve<StatusCode>(PurchaseStatuses.Active);
			StatusCode open = Entity.Retrieve<StatusCode>(PurchaseStatuses.Open);
			StatusCode oreceived = Entity.Retrieve<StatusCode>(OrderStatuses.Received);
			StatusCode received = Entity.Retrieve<StatusCode>(PurchaseStatuses.Received);
			//
			ProjectionList projections = Projections.ProjectionList()
				.Add(Projections.Distinct(Projections.Property("PurchaseOrderDetail")), "PurchaseOrderDetail");
			DetachedCriteria criteria = DetachedCriteria.For<ReceiptDetail>()
				.CreateAlias("PurchaseOrderDetail", "pod")
				.Add(Expression.Eq("Item", _item))
				.Add(Expression.Or(Expression.Eq("pod.StatusCode", active), Expression.Eq("pod.StatusCode", active)))
				.Add(Expression.Eq("pod.UnitOfMeasure", _unitOfMeasure))
				.Add(Expression.Eq("ReceiptHeader", _receiptHeader))
				.SetFetchMode("PurchaseOrderDetail", FetchMode.Join)
				.SetProjection(projections)
				.SetResultTransformer(Transformers.AliasToBean<ReceiptDetail>());
			IList<ReceiptDetail> details = Repositories.Get<ReceiptDetail>().List(criteria);
			//
			projections = Projections.ProjectionList()
				.Add(Projections.GroupProperty("pod.Id"), "Id")
				.Add(Projections.Sum("Quantity"), "ReceivedQuantity");
			criteria = DetachedCriteria.For<ReceiptDetail>()
				.CreateAlias("PurchaseOrderDetail", "pod")
				.Add(Expression.Eq("Item", _item))
				.Add(Expression.Or(Expression.Eq("pod.StatusCode", active), Expression.Eq("pod.StatusCode", active)))
				.Add(Expression.Eq("pod.UnitOfMeasure", _unitOfMeasure))
				.Add(Expression.Eq("ReceiptHeader", _receiptHeader))
				.Add(Expression.Eq("StatusCode", oreceived))
				.SetProjection(projections)
				.SetResultTransformer(Transformers.AliasToBean<ReceiptDetail>());
			IList<ReceiptDetail> processed = Repositories.Get<ReceiptDetail>().List(criteria);
			//
			foreach (ReceiptDetail element in processed)
			{
				ReceiptDetail match = details
					.Where(e => e.PurchaseOrderDetail.Id == element.Id)
					.FirstOrDefault();
				if (match == null) continue;
				//
				if (match.PurchaseOrderDetail.Quantity > element.ReceivedQuantity)
					match.PurchaseOrderDetail.ChangeStatus(active);
				else if (manual.HasValue && !manual.Value)
					match.PurchaseOrderDetail.ChangeStatus(received);
				else match.PurchaseOrderDetail.ChangeStatus(active);
			}
			/* (Unavoidable?) Performance nightmare: */
			foreach (ReceiptDetail element in details)
				element.PurchaseOrderDetail.PurchaseOrderHeader.UpdateStatus();
		}

		private void Receive(decimal quantity, bool match, IList<UdfMetadataValue> metadata)
		{
			Decimal received = match ? quantity : (quantity * _uomQuantity).Value;
			InventoryItem created = InventoryItem.Create(_item, _location, _stockStatusCode, received);
			created.LotExpiration = this.LotExpiration;
			created.LotNumber = _lotNumber;
			created.ReceiptDetail = this;
			created.Received = DateTime.Now;
			created.UnitCost = _unitCost;
			created.UnitOfMeasure = _item.UnitOfMeasure;
			created.UomQuantity = _uomQuantity;
			Repositories.Get<InventoryItem>().Add(created);
			//
			created.LicensePlate.ChangeParent(_licensePlate);
			created.WriteReceived(this);
			created.WriteUdfValues(metadata, this.UdfItemValues);
			//
			if (_expectedQuantity > quantity)
			{
				ReceiptDetail split = Entity.Clone<ReceiptDetail>(this);
				split.ExpectedQuantity = _expectedQuantity - quantity;
				split.LicensePlate = null;
				split.Location = null;
				split.LotNumber = null;
				split.Quantity = 0;
				split.StatusCode = Entity.Retrieve<StatusCode>(OrderStatuses.Active);
				split.StockStatusCode = null;
				split.UomQuantity = null;
				Repositories.Get<ReceiptDetail>().Add(split);
				//
				_expectedQuantity = quantity;
			}
			//
			_dateModified = DateTime.Now;
			_quantity = quantity;
			_statusCode = Entity.Retrieve<StatusCode>(OrderStatuses.Received);
			_userModified = Registry.Find<UserAccount>().UserName;
			Repositories.Get<ReceiptDetail>().Update(this);
		}

		#endregion

		#region Methods.Private.Undo

		private void Restore()
		{
			CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
			String inventory = CodeValue.GetCode(FunctionalAreas.Inventory);
			//
			DetachedCriteria criteria = DetachedCriteria.For<Item>()
				.Add("CompanyLocationType", warehouse)
				.Add("ItemCode", this.ItemCode)
				.SetMaxResults(1);
			_item = Repositories.Get<Item>().Retrieve(criteria);
			//
			if (String.IsNullOrEmpty(this.LicensePlateCode)) _licensePlate = null;
			else
			{
				criteria = DetachedCriteria.For<LicensePlate>()
					.Add("CompanyLocationType", warehouse)
					.Add("LicensePlateCode", this.LicensePlateCode)
					.SetMaxResults(1);
				_licensePlate = Repositories.Get<LicensePlate>().Retrieve(criteria);
			}
			//
			if (_id.HasValue) _purchaseOrderDetail = Repositories.Get<PurchaseOrderDetail>().Retrieve(_id);
			//
			criteria = DetachedCriteria.For<ReceiptHeader>()
				.Add("CompanyLocationType", warehouse)
				.Add("ReceiptCode", this.ReceiptCode)
				.SetMaxResults(1);
			_receiptHeader = Repositories.Get<ReceiptHeader>().Retrieve(criteria);
			//
			criteria = DetachedCriteria.For<StatusCode>()
				.Add("Code", this.StockStatus)
				.Add("FunctionalAreaCode.Code", inventory)
				.SetMaxResults(1);
			_stockStatusCode = Repositories.Get<StatusCode>().Retrieve(criteria);
			//
			criteria = DetachedCriteria.For<UnitOfMeasure>()
				.Add("FunctionalAreaCode.Code", inventory)
				.Add("UomCode", this.UomCode)
				.SetMaxResults(1);
			_unitOfMeasure = Repositories.Get<UnitOfMeasure>().Retrieve(criteria);
		}

		#endregion

		#region Methods.Public

		public virtual void ChangeStatus(StatusCode status)
		{
			_dateModified = DateTime.Now;
			_statusCode = status;
			_userModified = Registry.Find<UserAccount>().UserName;
			Repositories.Get<ReceiptDetail>().Update(this);
		}
		/* LeMans Returns
		public virtual void Return()
		{
			StatusCode active = Entity.Retrieve<StatusCode>(OrderStatuses.Active);
			StatusCode received = Entity.Retrieve<StatusCode>(OrderStatuses.Received);
			//
			DateTime? date = BusinessRule.RetrieveDateTime("4052");
			if (!date.HasValue) date = DateTime.Now;
			//
			LicensePlate plate = null;
			Location location = null;
			if (this.ReceivedQuantity < _expectedQuantity)
			{
				ReceiptDetail split = Entity.Clone<ReceiptDetail>(this);
				split.DateModified = null;
				split.ExpectedQuantity = split.ReceivedQuantity;
				split.Location = split.LocationTo;
				split.Quantity = split.ReceivedQuantity;
				split.StatusCode = received;
				split.UserModified = null;
				Repositories.Get<ReceiptDetail>().Add(split);
				//
				plate = split.LicensePlate;
				location = split.Location;
				//
				_expectedQuantity -= this.ReceivedQuantity;
				_licensePlate = null;
				_location = null;
				_quantity = 0;
				_statusCode = active;
				_stockStatusCode = null;
				//
				InventoryItem created = this.CreateInventoryItem(split);
				created.Received = date;
				this.WriteReturned(created);
			}
			else
			{
				_location = this.LocationTo;
				_quantity = this.ReceivedQuantity;
				_statusCode = received;
				//
				plate = _licensePlate;
				location = _location;
				//
				InventoryItem created = this.CreateInventoryItem(this);
				created.Received = date;
				this.WriteReturned(created);
			}
			//
			if (!location.SameAs(plate.CurrentLocation)) plate.ChangeLocation(location);
			if (this.FullLpn) plate.ChangeStatus(Entity.Retrieve<StatusCode>(LicensePlateStatuses.Complete));
			//
			_receiptHeader.Receive();
			_purchaseOrderDetail.Complete();
			_purchaseOrderDetail.PurchaseOrderHeader.Complete();
		}
		*/
		#endregion

		#region Methods.Public.Receive

		public virtual void FindUomInfo()
		{
			CompanyLocationType warehouse = Registry.Find<CompanyLocationType>();
			String inventory = CodeValue.GetCode(FunctionalAreas.Inventory);
			UnitOfMeasure each = Entity.Retrieve<UnitOfMeasure>(InventoryUoms.Each);
			//
			DetachedCriteria criteria = DetachedCriteria.For<Item>()
				.Add(Expression.Eq("CompanyLocationType", warehouse))
				.Add(Expression.Eq("ItemCode", this.ItemCode))
				.SetMaxResults(1);
			_item = Repositories.Get<Item>().Retrieve(criteria);
			if (_item.UnitOfMeasure == null)
			{
				String error = String.Format("Missing standard UOM for item {0}.", _item.ItemCode);
				throw new Exception(error);
			}
			//
			criteria = DetachedCriteria.For<UnitOfMeasure>()
				.CreateAlias("FunctionalAreaCode", "fac")
				.Add(Expression.Eq("fac.Code", inventory))
				.Add(Expression.Eq("UomCode", this.UomCode))
				.SetMaxResults(1);
			_unitOfMeasure = Repositories.Get<UnitOfMeasure>().Retrieve(criteria);
			//
			if (_item.UnitOfMeasure.Equals(each)) this.UnitDescription = " Each =";
			else this.UnitDescription = String.Format(" {0} =", Inflector.Pluralize(_item.UnitOfMeasure.Description));
			//
			UnitOfMeasure unit = _unitOfMeasure;
			if (_item.UnitOfMeasure.Equals(_unitOfMeasure))
			{
				/* Default receiving UOM. */
				String code = BusinessRule.RetrieveString("11065");
				if (String.IsNullOrEmpty(code)) unit = Entity.Retrieve<UnitOfMeasure>(InventoryUoms.Case);
				else
				{
					criteria = DetachedCriteria.For<UnitOfMeasure>()
						.CreateAlias("FunctionalAreaCode", "fac")
						.Add(Expression.Eq("fac.Code", inventory))
						.Add(Expression.Eq("UomCode", code))
						.SetMaxResults(1);
					unit = Repositories.Get<UnitOfMeasure>().Retrieve(criteria);
					if (unit == null) unit = Entity.Retrieve<UnitOfMeasure>(InventoryUoms.Case);
				}
				//
				criteria = DetachedCriteria.For<ItemUomRelationship>()
					.Add(Expression.Eq("Active", "A"))
					.Add(Expression.Eq("Item", _item))
					.Add(Expression.Eq("UnitOfMeasure", unit))
					.SetMaxResults(1);
				ItemUomRelationship relationship = Repositories.Get<ItemUomRelationship>().Retrieve(criteria);
				Decimal? factor = (relationship == null) ? _unitOfMeasure.Factor : relationship.Factor;
				_uomQuantity = factor.HasValue ? Converter.ToInt32(factor) : 1;
				this.UomDescription = String.Format(" {0}", Inflector.Pluralize(unit.Description));
			}
			else
			{
				criteria = DetachedCriteria.For<ItemUomRelationship>()
					.Add(Expression.Eq("Active", "A"))
					.Add(Expression.Eq("Item", _item))
					.Add(Expression.Eq("UnitOfMeasure", _unitOfMeasure))
					.SetMaxResults(1);
				ItemUomRelationship relationship = Repositories.Get<ItemUomRelationship>().Retrieve(criteria);
				Decimal? factor = (relationship == null) ? _unitOfMeasure.Factor : relationship.Factor;
				_uomQuantity = factor.HasValue ? Converter.ToInt32(factor) : 1;
				//
				this.UomDescription = String.Format(" {0}", Inflector.Pluralize(_unitOfMeasure.Description));
			}
		}

		public virtual void Prepare(Location location)
		{
			_stockStatusCode = "Y".Equals(_item.QualityAssurance) ?
				Entity.Retrieve<StatusCode>(InventoryStatuses.QaHold) :
				Entity.Retrieve<StatusCode>(InventoryStatuses.Available);
			_stockStatusCode.SuggestLpn(_item.CompanyLocationZone, location);
			/* Show PO Comments */
			Boolean? comments = BusinessRule.RetrieveBoolean("6260");
			if (comments.HasValue && comments.Value)
			{
				StatusCode active = Entity.Retrieve<StatusCode>(OrderStatuses.Active);
				StatusCode open = Entity.Retrieve<StatusCode>(OrderStatuses.Open);
				//
				DetachedCriteria criteria = DetachedCriteria.For<ReceiptDetail>()
					.CreateAlias("PurchaseOrderDetail", "pod")
					.Add(Expression.Eq("Item", _item))
					.Add(Expression.IsNotNull("pod.Comments"))
					.Add(Expression.Eq("pod.UnitOfMeasure", _unitOfMeasure))
					.Add(Expression.Eq("ReceiptHeader", _receiptHeader))
					.Add(Expression.Or(Expression.Eq("StatusCode", active), Expression.Eq("StatusCode", open)))
					.SetProjection(Projections.Property("pod.Comments"))
					.SetResultTransformer(Transformers.AliasToBean<ReceiptDetail>());
				IList<ReceiptDetail> details = Repositories.Get<ReceiptDetail>().List(criteria);
				this.DetailComments = details.Select(e => e.Comments).ToList();
			}
			/* Default quantity. */
			this.Default = BusinessRule.RetrieveString("6252");
			/* Default full pallet value. */
			Boolean? full = BusinessRule.RetrieveBoolean("1041");
			this.FullLpn = full.HasValue ? full.Value : false;
			//
			DetachedCriteria critera = DetachedCriteria.For<InventoryItem>()
				.CreateAlias("ReceiptDetail", "rd")
				.Add(Expression.Eq("Item", _item))
				.Add(Expression.Eq("rd.ReceiptHeader", _receiptHeader))
				.Add(Expression.Eq("rd.UnitOfMeasure", _unitOfMeasure))
				.Add(Expression.Eq("StatusCode", _stockStatusCode))
				.SetProjection(Projections.Sum("Quantity"));
			this.ReceivedQuantity = Repositories.Get<InventoryItem>().Function<Decimal>(critera);
			//
			this.UdfItemValues = _item.FindUdfItemValues();
		}

		public virtual void Receive()
		{
			DetachedCriteria criteria = null;
			//
			Boolean match = _item.UnitOfMeasure.Equals(_unitOfMeasure);
			Decimal received = match ? (_quantity * _uomQuantity).Value : _quantity;
			//
			StatusCode active = Entity.Retrieve<StatusCode>(OrderStatuses.Active);
			StatusCode open = Entity.Retrieve<StatusCode>(OrderStatuses.Open);
			//
			IList<UdfMetadataValue> metadata = null;
			if (this.UdfItemValues != null && this.UdfItemValues.Count > 0)
			{
				String[] labels = this.UdfItemValues.Select(e => e.Label).ToArray();
				criteria = DetachedCriteria.For<UdfMetadataValue>()
					.Add(Expression.Eq("Active", "A"))
					.Add(Expression.In("Label", labels))
					.Add(Expression.Eq("TableName", "ITEMS"));
				metadata = Repositories.Get<UdfMetadataValue>().List(criteria);
			}
			//
			if (_licensePlate != null)
			{
				if (!_licensePlate.Id.HasValue)
				{
					_licensePlate.CompanyLocationType = Registry.Find<CompanyLocationType>();
					_licensePlate.LicenseType = Entity.Retrieve<LicenseType>(InventoryLicenseTypes.Pallet);
					Repositories.Get<LicensePlate>().Add(_licensePlate);
				}
				//else _licensePlate = Repositories.Get<LicensePlate>().Retrieve(_licensePlate.Id);
				_licensePlate.ChangeLocation(_location);
				if (this.FullLpn) _licensePlate.ChangeStatus(Entity.Retrieve<StatusCode>(LicensePlateStatuses.Complete));
				else _licensePlate.ChangeStatus(Entity.Retrieve<StatusCode>(LicensePlateStatuses.Open));
			}
			//
			criteria = DetachedCriteria.For<ReceiptDetail>()
				.CreateAlias("PurchaseOrderDetail", "pod")
					.CreateAlias("pod.PurchaseOrderHeader", "poh")
				.Add(Expression.Eq("Item", _item))
				.Add(Expression.Eq("pod.UnitOfMeasure", _unitOfMeasure))
				.Add(Expression.Eq("ReceiptHeader", _receiptHeader))
				.Add(Expression.Or(Expression.Eq("StatusCode", active), Expression.Eq("StatusCode", open)))
				.AddOrder(Order.Asc("poh.Requested"))
				.AddOrder(Order.Asc("pod.LineNumber"));
			IList<ReceiptDetail> details = Repositories.Get<ReceiptDetail>().List(criteria);
            //
            Decimal? openQuantity = details.Sum(c => c.ExpectedQuantity);
            if (openQuantity.HasValue && received > openQuantity)
            {
                if (this.Overages == 1 || this.Overages == 4)
                {
                    String message = String.Format("Cannot receive more than the expected quantity {0}. seems to be somebody received the same item", openQuantity);
                    throw new Exception(message);
                }
            }
			//
			Decimal remaining = received;
			for (int i = 0; i < details.Count; i++)
			{
				if (remaining <= 0) break;
				//
				if (remaining < details[i].ExpectedQuantity.Value)
				{
					details[i].LicensePlate = _licensePlate;
					details[i].Location = _location;
					details[i].LotExpiration = this.LotExpiration;
					details[i].LotNumber = _lotNumber;
					details[i].StockStatusCode = _stockStatusCode;
					details[i].UdfItemValues = this.UdfItemValues;
					details[i].UnitOfMeasure = _unitOfMeasure;
					details[i].UomQuantity = _uomQuantity;
					details[i].Receive(remaining, match, metadata);
					//
					break;
				}
				else
				{
					Decimal portion = (i == details.Count - 1) ? remaining : details[i].ExpectedQuantity.Value;
					//
					details[i].LicensePlate = _licensePlate;
					details[i].Location = _location;
					details[i].LotExpiration = this.LotExpiration;
					details[i].LotNumber = _lotNumber;
					details[i].StockStatusCode = _stockStatusCode;
					details[i].UdfItemValues = this.UdfItemValues;
					details[i].UnitOfMeasure = _unitOfMeasure;
					details[i].UomQuantity = _uomQuantity;
					details[i].Receive(portion, match, metadata);
					//
					remaining -= portion;
				}
			}
			/* Close PO|RT manually? */
			Boolean? manual = BusinessRule.RetrieveBoolean("1012");
			if (received >= this.RequiredQuantity && (!manual.HasValue || manual.Value))
			{
				ReceiptDetail created = Entity.Clone<ReceiptDetail>(details[0]);
				created.ExpectedQuantity = created.Quantity = 0;
				created.LicensePlate = null;
				created.Location = null;
				created.LotNumber = null;
				created.SerialNumber = null;
				created.StatusCode = active;
				created.StockStatusCode = null;
				Repositories.Get<ReceiptDetail>().Add(created);
			}
			_receiptHeader = Repositories.Get<ReceiptHeader>().Retrieve(_receiptHeader.Id);
			_receiptHeader.UpdateStatus();
			_stockStatusCode.LicensePlate = null;
			//
			this.ClosePo(manual);
		}

		#endregion

		#region Methods.Public.Undo

		public virtual void FindReceivedQuantity()
		{
			this.Restore();
			//
			ProjectionList projections = Projections.ProjectionList()
				.Add(Projections.GroupProperty("uom.UomCode"), "UomCode")
				.Add(Projections.Sum("Quantity"), "Quantity");
			DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
				.CreateAlias("LicensePlate", "lp")
				.CreateAlias("ReceiptDetail", "rd")
					.CreateAlias("rd.UnitOfMeasure", "ruom")
				.CreateAlias("UnitOfMeasure", "uom")
				.Add(Expression.Eq("Item", _item))
				.Add(Expression.Gt("Quantity", 0M))
				.Add(Expression.Eq("rd.ReceiptHeader", _receiptHeader))
				.Add(Expression.Eq("ruom.UomCode", this.UomCode))
				.Add(Expression.Eq("StatusCode", _stockStatusCode))
				.SetMaxResults(1)
				.SetProjection(projections)
				.SetResultTransformer(Transformers.AliasToBean<InventoryItem>());
			if (_licensePlate != null) criteria = criteria.Add(Expression.Eq("lp.ParentLicensePlate", _licensePlate));
			InventoryItem inventory = Repositories.Get<InventoryItem>().Retrieve(criteria);
			//
            if (inventory == null) return;
            //
			this.ReceivedQuantity = inventory.Quantity;
			if (!inventory.UomCode.Equals(this.UomCode)) this.ReceivedQuantity /= _uomQuantity.Value;
		}

		public virtual void Undo(decimal quantity)
		{
			this.Restore();
			//
			StatusCode active = Entity.Retrieve<StatusCode>(OrderStatuses.Active);
			StatusCode open = Entity.Retrieve<StatusCode>(OrderStatuses.Open);
			StatusCode received = Entity.Retrieve<StatusCode>(OrderStatuses.Received);
			StatusCode unavailable = Entity.Retrieve<StatusCode>(InventoryStatuses.Unavailable);
			//
			DetachedCriteria criteria = DetachedCriteria.For<ReceiptDetail>()
				.Add(Expression.Eq("Item", _item))
				.Add(Expression.Eq("ReceiptHeader", _receiptHeader))
				.Add(Expression.Or(Expression.Eq("StatusCode", active), Expression.Eq("StatusCode", open)))
				.SetMaxResults(1);
			ReceiptDetail remaining = Repositories.Get<ReceiptDetail>().Retrieve(criteria);
			if (remaining != null && _expectedQuantity.HasValue)
			{
				criteria = DetachedCriteria.For<ReceiptDetail>()
					.CreateAlias("UnitOfMeasure", "uom")
					.Add(Expression.Eq("Item", _item))
					.Add(Expression.Eq("ReceiptHeader", _receiptHeader))
					.Add(Expression.Eq("StatusCode", received))
					.Add(Expression.Eq("uom.UomCode", this.UomCode))
					.SetProjection(Projections.Sum("Quantity"));
				Decimal done = Repositories.Get<ReceiptDetail>().Function<Decimal>(criteria);
				Decimal expected = _expectedQuantity.Value;
				Decimal overage = (done > expected) ? (done - expected) : 0;
				Decimal increment = (quantity > overage) ? quantity - overage : 0;
				//
				remaining.DateModified = DateTime.Now;
				remaining.ExpectedQuantity += increment;
				remaining.UserModified = Registry.Find<UserAccount>().UserName;
				Repositories.Get<ReceiptDetail>().Update(remaining);
			}
			//
			ReceiptDetail created = Entity.Activate<ReceiptDetail>();
			created.ExpectedQuantity = created.Quantity = (quantity * -1);
			created.Item = _item;
			created.ItemCode = _item.ItemCode;
			created.ItemDescription = _item.Description;
			created.LicensePlate = _licensePlate;
			created.PurchaseOrderDetail = _purchaseOrderDetail;
			created.ReceiptHeader = _receiptHeader;
			created.StatusCode = Entity.Retrieve<StatusCode>(OrderStatuses.Received);
			created.StockStatusCode = _stockStatusCode;
			created.UnitOfMeasure = _unitOfMeasure;
			created.UomQuantity = _uomQuantity;
			Repositories.Get<ReceiptDetail>().Add(created);
			//
			criteria = DetachedCriteria.For<InventoryItem>()
				.CreateAlias("LicensePlate", "lp")
				.CreateAlias("ReceiptDetail", "rd")
					.CreateAlias("rd.UnitOfMeasure", "uom")
				.Add(Expression.Eq("Item", _item))
				.Add(Expression.Gt("Quantity", 0M))
				.Add(Expression.Eq("rd.ReceiptHeader", _receiptHeader))
				.Add(Expression.Eq("StatusCode", _stockStatusCode))
				.Add(Expression.Eq("uom.UomCode", this.UomCode))
				.AddOrder(Order.Desc("Received"));
			if (_licensePlate != null) criteria = criteria.Add(Expression.Eq("lp.ParentLicensePlate", _licensePlate));
			IList<InventoryItem> inventory = Repositories.Get<InventoryItem>().List(criteria);
            if (inventory == null || inventory.Count <= 0)
            {
                String error = String.Format("Inventory no longer located at the receiving location. Receipt cannot be undone.");
                throw new Exception(error);
            }
            //
			UnitOfMeasure uom = inventory[0].UnitOfMeasure;
			//
			Decimal undone = uom.UomCode.Equals(this.UomCode) ? quantity : (quantity * _uomQuantity).Value;
			foreach (InventoryItem element in inventory)
			{
				if (undone > element.Quantity)
				{
					undone -= element.Quantity;
					this.WriteUnReceived(created, element, element.Quantity);
					//
					element.DateModified = DateTime.Now;
					element.Quantity = 0;
					element.StatusCode = unavailable;
					element.UserModified = Registry.Find<UserAccount>().UserName;
					Repositories.Get<InventoryItem>().Update(element);
				}
				else if (undone == element.Quantity)
				{
					this.WriteUnReceived(created, element, element.Quantity);
					//
					element.DateModified = DateTime.Now;
					element.Quantity = 0;
					element.StatusCode = unavailable;
					element.UserModified = Registry.Find<UserAccount>().UserName;
					Repositories.Get<InventoryItem>().Update(element);
					//
					break;
				}
				else
				{
					this.WriteUnReceived(created, element, undone);
					//
					element.DateModified = DateTime.Now;
					element.Quantity -= undone;
					element.UserModified = Registry.Find<UserAccount>().UserName;
					Repositories.Get<InventoryItem>().Update(element);
					//
					break;
				}
			}
		}

		#endregion

		#region Methods.Public.Transactions

		public virtual void WriteReceived(InventoryItemDetail detail)
		{
			TransactionType type = Entity.Retrieve<TransactionType>(OrderTransactions.Received);
			ItemTransaction transaction = Entity.Activate<ItemTransaction>();
			transaction.InventoryItem = detail.InventoryItem;
			transaction.Item = detail.InventoryItem.Item;
			transaction.LocationTo = detail.InventoryItem.Location;
			transaction.LotNumber = detail.InventoryItem.LotNumber;
			transaction.Occurred = DateTime.Now;
			transaction.ParticipantBy = Registry.Find<UserAccount>().OrganizationParticipant;
			transaction.Quantity = 1;
			transaction.ReceiptDetail = detail.InventoryItem.ReceiptDetail;
			transaction.SerialNumber = detail.SerialNumber;
			transaction.StatusCode = detail.StatusCode;
			transaction.StatusReasonCode = detail.StatusCode.StatusReasonCode;
			transaction.TransactionType = type;
			if (_purchaseOrderDetail != null) transaction.UnitOfMeasure = _purchaseOrderDetail.UnitOfMeasure;
			else transaction.UnitOfMeasure = detail.InventoryItem.UnitOfMeasure;
			//
			Repositories.Get<ItemTransaction>().Add(transaction);
		}

		public virtual void WriteUnReceived(ReceiptDetail detail, InventoryItem item, decimal quantity)
		{
			TransactionType type = Entity.Retrieve<TransactionType>(OrderTransactions.Received);
			ItemTransaction transaction = Entity.Activate<ItemTransaction>();
			transaction.InventoryItem = item;
			transaction.Item = item.Item;
			transaction.LocationTo = item.Location;
			transaction.LotNumber = item.LotNumber;
			transaction.Occurred = DateTime.Now;
			transaction.ParticipantBy = Registry.Find<UserAccount>().OrganizationParticipant;
			transaction.Quantity = Converter.ToInt32(quantity * -1);
			transaction.ReceiptDetail = detail;
			transaction.SerialNumber = item.SerialNumber;
			transaction.StatusCode = item.StatusCode;
			transaction.StatusReasonCode = item.StatusCode.StatusReasonCode;
			transaction.TransactionType = type;
			transaction.UnitOfMeasure = item.UnitOfMeasure;
			//
			Repositories.Get<ItemTransaction>().Add(transaction);
		}

		#endregion

		#region Methods.Static

		public static ReceiptDetail Create(Item item)
		{
			ReceiptDetail entity = Entity.Activate<ReceiptDetail>();
			entity.ExpectedQuantity = 0;
			entity.Item = item;
			entity.ItemCode = item.ItemCode;
			entity.ItemDescription = item.Description;
			entity.Location = item.PrimaryLocation;
			entity.LotNumber = item.LotNumber;
			entity.Quantity = item.Quantity;
			entity.StatusCode = Entity.Retrieve<StatusCode>(OrderStatuses.Received);
			entity.StockStatusCode = item.StatusCode;
			entity.UnitCost = item.UnitCost;
			entity.UnitOfMeasure = item.UnitOfMeasure;
			if (entity.UnitOfMeasure != null) entity.UomQuantity = Converter.ToNullableInt32(entity.UnitOfMeasure.Factor);
			//
			return entity;
		}

		public static ReceiptDetail Create(ReceiptHeader receipt, PurchaseOrderDetail detail)
		{
			ReceiptDetail created = Entity.Activate<ReceiptDetail>();
			created.ExpectedQuantity = detail.Quantity;
			created.Item = detail.Item;
			created.ItemCode = detail.Item.ItemCode;
			created.ItemDescription = detail.Item.Description;
			created.PurchaseOrderDetail = detail;
			created.Quantity = detail.Quantity;
			created.ReceiptHeader = receipt;
			created.UnitCost = detail.UnitCost;
			created.UnitOfMeasure = detail.UnitOfMeasure;
			created.VendorItem = detail.VendorItem;
			//
			return created;
		}

		#endregion
	}
}
