using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ReceiptDocument : Entity
	{
		#region Fields

		private DocumentType _documentType;
		private ReceiptDetail _receiptDetail;
		private ReceiptHeader _receiptHeader;
		private String _receiptDocumentName;

		#endregion

		#region Properties

		[DataMember]
		public virtual DocumentType DocumentType
		{
			get { return _documentType; }
			set { _documentType = value; }
		}

		[DataMember]
		public virtual ReceiptDetail ReceiptDetail
		{
			get { return _receiptDetail; }
			set { _receiptDetail = value; }
		}

		[DataMember]
		public virtual ReceiptHeader ReceiptHeader
		{
			get { return _receiptHeader; }
			set { _receiptHeader = value; }
		}

		[DataMember]
		public virtual String ReceiptDocumentName
		{
			get { return _receiptDocumentName; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ReceiptDocumentName must not be blank or null.");
				else _receiptDocumentName = value;
			}
		}


		#endregion
	}
}
