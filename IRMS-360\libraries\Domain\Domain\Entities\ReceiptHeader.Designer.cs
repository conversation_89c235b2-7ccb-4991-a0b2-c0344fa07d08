using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ReceiptHeader : Entity
	{
		#region Fields

		private AgencyLocation _agencyLocation;
		private AgencyLocationType _agencyLocationType;
		private Carrier _carrier;
		private CompanyLocation _companyLocation;
		private CompanyLocationType _companyLocationType;
		private DateTime? _delivered;
		private Decimal? _shippingCost;
		private Int32? _totalCartons;
		private Decimal? _totalWeight;
		private ICollection<InventoryTask> _inventoryTasks = new HashSet<InventoryTask>();
		private ICollection<ReceiptDetail> _receiptDetails = new HashSet<ReceiptDetail>();
		private ICollection<ReceiptReferenceCode> _receiptReferenceCodes = new HashSet<ReceiptReferenceCode>();
		private OrderHeader _orderHeader;
		private OrganizationParticipant _receivedBy;
		private PurchaseOrderHeader _purchaseOrderHeader;
		private ReceiptType _receiptType;
		private StatusCode _statusCode;
		private String _carrierTrackingNumber;
		private String _comments;
		private String _customerReturn;
		private String _customsHold;
		private String _customsHoldComments;
		private String _receiptCode;
		private String _truckCode;
		private String _vendorInvoiceNumber;
		private String _vendorPackingSlip;
		private UnitOfMeasure _unitOfMeasure;

		#endregion

		#region Properties

		[DataMember]
		public virtual AgencyLocation AgencyLocation
		{
			get { return _agencyLocation; }
			set { _agencyLocation = value; }
		}

		[DataMember]
		public virtual AgencyLocationType AgencyLocationType
		{
			get { return _agencyLocationType; }
			set { _agencyLocationType = value; }
		}

		[DataMember]
		public virtual Carrier Carrier
		{
			get { return _carrier; }
			set { _carrier = value; }
		}

		[DataMember]
		public virtual CompanyLocation CompanyLocation
		{
			get { return _companyLocation; }
			set { _companyLocation = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual DateTime? Delivered
		{
			get { return _delivered; }
			set { _delivered = value; }
		}

		[DataMember]
		public virtual Decimal? ShippingCost
		{
			get { return _shippingCost; }
			set { _shippingCost = value; }
		}

		[DataMember]
		public virtual Int32? TotalCartons
		{
			get { return _totalCartons; }
			set { _totalCartons = value; }
		}

		[DataMember]
		public virtual Decimal? TotalWeight
		{
			get { return _totalWeight; }
			set { _totalWeight = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryTask> InventoryTasks
		{
			get { return _inventoryTasks; }
			set { _inventoryTasks = value; }
		}

		[DataMember]
		public virtual ICollection<ReceiptDetail> ReceiptDetails
		{
			get { return _receiptDetails; }
			set { _receiptDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ReceiptReferenceCode> ReceiptReferenceCodes
		{
			get { return _receiptReferenceCodes; }
			set { _receiptReferenceCodes = value; }
		}

		[DataMember]
		public virtual OrderHeader OrderHeader
		{
			get { return _orderHeader; }
			set { _orderHeader = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant ReceivedBy
		{
			get { return _receivedBy; }
			set { _receivedBy = value; }
		}

		[DataMember]
		public virtual PurchaseOrderHeader PurchaseOrderHeader
		{
			get { return _purchaseOrderHeader; }
			set { _purchaseOrderHeader = value; }
		}

		[DataMember]
		public virtual ReceiptType ReceiptType
		{
			get { return _receiptType; }
			set { _receiptType = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual String CarrierTrackingNumber
		{
			get { return _carrierTrackingNumber; }
			set { _carrierTrackingNumber = value; }
		}

		[DataMember]
		public virtual String Comments
		{
			get { return _comments; }
			set { _comments = value; }
		}

		[DataMember]
		public virtual String CustomerReturn
		{
			get { return _customerReturn; }
			set { _customerReturn = value; }
		}

		[DataMember]
		public virtual String CustomsHold
		{
			get { return _customsHold; }
			set { _customsHold = value; }
		}

		[DataMember]
		public virtual String CustomsHoldComments
		{
			get { return _customsHoldComments; }
			set { _customsHoldComments = value; }
		}

		[DataMember]
		public virtual String ReceiptCode
		{
			get { return _receiptCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ReceiptCode must not be blank or null.");
				else _receiptCode = value;
			}
		}

		[DataMember]
		public virtual String TruckCode
		{
			get { return _truckCode; }
			set { _truckCode = value; }
		}

		[DataMember]
		public virtual String VendorInvoiceNumber
		{
			get { return _vendorInvoiceNumber; }
			set { _vendorInvoiceNumber = value; }
		}

		[DataMember]
		public virtual String VendorPackingSlip
		{
			get { return _vendorPackingSlip; }
			set { _vendorPackingSlip = value; }
		}

		[DataMember]
		public virtual UnitOfMeasure UnitOfMeasure
		{
			get { return _unitOfMeasure; }
			set { _unitOfMeasure = value; }
		}


		#endregion
	}
}
