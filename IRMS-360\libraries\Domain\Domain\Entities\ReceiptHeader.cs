using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;

using NHibernate.Criterion;
using NHibernate.SqlCommand;
using NHibernate.Transform;

using Upp.Irms.Constants;
using Upp.Irms.Core;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class ReceiptHeader : Entity
	{
		#region Properties

		[DataMember]
		public virtual Int32 TransactionCount { get; set; }
		[DataMember]
		public virtual Int32? CloseQuestion { get; set; }
		[DataMember]
		public virtual String CarrierCode { get; set; }
		[DataMember]
		public virtual String Status { get; set; }

		#endregion

		#region Constructor

		public ReceiptHeader()
		{
			//
		}

		#endregion

		#region Methods.Public

		public virtual void ChangeStatus(StatusCode status)
		{
			_dateModified = DateTime.Now;
			_statusCode = status;
			_userModified = Registry.Find<UserAccount>().UserName;
			Repositories.Get<ReceiptHeader>().Update(this);
		}

		public virtual StatusCode FindDetailStatus()
		{
			DetachedCriteria criteria = DetachedCriteria.For<ReceiptDetail>()
				.Add(Expression.Eq("ReceiptHeader", this))
				.SetProjection(Projections.Count("Id"));
			Int32 details = Repositories.Get<ReceiptDetail>().Function<Int32>(criteria);
			if (details == 0)
			{
				String error = String.Format("Receipt {0} has no receipt details.", _receiptCode);
				throw new Exception(error);
			}
			//
			ProjectionList projections = Projections.ProjectionList()
				.Add(Projections.Count("Id"), "Count")
				.Add(Projections.GroupProperty("sc.Code"), "Status");
			criteria = DetachedCriteria.For<ReceiptDetail>()
				.CreateAlias("StatusCode", "sc")
				.Add(Expression.Eq("ReceiptHeader", this))
				.SetProjection(projections)
				.SetResultTransformer(Transformers.AliasToBean<ReceiptDetail>());
			IList<ReceiptDetail> distribution = Repositories.Get<ReceiptDetail>().List(criteria);
			//
			OrderStatuses[] statuses = new OrderStatuses[]
			{
				OrderStatuses.Open,
				OrderStatuses.Received,
			};
			foreach (OrderStatuses element in statuses)
			{
				ReceiptDetail match = distribution
					.Where(e => e.Status.Equals(CodeValue.GetCode(element)))
					.FirstOrDefault();
				if (match != null && match.Count == details) return Entity.Retrieve<StatusCode>(element);
			}
			//
			return Entity.Retrieve<StatusCode>(OrderStatuses.Active);
		}

		public virtual void UpdateStatus()
		{
			StatusCode detail = this.FindDetailStatus();
			if (!_statusCode.Equals(detail)) this.ChangeStatus(detail);
		}

		public virtual void ValidateActive()
		{
			StatusCode active = Entity.Retrieve<StatusCode>(OrderStatuses.Active);
			StatusCode open = Entity.Retrieve<StatusCode>(OrderStatuses.Active);
			if (!active.SameAs(_statusCode) && !open.SameAs(_statusCode))
			{
				String error = String.Format("PO {0} status is {1}.", _receiptCode, _statusCode.Description);
				throw new Exception(error);
			}
		}

		#endregion

		#region Methods.Public.SendClose

		public virtual void CountTransactions()
		{
			/* Prompt user to close the PO? */
			this.CloseQuestion = BusinessRule.RetrieveInteger("1011");
			/* Use PO|RT send|close screen? */
			Boolean? send = BusinessRule.RetrieveBoolean("1012");
			if (!send.HasValue || !send.Value) return;
            //
            StatusCode open = Entity.Retrieve<StatusCode>(IntegrationStatuses.Open);
            TransactionType received = Entity.Retrieve<TransactionType>(OrderTransactions.Received);
			DetachedCriteria criteria = DetachedCriteria.For<ItemTransaction>()
				.CreateAlias("ReceiptDetail", "rd")
				.Add(Expression.Eq("rd.ReceiptHeader", this))
				.Add(Expression.Eq("TransactionType", received))
				.Add(Expression.Eq("IntegrationStatusCode", open))
                .Add(Expression.IsNull("Task"))
                .SetProjection(Projections.Count("Id"));
			this.TransactionCount = Repositories.Get<ItemTransaction>().Function<Int32>(criteria);
		}

		public virtual void Send(bool close)
		{
			TransactionType send = Entity.Retrieve<TransactionType>(InventoryTransactions.ReceiptSend);
			Task created = Task.Create(send);
			Repositories.Get<Task>().Add(created);
			//
			StatusCode open = Entity.Retrieve<StatusCode>(IntegrationStatuses.Open);
			TransactionType received = Entity.Retrieve<TransactionType>(OrderTransactions.Received);
			DetachedCriteria criteria = DetachedCriteria.For<ItemTransaction>()
				.CreateAlias("ReceiptDetail", "rd")
				.Add(Expression.Eq("rd.ReceiptHeader", this))
				.Add(Expression.Eq("TransactionType", received))
				.Add(Expression.Eq("IntegrationStatusCode", open))
                .Add(Expression.IsNull("Task"));
			IList<ItemTransaction> transactions = Repositories.Get<ItemTransaction>().List(criteria);
			foreach (ItemTransaction element in transactions)
			{
				element.DateModified = DateTime.Now;
				element.IntegrationStatusCode = open;
				element.Task = created;
				element.UserModified = Registry.Find<UserAccount>().UserName;
				Repositories.Get<ItemTransaction>().Update(element);
			}
			//
			if (!close) return;
			//
			StatusCode oreceived = Entity.Retrieve<StatusCode>(OrderStatuses.Received);
			criteria = DetachedCriteria.For<ReceiptDetail>()
				.Add(Expression.Eq("ReceiptHeader", this))
				.Add(Expression.Not(Expression.Eq("StatusCode", oreceived)));
			IList<ReceiptDetail> details = Repositories.Get<ReceiptDetail>().List(criteria);
			foreach (ReceiptDetail element in details) element.ChangeStatus(oreceived);
			//
			this.ChangeStatus(oreceived);
		}

		#endregion

		#region Methods.Static

		public static ReceiptHeader Create(Item item)
		{
			ReceiptHeader entity = Entity.Activate<ReceiptHeader>();
			if (item.AgencyOrganizationalUnit != null) entity.AgencyLocation = item.AgencyOrganizationalUnit.AgencyLocation;
			entity.CompanyLocationType = item.CompanyLocationType;
			entity.Delivered = DateTime.Now;
			entity.ReceiptCode = item.ReceiptCode;
			entity.ReceiptType = Entity.Retrieve<ReceiptType>(ReceiptTypes.UnplannedBulk);
			entity.ReceivedBy = Registry.Find<UserAccount>().OrganizationParticipant;
			entity.StatusCode = Entity.Retrieve<StatusCode>(OrderStatuses.Received);
			//
			return entity;
		}

		public static ReceiptHeader Create(PurchaseOrderHeader header)
		{
			AgencyOrganizationalUnit unit = header.AgencyOrganizationalUnit;
			CarrierService service = header.CarrierService;
			//
			ReceiptHeader entity = Entity.Activate<ReceiptHeader>();
			entity.AgencyLocation = (unit == null) ? header.AgencyLocation : unit.AgencyLocation;
			entity.Carrier = (service == null) ? header.Carrier : service.Carrier;
			entity.CompanyLocationType = header.CompanyLocationType;
			entity.Delivered = DateTime.Now;
			entity.PurchaseOrderHeader = header;
			entity.ReceiptCode = EntityCode.GetCurrentValue(EntityCodes.ReceiptHeader);
			entity.ReceiptType = Entity.Retrieve<ReceiptType>(ReceiptTypes.Po);
			entity.ReceivedBy = Registry.Find<UserAccount>().OrganizationParticipant;
			entity.StatusCode = Entity.Retrieve<StatusCode>(OrderStatuses.Open);
			//
			return entity;
		}

		#endregion
	}
}
