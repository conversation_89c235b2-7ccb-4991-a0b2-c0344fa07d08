using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ReceiptReferenceCode : Entity
	{
		#region Fields

		private ReceiptHeader _receiptHeader;
		private ReferenceCode _referenceCode;
		private String _comments;
		private String _referenceValue;

		#endregion

		#region Properties

		[DataMember]
		public virtual ReceiptHeader ReceiptHeader
		{
			get { return _receiptHeader; }
			set { _receiptHeader = value; }
		}

		[DataMember]
		public virtual ReferenceCode ReferenceCode
		{
			get { return _referenceCode; }
			set { _referenceCode = value; }
		}

		[DataMember]
		public virtual String Comments
		{
			get { return _comments; }
			set { _comments = value; }
		}

		[DataMember]
		public virtual String ReferenceValue
		{
			get { return _referenceValue; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ReferenceValue must not be blank or null.");
				else _referenceValue = value;
			}
		}


		#endregion
	}
}
