using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ReceiptReturnDetail : Entity
	{
		#region Fields

		private Decimal? _quantity;
		private ReceiptDetail _receiptDetail;
		private ReturnReason _returnReason;
		private String _comments;

		#endregion

		#region Properties

		[DataMember]
		public virtual Decimal? Quantity
		{
			get { return _quantity; }
			set { _quantity = value; }
		}

		[DataMember]
		public virtual ReceiptDetail ReceiptDetail
		{
			get { return _receiptDetail; }
			set { _receiptDetail = value; }
		}

		[DataMember]
		public virtual ReturnReason ReturnReason
		{
			get { return _returnReason; }
			set { _returnReason = value; }
		}

		[DataMember]
		public virtual String Comments
		{
			get { return _comments; }
			set { _comments = value; }
		}


		#endregion
	}
}
