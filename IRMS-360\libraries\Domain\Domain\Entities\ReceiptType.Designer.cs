using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ReceiptType : Entity
	{
		#region Fields

		private ICollection<ItemHistory> _itemHistories = new HashSet<ItemHistory>();
		private ICollection<ReceiptHeader> _receiptHeaders = new HashSet<ReceiptHeader>();
		private String _active;
		private String _description;
		private String _receiptTypeCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<ItemHistory> ItemHistories
		{
			get { return _itemHistories; }
			set { _itemHistories = value; }
		}

		[DataMember]
		public virtual ICollection<ReceiptHeader> ReceiptHeaders
		{
			get { return _receiptHeaders; }
			set { _receiptHeaders = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String ReceiptTypeCode
		{
			get { return _receiptTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ReceiptTypeCode must not be blank or null.");
				else _receiptTypeCode = value;
			}
		}


		#endregion
	}
}
