using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ReferenceCode : Entity
	{
		#region Fields

		private FunctionalAreaCode _functionalAreaCode;
		private ICollection<CustomerShipmentReference> _customerShipmentReferences = new HashSet<CustomerShipmentReference>();
		private ICollection<OrderReferenceCode> _orderReferenceCodes = new HashSet<OrderReferenceCode>();
		private ICollection<PurchaseOrderReference> _purchaseOrderReferences = new HashSet<PurchaseOrderReference>();
		private ICollection<ReceiptReferenceCode> _receiptReferenceCodes = new HashSet<ReceiptReferenceCode>();
		private ICollection<RequisitionReference> _requisitionReferences = new HashSet<RequisitionReference>();
		private ICollection<ShipmentReferenceCode> _shipmentReferenceCodes = new HashSet<ShipmentReferenceCode>();
		private String _active;
		private String _code;
		private String _description;

		#endregion

		#region Properties

		[DataMember]
		public virtual FunctionalAreaCode FunctionalAreaCode
		{
			get { return _functionalAreaCode; }
			set { _functionalAreaCode = value; }
		}

		[DataMember]
		public virtual ICollection<CustomerShipmentReference> CustomerShipmentReferences
		{
			get { return _customerShipmentReferences; }
			set { _customerShipmentReferences = value; }
		}

		[DataMember]
		public virtual ICollection<OrderReferenceCode> OrderReferenceCodes
		{
			get { return _orderReferenceCodes; }
			set { _orderReferenceCodes = value; }
		}

		[DataMember]
		public virtual ICollection<PurchaseOrderReference> PurchaseOrderReferences
		{
			get { return _purchaseOrderReferences; }
			set { _purchaseOrderReferences = value; }
		}

		[DataMember]
		public virtual ICollection<ReceiptReferenceCode> ReceiptReferenceCodes
		{
			get { return _receiptReferenceCodes; }
			set { _receiptReferenceCodes = value; }
		}

		[DataMember]
		public virtual ICollection<RequisitionReference> RequisitionReferences
		{
			get { return _requisitionReferences; }
			set { _requisitionReferences = value; }
		}

		[DataMember]
		public virtual ICollection<ShipmentReferenceCode> ShipmentReferenceCodes
		{
			get { return _shipmentReferenceCodes; }
			set { _shipmentReferenceCodes = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Code
		{
			get { return _code; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Code must not be blank or null.");
				else _code = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}


		#endregion
	}
}
