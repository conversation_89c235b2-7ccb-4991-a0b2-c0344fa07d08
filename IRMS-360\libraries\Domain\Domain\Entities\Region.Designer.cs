using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Region : Entity
	{
		#region Fields

		private Agency _agency;
		private ICollection<Participant> _participants = new HashSet<Participant>();
		private ICollection<RequestorGroupOrgParticipant> _requestorGroupOrgParticipants = new HashSet<RequestorGroupOrgParticipant>();
		private ICollection<RequisitionHeader> _requisitionHeaders = new HashSet<RequisitionHeader>();
		private String _active;
		private String _description;
		private String _regionCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual Agency Agency
		{
			get { return _agency; }
			set { _agency = value; }
		}

		[DataMember]
		public virtual ICollection<Participant> Participants
		{
			get { return _participants; }
			set { _participants = value; }
		}

		[DataMember]
		public virtual ICollection<RequestorGroupOrgParticipant> RequestorGroupOrgParticipants
		{
			get { return _requestorGroupOrgParticipants; }
			set { _requestorGroupOrgParticipants = value; }
		}

		[DataMember]
		public virtual ICollection<RequisitionHeader> RequisitionHeaders
		{
			get { return _requisitionHeaders; }
			set { _requisitionHeaders = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String RegionCode
		{
			get { return _regionCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("RegionCode must not be blank or null.");
				else _regionCode = value;
			}
		}


		#endregion
	}
}
