using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Report : Entity
	{
		#region Fields

		private ApplicationModule _applicationModule;
		private ICollection<CarrierReport> _carrierReports = new HashSet<CarrierReport>();
		private ICollection<DockPrinter> _dockPrinters = new HashSet<DockPrinter>();
		private ICollection<ReportEntity> _reportEntities = new HashSet<ReportEntity>();
		private ICollection<ReportFormat> _reportFormats = new HashSet<ReportFormat>();
		private ICollection<ReportParameter> _reportParameters = new HashSet<ReportParameter>();
		private ICollection<ReportRequestHeader> _reportRequestHeaders = new HashSet<ReportRequestHeader>();
		private ICollection<ReportSchedule> _reportSchedules = new HashSet<ReportSchedule>();
		private ReportType _reportType;
		private String _active;
        private String _bartender;
        private String _bartenderFormat;
		private String _entityName;
		private String _methodName;
		private String _retentionAllowed;

		#endregion

		#region Properties

		[DataMember]
		public virtual ApplicationModule ApplicationModule
		{
			get { return _applicationModule; }
			set { _applicationModule = value; }
		}

		[DataMember]
		public virtual ICollection<CarrierReport> CarrierReports
		{
			get { return _carrierReports; }
			set { _carrierReports = value; }
		}

		[DataMember]
		public virtual ICollection<DockPrinter> DockPrinters
		{
			get { return _dockPrinters; }
			set { _dockPrinters = value; }
		}

		[DataMember]
		public virtual ICollection<ReportEntity> ReportEntities
		{
			get { return _reportEntities; }
			set { _reportEntities = value; }
		}

		[DataMember]
		public virtual ICollection<ReportFormat> ReportFormats
		{
			get { return _reportFormats; }
			set { _reportFormats = value; }
		}

		[DataMember]
		public virtual ICollection<ReportParameter> ReportParameters
		{
			get { return _reportParameters; }
			set { _reportParameters = value; }
		}

		[DataMember]
		public virtual ICollection<ReportRequestHeader> ReportRequestHeaders
		{
			get { return _reportRequestHeaders; }
			set { _reportRequestHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<ReportSchedule> ReportSchedules
		{
			get { return _reportSchedules; }
			set { _reportSchedules = value; }
		}

		[DataMember]
		public virtual ReportType ReportType
		{
			get { return _reportType; }
			set { _reportType = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

        [DataMember]
        public virtual String Bartender
        {
            get { return _bartender; }
            set { _bartender = value; }
        }

        [DataMember]
        public virtual String BartenderFormat
        {
            get { return _bartenderFormat; }
            set { _bartenderFormat = value; }
        }

		[DataMember]
		public virtual String EntityName
		{
			get { return _entityName; }
			set { _entityName = value; }
		}

		[DataMember]
		public virtual String MethodName
		{
			get { return _methodName; }
			set { _methodName = value; }
		}

		[DataMember]
		public virtual String RetentionAllowed
		{
			get { return _retentionAllowed; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("RetentionAllowed must not be blank or null.");
				else _retentionAllowed = value;
			}
		}


		#endregion
	}
}
