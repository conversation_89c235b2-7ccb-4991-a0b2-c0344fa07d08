using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class Report : Entity
	{
		#region Properties

		[DataMember]
		public virtual String ModuleCode { get; set; }
		[DataMember]
		public virtual String ModuleName { get; set; }

		#endregion

		#region Constructor

		public Report()
		{
			//
		}

		#endregion
	}
}
