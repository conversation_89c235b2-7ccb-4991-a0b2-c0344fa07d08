using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ReportEntity : Entity
	{
		#region Fields

		private Report _report;
		private String _active;
		private String _entityName;

		#endregion

		#region Properties

		[DataMember]
		public virtual Report Report
		{
			get { return _report; }
			set { _report = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String EntityName
		{
			get { return _entityName; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("EntityName must not be blank or null.");
				else _entityName = value;
			}
		}


		#endregion
	}
}
