using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ReportFormat : Entity
	{
		#region Fields

		private Customer _customer;
		private ICollection<ReportRequestHeader> _reportRequestHeaders = new HashSet<ReportRequestHeader>();
		private Report _report;
		private ReportType _reportType;
		private String _active;
		private String _fileName;
		private String _orderBy;
		private String _templateCode;
		private String _templateName;
		private String _whereClause;

		#endregion

		#region Properties

		[DataMember]
		public virtual Customer Customer
		{
			get { return _customer; }
			set { _customer = value; }
		}

		[DataMember]
		public virtual ICollection<ReportRequestHeader> ReportRequestHeaders
		{
			get { return _reportRequestHeaders; }
			set { _reportRequestHeaders = value; }
		}

		[DataMember]
		public virtual Report Report
		{
			get { return _report; }
			set { _report = value; }
		}

		[DataMember]
		public virtual ReportType ReportType
		{
			get { return _reportType; }
			set { _reportType = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String FileName
		{
			get { return _fileName; }
			set { _fileName = value; }
		}

		[DataMember]
		public virtual String OrderBy
		{
			get { return _orderBy; }
			set { _orderBy = value; }
		}

		[DataMember]
		public virtual String TemplateCode
		{
			get { return _templateCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("TemplateCode must not be blank or null.");
				else _templateCode = value;
			}
		}

		[DataMember]
		public virtual String TemplateName
		{
			get { return _templateName; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("TemplateName must not be blank or null.");
				else _templateName = value;
			}
		}

		[DataMember]
		public virtual String WhereClause
		{
			get { return _whereClause; }
			set { _whereClause = value; }
		}


		#endregion
	}
}
