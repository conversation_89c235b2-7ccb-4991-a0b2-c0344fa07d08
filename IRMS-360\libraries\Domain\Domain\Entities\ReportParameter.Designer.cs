using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ReportParameter : Entity
	{
		#region Fields

		private Int32 _sortOrder;
		private ICollection<ReportRequestDetail> _reportRequestDetails = new HashSet<ReportRequestDetail>();
		private Parameter _parameter;
		private Report _report;
		private String _active;
		private String _dataRequired;

		#endregion

		#region Properties

		[DataMember]
		public virtual Int32 SortOrder
		{
			get { return _sortOrder; }
			set { _sortOrder = value; }
		}

		[DataMember]
		public virtual ICollection<ReportRequestDetail> ReportRequestDetails
		{
			get { return _reportRequestDetails; }
			set { _reportRequestDetails = value; }
		}

		[DataMember]
		public virtual Parameter Parameter
		{
			get { return _parameter; }
			set { _parameter = value; }
		}

		[DataMember]
		public virtual Report Report
		{
			get { return _report; }
			set { _report = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String DataRequired
		{
			get { return _dataRequired; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("DataRequired must not be blank or null.");
				else _dataRequired = value;
			}
		}


		#endregion
	}
}
