using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class ReportParameter : Entity
	{
		#region Properties

		[DataMember]
		public virtual String ParameterCode { get; set; }
		[DataMember]
		public virtual String ParameterDescription { get; set; }

		#endregion

		#region Constructor

		public ReportParameter()
		{
			//
		}

		#endregion
	}
}
