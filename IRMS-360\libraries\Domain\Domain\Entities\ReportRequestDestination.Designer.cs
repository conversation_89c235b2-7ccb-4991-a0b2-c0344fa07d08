using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ReportRequestDestination : Entity
	{
		#region Fields

		private CommunicationRole _communicationRole;
		private PrinterTypePrinter _printerTypePrinter;
		private ReportRequestHeader _reportRequestHeader;
		private StatusCode _statusCode;
        private String _customEmail;
		private String _destinationResult;
		private String _destinationValue;
		private String _email;
		private String _emailBcc;
		private String _emailCc;
        private Byte[] _emailContent;
		private String _emailMessage;
		private String _emailSubject;

		#endregion

		#region Properties

		[DataMember]
		public virtual CommunicationRole CommunicationRole
		{
			get { return _communicationRole; }
			set { _communicationRole = value; }
		}

		[DataMember]
		public virtual PrinterTypePrinter PrinterTypePrinter
		{
			get { return _printerTypePrinter; }
			set { _printerTypePrinter = value; }
		}

		[DataMember]
		public virtual ReportRequestHeader ReportRequestHeader
		{
			get { return _reportRequestHeader; }
			set { _reportRequestHeader = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

        [DataMember]
        public virtual String CustomEmail
        {
            get { return _customEmail; }
            set { _customEmail = value; }
        }

		[DataMember]
		public virtual String DestinationResult
		{
			get { return _destinationResult; }
			set { _destinationResult = value; }
		}

		[DataMember]
		public virtual String DestinationValue
		{
			get { return _destinationValue; }
			set { _destinationValue = value; }
		}
		
		[DataMember]
		public virtual String Email
		{
			get { return _email; }
			set { _email = value; }
		}

		[DataMember]
		public virtual String EmailBcc
		{
			get { return _emailBcc; }
			set { _emailBcc = value; }
		}

        [DataMember]
        public virtual Byte[] EmailContent
        {
            get { return _emailContent; }
            set { _emailContent = value; }
        }

		[DataMember]
		public virtual String EmailCc
		{
			get { return _emailCc; }
			set { _emailCc = value; }
		}

		[DataMember]
		public virtual String EmailMessage
		{
			get { return _emailMessage; }
			set { _emailMessage = value; }
		}

		[DataMember]
		public virtual String EmailSubject
		{
			get { return _emailSubject; }
			set { _emailSubject = value; }
		}

		#endregion
	}
}
