using System;
using System.Runtime.Serialization;

using Upp.Irms.Constants;
using Upp.Irms.Core;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class ReportRequestDestination : Entity
	{
		#region Properties

		[DataMember]
		public virtual String CommunicationRoleDescription { get; set; }
		[DataMember]
		public virtual String PrinterCode { get; set; }
		[DataMember]
		public virtual String QueueName { get; set; }

		#endregion

		#region Constructor

		public ReportRequestDestination()
		{
			//
		}

		#endregion

		#region Methods.Private

		private void ChangeStatus(ReportStatuses status)
		{
			_dateModified = DateTime.Now;
			if (status != ReportStatuses.Error) _destinationResult = null;
			_statusCode = Entity.Retrieve<StatusCode>(status);
			_userModified = Registry.Find<UserAccount>().UserName;
			//
			Repositories.Get<ReportRequestDestination>().Update(this);
		}

		#endregion

		#region Methods.Public

		public virtual void Activate()
		{
			this.ChangeStatus(ReportStatuses.Active);
		}

		public virtual void Complete()
		{
			this.ChangeStatus(ReportStatuses.Complete);
		}

		public virtual void Fail(string error)
		{
			_destinationResult = error;
			this.ChangeStatus(ReportStatuses.Error);
		}

		public virtual void Fix()
		{
			if (_printerTypePrinter != null)
			{
				_communicationRole = Entity.Retrieve<CommunicationRole>(CommunicationRoles.Print);
				_destinationValue = null;
			}
			else if (!CodeValue.GetCode(CommunicationRoles.Print).Equals(_communicationRole.CommunicationRoleCode))
				_printerTypePrinter = null;
		}

		#endregion
	}
}
