using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ReportRequestDetail : Entity
	{
		#region Fields

		private ParameterValue _parameterValue;
		private ReportParameter _reportParameter;
		private ReportRequestHeader _reportRequestHeader;
		private String _operand;
		private String _value;

		#endregion

		#region Properties

		[DataMember]
		public virtual ParameterValue ParameterValue
		{
			get { return _parameterValue; }
			set { _parameterValue = value; }
		}

		[DataMember]
		public virtual ReportParameter ReportParameter
		{
			get { return _reportParameter; }
			set { _reportParameter = value; }
		}

		[DataMember]
		public virtual ReportRequestHeader ReportRequestHeader
		{
			get { return _reportRequestHeader; }
			set { _reportRequestHeader = value; }
		}

		[DataMember]
		public virtual String Operand
		{
			get { return _operand; }
			set { _operand = value; }
		}

		[DataMember]
		public virtual String Value
		{
			get { return _value; }
			set { _value = value; }
		}


		#endregion
	}
}
