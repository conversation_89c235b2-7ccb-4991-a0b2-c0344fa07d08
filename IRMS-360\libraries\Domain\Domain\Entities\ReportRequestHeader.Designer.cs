using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ReportRequestHeader : Entity
	{
		#region Fields

		private Byte[] _reportOutput;
		private DateTime _requested;
		private DateTime? _completed;
		private DateTime? _started;
		private Int32? _copies;
		private ICollection<ReportRequestDestination> _reportRequestDestinations = new HashSet<ReportRequestDestination>();
		private ICollection<ReportRequestDetail> _reportRequestDetails = new HashSet<ReportRequestDetail>();
		private ICollection<Task> _tasks = new HashSet<Task>();
		private Report _report;
		private ReportFormat _reportFormat;
		private ReportSchedule _reportSchedule;
		private String _dynamicDates;
		private String _exportFormat;
		private UserAccount _userAccount;

		#endregion

		#region Properties

		[DataMember]
		public virtual Byte[] ReportOutput
		{
			get { return _reportOutput; }
			set { _reportOutput = value; }
		}

		[DataMember]
		public virtual DateTime Requested
		{
			get { return _requested; }
			set { _requested = value; }
		}

		[DataMember]
		public virtual DateTime? Completed
		{
			get { return _completed; }
			set { _completed = value; }
		}

		[DataMember]
		public virtual DateTime? Started
		{
			get { return _started; }
			set { _started = value; }
		}

		[DataMember]
		public virtual Int32? Copies
		{
			get { return _copies; }
			set { _copies = value; }
		}

		[DataMember]
		public virtual ICollection<ReportRequestDestination> ReportRequestDestinations
		{
			get { return _reportRequestDestinations; }
			set { _reportRequestDestinations = value; }
		}

		[DataMember]
		public virtual ICollection<ReportRequestDetail> ReportRequestDetails
		{
			get { return _reportRequestDetails; }
			set { _reportRequestDetails = value; }
		}

		[DataMember]
		public virtual ICollection<Task> Tasks
		{
			get { return _tasks; }
			set { _tasks = value; }
		}

		[DataMember]
		public virtual Report Report
		{
			get { return _report; }
			set { _report = value; }
		}

		[DataMember]
		public virtual ReportFormat ReportFormat
		{
			get { return _reportFormat; }
			set { _reportFormat = value; }
		}

		[DataMember]
		public virtual ReportSchedule ReportSchedule
		{
			get { return _reportSchedule; }
			set { _reportSchedule = value; }
		}
		
		[DataMember]
		public virtual String DynamicDates
		{
			get { return _dynamicDates; }
			set { _dynamicDates = value; }
		}

		[DataMember]
		public virtual String ExportFormat
		{
			get { return _exportFormat; }
			set { _exportFormat = value; }
		}

		[DataMember]
		public virtual UserAccount UserAccount
		{
			get { return _userAccount; }
			set { _userAccount = value; }
		}


		#endregion
	}
}
