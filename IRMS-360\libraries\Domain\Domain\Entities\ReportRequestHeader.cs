using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using NHibernate.Criterion;

using Upp.Irms.Constants;
using Upp.Irms.Core;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class ReportRequestHeader : Entity
	{
		#region Properties

		[DataMember]
		public virtual PrinterTypePrinter PrinterTypePrinter { get; set; }
		[DataMember]
		public virtual String ModuleCode { get; set; }
		[DataMember]
		public virtual String ModuleName { get; set; }
        [DataMember]
        public virtual String FontUrl { get; set; }

		#endregion

		#region Constructor

		public ReportRequestHeader()
		{
			//
		}

		#endregion

		#region Methods.Public

		public virtual void Complete()
		{
			StatusCode complete = Entity.Retrieve<StatusCode>(ReportStatuses.Complete);
			DetachedCriteria count = DetachedCriteria.For<ReportRequestDestination>()
				.Add("ReportRequestHeader", this)
				.Add("StatusCode", "!=", complete);
			if (Repositories.Get<ReportRequestDestination>().Count(count) != 0) return;
			//
			_completed = DateTime.Now;
			_dateModified = DateTime.Now;
			_userModified = Registry.Find<UserAccount>().UserName;
			//
			Repositories.Get<ReportRequestHeader>().Update(this);
		}

		public virtual void Start()
		{
			_completed = null;
			_dateModified = DateTime.Now;
			_started = DateTime.Now;
			_userModified = Registry.Find<UserAccount>().UserName;
			//
			Repositories.Get<ReportRequestHeader>().Update(this);
		}

		#endregion

		#region Methods.Static

		public static ReportRequestHeader Create(string reportCode, Dictionary<String, String> parameters, 
			PrinterTypePrinter printer, int? copies/*, string output*/)
		{
			DetachedCriteria criteria = DetachedCriteria.For<Report>()
				.Add("ApplicationModule.Active", "A")
				.Add("ApplicationModule.Module.ModuleCode", reportCode);
			Report report = Repositories.Get<Report>().Retrieve(criteria);
			if (report == null) throw new Exception(String.Format("Unable to find Report for {0}.", reportCode));
			//
			ReportRequestHeader entity = Entity.Activate<ReportRequestHeader>();
			entity.Copies = copies;
			entity.PrinterTypePrinter = printer;
			entity.Report = report;
			entity.Requested = DateTime.Now;
			Repositories.Get<ReportRequestHeader>().Add(entity);
			//
			foreach (String key in parameters.Keys)
			{
				criteria = DetachedCriteria.For<ReportParameter>()
					.Add("Parameter.Description", key)
					.Add("Report", entity.Report)
					.SetMaxResults(1);
				IList<ReportParameter> matching = Repositories.Get<ReportParameter>().List(criteria);
				if (matching.Count != 1) continue;
				//
				ReportRequestDetail detail = Entity.Activate<ReportRequestDetail>();
				detail.Operand = "=";
				detail.ReportParameter = matching[0];
				detail.ReportRequestHeader = entity;
				detail.Value = parameters[key];
				//
				Repositories.Get<ReportRequestDetail>().Add(detail);
			}
			//
			ReportRequestDestination destination = Entity.Activate<ReportRequestDestination>();
			if (printer != null)
			{
				destination.CommunicationRole = Entity.Retrieve<CommunicationRole>(CommunicationRoles.Print);
				destination.PrinterTypePrinter = printer;
				destination.ReportRequestHeader = entity;
				destination.StatusCode = Entity.Retrieve<StatusCode>(ReportStatuses.Open);
				//
				Repositories.Get<ReportRequestDestination>().Add(destination);
			}
			else
			{
				// TODO: email destination
			}
			//
			return entity;
		}

		#endregion
	}
}
