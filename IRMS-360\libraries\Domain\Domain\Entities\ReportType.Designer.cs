using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ReportType : Entity
	{
		#region Fields

		private ICollection<Report> _reports = new HashSet<Report>();
		private ICollection<ReportFormat> _reportFormats = new HashSet<ReportFormat>();
		private String _active;
		private String _description;
		private String _reportTypeCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<Report> Reports
		{
			get { return _reports; }
			set { _reports = value; }
		}

		[DataMember]
		public virtual ICollection<ReportFormat> ReportFormats
		{
			get { return _reportFormats; }
			set { _reportFormats = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String ReportTypeCode
		{
			get { return _reportTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ReportTypeCode must not be blank or null.");
				else _reportTypeCode = value;
			}
		}


		#endregion
	}
}
