using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class RequestorGroup : Entity
	{
		#region Fields

		private ICollection<RequestorGroupOrgParticipant> _requestorGroupOrgParticipants = new HashSet<RequestorGroupOrgParticipant>();
		private String _active;
		private String _description;
		private String _requestorGroupCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<RequestorGroupOrgParticipant> RequestorGroupOrgParticipants
		{
			get { return _requestorGroupOrgParticipants; }
			set { _requestorGroupOrgParticipants = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String RequestorGroupCode
		{
			get { return _requestorGroupCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("RequestorGroupCode must not be blank or null.");
				else _requestorGroupCode = value;
			}
		}


		#endregion
	}
}
