using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class RequestorGroupOrgPartApprover : Entity
	{
		#region Fields

		private ApprovalParticipant _approvalParticipant;
		private RequestorGroupOrgParticipant _requestorGroupOrgParticipant;
		private String _active;

		#endregion

		#region Properties

		[DataMember]
		public virtual ApprovalParticipant ApprovalParticipant
		{
			get { return _approvalParticipant; }
			set { _approvalParticipant = value; }
		}

		[DataMember]
		public virtual RequestorGroupOrgParticipant RequestorGroupOrgParticipant
		{
			get { return _requestorGroupOrgParticipant; }
			set { _requestorGroupOrgParticipant = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}


		#endregion
	}
}
