using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class RequestorGroupOrgParticipant : Entity
	{
		#region Fields

		private ICollection<RequestorGroupOrgPartApprover> _requestorGroupOrgPartApprovers = new HashSet<RequestorGroupOrgPartApprover>();
		private OrganizationParticipant _organizationParticipant;
		private Region _region;
		private RequestorGroup _requestorGroup;
		private String _active;
		private String _addressLine1;
		private String _addressLine2;
		private String _addressLine3;
		private String _city;
		private String _email;
		private String _phone;
		private String _state;
		private String _zip;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<RequestorGroupOrgPartApprover> RequestorGroupOrgPartApprovers
		{
			get { return _requestorGroupOrgPartApprovers; }
			set { _requestorGroupOrgPartApprovers = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant OrganizationParticipant
		{
			get { return _organizationParticipant; }
			set { _organizationParticipant = value; }
		}

		[DataMember]
		public virtual Region Region
		{
			get { return _region; }
			set { _region = value; }
		}

		[DataMember]
		public virtual RequestorGroup RequestorGroup
		{
			get { return _requestorGroup; }
			set { _requestorGroup = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String AddressLine1
		{
			get { return _addressLine1; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("AddressLine1 must not be blank or null.");
				else _addressLine1 = value;
			}
		}

		[DataMember]
		public virtual String AddressLine2
		{
			get { return _addressLine2; }
			set { _addressLine2 = value; }
		}

		[DataMember]
		public virtual String AddressLine3
		{
			get { return _addressLine3; }
			set { _addressLine3 = value; }
		}

		[DataMember]
		public virtual String City
		{
			get { return _city; }
			set { _city = value; }
		}

		[DataMember]
		public virtual String Email
		{
			get { return _email; }
			set { _email = value; }
		}

		[DataMember]
		public virtual String Phone
		{
			get { return _phone; }
			set { _phone = value; }
		}

		[DataMember]
		public virtual String State
		{
			get { return _state; }
			set { _state = value; }
		}

		[DataMember]
		public virtual String Zip
		{
			get { return _zip; }
			set { _zip = value; }
		}


		#endregion
	}
}
