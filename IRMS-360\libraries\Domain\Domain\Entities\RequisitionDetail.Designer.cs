using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class RequisitionDetail : Entity
	{
		#region Fields

		private CompanyLocationType _approverCompanyLocationType;
		private DateTime? _required;
		private Int32 _lineNumber;
		private Decimal _quantity;
		private Decimal? _backorderedQuantity;
		private InventoryItem _inventoryItem;
		private Item _item;
		private ICollection<Alert> _alerts = new HashSet<Alert>();
		private ICollection<ItemFulfillment> _itemFulfillments = new HashSet<ItemFulfillment>();
		private ICollection<ItemTransaction> _itemTransactions = new HashSet<ItemTransaction>();
		private ICollection<PurchaseOrderDetail> _purchaseOrderDetails = new HashSet<PurchaseOrderDetail>();
		private OrganizationParticipant _approver;
		private OrganizationParticipant _contact;
		private RequisitionHeader _requisitionHeader;
		private StatusCode _statusCode;
		private String _comments;
		private String _itemDescription;
		private UnitOfMeasure _unitOfMeasure;

		#endregion

		#region Properties

		[DataMember]
		public virtual CompanyLocationType ApproverCompanyLocationType
		{
			get { return _approverCompanyLocationType; }
			set { _approverCompanyLocationType = value; }
		}

		[DataMember]
		public virtual DateTime? Required
		{
			get { return _required; }
			set { _required = value; }
		}

		[DataMember]
		public virtual Int32 LineNumber
		{
			get { return _lineNumber; }
			set { _lineNumber = value; }
		}

		[DataMember]
		public virtual Decimal Quantity
		{
			get { return _quantity; }
			set { _quantity = value; }
		}

		[DataMember]
		public virtual Decimal? BackorderedQuantity
		{
			get { return _backorderedQuantity; }
			set { _backorderedQuantity = value; }
		}

		[DataMember]
		public virtual InventoryItem InventoryItem
		{
			get { return _inventoryItem; }
			set { _inventoryItem = value; }
		}

		[DataMember]
		public virtual Item Item
		{
			get { return _item; }
			set { _item = value; }
		}

		[DataMember]
		public virtual ICollection<Alert> Alerts
		{
			get { return _alerts; }
			set { _alerts = value; }
		}

		[DataMember]
		public virtual ICollection<ItemFulfillment> ItemFulfillments
		{
			get { return _itemFulfillments; }
			set { _itemFulfillments = value; }
		}

		[DataMember]
		public virtual ICollection<ItemTransaction> ItemTransactions
		{
			get { return _itemTransactions; }
			set { _itemTransactions = value; }
		}

		[DataMember]
		public virtual ICollection<PurchaseOrderDetail> PurchaseOrderDetails
		{
			get { return _purchaseOrderDetails; }
			set { _purchaseOrderDetails = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant Approver
		{
			get { return _approver; }
			set { _approver = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant Contact
		{
			get { return _contact; }
			set { _contact = value; }
		}

		[DataMember]
		public virtual RequisitionHeader RequisitionHeader
		{
			get { return _requisitionHeader; }
			set { _requisitionHeader = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual String Comments
		{
			get { return _comments; }
			set { _comments = value; }
		}

		[DataMember]
		public virtual String ItemDescription
		{
			get { return _itemDescription; }
			set { _itemDescription = value; }
		}

		[DataMember]
		public virtual UnitOfMeasure UnitOfMeasure
		{
			get { return _unitOfMeasure; }
			set { _unitOfMeasure = value; }
		}


		#endregion
	}
}
