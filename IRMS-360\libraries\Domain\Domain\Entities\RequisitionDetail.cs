using System;
using System.Runtime.Serialization;

using Upp.Irms.Constants;
using Upp.Irms.Core;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class RequisitionDetail : Entity
	{
		#region Constructor

		public RequisitionDetail()
		{
			//
		}

		#endregion

		#region Methods.Public

		public virtual void UpdateStatus()
		{
			Entity.UpdateStatus<RequisitionDetail, ItemFulfillment>(this, FunctionalAreas.Requsition);
		}

		#endregion
	}
}
