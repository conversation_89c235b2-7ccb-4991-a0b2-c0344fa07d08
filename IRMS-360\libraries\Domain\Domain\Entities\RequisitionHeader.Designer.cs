using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class RequisitionHeader : Entity
	{
		#region Fields

		private AgencyLocation _agencyLocation;
		private AgencyOrganizationalUnit _agencyOrganizationalUnit;
		private CompanyLocation _companyLocation;
		private CompanyOrganizationalUnit _companyOrganizationalUnit;
		private Contract _contract;
		private DateTime _requested;
		private DateTime? _requiredBy;
		private DateTime? _requisitionPrinted;
		private ICollection<Alert> _alerts = new HashSet<Alert>();
		private ICollection<PurchaseOrderHeader> _purchaseOrderHeaders = new HashSet<PurchaseOrderHeader>();
		private ICollection<RequisitionDetail> _requisitionDetails = new HashSet<RequisitionDetail>();
		private ICollection<RequisitionLocation> _requisitionLocations = new HashSet<RequisitionLocation>();
		private ICollection<RequisitionReference> _requisitionReferences = new HashSet<RequisitionReference>();
		private ICollection<RequisitionStatus> _requisitionStatuses = new HashSet<RequisitionStatus>();
		private OrderType _orderType;
		private OrganizationParticipant _approver;
		private OrganizationParticipant _contact;
		private OrganizationParticipant _requestor;
		private Priority _priority;
		private String _comments;
		private String _deliveryMethod;
		private String _requisitionCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual AgencyLocation AgencyLocation
		{
			get { return _agencyLocation; }
			set { _agencyLocation = value; }
		}

		[DataMember]
		public virtual AgencyOrganizationalUnit AgencyOrganizationalUnit
		{
			get { return _agencyOrganizationalUnit; }
			set { _agencyOrganizationalUnit = value; }
		}

		[DataMember]
		public virtual CompanyLocation CompanyLocation
		{
			get { return _companyLocation; }
			set { _companyLocation = value; }
		}

		[DataMember]
		public virtual CompanyOrganizationalUnit CompanyOrganizationalUnit
		{
			get { return _companyOrganizationalUnit; }
			set { _companyOrganizationalUnit = value; }
		}

		[DataMember]
		public virtual Contract Contract
		{
			get { return _contract; }
			set { _contract = value; }
		}

		[DataMember]
		public virtual DateTime Requested
		{
			get { return _requested; }
			set { _requested = value; }
		}

		[DataMember]
		public virtual DateTime? RequiredBy
		{
			get { return _requiredBy; }
			set { _requiredBy = value; }
		}

		[DataMember]
		public virtual DateTime? RequisitionPrinted
		{
			get { return _requisitionPrinted; }
			set { _requisitionPrinted = value; }
		}

		[DataMember]
		public virtual ICollection<Alert> Alerts
		{
			get { return _alerts; }
			set { _alerts = value; }
		}

		[DataMember]
		public virtual ICollection<PurchaseOrderHeader> PurchaseOrderHeaders
		{
			get { return _purchaseOrderHeaders; }
			set { _purchaseOrderHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<RequisitionDetail> RequisitionDetails
		{
			get { return _requisitionDetails; }
			set { _requisitionDetails = value; }
		}

		[DataMember]
		public virtual ICollection<RequisitionLocation> RequisitionLocations
		{
			get { return _requisitionLocations; }
			set { _requisitionLocations = value; }
		}

		[DataMember]
		public virtual ICollection<RequisitionReference> RequisitionReferences
		{
			get { return _requisitionReferences; }
			set { _requisitionReferences = value; }
		}

		[DataMember]
		public virtual ICollection<RequisitionStatus> RequisitionStatuses
		{
			get { return _requisitionStatuses; }
			set { _requisitionStatuses = value; }
		}

		[DataMember]
		public virtual OrderType OrderType
		{
			get { return _orderType; }
			set { _orderType = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant Approver
		{
			get { return _approver; }
			set { _approver = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant Contact
		{
			get { return _contact; }
			set { _contact = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant Requestor
		{
			get { return _requestor; }
			set { _requestor = value; }
		}

		[DataMember]
		public virtual Priority Priority
		{
			get { return _priority; }
			set { _priority = value; }
		}

		[DataMember]
		public virtual String Comments
		{
			get { return _comments; }
			set { _comments = value; }
		}

		[DataMember]
		public virtual String DeliveryMethod
		{
			get { return _deliveryMethod; }
			set { _deliveryMethod = value; }
		}

		[DataMember]
		public virtual String RequisitionCode
		{
			get { return _requisitionCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("RequisitionCode must not be blank or null.");
				else _requisitionCode = value;
			}
		}


		#endregion
	}
}
