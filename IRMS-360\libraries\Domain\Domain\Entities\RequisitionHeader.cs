using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using NHibernate.Criterion;

using Upp.Irms.Constants;
using Upp.Irms.Core;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class RequisitionHeader : Entity
	{
		#region Properties

		[DataMember]
		public virtual InventoryItem InventoryItem { get; set; }
		[DataMember]
		public virtual OrganizationParticipant OrganizationParticipant { get; set; }
		[DataMember]
		public virtual StatusCode CurrentStatus { get; set; }

		#endregion

		#region Constructor

		public RequisitionHeader()
		{
			//
		}

		#endregion

		#region Methods.Private.Transactions

		private void WriteCheckOut(ItemTransaction staged)
		{
			TransactionType type = Entity.Retrieve<TransactionType>(InventoryTransactions.CheckOut);
			ItemTransaction transaction = Entity.Activate<ItemTransaction>();
			transaction.InventoryItem = staged.InventoryItem;
			transaction.Item = staged.Item;
			transaction.LocationFrom = staged.LocationTo;
			transaction.LotNumber = staged.InventoryItem.LotNumber;
			transaction.Occurred = DateTime.Now;
			if (this.OrganizationParticipant == null) transaction.ParentInventoryItem = this.InventoryItem;
			transaction.ParticipantBy = Registry.Find<UserAccount>().OrganizationParticipant;
			transaction.ParticipantTo = this.OrganizationParticipant;
			transaction.Quantity = staged.Quantity;
			transaction.RequisitionDetail = staged.RequisitionDetail;
			transaction.SerialNumber = staged.InventoryItem.SerialNumber;
			transaction.TransactionType = type;
			//
			Repositories.Get<ItemTransaction>().Add(transaction);
		}

		#endregion

		#region Methods.Public

		public virtual void ChangeStatus(StatusCode status)
		{
			RequisitionStatus created = Entity.Activate<RequisitionStatus>();
			created.Occurred = DateTime.Now;
			created.RequisitionHeader = this;
			created.StatusCode = status;
			//
			Repositories.Get<RequisitionStatus>().Add(created);
		}

		public virtual void FindCurrentStatus()
		{
			DetachedCriteria criteria = DetachedCriteria.For<RequisitionStatus>()
				.Add("RequisitionHeader", this)
				.AddOrder("Occurred", false)
				.AddOrder("StatusCode.SortOrder", false)
				.SetMaxResults(1);
			RequisitionStatus status = Repositories.Get<RequisitionStatus>().Retrieve(criteria);
			if (status != null) this.CurrentStatus = status.StatusCode;
		}

		public virtual Boolean ShouldUpdateStatus()
		{
			StatusCode current = Entity.RetrieveStatus<RequisitionHeader, RequisitionDetail>(this, FunctionalAreas.Requsition);
			if (current == null) return false;
			//
			this.FindCurrentStatus();
			return (this.CurrentStatus == null || current.SortOrder != this.CurrentStatus.SortOrder);
		}

		public virtual void UpdateStatus()
		{
			Entity.UpdateStatus<RequisitionHeader, RequisitionDetail>(this, FunctionalAreas.Requsition);
			//
			this.FindCurrentStatus();
			if (this.CurrentStatus == null) return;
			else  if (CodeValue.GetCode(Upp.Irms.Constants.RequisitionStatuses.Packed).Equals(this.CurrentStatus.Code))
				this.ChangeStatus(Entity.Retrieve<StatusCode>(Upp.Irms.Constants.RequisitionStatuses.Staged));
		}

		#endregion

		#region Methods.Public.Assets

		public virtual void CheckOut()
		{
			StatusCode issued = Entity.Retrieve<StatusCode>(InventoryStatuses.Issued);
			StatusCode staged = Entity.Retrieve<StatusCode>(InventoryStatuses.Staged);
			//
			DetachedCriteria criteria = DetachedCriteria.For<ItemTransaction>()
				.Add("RequisitionDetail.RequisitionHeader", this)
				.Add("TransactionType", Entity.Retrieve<TransactionType>(RequisitionTransactions.Staged));
			IList<ItemTransaction> transactions = Repositories.Get<ItemTransaction>().List(criteria);
			foreach (ItemTransaction element in transactions)
			{
				InventoryItem asset = element.InventoryItem;
				ItemType type = asset.Item.ItemType;
				//
				this.WriteCheckOut(element);
				//
				if ("Y".Equals(type.Consumable))
				{
					criteria = DetachedCriteria.For<InventoryItemDetail>()
						.Add("InventoryItem", element.InventoryItem)
						.Add("KitHeader", null)
						.Add("Location", element.LocationTo)
						.Add("Quantity", element.Quantity)
						.Add("StatusCode", staged)
						.SetMaxResults(1);
					InventoryItemDetail detail = Repositories.Get<InventoryItemDetail>().Retrieve(criteria);
					if (detail == null) continue; // throw an exception?
					//
					if (this.OrganizationParticipant != null)
					{
						asset.CalculateReturnDate();
						//
						ParticipantAsset issuance = ParticipantAsset.Create(asset);
						issuance.OrganizationParticipant = this.OrganizationParticipant;
						issuance.Quantity = element.Quantity;
						Repositories.Get<ParticipantAsset>().Add(issuance);
					}
					else detail.ParentInventoryItem = this.InventoryItem;
					//
					detail.ChangeStatus(issued);
				}
				else
				{
					if (this.OrganizationParticipant != null)
					{
						asset.CalculateReturnDate();
						//
						ParticipantAsset issuance = ParticipantAsset.Create(asset);
						issuance.OrganizationParticipant = this.OrganizationParticipant;
						Repositories.Get<ParticipantAsset>().Add(issuance);
					}
					else asset.ParentInventoryItem = this.InventoryItem;
					//
					asset.ChangeStatus(issued);
				}
			}
			//
			this.ChangeStatus(Entity.Retrieve<StatusCode>(Upp.Irms.Constants.RequisitionStatuses.Complete));
		}

		#endregion
	}
}
