using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class RequisitionReference : Entity
	{
		#region Fields

		private ReferenceCode _referenceCode;
		private RequisitionHeader _requisitionHeader;
		private String _comments;
		private String _referenceValue;

		#endregion

		#region Properties

		[DataMember]
		public virtual ReferenceCode ReferenceCode
		{
			get { return _referenceCode; }
			set { _referenceCode = value; }
		}

		[DataMember]
		public virtual RequisitionHeader RequisitionHeader
		{
			get { return _requisitionHeader; }
			set { _requisitionHeader = value; }
		}

		[DataMember]
		public virtual String Comments
		{
			get { return _comments; }
			set { _comments = value; }
		}

		[DataMember]
		public virtual String ReferenceValue
		{
			get { return _referenceValue; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ReferenceValue must not be blank or null.");
				else _referenceValue = value;
			}
		}


		#endregion
	}
}
