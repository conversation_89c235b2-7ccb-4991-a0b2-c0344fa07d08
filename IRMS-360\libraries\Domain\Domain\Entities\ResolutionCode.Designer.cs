using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ResolutionCode : Entity
	{
		#region Fields

		private ICollection<ClaimResolution> _claimResolutions = new HashSet<ClaimResolution>();
		private String _active;
		private String _description;
		private String _resolutionCodeCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<ClaimResolution> ClaimResolutions
		{
			get { return _claimResolutions; }
			set { _claimResolutions = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String ResolutionCodeCode
		{
			get { return _resolutionCodeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ResolutionCodeCode must not be blank or null.");
				else _resolutionCodeCode = value;
			}
		}


		#endregion
	}
}
