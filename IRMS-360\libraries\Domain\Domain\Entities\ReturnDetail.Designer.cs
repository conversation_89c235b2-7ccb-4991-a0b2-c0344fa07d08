using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ReturnDetail : Entity
	{
		#region Fields

		private ActionCode _actionCode;
		private CartonHeader _cartonHeader;
		private DispositionCode _dispositionCode;
		private Decimal _quantity;
		private InventoryItem _inventoryItem;
		private Item _item;
		private ICollection<ItemTransaction> _itemTransactions = new HashSet<ItemTransaction>();
		private ICollection<ReturnReasonDetail> _returnReasonDetails = new HashSet<ReturnReasonDetail>();
		private OrderDetail _orderDetail;
		private PurchaseOrderDetail _purchaseOrderDetail;
		private ReturnHeader _returnHeader;
		private ReturnReason _returnReason;
		private ShipmentDetail _shipmentDetail;
		private StatusCode _statusCode;
		private String _itemDescription;
		private String _lotNumber;
		private String _notes;
		private String _serialNumber;

		#endregion

		#region Properties

		[DataMember]
		public virtual ActionCode ActionCode
		{
			get { return _actionCode; }
			set { _actionCode = value; }
		}

		[DataMember]
		public virtual CartonHeader CartonHeader
		{
			get { return _cartonHeader; }
			set { _cartonHeader = value; }
		}

		[DataMember]
		public virtual DispositionCode DispositionCode
		{
			get { return _dispositionCode; }
			set { _dispositionCode = value; }
		}

		[DataMember]
		public virtual Decimal Quantity
		{
			get { return _quantity; }
			set { _quantity = value; }
		}

		[DataMember]
		public virtual InventoryItem InventoryItem
		{
			get { return _inventoryItem; }
			set { _inventoryItem = value; }
		}

		[DataMember]
		public virtual Item Item
		{
			get { return _item; }
			set { _item = value; }
		}

		[DataMember]
		public virtual ICollection<ItemTransaction> ItemTransactions
		{
			get { return _itemTransactions; }
			set { _itemTransactions = value; }
		}

		[DataMember]
		public virtual ICollection<ReturnReasonDetail> ReturnReasonDetails
		{
			get { return _returnReasonDetails; }
			set { _returnReasonDetails = value; }
		}

		[DataMember]
		public virtual OrderDetail OrderDetail
		{
			get { return _orderDetail; }
			set { _orderDetail = value; }
		}

		[DataMember]
		public virtual PurchaseOrderDetail PurchaseOrderDetail
		{
			get { return _purchaseOrderDetail; }
			set { _purchaseOrderDetail = value; }
		}

		[DataMember]
		public virtual ReturnHeader ReturnHeader
		{
			get { return _returnHeader; }
			set { _returnHeader = value; }
		}

		[DataMember]
		public virtual ReturnReason ReturnReason
		{
			get { return _returnReason; }
			set { _returnReason = value; }
		}

		[DataMember]
		public virtual ShipmentDetail ShipmentDetail
		{
			get { return _shipmentDetail; }
			set { _shipmentDetail = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual String ItemDescription
		{
			get { return _itemDescription; }
			set { _itemDescription = value; }
		}

		[DataMember]
		public virtual String LotNumber
		{
			get { return _lotNumber; }
			set { _lotNumber = value; }
		}

		[DataMember]
		public virtual String Notes
		{
			get { return _notes; }
			set { _notes = value; }
		}

		[DataMember]
		public virtual String SerialNumber
		{
			get { return _serialNumber; }
			set { _serialNumber = value; }
		}


		#endregion
	}
}
