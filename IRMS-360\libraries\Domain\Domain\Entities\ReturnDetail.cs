using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;

using NHibernate.Criterion;
using NHibernate.Transform;

using Upp.Irms.Constants;
using Upp.Irms.Core;
using Upp.Shared.Utilities;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class ReturnDetail : Entity
	{
		#region Properties

		[DataMember]
		public virtual Decimal Amount { get; set; }
		[DataMember]
		public virtual Int32 LineNumber { get; set; }
		[DataMember]
		public virtual Decimal OrderQuantity { get; set; }
		[DataMember]
		public virtual Decimal PreviousQuantity { get; set; }
		[DataMember]
		public virtual Decimal RemainingQuantity { get; set; }
		[DataMember]
		public virtual Decimal ReturnedQuantity { get; set; }
		[DataMember]
		public virtual LicensePlate LicensePlate { get; set; }
		[DataMember]
		public virtual LicensePlate LicensePlateTo { get; set; }
		[DataMember]
		public virtual Location Location { get; set; }
		[DataMember]
		public virtual String ItemCode { get; set; }
		[DataMember]
		public virtual String OrderCodeSuffix { get; set; }
		[DataMember]
		public virtual String ZoneCode { get; set; }

		#endregion

		#region Constructor

		public ReturnDetail()
		{
			//
		}

		#endregion

		#region Methods.Private.Transactions

		private void WriteReturned()
		{
			TransactionType type = Entity.Retrieve<TransactionType>(InventoryTransactions.Return);
			ItemTransaction transaction = Entity.Activate<ItemTransaction>();
			transaction.InventoryItem = _inventoryItem;
			transaction.Item = _item;
			transaction.LocationFrom = null;
			transaction.LocationTo = this.Location;
			transaction.LotNumber = _lotNumber;
			transaction.Occurred = DateTime.Now;
			transaction.OrderHeader = _orderDetail.OrderHeader;
			transaction.ParticipantBy = Registry.Find<UserAccount>().OrganizationParticipant;
			transaction.Quantity = _quantity;
            transaction.ReturnDetail = this;
			transaction.SerialNumber = null;
			transaction.StatusCode = _inventoryItem.StatusCode;
			transaction.StatusReasonCode = _inventoryItem.StatusCode.StatusReasonCode;
			transaction.TransactionType = type;
			//transaction.Wave = ?
			//
			Repositories.Get<ItemTransaction>().Add(transaction);
		}

		public virtual void WriteStockAdjustment()
		{
			TransactionType type = Entity.Retrieve<TransactionType>(StockAdjustmentTransactions.StockAdjustment);
			//
			String code = BusinessRule.RetrieveString("11055");
			if (String.IsNullOrEmpty(code)) throw new Exception("Business Rule 11055 is not set.");

			DetachedCriteria criteria = DetachedCriteria.For<InventoryAdjustmentCode>()
					.Add("Active", "A")
					.Add("Code", code)
					.Add("CompanyLocationType", Registry.Find<CompanyLocationType>())
					.SetMaxResults(1);
			InventoryAdjustmentCode adjustment = Repositories.Get<InventoryAdjustmentCode>().Retrieve(criteria);
			if (adjustment == null) throw new Exception(String.Format("Unable to find adjustment code for {0}.", code));
			//
			string orderCode = string.Empty;

			if (this.ShipmentDetail.CartonHeader != null && this.ShipmentDetail.CartonHeader.OrderHeader != null)
				orderCode = this.ShipmentDetail.CartonHeader.OrderHeader.OrderCode;
			else if (this.ShipmentDetail.LicensePlate != null)
			{
				if (this.ShipmentDetail.LicensePlate != null)
					if (this.ShipmentDetail.LicensePlate.InventoryPicks != null && this.ShipmentDetail.LicensePlate.InventoryPicks.Count > 0)
						orderCode = this.ShipmentDetail.LicensePlate.InventoryPicks.First().ItemFulfillment.OrderDetail.OrderHeader.OrderCode;
			}
			//
			ItemTransaction transaction = Entity.Activate<ItemTransaction>();

			transaction.InventoryAdjustmentCode = adjustment;
			transaction.InventoryItem = _inventoryItem;
			transaction.Item = _inventoryItem.Item;
			transaction.LicensePlateFrom = this.LicensePlate;
			transaction.LicensePlateTo = this.LicensePlateTo;
			transaction.LotNumber = _lotNumber;
			transaction.Occurred = DateTime.Now;
			transaction.OrderCode = orderCode;
			transaction.Quantity = _quantity;
			transaction.QuantityFrom = 0;
			transaction.StatusCode = _inventoryItem.StatusCode;
			transaction.StatusReasonCode = _inventoryItem.StatusCode.StatusReasonCode;
			transaction.TransactionType = type;
			//
			Repositories.Get<ItemTransaction>().Add(transaction);
		}

		#endregion

		#region Methods.Public

		public virtual void CalculateOrderQuantity()
		{
			DetachedCriteria criteria = DetachedCriteria.For<InventoryPick>()
				.Add("OrderDetail", _orderDetail)
				.SetProjection(Projections.Sum("Quantity"));
			this.OrderQuantity = Repositories.Get<InventoryPick>().Function<Int32>(criteria);
		}

		public virtual void CalculatePreviousQuantity()
		{
			StatusCode complete = Entity.Retrieve<StatusCode>(ReturnStatuses.Complete);
			DetachedCriteria criteria = DetachedCriteria.For<ReturnDetail>()
				.Add("OrderDetail", _orderDetail)
				.Add("StatusCode", complete)
				.SetProjection(Projections.Sum("Quantity"));
			/*if (_returnHeader != null) criteria = criteria.Add("ReturnHeader", _returnHeader);*/
			this.PreviousQuantity = Repositories.Get<ReturnDetail>().Function<Int32>(criteria);
		}

		public virtual void Return()
		{
			StatusCode complete = Entity.Retrieve<StatusCode>(ReturnStatuses.Complete);
			//
			DateTime? manufacture = InventoryItem.CalculateManufactureDate(this.LotNumber);
			this.StatusCode = InventoryItem.UpdateStatus(_item, this.StatusCode, manufacture);
			//
			InventoryItem item = InventoryItem.Create(_item, this.Location, this.StatusCode, this.ReturnedQuantity);
			item.LotNumber = this.LotNumber;
			item.ManufactureDate = manufacture;
			item.Received = DateTime.Now;
			//
            DetachedCriteria criteria = DetachedCriteria.For<InventoryPick>().Add("OrderDetail", this.OrderDetail);
            if (this.LicensePlate != null) criteria = criteria.AddOr("LicensePlate", this.LicensePlate, "LicensePlate.ParentLicensePlate", this.LicensePlate);
            IList<InventoryPick> picks = Repositories.Get<InventoryPick>().List(criteria);
            if (picks != null && picks.Count > 0)
            {
                if (picks.First().ItemFulfillment != null && picks.First().ItemFulfillment.InventoryItem != null)
                {
                    item.UnitOfMeasure = picks.First().ItemFulfillment.InventoryItem.UnitOfMeasure;
                }
                else
                {
                    ProjectionList projections = Projections.ProjectionList()
                         .Add(Projections.Property("InventoryItem"), "InventoryItem");
                    criteria = DetachedCriteria.For<ItemTransaction>()
                          .Add("InventoryPick.Id", picks.First().Id)
                          .Add("TransactionType", "=", Entity.Retrieve<TransactionType>(OrderTransactions.Picked))
                          .Add(Restrictions.IsNotNull("InventoryItem"))
                          .SetProjection(projections)
                          .SetMaxResults(1)
                          .SetResultTransformer(Transformers.AliasToBean<ItemTransaction>());
                    ItemTransaction transaction = Repositories.Get<ItemTransaction>().Retrieve(criteria);

                    if (transaction != null) item.UnitOfMeasure = transaction.InventoryItem.UnitOfMeasure;
                }
			}
			if (item.UnitOfMeasure == null) item.UnitOfMeasure = _item.UnitOfMeasure;
			if (item.UnitOfMeasure == null)
			{
				UnitOfMeasure each = Entity.Retrieve<UnitOfMeasure>(InventoryUoms.Each);
				item.UnitOfMeasure = each;
			}
			//
			Repositories.Get<InventoryItem>().Add(item);
			if (this.LicensePlateTo != null) item.LicensePlate.ChangeParent(this.LicensePlateTo);
			//
			if (_id != null)
			{
				if (_quantity > this.ReturnedQuantity)
				{
					ReturnDetail split = Entity.Clone<ReturnDetail>(this);
					split.InventoryItem = item;
					split.Quantity = this.ReturnedQuantity;
					split.StatusCode = complete;
					//
					Repositories.Get<ReturnDetail>().Add(split);
					//
					split.WriteReturned();
				}
			}
			else
			{
				_returnHeader = ReturnHeader.Create();
				Repositories.Get<ReturnHeader>().Add(_returnHeader);
				//
				_dateCreated = DateTime.Now;
				_inventoryItem = item;
				_quantity = this.ReturnedQuantity;
				_statusCode = complete;
				//
				this.WriteReturned();
			}
            //
            if (_orderDetail != null)
            {
                _orderDetail = Repositories.Get<OrderDetail>().Retrieve(_orderDetail.Id);
                _orderDetail.UpdateReturnQuantity(this.ReturnedQuantity);
            }
		}

		public virtual void VoidShipment()
		{
			StatusCode lpnVoid = Entity.Retrieve<StatusCode>(LicensePlateStatuses.Void);
			StatusCode shippingVoid = Entity.Retrieve<StatusCode>(ShippingStatuses.Void);
			//
			this.ShipmentDetail.ChangeStatus(shippingVoid);
			//
			if (this.ShipmentDetail.LicensePlate != null)
			{
				DetachedCriteria criteria = DetachedCriteria.For<ShipmentDetail>()
					.Add("LicensePlate", this.ShipmentDetail.LicensePlate)
					.Add("StatusCode", "!=", shippingVoid)
					.SetProjection(Projections.Count("Id"));

				if (Repositories.Get<ShipmentDetail>().Function<Int32>(criteria) == 0) this.ShipmentDetail.LicensePlate.ChangeStatus(lpnVoid);
			}
			//
			if (this.ShipmentDetail.CartonHeader != null)
			{
				DetachedCriteria criteria = DetachedCriteria.For<ShipmentDetail>()
					.Add("LicensePlate", this.ShipmentDetail.CartonHeader.LicensePlate)
					.Add("StatusCode", "!=", shippingVoid)
					.SetProjection(Projections.Count("Id"));

				if (Repositories.Get<ShipmentDetail>().Function<Int32>(criteria) == 0) this.ShipmentDetail.CartonHeader.LicensePlate.ChangeStatus(lpnVoid);
			}
			//
			this.WriteStockAdjustment();
		}

		#endregion

		#region Methods.Static

		public static ReturnDetail Create(InventoryPick pick)
		{
			pick.OrderDetail.FindOrderChargeAmount();
			//
			ReturnDetail entity = Entity.Activate<ReturnDetail>();
			entity.Amount = pick.OrderDetail.Amount;
			entity.Item = pick.OrderDetail.Item;
			entity.ItemCode = pick.OrderDetail.Item.ItemCode;
			entity.ItemDescription = pick.OrderDetail.Item.Description;
			entity.LineNumber = pick.OrderDetail.LineNumber;
			entity.OrderCodeSuffix = pick.OrderDetail.OrderHeader.OrderCodeSuffix();
			entity.OrderDetail = pick.OrderDetail;
			entity.OrderQuantity = pick.Quantity;
			if (pick.OrderDetail.Item.CompanyLocationZone != null)
				entity.ZoneCode = pick.OrderDetail.Item.CompanyLocationZone.Zone.ZoneCode;
			//
			return entity;
		}

		public static ReturnDetail Create(ReturnHeader header, PurchaseOrderDetail detail, InventoryItem item)
		{
			ReturnDetail created = Entity.Activate<ReturnDetail>();
			created.InventoryItem = item;
			created.Item = detail.Item;
			created.ItemDescription = detail.Item.Description;
			created.LotNumber = detail.LotNumber;
			created.PurchaseOrderDetail = detail;
			created.ReturnHeader = header;
			//
			return created;
		}

		#endregion
	}
}
