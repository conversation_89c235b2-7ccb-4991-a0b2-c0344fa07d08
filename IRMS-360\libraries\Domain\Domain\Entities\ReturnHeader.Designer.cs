using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ReturnHeader : Entity
	{
		#region Fields

		private CompanyLocationType _companyLocationType;
		private Customer _customer;
		private ICollection<ReturnDetail> _returnDetails = new HashSet<ReturnDetail>();
		private ICollection<ReturnLocation> _returnLocations = new HashSet<ReturnLocation>();
		private ICollection<ReturnReasonDetail> _returnReasonDetails = new HashSet<ReturnReasonDetail>();
		private ICollection<ReturnStatus> _returnStatuses = new HashSet<ReturnStatus>();
		private String _contactAlternalteEmail;
		private String _contactEmail;
		private String _contactFirstName;
		private String _contactHomePhone;
		private String _contactLastName;
		private String _contactMobilePhone;
		private String _quantityType;
		private String _returnTypeCode;
		private String _rmaCode;
		private Vendor _vendor;

		#endregion

		#region Properties

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual Customer Customer
		{
			get { return _customer; }
			set { _customer = value; }
		}

		[DataMember]
		public virtual ICollection<ReturnDetail> ReturnDetails
		{
			get { return _returnDetails; }
			set { _returnDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ReturnLocation> ReturnLocations
		{
			get { return _returnLocations; }
			set { _returnLocations = value; }
		}

		[DataMember]
		public virtual ICollection<ReturnReasonDetail> ReturnReasonDetails
		{
			get { return _returnReasonDetails; }
			set { _returnReasonDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ReturnStatus> ReturnStatuses
		{
			get { return _returnStatuses; }
			set { _returnStatuses = value; }
		}

		[DataMember]
		public virtual String ContactAlternalteEmail
		{
			get { return _contactAlternalteEmail; }
			set { _contactAlternalteEmail = value; }
		}

		[DataMember]
		public virtual String ContactEmail
		{
			get { return _contactEmail; }
			set { _contactEmail = value; }
		}

		[DataMember]
		public virtual String ContactFirstName
		{
			get { return _contactFirstName; }
			set { _contactFirstName = value; }
		}

		[DataMember]
		public virtual String ContactHomePhone
		{
			get { return _contactHomePhone; }
			set { _contactHomePhone = value; }
		}

		[DataMember]
		public virtual String ContactLastName
		{
			get { return _contactLastName; }
			set { _contactLastName = value; }
		}

		[DataMember]
		public virtual String ContactMobilePhone
		{
			get { return _contactMobilePhone; }
			set { _contactMobilePhone = value; }
		}

		[DataMember]
		public virtual String QuantityType
		{
			get { return _quantityType; }
			set { _quantityType = value; }
		}

		[DataMember]
		public virtual String ReturnTypeCode
		{
			get { return _returnTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ReturnTypeCode must not be blank or null.");
				else _returnTypeCode = value;
			}
		}

		[DataMember]
		public virtual String RmaCode
		{
			get { return _rmaCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("RmaCode must not be blank or null.");
				else _rmaCode = value;
			}
		}

		[DataMember]
		public virtual Vendor Vendor
		{
			get { return _vendor; }
			set { _vendor = value; }
		}


		#endregion
	}
}
