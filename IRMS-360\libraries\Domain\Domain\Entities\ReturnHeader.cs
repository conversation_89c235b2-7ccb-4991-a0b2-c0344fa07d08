using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using NHibernate.Criterion;
using NHibernate.Transform;

using Upp.Irms.Constants;
using Upp.Irms.Core;
using Upp.Shared.Utilities;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class ReturnHeader : Entity
	{
		#region Properties

		[DataMember]
		public virtual StatusCode CurrentStatus { get; set; }
		[DataMember]
		public virtual String PurchaseOrderCode { get; set; }
		[DataMember]
		public virtual String PurchaseOrderSuffix { get; set; }

		#endregion

		#region Constructor

		public ReturnHeader()
		{
			//
		}

		#endregion

		#region Methods.Public

		public virtual void ChangeStatus(StatusCode status)
		{
			ReturnStatus created = Entity.Activate<ReturnStatus>();
			created.Occurred = DateTime.Now;
			created.ReturnHeader = this;
			created.StatusCode = status;
			//
			Repositories.Get<ReturnStatus>().Add(created);
		}

		public virtual void FindCurrentStatus()
		{
			ProjectionList projections = Projections.ProjectionList()
				.Add(Projections.Max("Occurred"), "Occurred")
				.Add(Projections.GroupProperty("StatusCode"), "StatusCode");
			DetachedCriteria criteria = DetachedCriteria.For<ReturnStatus>()
				.Add("ReturnHeader", this)
				.SetProjection(projections)
				.SetResultTransformer(Transformers.AliasToBean<ReturnStatus>())
				.SetMaxResults(1);
			IList<ReturnStatus> statuses = Repositories.Get<ReturnStatus>().List(criteria);
			if (statuses.Count == 1) this.CurrentStatus = statuses[0].StatusCode;
		}

		public virtual void UpdateStatus()
		{
			DetachedCriteria criteria = DetachedCriteria.For<StatusCode>()
				.Add("Active", "A")
				.Add("FunctionalAreaCode.Code", CodeValue.GetCode(FunctionalAreas.Return))
				.Add("SortOrder", "!=", null)
				.AddOrder("SortOrder");
			IList<StatusCode> statuses = Repositories.Get<StatusCode>().List(criteria);
			foreach (StatusCode status in statuses)
			{
				criteria = DetachedCriteria.For<ReturnDetail>()
					.Add("ReturnHeader", this)
					.Add("StatusCode", status);
				Int32 count = Repositories.Get<ReturnDetail>().Count(criteria);
				if (count > 0)
				{
					this.ChangeStatus(status);
					break;
				}
			}
		}

		#endregion

		#region Methods.Static

		public static ReturnHeader Create()
		{
			ReturnHeader entity = Entity.Activate<ReturnHeader>();
			entity.CompanyLocationType = Registry.Find<CompanyLocationType>();
			entity.ReturnTypeCode = "C";
			entity.RmaCode = EntityCode.GetCurrentValue(EntityCodes.CustomerReturn);
			//
			return entity;
		}

		#endregion
	}
}
