using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ReturnLocation : Entity
	{
		#region Fields

		private LocationType _locationType;
		private ReturnHeader _returnHeader;
		private String _addressLine1;
		private String _addressLine2;
		private String _addressLine3;
		private String _businessName;
		private String _city;
		private String _stateCode;
		private String _zipCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual LocationType LocationType
		{
			get { return _locationType; }
			set { _locationType = value; }
		}

		[DataMember]
		public virtual ReturnHeader ReturnHeader
		{
			get { return _returnHeader; }
			set { _returnHeader = value; }
		}

		[DataMember]
		public virtual String AddressLine1
		{
			get { return _addressLine1; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("AddressLine1 must not be blank or null.");
				else _addressLine1 = value;
			}
		}

		[DataMember]
		public virtual String AddressLine2
		{
			get { return _addressLine2; }
			set { _addressLine2 = value; }
		}

		[DataMember]
		public virtual String AddressLine3
		{
			get { return _addressLine3; }
			set { _addressLine3 = value; }
		}

		[DataMember]
		public virtual String BusinessName
		{
			get { return _businessName; }
			set { _businessName = value; }
		}

		[DataMember]
		public virtual String City
		{
			get { return _city; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("City must not be blank or null.");
				else _city = value;
			}
		}

		[DataMember]
		public virtual String StateCode
		{
			get { return _stateCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("StateCode must not be blank or null.");
				else _stateCode = value;
			}
		}

		[DataMember]
		public virtual String ZipCode
		{
			get { return _zipCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ZipCode must not be blank or null.");
				else _zipCode = value;
			}
		}


		#endregion
	}
}
