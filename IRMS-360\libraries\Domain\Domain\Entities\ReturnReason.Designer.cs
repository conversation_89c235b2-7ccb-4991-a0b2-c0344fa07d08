using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ReturnReason : Entity
	{
		#region Fields

		private ICollection<OrderReturnReason> _orderReturnReasons = new HashSet<OrderReturnReason>();
		private ICollection<ReceiptReturnDetail> _receiptReturnDetails = new HashSet<ReceiptReturnDetail>();
		private ICollection<ReturnDetail> _returnDetails = new HashSet<ReturnDetail>();
		private String _active;
		private String _description;
		private String _returnReasonCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<OrderReturnReason> OrderReturnReasons
		{
			get { return _orderReturnReasons; }
			set { _orderReturnReasons = value; }
		}

		[DataMember]
		public virtual ICollection<ReceiptReturnDetail> ReceiptReturnDetails
		{
			get { return _receiptReturnDetails; }
			set { _receiptReturnDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ReturnDetail> ReturnDetails
		{
			get { return _returnDetails; }
			set { _returnDetails = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String ReturnReasonCode
		{
			get { return _returnReasonCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ReturnReasonCode must not be blank or null.");
				else _returnReasonCode = value;
			}
		}


		#endregion
	}
}
