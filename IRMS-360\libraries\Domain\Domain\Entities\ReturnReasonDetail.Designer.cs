using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ReturnReasonDetail : Entity
	{
		#region Fields

		private PurchaseOrderDetail _purchaseOrderDetail;
		private ReturnDetail _returnDetail;
		private ReturnHeader _returnHeader;
		private StatusReasonCode _statusReasonCode;
		private String _notes;

		#endregion

		#region Properties

		[DataMember]
		public virtual PurchaseOrderDetail PurchaseOrderDetail
		{
			get { return _purchaseOrderDetail; }
			set { _purchaseOrderDetail = value; }
		}

		[DataMember]
		public virtual ReturnDetail ReturnDetail
		{
			get { return _returnDetail; }
			set { _returnDetail = value; }
		}

		[DataMember]
		public virtual ReturnHeader ReturnHeader
		{
			get { return _returnHeader; }
			set { _returnHeader = value; }
		}

		[DataMember]
		public virtual StatusReasonCode StatusReasonCode
		{
			get { return _statusReasonCode; }
			set { _statusReasonCode = value; }
		}

		[DataMember]
		public virtual String Notes
		{
			get { return _notes; }
			set { _notes = value; }
		}


		#endregion
	}
}
