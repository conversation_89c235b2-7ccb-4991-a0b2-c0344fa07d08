using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class RoleCode : Entity
	{
		#region Fields

		private ICollection<ParticipantRole> _participantRoles = new HashSet<ParticipantRole>();
		private String _active;
		private String _code;
		private String _cycleCount;
		private String _description;
		private String _pack;
		private String _physicalCount;
		private String _pick;
		private String _ship;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<ParticipantRole> ParticipantRoles
		{
			get { return _participantRoles; }
			set { _participantRoles = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Code
		{
			get { return _code; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Code must not be blank or null.");
				else _code = value;
			}
		}

		[DataMember]
		public virtual String CycleCount
		{
			get { return _cycleCount; }
			set { _cycleCount = value; }
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String Pack
		{
			get { return _pack; }
			set { _pack = value; }
		}

		[DataMember]
		public virtual String PhysicalCount
		{
			get { return _physicalCount; }
			set { _physicalCount = value; }
		}

		[DataMember]
		public virtual String Pick
		{
			get { return _pick; }
			set { _pick = value; }
		}

		[DataMember]
		public virtual String Ship
		{
			get { return _ship; }
			set { _ship = value; }
		}


		#endregion
	}
}
