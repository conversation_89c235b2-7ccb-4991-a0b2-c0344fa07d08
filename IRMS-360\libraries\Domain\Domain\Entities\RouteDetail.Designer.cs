using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class RouteDetail : Entity
	{
		#region Fields

		private CustomerLocationType _customerLocationType;
		private Int32 _stopSequence;
		private RouteHeader _routeHeader;
		private String _active;
		private String _customerCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual CustomerLocationType CustomerLocationType
		{
			get { return _customerLocationType; }
			set { _customerLocationType = value; }
		}

		[DataMember]
		public virtual Int32 StopSequence
		{
			get { return _stopSequence; }
			set { _stopSequence = value; }
		}

		[DataMember]
		public virtual RouteHeader RouteHeader
		{
			get { return _routeHeader; }
			set { _routeHeader = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String CustomerCode
		{
			get { return _customerCode; }
			set { _customerCode = value; }
		}


		#endregion
	}
}
