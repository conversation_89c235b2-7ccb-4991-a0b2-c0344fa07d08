using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class RouteHeader : Entity
	{
		#region Fields

		private Agency _agency;
		private Carrier _carrier;
		private CompanyLocationType _companyLocationType;
		private DateTime _effective;
		private DateTime? _departure;
		private DateTime? _expiration;
		private ICollection<CustomerLocationType> _customerLocationTypes = new HashSet<CustomerLocationType>();
		private ICollection<Dock> _docks = new HashSet<Dock>();
		private ICollection<RouteDetail> _routeDetails = new HashSet<RouteDetail>();
		private ICollection<ShipmentHeader> _shipmentHeaders = new HashSet<ShipmentHeader>();
		private String _description;
		private String _friday;
		private String _monday;
		private String _routeCode;
		private String _routeTypeCode;
		private String _saturday;
		private String _sunday;
		private String _thursday;
		private String _tuesday;
		private String _wednesday;

		#endregion

		#region Properties

		[DataMember]
		public virtual Agency Agency
		{
			get { return _agency; }
			set { _agency = value; }
		}

		[DataMember]
		public virtual Carrier Carrier
		{
			get { return _carrier; }
			set { _carrier = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual DateTime Effective
		{
			get { return _effective; }
			set { _effective = value; }
		}

		[DataMember]
		public virtual DateTime? Departure
		{
			get { return _departure; }
			set { _departure = value; }
		}

		[DataMember]
		public virtual DateTime? Expiration
		{
			get { return _expiration; }
			set { _expiration = value; }
		}

		[DataMember]
		public virtual ICollection<CustomerLocationType> CustomerLocationTypes
		{
			get { return _customerLocationTypes; }
			set { _customerLocationTypes = value; }
		}

		[DataMember]
		public virtual ICollection<Dock> Docks
		{
			get { return _docks; }
			set { _docks = value; }
		}

		[DataMember]
		public virtual ICollection<RouteDetail> RouteDetails
		{
			get { return _routeDetails; }
			set { _routeDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ShipmentHeader> ShipmentHeaders
		{
			get { return _shipmentHeaders; }
			set { _shipmentHeaders = value; }
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String Friday
		{
			get { return _friday; }
			set { _friday = value; }
		}

		[DataMember]
		public virtual String Monday
		{
			get { return _monday; }
			set { _monday = value; }
		}

		[DataMember]
		public virtual String RouteCode
		{
			get { return _routeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("RouteCode must not be blank or null.");
				else _routeCode = value;
			}
		}

		[DataMember]
		public virtual String RouteTypeCode
		{
			get { return _routeTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("RouteTypeCode must not be blank or null.");
				else _routeTypeCode = value;
			}
		}

		[DataMember]
		public virtual String Saturday
		{
			get { return _saturday; }
			set { _saturday = value; }
		}

		[DataMember]
		public virtual String Sunday
		{
			get { return _sunday; }
			set { _sunday = value; }
		}

		[DataMember]
		public virtual String Thursday
		{
			get { return _thursday; }
			set { _thursday = value; }
		}

		[DataMember]
		public virtual String Tuesday
		{
			get { return _tuesday; }
			set { _tuesday = value; }
		}

		[DataMember]
		public virtual String Wednesday
		{
			get { return _wednesday; }
			set { _wednesday = value; }
		}


		#endregion
	}
}
