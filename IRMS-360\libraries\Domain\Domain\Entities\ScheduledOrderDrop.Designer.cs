using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ScheduledOrderDrop : Entity
	{
		#region Fields

		private int _favouriteQueryId;
		private int? _companyLocationTypeId;
		
		private DateTime? _lastRunDateTime;
		private DateTime? _nextRunDateTime;
		
		private String _active;
		private String _brPromptValues;
        private String _scheduledOrderDropName;
        private String _statusCodeDescription;
		private String _savedrderDropSearchName;

		private FavouriteQuery _favouriteQuery;
		private CompanyLocationType _companyLocationType;
		private StatusCode _statusCode;
        private ReportSchedule _reportSchedule;
        private ReportRequestHeader _reportRequestHeader;

        #endregion

        #region Properties

        [DataMember]
		public virtual FavouriteQuery FavouriteQuery
		{
			get { return _favouriteQuery; }
			set { _favouriteQuery = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}
		
		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

        [DataMember]
        public virtual ReportSchedule ReportSchedule
        {
            get { return _reportSchedule; }
            set { _reportSchedule = value; }
        }

        [DataMember]
        public virtual ReportRequestHeader ReportRequestHeader
        {
            get { return _reportRequestHeader; }
            set { _reportRequestHeader = value; }
        }


        [DataMember]
		public virtual DateTime? LastRunDateTime
		{
			get { return _lastRunDateTime; }
			set { _lastRunDateTime = value; }
		}
		
		[DataMember]
		public virtual DateTime? NextRunDateTime
		{
			get { return _nextRunDateTime; }
			set { _nextRunDateTime = value; }
		}
		
		[DataMember]
		public virtual String SavedOrderDropSearchName
		{
			get { return _savedrderDropSearchName; }
			set { _savedrderDropSearchName = value; }
		}
		
		[DataMember]
		public virtual String StatusCodeDescription
		{
			get { return _statusCodeDescription; }
			set { _statusCodeDescription = value; }
		}
		
		[DataMember]
		public virtual String BRPromptValues
		{
			get { return _brPromptValues; }
			set { _brPromptValues = value; }
		}

        [DataMember]
        public virtual String ScheduledOrderDropName
        {
            get { return _scheduledOrderDropName; }
            set { _scheduledOrderDropName = value; }
        }

        [DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}
		
		[DataMember]
		public virtual int FavouriteQueryId
		{
			get { return _favouriteQueryId; }
			set { _favouriteQueryId = value; }
		}
		
		[DataMember]
		public virtual int? CompanyLocationTypeId
		{
			get { return _companyLocationTypeId; }
			set { _companyLocationTypeId = value; }
		}

		#endregion
	}
}
