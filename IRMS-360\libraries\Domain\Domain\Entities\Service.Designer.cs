using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Service : Entity
	{
		#region Fields

		private CvxCode _cvxCode;
		private DateTime? _effectiveDate;
		private DateTime? _terminationDate;
		private Decimal? _units;
		private ICollection<InsurancePayerService> _insurancePayerServices = new HashSet<InsurancePayerService>();
		private ICollection<ParticipantImmunization> _participantImmunizations = new HashSet<ParticipantImmunization>();
		private ICollection<ServiceDetail> _serviceDetails = new HashSet<ServiceDetail>();
		private MvxCode _mvxCode;
		private NdcCode _ndcCode;
		private OrganizationParticipant _organizationParticipant;
		private Provider _provider;
		private ProviderLocation _providerLocation;
		private ProviderOrganizationalUnit _providerOrganizationalUnit;
		private ServiceType _serviceType;
		private String _active;
		private String _autoGenerated;
		private String _description;
		private String _payerType;
		private String _serviceCode;
		private UnitOfMeasure _unitOfMeasure;

		#endregion

		#region Properties

		[DataMember]
		public virtual CvxCode CvxCode
		{
			get { return _cvxCode; }
			set { _cvxCode = value; }
		}

		[DataMember]
		public virtual DateTime? EffectiveDate
		{
			get { return _effectiveDate; }
			set { _effectiveDate = value; }
		}

		[DataMember]
		public virtual DateTime? TerminationDate
		{
			get { return _terminationDate; }
			set { _terminationDate = value; }
		}

		[DataMember]
		public virtual Decimal? Units
		{
			get { return _units; }
			set { _units = value; }
		}

		[DataMember]
		public virtual ICollection<InsurancePayerService> InsurancePayerServices
		{
			get { return _insurancePayerServices; }
			set { _insurancePayerServices = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantImmunization> ParticipantImmunizations
		{
			get { return _participantImmunizations; }
			set { _participantImmunizations = value; }
		}

		[DataMember]
		public virtual ICollection<ServiceDetail> ServiceDetails
		{
			get { return _serviceDetails; }
			set { _serviceDetails = value; }
		}

		[DataMember]
		public virtual MvxCode MvxCode
		{
			get { return _mvxCode; }
			set { _mvxCode = value; }
		}

		[DataMember]
		public virtual NdcCode NdcCode
		{
			get { return _ndcCode; }
			set { _ndcCode = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant OrganizationParticipant
		{
			get { return _organizationParticipant; }
			set { _organizationParticipant = value; }
		}

		[DataMember]
		public virtual Provider Provider
		{
			get { return _provider; }
			set { _provider = value; }
		}

		[DataMember]
		public virtual ProviderLocation ProviderLocation
		{
			get { return _providerLocation; }
			set { _providerLocation = value; }
		}

		[DataMember]
		public virtual ProviderOrganizationalUnit ProviderOrganizationalUnit
		{
			get { return _providerOrganizationalUnit; }
			set { _providerOrganizationalUnit = value; }
		}

		[DataMember]
		public virtual ServiceType ServiceType
		{
			get { return _serviceType; }
			set { _serviceType = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String AutoGenerated
		{
			get { return _autoGenerated; }
			set { _autoGenerated = value; }
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String PayerType
		{
			get { return _payerType; }
			set { _payerType = value; }
		}

		[DataMember]
		public virtual String ServiceCode
		{
			get { return _serviceCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ServiceCode must not be blank or null.");
				else _serviceCode = value;
			}
		}

		[DataMember]
		public virtual UnitOfMeasure UnitOfMeasure
		{
			get { return _unitOfMeasure; }
			set { _unitOfMeasure = value; }
		}


		#endregion
	}
}
