using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ServiceDetail : Entity
	{
		#region Fields

		private CptCode _cptCode;
		private Decimal _quantity;
		private Decimal _unitCost;
		private IcdCode _icdCode;
		private ICollection<ParticipantImmunization> _participantImmunizations = new HashSet<ParticipantImmunization>();
		private ICollection<Transaction> _transactions = new HashSet<Transaction>();
		private Service _service;
		private String _active;
		private String _modifier1;
		private String _modifier2;
		private String _modifier3;
		private String _modifier4;
		private String _notes;
		private String _private;
		private UnitOfMeasure _unitOfMeasure;

		#endregion

		#region Properties

		[DataMember]
		public virtual CptCode CptCode
		{
			get { return _cptCode; }
			set { _cptCode = value; }
		}

		[DataMember]
		public virtual Decimal Quantity
		{
			get { return _quantity; }
			set { _quantity = value; }
		}

		[DataMember]
		public virtual Decimal UnitCost
		{
			get { return _unitCost; }
			set { _unitCost = value; }
		}

		[DataMember]
		public virtual IcdCode IcdCode
		{
			get { return _icdCode; }
			set { _icdCode = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantImmunization> ParticipantImmunizations
		{
			get { return _participantImmunizations; }
			set { _participantImmunizations = value; }
		}

		[DataMember]
		public virtual ICollection<Transaction> Transactions
		{
			get { return _transactions; }
			set { _transactions = value; }
		}

		[DataMember]
		public virtual Service Service
		{
			get { return _service; }
			set { _service = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Modifier1
		{
			get { return _modifier1; }
			set { _modifier1 = value; }
		}

		[DataMember]
		public virtual String Modifier2
		{
			get { return _modifier2; }
			set { _modifier2 = value; }
		}

		[DataMember]
		public virtual String Modifier3
		{
			get { return _modifier3; }
			set { _modifier3 = value; }
		}

		[DataMember]
		public virtual String Modifier4
		{
			get { return _modifier4; }
			set { _modifier4 = value; }
		}

		[DataMember]
		public virtual String Notes
		{
			get { return _notes; }
			set { _notes = value; }
		}

		[DataMember]
		public virtual String Private
		{
			get { return _private; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Private must not be blank or null.");
				else _private = value;
			}
		}

		[DataMember]
		public virtual UnitOfMeasure UnitOfMeasure
		{
			get { return _unitOfMeasure; }
			set { _unitOfMeasure = value; }
		}


		#endregion
	}
}
