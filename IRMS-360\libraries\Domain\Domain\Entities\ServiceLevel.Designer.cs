using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ServiceLevel : Entity
	{
		#region Fields

		private String _active;
		private String _description;
		private String _serviceLevelCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String ServiceLevelCode
		{
			get { return _serviceLevelCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ServiceLevelCode must not be blank or null.");
				else _serviceLevelCode = value;
			}
		}


		#endregion
	}
}
