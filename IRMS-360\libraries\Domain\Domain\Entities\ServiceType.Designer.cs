using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ServiceType : Entity
	{
		#region Fields

		private ICollection<ParticipantAppointment> _participantAppointments = new HashSet<ParticipantAppointment>();
		private ICollection<ParticipantEncounter> _participantEncounters = new HashSet<ParticipantEncounter>();
		private ICollection<Service> _services = new HashSet<Service>();
		private String _active;
		private String _description;
		private String _serviceTypeCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<ParticipantAppointment> ParticipantAppointments
		{
			get { return _participantAppointments; }
			set { _participantAppointments = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantEncounter> ParticipantEncounters
		{
			get { return _participantEncounters; }
			set { _participantEncounters = value; }
		}

		[DataMember]
		public virtual ICollection<Service> Services
		{
			get { return _services; }
			set { _services = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String ServiceTypeCode
		{
			get { return _serviceTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ServiceTypeCode must not be blank or null.");
				else _serviceTypeCode = value;
			}
		}


		#endregion
	}
}
