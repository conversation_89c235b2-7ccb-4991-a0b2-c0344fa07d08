using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Shift : Entity
	{
		#region Fields

		private Agency _agency;
		private CompanyLocationType _companyLocationType;
		private DateTime _endTime;
		private DateTime _startTime;
		private Int32? _breakDurationMinutes;
		private Int32? _lunchDurationMinutes;
		private ICollection<CompanyLocationTransactionType> _companyLocationTransactionTypes = new HashSet<CompanyLocationTransactionType>();
		private ICollection<OrganizationParticipant> _organizationParticipants = new HashSet<OrganizationParticipant>();
		private String _active;
		private String _description;
		private String _shiftCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual Agency Agency
		{
			get { return _agency; }
			set { _agency = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual DateTime EndTime
		{
			get { return _endTime; }
			set { _endTime = value; }
		}

		[DataMember]
		public virtual DateTime StartTime
		{
			get { return _startTime; }
			set { _startTime = value; }
		}

		[DataMember]
		public virtual Int32? BreakDurationMinutes
		{
			get { return _breakDurationMinutes; }
			set { _breakDurationMinutes = value; }
		}

		[DataMember]
		public virtual Int32? LunchDurationMinutes
		{
			get { return _lunchDurationMinutes; }
			set { _lunchDurationMinutes = value; }
		}

		[DataMember]
		public virtual ICollection<CompanyLocationTransactionType> CompanyLocationTransactionTypes
		{
			get { return _companyLocationTransactionTypes; }
			set { _companyLocationTransactionTypes = value; }
		}

		[DataMember]
		public virtual ICollection<OrganizationParticipant> OrganizationParticipants
		{
			get { return _organizationParticipants; }
			set { _organizationParticipants = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String ShiftCode
		{
			get { return _shiftCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ShiftCode must not be blank or null.");
				else _shiftCode = value;
			}
		}


		#endregion
	}
}
