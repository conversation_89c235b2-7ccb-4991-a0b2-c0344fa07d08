using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ShipFuelSurcharge : Entity
	{
		#region Fields

		private DateTime? _effectiveDate;
		private Decimal _fuelPrice;
		private String _active;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime? EffectiveDate
		{
			get { return _effectiveDate; }
			set { _effectiveDate = value; }
		}

		[DataMember]
		public virtual Decimal FuelPrice
		{
			get { return _fuelPrice; }
			set { _fuelPrice = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set { _active = value; }
		}


		#endregion
	}
}
