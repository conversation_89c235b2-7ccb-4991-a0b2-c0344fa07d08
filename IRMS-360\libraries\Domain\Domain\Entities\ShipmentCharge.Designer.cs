using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ShipmentCharge : Entity
	{
		#region Fields

		private BillingCode _billingCode;
		private Decimal _amount;
		private Decimal? _quantity;
		private ShipmentDetail _shipmentDetail;
		private ShipmentHeader _shipmentHeader;
		private String _comments;

		#endregion

		#region Properties

		[DataMember]
		public virtual BillingCode BillingCode
		{
			get { return _billingCode; }
			set { _billingCode = value; }
		}

		[DataMember]
		public virtual Decimal Amount
		{
			get { return _amount; }
			set { _amount = value; }
		}

		[DataMember]
		public virtual Decimal? Quantity
		{
			get { return _quantity; }
			set { _quantity = value; }
		}

		[DataMember]
		public virtual ShipmentDetail ShipmentDetail
		{
			get { return _shipmentDetail; }
			set { _shipmentDetail = value; }
		}

		[DataMember]
		public virtual ShipmentHeader ShipmentHeader
		{
			get { return _shipmentHeader; }
			set { _shipmentHeader = value; }
		}

		[DataMember]
		public virtual String Comments
		{
			get { return _comments; }
			set { _comments = value; }
		}


		#endregion
	}
}
