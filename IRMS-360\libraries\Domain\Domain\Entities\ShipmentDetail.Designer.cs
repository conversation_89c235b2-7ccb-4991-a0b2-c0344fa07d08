using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ShipmentDetail : Entity
	{
		#region Fields

		private CarrierService _carrierService;
		private CartonHeader _cartonHeader;
		private Comment _comment;
		private Decimal? _shippingCost;
		private Decimal? _weight;
		private Decimal _quantity;
		private InventoryItem _inventoryItem;
		private InventoryPick _inventoryPick;
		private ItemFulfillment _itemFulfillment;
		private LicensePlate _licensePlate;
		private ICollection<InvoiceDetail> _invoiceDetails = new HashSet<InvoiceDetail>();
		private ICollection<RecallItem> _recallItems = new HashSet<RecallItem>();
		private ICollection<ReturnDetail> _returnDetails = new HashSet<ReturnDetail>();
		private ICollection<ShipmentCharge> _shipmentCharges = new HashSet<ShipmentCharge>();
		private OrderDetail _orderDetail;
		private PreReceiptDetail _preReceiptDetail;
		private ShipmentHeader _shipmentHeader;
		private StatusCode _statusCode;
		private String _comments;
		private String _proNumber;

		#endregion

		#region Properties

		[DataMember]
		public virtual CarrierService CarrierService
		{
			get { return _carrierService; }
			set { _carrierService = value; }
		}

		[DataMember]
		public virtual CartonHeader CartonHeader
		{
			get { return _cartonHeader; }
			set { _cartonHeader = value; }
		}

		[DataMember]
		public virtual Comment Comment
		{
			get { return _comment; }
			set { _comment = value; }
		}

		[DataMember]
		public virtual Decimal? ShippingCost
		{
			get { return _shippingCost; }
			set { _shippingCost = value; }
		}

		[DataMember]
		public virtual Decimal? Weight
		{
			get { return _weight; }
			set { _weight = value; }
		}

		[DataMember]
		public virtual Decimal Quantity
		{
			get { return _quantity; }
			set { _quantity = value; }
		}

		[DataMember]
		public virtual InventoryItem InventoryItem
		{
			get { return _inventoryItem; }
			set { _inventoryItem = value; }
		}

		[DataMember]
		public virtual InventoryPick InventoryPick
		{
			get { return _inventoryPick; }
			set { _inventoryPick = value; }
		}

		[DataMember]
		public virtual ItemFulfillment ItemFulfillment
		{
			get { return _itemFulfillment; }
			set { _itemFulfillment = value; }
		}

		[DataMember]
		public virtual LicensePlate LicensePlate
		{
			get { return _licensePlate; }
			set { _licensePlate = value; }
		}

		[DataMember]
		public virtual ICollection<InvoiceDetail> InvoiceDetails
		{
			get { return _invoiceDetails; }
			set { _invoiceDetails = value; }
		}

		[DataMember]
		public virtual ICollection<RecallItem> RecallItems
		{
			get { return _recallItems; }
			set { _recallItems = value; }
		}

		[DataMember]
		public virtual ICollection<ReturnDetail> ReturnDetails
		{
			get { return _returnDetails; }
			set { _returnDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ShipmentCharge> ShipmentCharges
		{
			get { return _shipmentCharges; }
			set { _shipmentCharges = value; }
		}

		[DataMember]
		public virtual OrderDetail OrderDetail
		{
			get { return _orderDetail; }
			set { _orderDetail = value; }
		}

		[DataMember]
		public virtual PreReceiptDetail PreReceiptDetail
		{
			get { return _preReceiptDetail; }
			set { _preReceiptDetail = value; }
		}

		[DataMember]
		public virtual ShipmentHeader ShipmentHeader
		{
			get { return _shipmentHeader; }
			set { _shipmentHeader = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual String Comments
		{
			get { return _comments; }
			set { _comments = value; }
		}

		[DataMember]
		public virtual String ProNumber
		{
			get { return _proNumber; }
			set { _proNumber = value; }
		}


		#endregion
	}
}
