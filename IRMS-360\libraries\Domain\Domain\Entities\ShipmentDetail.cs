using System;
using System.Runtime.Serialization;
using Upp.Irms.Core;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class ShipmentDetail : Entity
	{
        #region Properties

        [DataMember]
        public virtual String BillOfLading { get; set; }
        [DataMember]
        public virtual String DockName { get; set; }

        [DataMember]
        public virtual String TrailerCode { get; set; }
        [DataMember]
        public virtual Item Item { get; set; }


        #endregion

        #region Report Properties
        public virtual int? LpnId { get; set; }
        public virtual int? ShipmentHeaderId { get; set; }        
        public virtual String CarrierCode { get; set; }
        public virtual String CarrierName { get; set; }
        public virtual String ShipmentComments { get; set; }
        public virtual String DockCode { get; set; }
        public virtual String FobDescription { get; set; }    
        public virtual String SealNumber { get; set; }
        public virtual String ManifestCode { get; set; }
        public virtual String ScacCode { get; set; }
        public virtual String ShipmentReferenceNumber { get; set; } 
    

        #endregion

        #region Constructor

        public ShipmentDetail()
		{
			//
		}

		#endregion

		#region Methods.Public

		public virtual void ChangeStatus(StatusCode status)
		{
			_dateModified = DateTime.Now;
			_statusCode = status;
			_userModified = Registry.Find<UserAccount>().UserName;
			//
			Repositories.Get<ShipmentDetail>().Update(this);
		}

        public virtual void UpdateProNumber(string proNumber)
        {
            _dateModified = DateTime.Now;
            _proNumber = proNumber;
            _userModified = Registry.Find<UserAccount>().UserName;
            //
            Repositories.Get<ShipmentDetail>().Update(this);
        }

		#endregion
	}
}
