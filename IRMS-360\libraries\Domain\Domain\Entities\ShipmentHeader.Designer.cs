using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ShipmentHeader : Entity
	{
		#region Fields

		private AgencyLocation _agencyLocation;
		private Carrier _carrier;
		private CarrierAppointment _carrierAppointment;
		private CarrierService _carrierService;
		private Comment _comment;
		private CompanyLocationType _companyLocationType;
		private DateTime? _printed;
		private Decimal? _shippingCost;
		private Dock _dock;
		private FobCode _fobCode;
		private LicensePlate _licensePlate;
		private ICollection<ShipmentCharge> _shipmentCharges = new HashSet<ShipmentCharge>();
		private ICollection<ShipmentDetail> _shipmentDetails = new HashSet<ShipmentDetail>();
		private ICollection<ShipmentLocation> _shipmentLocations = new HashSet<ShipmentLocation>();
		private ICollection<ShipmentReferenceCode> _shipmentReferenceCodes = new HashSet<ShipmentReferenceCode>();
		private ICollection<ShipmentStatus> _shipmentStatuses = new HashSet<ShipmentStatus>();
		private Location _location;
		private OrganizationParticipant _organizationParticipant;
		private RouteHeader _routeHeader;
		private ShipmentType _shipmentType;
		private StatusCode _statusCode;
		private String _billOfLading;
		private String _comments;
		private String _manifestCode;
		private String _proNumber;
		private String _referenceNumber;
		private String _sealNumber;
		private String _trailerCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual AgencyLocation AgencyLocation
		{
			get { return _agencyLocation; }
			set { _agencyLocation = value; }
		}

		[DataMember]
		public virtual Carrier Carrier
		{
			get { return _carrier; }
			set { _carrier = value; }
		}

		[DataMember]
		public virtual CarrierAppointment CarrierAppointment
		{
			get { return _carrierAppointment; }
			set { _carrierAppointment = value; }
		}

		[DataMember]
		public virtual CarrierService CarrierService
		{
			get { return _carrierService; }
			set { _carrierService = value; }
		}

		[DataMember]
		public virtual Comment Comment
		{
			get { return _comment; }
			set { _comment = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual DateTime? Printed
		{
			get { return _printed; }
			set { _printed = value; }
		}

		[DataMember]
		public virtual Decimal? ShippingCost
		{
			get { return _shippingCost; }
			set { _shippingCost = value; }
		}

		[DataMember]
		public virtual Dock Dock
		{
			get { return _dock; }
			set { _dock = value; }
		}

		[DataMember]
		public virtual FobCode FobCode
		{
			get { return _fobCode; }
			set { _fobCode = value; }
		}

		[DataMember]
		public virtual LicensePlate LicensePlate
		{
			get { return _licensePlate; }
			set { _licensePlate = value; }
		}

		[DataMember]
		public virtual ICollection<ShipmentCharge> ShipmentCharges
		{
			get { return _shipmentCharges; }
			set { _shipmentCharges = value; }
		}

		[DataMember]
		public virtual ICollection<ShipmentDetail> ShipmentDetails
		{
			get { return _shipmentDetails; }
			set { _shipmentDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ShipmentLocation> ShipmentLocations
		{
			get { return _shipmentLocations; }
			set { _shipmentLocations = value; }
		}

		[DataMember]
		public virtual ICollection<ShipmentReferenceCode> ShipmentReferenceCodes
		{
			get { return _shipmentReferenceCodes; }
			set { _shipmentReferenceCodes = value; }
		}

		[DataMember]
		public virtual ICollection<ShipmentStatus> ShipmentStatuses
		{
			get { return _shipmentStatuses; }
			set { _shipmentStatuses = value; }
		}

		[DataMember]
		public virtual Location Location
		{
			get { return _location; }
			set { _location = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant OrganizationParticipant
		{
			get { return _organizationParticipant; }
			set { _organizationParticipant = value; }
		}

		[DataMember]
		public virtual RouteHeader RouteHeader
		{
			get { return _routeHeader; }
			set { _routeHeader = value; }
		}

		[DataMember]
		public virtual ShipmentType ShipmentType
		{
			get { return _shipmentType; }
			set { _shipmentType = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual String BillOfLading
		{
			get { return _billOfLading; }
			set { _billOfLading = value; }
		}

		[DataMember]
		public virtual String Comments
		{
			get { return _comments; }
			set { _comments = value; }
		}

		[DataMember]
		public virtual String ManifestCode
		{
			get { return _manifestCode; }
			set { _manifestCode = value; }
		}

		[DataMember]
		public virtual String ProNumber
		{
			get { return _proNumber; }
			set { _proNumber = value; }
		}

		[DataMember]
		public virtual String ReferenceNumber
		{
			get { return _referenceNumber; }
			set { _referenceNumber = value; }
		}

		[DataMember]
		public virtual String SealNumber
		{
			get { return _sealNumber; }
			set { _sealNumber = value; }
		}

		[DataMember]
		public virtual String TrailerCode
		{
			get { return _trailerCode; }
			set { _trailerCode = value; }
		}


		#endregion
	}
}
