using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using NHibernate.Criterion;
using NHibernate.SqlCommand;
using NHibernate.Transform;

using Upp.Irms.Constants;
using Upp.Irms.Core;
using Upp.Shared.Utilities;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class ShipmentHeader : Entity
	{
		#region Properties

		[DataMember]
		public virtual StatusCode CurrentStatus { get; set; }
        public virtual string ShipIDShipperInfo { get; set; }
        public virtual string CHORDShipperInfo { get; set; }
        public virtual string CONFShipperInfo { get; set; }

        #endregion

        #region Report Properties

        public virtual int? CarrierId { get; set; }
        public virtual int? CompanyLocationTypeId { get; set; }
        public virtual int? DockId { get; set; }        
        public virtual string DockCode { get; set; }
        public virtual string ScacCode { get; set; }
        public virtual string ScheduledDepartureTime { get; set; } 

		#endregion

		#region Constructor

		public ShipmentHeader()
		{
			//
		}

		#endregion

		#region Methods.Public

		public virtual void ChangeStatus(StatusCode status)
		{
			ShipmentStatus created = Entity.Activate<ShipmentStatus>();
			created.Occurred = DateTime.Now;
			created.ShipmentHeader = this;
			created.StatusCode = status;
			//
			Repositories.Get<ShipmentStatus>().Add(created);
		}

        public virtual void GenerateBOLByItem()
        {
            ProjectionList projections = Projections.ProjectionList()
                        .Add(Projections.Property("od.Item"), "Item");
            DetachedCriteria criteria = DetachedCriteria.For<ShipmentDetail>()
                .CreateAlias("LicensePlate", "lp")
                    .CreateAlias("lp.InventoryPicks", "ip")
                        .CreateAlias("ip.OrderDetail", "od")
                .Add("ShipmentHeader", this)
                .SetProjection(Projections.Distinct(projections))
                .SetResultTransformer(Transformers.AliasToBean<ShipmentDetail>());
            IList<ShipmentDetail> shipments = Repositories.Get<ShipmentDetail>().List(criteria);
            //
            if (shipments != null && shipments.Count > 1)
            {
                this.BillOfLading = EntityCode.GetCurrentValue(EntityCodes.Bol);
                this.DateModified = DateTime.Now;
                this.UserModified = Registry.Find<UserAccount>().UserName;
                //
                Repositories.Get<ShipmentHeader>().Update(this);
            }
            else if (shipments != null && shipments.Count == 1)
            {
                criteria = DetachedCriteria.For<UdfItemValue>()
                    .Add("Item", shipments[0].Item)
                    .Add("UdfMetadataValue.Label", "BOL Number")
                    .SetMaxResults(1);
                UdfItemValue udfItemValue = Repositories.Get<UdfItemValue>().Retrieve(criteria);
                if (udfItemValue == null)
                {
                    this.BillOfLading = EntityCode.GetCurrentValue(EntityCodes.Bol);
                    this.DateModified = DateTime.Now;
                    this.UserModified = Registry.Find<UserAccount>().UserName;
                    //
                    Repositories.Get<ShipmentHeader>().Update(this);
                }
                else
                {
                    this.BillOfLading = udfItemValue.UserDefinedValue;
                    this.DateModified = DateTime.Now;
                    this.UserModified = Registry.Find<UserAccount>().UserName;
                    //
                    Repositories.Get<ShipmentHeader>().Update(this);
                    //
                    Int64 value;
                    if (Int64.TryParse(udfItemValue.UserDefinedValue, out value)) udfItemValue.UserDefinedValue = Converter.ToString(value + 1);
                    else throw new Exception("Item UDF value is not a valid numeric - " + udfItemValue.UserDefinedValue);
                    udfItemValue.DateModified = DateTime.Now;
                    udfItemValue.UserModified = Registry.Find<UserAccount>().UserName;
                    //
                    Repositories.Get<UdfItemValue>().Update(udfItemValue);
                }
            }
        }

        public virtual Boolean ValidateShipment()
        {
            Boolean valid = true;
            ProjectionList projections = Projections.ProjectionList()
                        .Add(Projections.Property("od.Item"), "Item");
            DetachedCriteria criteria = DetachedCriteria.For<ShipmentDetail>()
                .CreateAlias("LicensePlate", "lp")
                    .CreateAlias("lp.InventoryPicks", "ip")
                        .CreateAlias("ip.OrderDetail", "od")
                .Add("ShipmentHeader", this)
                .SetProjection(Projections.Distinct(projections))
                .SetResultTransformer(Transformers.AliasToBean<ShipmentDetail>());
            IList<ShipmentDetail> shipments = Repositories.Get<ShipmentDetail>().List(criteria);
            //
            if (shipments != null && shipments.Count > 1) valid = false;
            else if (shipments != null && shipments.Count == 1)
            {
                criteria = DetachedCriteria.For<UdfItemValue>()
                    .Add("Item", shipments[0].Item)
                    .Add("UdfMetadataValue.Label", "BOL Number")
                    .SetMaxResults(1);
                UdfItemValue udfItemValue = Repositories.Get<UdfItemValue>().Retrieve(criteria);
                if (udfItemValue == null) valid = false;
            }
            //
            return valid;
        }

		#endregion

		#region Methods.Static

		public static ShipmentHeader Create(Dock dock)
		{
			dock.Carrier.IncrementManifest();
			//
			ShipmentHeader entity = Entity.Activate<ShipmentHeader>();
			entity.Carrier = dock.Carrier;
			entity.CompanyLocationType = Registry.Find<CompanyLocationType>();
			entity.Dock = dock;
			entity.ManifestCode = String.Format("{0}-{1}", dock.Carrier.CarrierCode, dock.Carrier.Manifest);
            entity.SealNumber = dock.SealNumber;
			entity.TrailerCode = dock.TrailerCode;
			//
			return entity;
		}

		#endregion
	}
}
