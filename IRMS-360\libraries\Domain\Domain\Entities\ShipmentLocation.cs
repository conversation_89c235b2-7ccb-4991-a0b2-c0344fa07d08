using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class ShipmentLocation : Entity
	{
		#region Constructor

        #region Properties
        [DataMember]
        public virtual int? ShipmentHeaderId
        {
            get;
            set;
        }

        #endregion
        #region Report Properties

        [DataMember]
        public virtual string LocationTypeCode
        {
            get;
            set;
        }

        #endregion
		public ShipmentLocation()
		{
			//
		}

		#endregion
	}
}
