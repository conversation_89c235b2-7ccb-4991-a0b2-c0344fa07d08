using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class ShipmentReferenceCode : Entity
	{
		#region Constructor

		public ShipmentReferenceCode()
		{
			//
		}

		#endregion
        #region Properties.Reports
        public virtual Int32? ShipmentDetailId { get; set; }
        public virtual Int32? ShipmentHeaderId { get; set; }
        public virtual String ReferenceCodeCode { get; set; }
        #endregion
	}
}
