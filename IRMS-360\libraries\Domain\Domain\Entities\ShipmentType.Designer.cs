using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ShipmentType : Entity
	{
		#region Fields

		private ICollection<ShipmentHeader> _shipmentHeaders = new HashSet<ShipmentHeader>();
		private String _active;
		private String _description;
		private String _shipmentTypeCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<ShipmentHeader> ShipmentHeaders
		{
			get { return _shipmentHeaders; }
			set { _shipmentHeaders = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String ShipmentTypeCode
		{
			get { return _shipmentTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ShipmentTypeCode must not be blank or null.");
				else _shipmentTypeCode = value;
			}
		}


		#endregion
	}
}
