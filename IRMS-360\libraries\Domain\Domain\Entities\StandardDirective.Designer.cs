using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class StandardDirective : Entity
	{
		#region Fields

		private ICollection<QuestionnaireAnswer> _questionnaireAnswers = new HashSet<QuestionnaireAnswer>();
		private ICollection<QuestionnaireDirective> _questionnaireDirectives = new HashSet<QuestionnaireDirective>();
		private String _active;
		private String _description;
		private String _standardDirectiveCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<QuestionnaireAnswer> QuestionnaireAnswers
		{
			get { return _questionnaireAnswers; }
			set { _questionnaireAnswers = value; }
		}

		[DataMember]
		public virtual ICollection<QuestionnaireDirective> QuestionnaireDirectives
		{
			get { return _questionnaireDirectives; }
			set { _questionnaireDirectives = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String StandardDirectiveCode
		{
			get { return _standardDirectiveCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("StandardDirectiveCode must not be blank or null.");
				else _standardDirectiveCode = value;
			}
		}


		#endregion
	}
}
