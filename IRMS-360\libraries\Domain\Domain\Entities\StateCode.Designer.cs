using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class StateCode : Entity
	{
		#region Fields

		private CountryCode _countryCode;
		private ICollection<CountyCode> _countyCodes = new HashSet<CountyCode>();
		private ICollection<FemaRegionState> _femaRegionStates = new HashSet<FemaRegionState>();
		private ICollection<InterfaceCommunication> _interfaceCommunications = new HashSet<InterfaceCommunication>();
		private ICollection<InterfaceDetailDefault> _interfaceDetailDefaults = new HashSet<InterfaceDetailDefault>();
		private ICollection<ZipCode> _zipCodes = new HashSet<ZipCode>();
		private String _active;
		private String _code;
		private String _description;
		private String _fipsStateCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual CountryCode CountryCode
		{
			get { return _countryCode; }
			set { _countryCode = value; }
		}

		[DataMember]
		public virtual ICollection<CountyCode> CountyCodes
		{
			get { return _countyCodes; }
			set { _countyCodes = value; }
		}

		[DataMember]
		public virtual ICollection<FemaRegionState> FemaRegionStates
		{
			get { return _femaRegionStates; }
			set { _femaRegionStates = value; }
		}

		[DataMember]
		public virtual ICollection<InterfaceCommunication> InterfaceCommunications
		{
			get { return _interfaceCommunications; }
			set { _interfaceCommunications = value; }
		}

		[DataMember]
		public virtual ICollection<InterfaceDetailDefault> InterfaceDetailDefaults
		{
			get { return _interfaceDetailDefaults; }
			set { _interfaceDetailDefaults = value; }
		}

		[DataMember]
		public virtual ICollection<ZipCode> ZipCodes
		{
			get { return _zipCodes; }
			set { _zipCodes = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Code
		{
			get { return _code; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Code must not be blank or null.");
				else _code = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String FipsStateCode
		{
			get { return _fipsStateCode; }
			set { _fipsStateCode = value; }
		}


		#endregion
	}
}
