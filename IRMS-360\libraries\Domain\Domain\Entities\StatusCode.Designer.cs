using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class StatusCode : Entity
	{
		#region Fields

		private FunctionalAreaCode _functionalAreaCode;
		private Int32? _sortOrder;
		private ICollection<Alert> _alerts = new HashSet<Alert>();
		private ICollection<CarrierAppointment> _carrierAppointments = new HashSet<CarrierAppointment>();
		private ICollection<CartonDetail> _cartonDetails = new HashSet<CartonDetail>();
		private ICollection<CartonHeader> _cartonHeaders = new HashSet<CartonHeader>();
		private ICollection<EligibilityRequest> _eligibilityRequests = new HashSet<EligibilityRequest>();
		private ICollection<EraProviderReasonCode> _eraProviderReasonCodes = new HashSet<EraProviderReasonCode>();
		private ICollection<EraReasonCode> _eraReasonCodes = new HashSet<EraReasonCode>();
		private ICollection<InspectionHeader> _inspectionHeaders = new HashSet<InspectionHeader>();
		private ICollection<InterfaceHeader> _interfaceHeaders = new HashSet<InterfaceHeader>();
		private ICollection<InventoryItemDetail> _inventoryItemDetails = new HashSet<InventoryItemDetail>();
		private ICollection<InventoryPick> _inventoryPicks = new HashSet<InventoryPick>();
		private ICollection<InventoryTask> _inventoryTasks = new HashSet<InventoryTask>();
		private ICollection<InvoiceBatch> _invoiceBatches = new HashSet<InvoiceBatch>();
		private ICollection<ItemFulfillment> _itemFulfillments = new HashSet<ItemFulfillment>();
		private ICollection<ItemHistory> _itemHistories = new HashSet<ItemHistory>();
		private ICollection<MaintenanceRequest> _maintenanceRequests = new HashSet<MaintenanceRequest>();
		private ICollection<OrderHeader> _orderHeaders = new HashSet<OrderHeader>();
		private ICollection<Participant> _participants = new HashSet<Participant>();
		private ICollection<ParticipantAppointment> _participantAppointments = new HashSet<ParticipantAppointment>();
		private ICollection<ParticipantEncounterMessage> _participantEncounterMessages = new HashSet<ParticipantEncounterMessage>();
		private ICollection<PreReceiptCarton> _preReceiptCartons = new HashSet<PreReceiptCarton>();
		private ICollection<PreReceiptHeader> _preReceiptHeaders = new HashSet<PreReceiptHeader>();
		private ICollection<PurchaseOrderDetail> _purchaseOrderDetails = new HashSet<PurchaseOrderDetail>();
		private ICollection<Recall> _recalls = new HashSet<Recall>();
		private ICollection<ReceiptHeader> _receiptHeaders = new HashSet<ReceiptHeader>();
		private ICollection<ReportRequestDestination> _reportRequestDestinations = new HashSet<ReportRequestDestination>();
		private ICollection<RequisitionDetail> _requisitionDetails = new HashSet<RequisitionDetail>();
		private ICollection<ReturnDetail> _returnDetails = new HashSet<ReturnDetail>();
		private ICollection<ReturnStatus> _returnStatuses = new HashSet<ReturnStatus>();
		private ICollection<ShipmentDetail> _shipmentDetails = new HashSet<ShipmentDetail>();
		private ICollection<ShipmentHeader> _shipmentHeaders = new HashSet<ShipmentHeader>();
		private ICollection<StatusReasonCode> _statusReasonCodes = new HashSet<StatusReasonCode>();
		private ICollection<Task> _tasks = new HashSet<Task>();
		private ICollection<Transaction> _transactions = new HashSet<Transaction>();
		private ICollection<WorkOrderDetail> _workOrderDetails = new HashSet<WorkOrderDetail>();
		private ICollection<WorkOrderHeader> _workOrderHeaders = new HashSet<WorkOrderHeader>();
		private String _active;
		private String _code;
		private String _description;

		#endregion

		#region Properties

		[DataMember]
		public virtual FunctionalAreaCode FunctionalAreaCode
		{
			get { return _functionalAreaCode; }
			set { _functionalAreaCode = value; }
		}

		[DataMember]
		public virtual Int32? SortOrder
		{
			get { return _sortOrder; }
			set { _sortOrder = value; }
		}

		[DataMember]
		public virtual ICollection<Alert> Alerts
		{
			get { return _alerts; }
			set { _alerts = value; }
		}

		[DataMember]
		public virtual ICollection<CarrierAppointment> CarrierAppointments
		{
			get { return _carrierAppointments; }
			set { _carrierAppointments = value; }
		}

		[DataMember]
		public virtual ICollection<CartonDetail> CartonDetails
		{
			get { return _cartonDetails; }
			set { _cartonDetails = value; }
		}

		[DataMember]
		public virtual ICollection<CartonHeader> CartonHeaders
		{
			get { return _cartonHeaders; }
			set { _cartonHeaders = value; }
		}

		[DataMember]		
		public virtual ICollection<EligibilityRequest> EligibilityRequests
		{
			get { return _eligibilityRequests; }
			set { _eligibilityRequests = value; }
		}

		[DataMember]
		public virtual ICollection<EraProviderReasonCode> EraProviderReasonCodes
		{
			get { return _eraProviderReasonCodes; }
			set { _eraProviderReasonCodes = value; }
		}

		[DataMember]
		public virtual ICollection<EraReasonCode> EraReasonCodes
		{
			get { return _eraReasonCodes; }
			set { _eraReasonCodes = value; }
		}

		[DataMember]
		public virtual ICollection<InspectionHeader> InspectionHeaders
		{
			get { return _inspectionHeaders; }
			set { _inspectionHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<InterfaceHeader> InterfaceHeaders
		{
			get { return _interfaceHeaders; }
			set { _interfaceHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryItemDetail> InventoryItemDetails
		{
			get { return _inventoryItemDetails; }
			set { _inventoryItemDetails = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryPick> InventoryPicks
		{
			get { return _inventoryPicks; }
			set { _inventoryPicks = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryTask> InventoryTasks
		{
			get { return _inventoryTasks; }
			set { _inventoryTasks = value; }
		}

		[DataMember]
		public virtual ICollection<InvoiceBatch> InvoiceBatches
		{
			get { return _invoiceBatches; }
			set { _invoiceBatches = value; }
		}

		[DataMember]
		public virtual ICollection<ItemFulfillment> ItemFulfillments
		{
			get { return _itemFulfillments; }
			set { _itemFulfillments = value; }
		}

		[DataMember]
		public virtual ICollection<ItemHistory> ItemHistories
		{
			get { return _itemHistories; }
			set { _itemHistories = value; }
		}

		[DataMember]
		public virtual ICollection<MaintenanceRequest> MaintenanceRequests
		{
			get { return _maintenanceRequests; }
			set { _maintenanceRequests = value; }
		}

		[DataMember]
		public virtual ICollection<OrderHeader> OrderHeaders
		{
			get { return _orderHeaders; }
			set { _orderHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<Participant> Participants
		{
			get { return _participants; }
			set { _participants = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantAppointment> ParticipantAppointments
		{
			get { return _participantAppointments; }
			set { _participantAppointments = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantEncounterMessage> ParticipantEncounterMessages
		{
			get { return _participantEncounterMessages; }
			set { _participantEncounterMessages = value; }
		}

		[DataMember]
		public virtual ICollection<PreReceiptCarton> PreReceiptCartons
		{
			get { return _preReceiptCartons; }
			set { _preReceiptCartons = value; }
		}

		[DataMember]
		public virtual ICollection<PreReceiptHeader> PreReceiptHeaders
		{
			get { return _preReceiptHeaders; }
			set { _preReceiptHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<PurchaseOrderDetail> PurchaseOrderDetails
		{
			get { return _purchaseOrderDetails; }
			set { _purchaseOrderDetails = value; }
		}

		[DataMember]
		public virtual ICollection<Recall> Recalls
		{
			get { return _recalls; }
			set { _recalls = value; }
		}

		[DataMember]
		public virtual ICollection<ReceiptHeader> ReceiptHeaders
		{
			get { return _receiptHeaders; }
			set { _receiptHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<ReportRequestDestination> ReportRequestDestinations
		{
			get { return _reportRequestDestinations; }
			set { _reportRequestDestinations = value; }
		}

		[DataMember]
		public virtual ICollection<RequisitionDetail> RequisitionDetails
		{
			get { return _requisitionDetails; }
			set { _requisitionDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ReturnDetail> ReturnDetails
		{
			get { return _returnDetails; }
			set { _returnDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ReturnStatus> ReturnStatuses
		{
			get { return _returnStatuses; }
			set { _returnStatuses = value; }
		}

		[DataMember]
		public virtual ICollection<ShipmentDetail> ShipmentDetails
		{
			get { return _shipmentDetails; }
			set { _shipmentDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ShipmentHeader> ShipmentHeaders
		{
			get { return _shipmentHeaders; }
			set { _shipmentHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<StatusReasonCode> StatusReasonCodes
		{
			get { return _statusReasonCodes; }
			set { _statusReasonCodes = value; }
		}

		[DataMember]
		public virtual ICollection<Task> Tasks
		{
			get { return _tasks; }
			set { _tasks = value; }
		}

		[DataMember]
		public virtual ICollection<Transaction> Transactions
		{
			get { return _transactions; }
			set { _transactions = value; }
		}

		[DataMember]
		public virtual ICollection<WorkOrderDetail> WorkOrderDetails
		{
			get { return _workOrderDetails; }
			set { _workOrderDetails = value; }
		}

		[DataMember]
		public virtual ICollection<WorkOrderHeader> WorkOrderHeaders
		{
			get { return _workOrderHeaders; }
			set { _workOrderHeaders = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Code
		{
			get { return _code; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Code must not be blank or null.");
				else _code = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}


		#endregion
	}
}
