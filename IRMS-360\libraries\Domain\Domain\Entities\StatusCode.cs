using System;
using System.Runtime.Serialization;

using NHibernate.Criterion;

using Upp.Irms.Constants;
using Upp.Irms.Core;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class StatusCode : Entity
	{
		#region Properties

		[DataMember]
		public virtual Boolean HasReasons { get; set; }
		[DataMember]
		public virtual LicensePlate LicensePlate { get; set; }
		[DataMember]
		public virtual StatusReasonCode StatusReasonCode { get; set; }

        public static StatusCode openIntegrationStatusCode { get; set; }

		#endregion

        #region Report Properties

        public virtual string FunctionalAreaCodeCode { get; set; }

        #endregion Report Properties


        #region Constructor

        public StatusCode()
		{
			//
		}

		#endregion

		#region Methods.Public

		public virtual void CheckReasonCodes()
		{
			DetachedCriteria criteria = DetachedCriteria.For<StatusReasonCode>()
				.Add(Expression.Eq("Active", "A"))
				.Add(Expression.Eq("StatusCode", this))
				.SetProjection(Projections.Count("Id"));
			this.HasReasons = (Repositories.Get<StatusReasonCode>().Function<Int32>(criteria) > 0);
		}

		#endregion

		#region Methods.Public.Receive

		public virtual void SuggestLpn(CompanyLocationZone zone, Location location)
		{
			if (zone == null) return;
			if (!location.IsPallet) return;
			/* Suggest LPN during receiving? */
			Boolean? suggest = BusinessRule.RetrieveBoolean("1053");
			if (!suggest.HasValue || !suggest.Value) return;
			//
			StatusCode complete = Entity.Retrieve<StatusCode>(LicensePlateStatuses.Complete);
			StatusCode staged = Entity.Retrieve<StatusCode>(LicensePlateStatuses.Staged);
			DetachedCriteria occurred = DetachedCriteria.For<LicensePlateStatus>("olps")
				.Add(Expression.EqProperty("plp.Id", "olps.LicensePlate.Id"))
				.SetProjection(Projections.Max("Occurred"));
			DetachedCriteria count = DetachedCriteria.For<LicensePlateStatus>("clps")
				.Add(Subqueries.PropertyEq("Occurred", occurred))
				.Add(Expression.Or(Expression.Eq("StatusCode", complete), Expression.Eq("StatusCode", staged)))
				.SetProjection(Projections.Count("Id"));
			DetachedCriteria criteria = DetachedCriteria.For<InventoryItem>()
				.CreateAlias("Item", "i")
				.CreateAlias("LicensePlate", "lp")
					.CreateAlias("lp.ParentLicensePlate", "plp")
				.Add(Expression.Eq("i.CompanyLocationZone", zone))
				.Add(Expression.Eq("Location", location))
				.Add(Expression.Gt("Quantity", 0M))
				.Add(Expression.Eq("StatusCode", this))
				.Add(Subqueries.Eq(0, count))
				.SetMaxResults(1);
			InventoryItem inventory = Repositories.Get<InventoryItem>().Retrieve(criteria);
			if (inventory != null) this.LicensePlate = inventory.LicensePlate.ParentLicensePlate;
		}

        public static StatusCode GetOpenIntegrationStatusCode()
        {
            try
            {
                if (openIntegrationStatusCode != null) return openIntegrationStatusCode;
                openIntegrationStatusCode = Entity.Retrieve<StatusCode>(IntegrationStatuses.Open);
                return openIntegrationStatusCode;
            }
            catch(Exception ex)
            {
                return null;
            }
        }
        #endregion
    }
}
