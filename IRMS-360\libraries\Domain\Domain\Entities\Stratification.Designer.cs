using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Stratification : Entity
	{
		#region Fields

		private ICollection<CompanyStratification> _companyStratifications = new HashSet<CompanyStratification>();
		private ICollection<Item> _items = new HashSet<Item>();
		private ICollection<Location> _locations = new HashSet<Location>();
		private String _active;
		private String _description;
		private String _stratificationCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<CompanyStratification> CompanyStratifications
		{
			get { return _companyStratifications; }
			set { _companyStratifications = value; }
		}

		[DataMember]
		public virtual ICollection<Item> Items
		{
			get { return _items; }
			set { _items = value; }
		}

		[DataMember]
		public virtual ICollection<Location> Locations
		{
			get { return _locations; }
			set { _locations = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String StratificationCode
		{
			get { return _stratificationCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("StratificationCode must not be blank or null.");
				else _stratificationCode = value;
			}
		}


		#endregion
	}
}
