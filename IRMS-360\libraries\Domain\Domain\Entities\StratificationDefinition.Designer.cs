using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class StratificationDefinition : Entity
	{
		#region Fields

		private CompanyLocationType _companyLocationType;
		private DateTime? _stratificationLastCalculation;
		private Frequency _frequency;
		private Decimal _timePeriodAmount;
		private String _active;
		private String _enableStratification;
		private String _includeInactiveItems;
		private String _stratificationType;
		private String _updateItemStratification;

		#endregion

		#region Properties

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual DateTime? StratificationLastCalculation
		{
			get { return _stratificationLastCalculation; }
			set { _stratificationLastCalculation = value; }
		}

		[DataMember]
		public virtual Frequency Frequency
		{
			get { return _frequency; }
			set { _frequency = value; }
		}

		[DataMember]
		public virtual Decimal TimePeriodAmount
		{
			get { return _timePeriodAmount; }
			set { _timePeriodAmount = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String EnableStratification
		{
			get { return _enableStratification; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("EnableStratification must not be blank or null.");
				else _enableStratification = value;
			}
		}

		[DataMember]
		public virtual String IncludeInactiveItems
		{
			get { return _includeInactiveItems; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("IncludeInactiveItems must not be blank or null.");
				else _includeInactiveItems = value;
			}
		}

		[DataMember]
		public virtual String StratificationType
		{
			get { return _stratificationType; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("StratificationType must not be blank or null.");
				else _stratificationType = value;
			}
		}

		[DataMember]
		public virtual String UpdateItemStratification
		{
			get { return _updateItemStratification; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("UpdateItemStratification must not be blank or null.");
				else _updateItemStratification = value;
			}
		}


		#endregion
	}
}
