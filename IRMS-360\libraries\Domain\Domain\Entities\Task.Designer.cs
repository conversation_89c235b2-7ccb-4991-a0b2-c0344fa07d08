using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Task : Entity
	{
		#region Fields

		private Agency _agency;
		private Alert _alert;
		private AlertDefinition _alertDefinition;
		private CompanyLocationType _companyLocationType;
		private DateTime? _completed;
		private DateTime? _expectedCompletion;
		private DateTime? _expectedStart;
		private DateTime? _nextExecution;
		private DateTime? _requested;
		private DateTime? _started;
		private Job _job;
		private ICollection<InventoryTask> _inventoryTasks = new HashSet<InventoryTask>();
		private ICollection<ItemTransaction> _itemTransactions = new HashSet<ItemTransaction>();
		private ICollection<JobExecution> _jobExecutions = new HashSet<JobExecution>();
		private ICollection<Task> _childTasks = new HashSet<Task>();
		private ICollection<TaskAssignment> _taskAssignments = new HashSet<TaskAssignment>();
		private ICollection<Wave> _waves = new HashSet<Wave>();
		private Provider _provider;
		private ReportRequestHeader _reportRequestHeader;
		private StatusCode _statusCode;
		private String _taskCode;
		private Task _parentTask;
		private TransactionType _transactionType;

		#endregion

		#region Properties

		[DataMember]
		public virtual Agency Agency
		{
			get { return _agency; }
			set { _agency = value; }
		}

		[DataMember]
		public virtual Alert Alert
		{
			get { return _alert; }
			set { _alert = value; }
		}

		[DataMember]
		public virtual AlertDefinition AlertDefinition
		{
			get { return _alertDefinition; }
			set { _alertDefinition = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual DateTime? Completed
		{
			get { return _completed; }
			set { _completed = value; }
		}

		[DataMember]
		public virtual DateTime? ExpectedCompletion
		{
			get { return _expectedCompletion; }
			set { _expectedCompletion = value; }
		}

		[DataMember]
		public virtual DateTime? ExpectedStart
		{
			get { return _expectedStart; }
			set { _expectedStart = value; }
		}

		[DataMember]
		public virtual DateTime? NextExecution
		{
			get { return _nextExecution; }
			set { _nextExecution = value; }
		}

		[DataMember]
		public virtual DateTime? Requested
		{
			get { return _requested; }
			set { _requested = value; }
		}

		[DataMember]
		public virtual DateTime? Started
		{
			get { return _started; }
			set { _started = value; }
		}

		[DataMember]
		public virtual Job Job
		{
			get { return _job; }
			set { _job = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryTask> InventoryTasks
		{
			get { return _inventoryTasks; }
			set { _inventoryTasks = value; }
		}

		[DataMember]
		public virtual ICollection<ItemTransaction> ItemTransactions
		{
			get { return _itemTransactions; }
			set { _itemTransactions = value; }
		}

		[DataMember]
		public virtual ICollection<JobExecution> JobExecutions
		{
			get { return _jobExecutions; }
			set { _jobExecutions = value; }
		}

		[DataMember]
		public virtual ICollection<Task> ChildTasks
		{
			get { return _childTasks; }
			set { _childTasks = value; }
		}

		[DataMember]
		public virtual ICollection<TaskAssignment> TaskAssignments
		{
			get { return _taskAssignments; }
			set { _taskAssignments = value; }
		}

		[DataMember]
		public virtual ICollection<Wave> Waves
		{
			get { return _waves; }
			set { _waves = value; }
		}

		[DataMember]
		public virtual Provider Provider
		{
			get { return _provider; }
			set { _provider = value; }
		}
		[DataMember]
		public virtual ReportRequestHeader ReportRequestHeader
		{
			get { return _reportRequestHeader; }
			set { _reportRequestHeader = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual String TaskCode
		{
			get { return _taskCode; }
			set { _taskCode = value; }
		}

		[DataMember]
		public virtual Task ParentTask
		{
			get { return _parentTask; }
			set { _parentTask = value; }
		}

		[DataMember]
		public virtual TransactionType TransactionType
		{
			get { return _transactionType; }
			set { _transactionType = value; }
		}


		#endregion
	}
}
