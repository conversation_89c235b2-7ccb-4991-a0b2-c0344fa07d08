using System;
using System.Linq;
using System.Collections.Generic;
using System.Runtime.Serialization;

using NHibernate.Criterion;
using NHibernate.SqlCommand;

using Upp.Irms.Constants;
using Upp.Irms.Core;
using Upp.Shared.Utilities;

namespace Upp.Irms.Domain
{
    [DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
    public partial class Task : Entity
    {
        #region Properties

        [DataMember]
        public virtual Boolean HasDiscrepancies { get; set; }
        [DataMember]
        public virtual InventoryItem InventoryItem { get; set; }
        [DataMember]
        public virtual Item Item { get; set; }
        [DataMember]
        public virtual Location Location { get; set; }
        [DataMember]
        public virtual OrganizationParticipant OrganizationParticipant { get; set; }
        [DataMember]
        public virtual PrinterTypePrinter PrinterTypePrinter { get; set; }
        [DataMember]
        public virtual String Description { get; set; }
        [DataMember]
        public virtual String ModuleName { get; set; }
        [DataMember]
        public virtual String NetworkUsername { get; set; }
        [DataMember]
        public virtual String NetworkPassword { get; set; }
        [DataMember]
        public virtual String StatusCodeCode { get; set; }
        [DataMember]
        public virtual String BartenderXMLPath { get; set; }
        [DataMember]
        public virtual IList<String> CustomerPackingList { get; set; }

        #endregion

        #region Constructor

        public Task()
        {
            //
        }

        #endregion

        #region Methods.Private

        private void Schedule()
        {
            //
        }

        private void setNextExecutionDateTime()
        {
            _started = DateTime.Now;
            _completed = null;
            int hours = DateTime.Now.Hour;
            int minutes = DateTime.Now.Minute;
            DateTime nextExecutionDate = DateTime.Now;
            string frequencyCode = _reportRequestHeader.ReportSchedule.Frequency.FrequencyCode;

            if (!String.IsNullOrEmpty(_reportRequestHeader.ReportSchedule.TimeOfDay))
            {
                string[] timeOfDay = _reportRequestHeader.ReportSchedule.TimeOfDay.Split(':');
                hours = Converter.ToInt32(timeOfDay[0]);
                minutes = Converter.ToInt32(timeOfDay[1]);
            }

            switch (frequencyCode)
            {
                case "D":
                    if (_reportRequestHeader.ReportSchedule.Frequency.Days.HasValue)
                        nextExecutionDate = DateTime.Now.AddDays(_reportRequestHeader.ReportSchedule.Frequency.Days.Value);
                    break;
                case "W":
                    int weekDay = (int)DateTime.Now.DayOfWeek;
                    List<int> weekDays = new List<int>();
                    if (_reportRequestHeader.ReportSchedule.Sunday == "Y")
                        weekDays.Add(0);
                    if (_reportRequestHeader.ReportSchedule.Monday == "Y")
                        weekDays.Add(1);
                    if (_reportRequestHeader.ReportSchedule.Tuesday == "Y")
                        weekDays.Add(2);
                    if (_reportRequestHeader.ReportSchedule.Wednesday == "Y")
                        weekDays.Add(3);
                    if (_reportRequestHeader.ReportSchedule.Thursday == "Y")
                        weekDays.Add(4);
                    if (_reportRequestHeader.ReportSchedule.Friday == "Y")
                        weekDays.Add(5);
                    if (_reportRequestHeader.ReportSchedule.Saturday == "Y")
                        weekDays.Add(6);

                    int nextWeek = weekDay;

                    if (weekDays != null && weekDays.Count == 1)
                    {
                        nextWeek = weekDays[0];
                    }
                    else if (weekDays != null && weekDays.Count > 1)
                    {
                        int? nextWeekDay;
                        nextWeekDay = weekDays.Where(x => x > weekDay).FirstOrDefault();
                        if (nextWeekDay.HasValue)
                        {
                            nextWeek = nextWeekDay.Value;
                        }
                        else
                        {
                            nextWeek = weekDays.OrderBy(x => x).FirstOrDefault();
                        }
                    }
                    if (weekDay == nextWeek)
                        nextExecutionDate = DateTime.Now.AddDays(7);
                    else if (weekDay < nextWeek)
                        nextExecutionDate = DateTime.Now.AddDays(nextWeek - weekDay);
                    else if (weekDay > nextWeek)
                        nextExecutionDate = DateTime.Now.AddDays((7 - weekDay) + nextWeek);
                    break;
                case "M":
                    if (_reportRequestHeader.ReportSchedule.DayOfMonth.HasValue)
                        if (DateTime.Now.Day == _reportRequestHeader.ReportSchedule.DayOfMonth.Value)
                        {
                            nextExecutionDate = DateTime.Now.AddMonths(1);
                        }
                        else if (DateTime.Now.Day < _reportRequestHeader.ReportSchedule.DayOfMonth.Value)
                        {
                            nextExecutionDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, _reportRequestHeader.ReportSchedule.DayOfMonth.Value);
                        }
                        else
                        {
                            nextExecutionDate = new DateTime(DateTime.Now.AddMonths(1).Year, DateTime.Now.AddMonths(1).AddMonths(1).Month, _reportRequestHeader.ReportSchedule.DayOfMonth.Value);
                        }
                    break;
                default:
                    nextExecutionDate = _reportRequestHeader.ReportSchedule.RunFromDatetime.Value;
                    break;
            }

            if (nextExecutionDate.Date > DateTime.Now)
                _nextExecution = new DateTime(nextExecutionDate.Year, nextExecutionDate.Month, nextExecutionDate.Day, hours, minutes, 0);
            else
                _nextExecution = null;
        }
        #endregion

        #region Methods.Public

        public virtual void Activate()
        {
            _completed = null;
            _dateModified = DateTime.Now;
            _started = DateTime.Now;
            _statusCode = Entity.Retrieve<StatusCode>(TaskStatuses.Active);
            _userModified = Registry.Find<UserAccount>().UserName;
            //
            Repositories.Get<Task>().Update(this);
        }

        public virtual void Open()
        {
            _dateModified = DateTime.Now;
            _started = DateTime.Now;
            _statusCode = Entity.Retrieve<StatusCode>(TaskStatuses.Open);
            _userModified = Registry.Find<UserAccount>().UserName;
            //
            Repositories.Get<Task>().Update(this);
        }

        public virtual void Complete()
        {
            _dateModified = DateTime.Now;
            _started = DateTime.Now;
            _statusCode = Entity.Retrieve<StatusCode>(TaskStatuses.Complete);
            _userModified = Registry.Find<UserAccount>().UserName;
            //
            Repositories.Get<Task>().Update(this);
        }

        public virtual void Fail()
        {
            _dateModified = DateTime.Now;
            _started = DateTime.Now;
            _statusCode = Entity.Retrieve<StatusCode>(TaskStatuses.Error);
            _userModified = Registry.Find<UserAccount>().UserName;
            //
            Repositories.Get<Task>().Update(this);
        }

        public virtual void UpdateStatus()
        {
            if (_alert != null)
            {
                DetachedCriteria criteria = DetachedCriteria.For<StatusCode>()
                    .Add("Active", "A")
                    .Add("FunctionalAreaCode.Code", CodeValue.GetCode(FunctionalAreas.Task))
                    .Add("Code", _alert.StatusCode.Code);
                //
                _dateModified = DateTime.Now;
                _statusCode = Repositories.Get<StatusCode>().Retrieve(criteria);
                if (_statusCode != null && CodeValue.GetCode(TaskStatuses.Complete).Equals(_statusCode.Code)) _completed = DateTime.Now;
                _userModified = Registry.Find<UserAccount>().UserName;
            }
            else
            {
                DetachedCriteria criteria = DetachedCriteria.For<StatusCode>()
                    .Add("Active", "A")
                    .Add("FunctionalAreaCode.Code", CodeValue.GetCode(FunctionalAreas.Task))
                    .AddOrder("SortOrder");
                IList<StatusCode> statuses = Repositories.Get<StatusCode>().List(criteria);
                foreach (StatusCode status in statuses)
                {
                    Int32 count = 0;
                    if (_reportRequestHeader != null)
                    {
                        criteria = DetachedCriteria.For<ReportRequestDestination>()
                            .Add("ReportRequestHeader", _reportRequestHeader)
                            .Add("StatusCode.Code", status.Code) // <-- Bad, potential problems.
                            .SetProjection(Projections.Count("Id"));
                        count = Repositories.Get<ReportRequestDestination>().Function<Int32>(criteria);
                    }
                    else
                    {
                        criteria = DetachedCriteria.For<InventoryTask>()
                            .Add("StatusCode", status)
                            .Add("Task", this)
                            .SetProjection(Projections.Count("Id"));
                        count = Repositories.Get<InventoryTask>().Function<Int32>(criteria);
                    }
                    //
                    if (count > 0)
                    {
                        if (CodeValue.GetCode(TaskStatuses.Complete).Equals(status.Code)) _completed = DateTime.Now;
                        else if (CodeValue.GetCode(TaskStatuses.Open).Equals(status.Code))
                        {
                            if (_reportRequestHeader.ReportSchedule != null && _reportRequestHeader.ReportSchedule.Frequency != null)
                            {
                                setNextExecutionDateTime();

                                if (!string.IsNullOrEmpty(_reportRequestHeader.DynamicDates) && "Y".Equals(_reportRequestHeader.DynamicDates))
                                {
                                    ReportRequestDetail detailFromDate = _reportRequestHeader.ReportRequestDetails
                                                         .Where(p => "FromDate".Equals(p.ReportParameter.Parameter.Description))
                                                         .Where(p => p.Value is String)
                                                         .FirstOrDefault();

                                    ReportRequestDetail detailToDate = _reportRequestHeader.ReportRequestDetails
                                                         .Where(p => "ToDate".Equals(p.ReportParameter.Parameter.Description))
                                                         .Where(p => p.Value is String)
                                                         .FirstOrDefault();

                                    if (detailFromDate != null && detailToDate != null)
                                    {
                                        DateTime fromDate = Converter.ToDateTime(detailFromDate.Value);
                                        DateTime toDate = Converter.ToDateTime(detailToDate.Value);

                                        int days = 1;
                                        if (_reportRequestHeader.ReportSchedule.Frequency.Days.HasValue)  
                                         days = _reportRequestHeader.ReportSchedule.Frequency.Days.Value;

                                        fromDate = fromDate.AddDays(days);
                                        toDate = toDate.AddDays(days);

                                        detailFromDate.Value = string.Format("{0:MM/dd/yyy}", fromDate);
                                        detailToDate.Value = string.Format("{0:MM/dd/yyy}", toDate);

                                        _reportRequestHeader.ReportRequestDetails.Add(detailFromDate);
                                        _reportRequestHeader.ReportRequestDetails.Add(detailToDate);
                                    }
                                }
                            }
                        }
                        _dateModified = DateTime.Now;
                        _statusCode = status;
                        _userModified = Registry.Find<UserAccount>().UserName;
                        //
                        break;
                    }
                }
            }
            //
            Repositories.Get<Task>().Update(this);
            if (_reportRequestHeader != null && _statusCode != null &&
                CodeValue.GetCode(TaskStatuses.Complete).Equals(_statusCode.Code))
            {
                this.Schedule();
                _reportRequestHeader.Complete();
            }
            //
            if (_reportRequestHeader != null && !string.IsNullOrEmpty(_reportRequestHeader.DynamicDates) && "Y".Equals(_reportRequestHeader.DynamicDates))
                foreach (ReportRequestDetail detail in _reportRequestHeader.ReportRequestDetails)
                {
                    Repositories.Get<ReportRequestDetail>().Update(detail);
                }
        }

        public virtual void UpdateStatusForScheduledOrderDrop()
        {
            if (_reportRequestHeader.ReportSchedule != null && _reportRequestHeader.ReportSchedule.Frequency != null)
            {
                if(_reportRequestHeader.ReportSchedule.ScheduledOrderDrop != null)
                {
                    setNextExecutionDateTime();
                    if (_nextExecution == null || _nextExecution > _reportRequestHeader.ReportSchedule.RunToDatetime)
                        this.Complete();
                    else
                        this.Open();

                    ScheduledOrderDrop scheduledOrderDrop = _reportRequestHeader.ReportSchedule.ScheduledOrderDrop;

                    if (scheduledOrderDrop.Active == "A")
                    {
                        scheduledOrderDrop.LastRunDateTime = DateTime.Now;
                        if (_nextExecution > _reportRequestHeader.ReportSchedule.RunToDatetime)
                            scheduledOrderDrop.NextRunDateTime = null;
                        else
                            scheduledOrderDrop.NextRunDateTime = _nextExecution;

                        scheduledOrderDrop.DateModified = DateTime.Now;
                        scheduledOrderDrop.UserModified = Registry.Find<UserAccount>().UserName;
                        Repositories.Get<ScheduledOrderDrop>().Update(scheduledOrderDrop);
                    }

                }
            }
        }
        #endregion

        #region Methods.Public.Assets

        public virtual void CheckDiscrepancies()
        {
            TransactionType found = Entity.Retrieve<TransactionType>(InventoryTransactions.FoundItem);
            TransactionType missing = Entity.Retrieve<TransactionType>(InventoryTransactions.MissingItem);
            DetachedCriteria criteria = DetachedCriteria.For<ItemTransaction>()
                .Add("Task", this)
                .AddOr("TransactionType", found, "TransactionType", missing)
                .SetProjection(Projections.Count("Id"));
            this.HasDiscrepancies = (Repositories.Get<ItemTransaction>().Function<Int32>(criteria) > 0);
        }

        public virtual void ClearCycleCount(Location location)
        {
            DetachedCriteria criteria = DetachedCriteria.For<InventoryTask>()
                .Add("Quantity", 0)
                .Add("Task", this)
                .SetMaxResults(1);
            InventoryTask main = Repositories.Get<InventoryTask>().Retrieve(criteria);
            //
            StatusCode complete = Entity.Retrieve<StatusCode>(TaskStatuses.Complete);
            criteria = DetachedCriteria.For<InventoryTask>()
                .Add("Quantity", ">", 0)
                .Add("StatusCode", "!=", complete)
                .Add("Task", this);
            if (location != null) criteria = criteria.Add("InventoryItem.Location", location);
            IList<InventoryTask> tasks = Repositories.Get<InventoryTask>().List(criteria);
            //
            String count = _transactionType.TransactionTypeCode;
            foreach (InventoryTask element in tasks)
            {
                element.Complete();
                //
                ItemType type = element.InventoryItem.Item.ItemType;
                if (CodeValue.GetCode(InventoryTransactions.CycleCountEmployee).Equals(count))
                {
                    criteria = DetachedCriteria.For<ParticipantAsset>()
                        .Add("InventoryItem", element.InventoryItem)
                        .Add("OrganizationParticipant", main.OrganizationParticipant)
                        .Add("Quantity", ">", 0)
                        .SetMaxResults(1);
                    ParticipantAsset issuance = Repositories.Get<ParticipantAsset>().Retrieve(criteria);
                    if (issuance != null) issuance.ClearCycleCount();
                    if (!"Y".Equals(type.Consumable) && !"Y".Equals(type.Expendable))
                        element.InventoryItem.WriteMissingItem(this, element.Quantity);
                }
                else if (CodeValue.GetCode(InventoryTransactions.CycleCountParent).Equals(count))
                {
                    criteria = DetachedCriteria.For<InventoryItemDetail>()
                        .Add("InventoryItem", element.InventoryItem)
                        .Add("ParentInventoryItem", main.InventoryItem)
                        .Add("Quantity", ">", 0M)
                        .Add("StatusCode", Entity.Retrieve<StatusCode>(InventoryStatuses.Issued))
                        .SetMaxResults(1);
                    InventoryItemDetail detail = Repositories.Get<InventoryItemDetail>().Retrieve(criteria);
                    if (detail != null) detail.ClearCycleCount();
                    if (!"Y".Equals(type.Consumable) && !"Y".Equals(type.Expendable))
                        element.InventoryItem.WriteMissingItem(this, element.Quantity);
                }
                else
                {
                    if ("Y".Equals(element.InventoryItem.CycleCount)) element.InventoryItem.ClearCycleCount();
                    element.InventoryItem.WriteMissingItem(this, element.Quantity);
                }
            }
            //
            main.Complete();
            if (main.LocationFrom != null) main.LocationFrom.ClearCycleCount();
            //
            _completed = DateTime.Now;
            _statusCode = complete;
        }

        public virtual void PrintDiscrepancyReport()
        {
            Dictionary<String, String> parameters = new Dictionary<String, String>();
            parameters.Add("TaskCode", _taskCode);
            //
            ReportRequestHeader request = ReportRequestHeader.Create(CodeValue.GetCode(Reports.CycleCountDiscrepancies),
                parameters, this.PrinterTypePrinter, 1);
            Repositories.Get<ReportRequestHeader>().Add(request);
            //
            Task task = Task.Create(request);
            Repositories.Get<Task>().Add(task);
        }

        #endregion

        #region Methods.Static

        public static Task Create(ReportRequestHeader request)
        {
            Task entity = Entity.Activate<Task>();
            entity.Agency = Registry.Find<Agency>();
            entity.CompanyLocationType = Registry.Find<CompanyLocationType>();
            entity.ReportRequestHeader = request;
            entity.Requested = DateTime.Now;
            entity.StatusCode = Entity.Retrieve<StatusCode>(TaskStatuses.Open);
            entity.TaskCode = EntityCode.GetCurrentValue(EntityCodes.Task);
            entity.TransactionType = Entity.Retrieve<TransactionType>(ReportTransactions.ReportRequest);
            //
            return entity;
        }

        public static Task Create(TransactionType type)
        {
            Task entity = Entity.Activate<Task>();
            entity.Agency = Registry.Find<Agency>();
            entity.CompanyLocationType = Registry.Find<CompanyLocationType>();
            entity.Requested = DateTime.Now;
            entity.StatusCode = Entity.Retrieve<StatusCode>(TaskStatuses.Open);
            entity.TaskCode = EntityCode.GetCurrentValue(EntityCodes.Task);
            entity.TransactionType = type;
            //
            return entity;
        }

        #endregion
    }
}
