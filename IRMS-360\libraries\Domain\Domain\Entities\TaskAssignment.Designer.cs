using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class TaskAssignment : Entity
	{
		#region Fields

		private ParticipantRole _participantRole;
		private String _active;
		private Task _task;

		#endregion

		#region Properties

		[DataMember]
		public virtual ParticipantRole ParticipantRole
		{
			get { return _participantRole; }
			set { _participantRole = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual Task Task
		{
			get { return _task; }
			set { _task = value; }
		}


		#endregion
	}
}
