using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class TimeZone : Entity
	{
		#region Fields

		private DateTime _effectiveFrom;
		private DateTime _effectiveThru;
		private Int32 _gmtOffset;
		private ICollection<Agency> _agencies = new HashSet<Agency>();
		private ICollection<AgencyLocation> _agencyLocations = new HashSet<AgencyLocation>();
		private ICollection<Carrier> _carriers = new HashSet<Carrier>();
		private ICollection<CarrierLocation> _carrierLocations = new HashSet<CarrierLocation>();
		private ICollection<Company> _companies = new HashSet<Company>();
		private ICollection<CompanyLocation> _companyLocations = new HashSet<CompanyLocation>();
		private ICollection<Customer> _customers = new HashSet<Customer>();
		private ICollection<CustomerLocation> _customerLocations = new HashSet<CustomerLocation>();
		private ICollection<Manufacturer> _manufacturers = new HashSet<Manufacturer>();
		private ICollection<ManufacturerLocation> _manufacturerLocations = new HashSet<ManufacturerLocation>();
		private ICollection<Provider> _providers = new HashSet<Provider>();
		private ICollection<ProviderLocation> _providerLocations = new HashSet<ProviderLocation>();
		private ICollection<Vendor> _vendors = new HashSet<Vendor>();
		private ICollection<VendorLocation> _vendorLocations = new HashSet<VendorLocation>();
		private String _description;
		private String _timeZoneCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime EffectiveFrom
		{
			get { return _effectiveFrom; }
			set { _effectiveFrom = value; }
		}

		[DataMember]
		public virtual DateTime EffectiveThru
		{
			get { return _effectiveThru; }
			set { _effectiveThru = value; }
		}

		[DataMember]
		public virtual Int32 GmtOffset
		{
			get { return _gmtOffset; }
			set { _gmtOffset = value; }
		}

		[DataMember]
		public virtual ICollection<Agency> Agencies
		{
			get { return _agencies; }
			set { _agencies = value; }
		}

		[DataMember]
		public virtual ICollection<AgencyLocation> AgencyLocations
		{
			get { return _agencyLocations; }
			set { _agencyLocations = value; }
		}

		[DataMember]
		public virtual ICollection<Carrier> Carriers
		{
			get { return _carriers; }
			set { _carriers = value; }
		}

		[DataMember]
		public virtual ICollection<CarrierLocation> CarrierLocations
		{
			get { return _carrierLocations; }
			set { _carrierLocations = value; }
		}

		[DataMember]
		public virtual ICollection<Company> Companies
		{
			get { return _companies; }
			set { _companies = value; }
		}

		[DataMember]
		public virtual ICollection<CompanyLocation> CompanyLocations
		{
			get { return _companyLocations; }
			set { _companyLocations = value; }
		}

		[DataMember]
		public virtual ICollection<Customer> Customers
		{
			get { return _customers; }
			set { _customers = value; }
		}

		[DataMember]
		public virtual ICollection<CustomerLocation> CustomerLocations
		{
			get { return _customerLocations; }
			set { _customerLocations = value; }
		}

		[DataMember]
		public virtual ICollection<Manufacturer> Manufacturers
		{
			get { return _manufacturers; }
			set { _manufacturers = value; }
		}

		[DataMember]
		public virtual ICollection<ManufacturerLocation> ManufacturerLocations
		{
			get { return _manufacturerLocations; }
			set { _manufacturerLocations = value; }
		}

		[DataMember]
		public virtual ICollection<Provider> Providers
		{
			get { return _providers; }
			set { _providers = value; }
		}

		[DataMember]
		public virtual ICollection<ProviderLocation> ProviderLocations
		{
			get { return _providerLocations; }
			set { _providerLocations = value; }
		}

		[DataMember]
		public virtual ICollection<Vendor> Vendors
		{
			get { return _vendors; }
			set { _vendors = value; }
		}

		[DataMember]
		public virtual ICollection<VendorLocation> VendorLocations
		{
			get { return _vendorLocations; }
			set { _vendorLocations = value; }
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String TimeZoneCode
		{
			get { return _timeZoneCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("TimeZoneCode must not be blank or null.");
				else _timeZoneCode = value;
			}
		}


		#endregion
	}
}
