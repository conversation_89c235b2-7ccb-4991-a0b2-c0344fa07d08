using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Transaction : Entity
	{
		#region Fields

		private AdjustmentGroup _adjustmentGroup;
		private AdjustmentType _adjustmentType;
		private BillingCode _billingCode;
		private CompanyOrganizationalUnit _companyOrganizationalUnit;
		private DateTime? _effectiveDate;
		private DateTime? _occurred;
		private DateTime? _occurredOff;
		private DateTime? _occurredOn;
		private DateTime? _paymentOccurred;
		private Decimal? _adjustmentAmount;
		private Decimal? _copayAmount;
		private Decimal? _paymentAmount;
		private Decimal? _quantity;
		private Decimal? _serviceAmount;
		private EraReasonCode _eraReasonCode;
		private LicensePlate _licensePlate;
		private LicensePlate _parentLicensePlate;
		private ICollection<EraClaimPosting> _eraClaimPostings = new HashSet<EraClaimPosting>();
		private Location _location;
		private OrderHeader _orderHeader;
		private OrganizationParticipant _organizationParticipant;
		private ParticipantEncounter _participantEncounter;
		private ParticipantImmunization _participantImmunization;
		private PaymentCategory _paymentCategory;
		private PaymentMethod _paymentMethod;
		private ServiceDetail _serviceDetail;
		private StatusCode _statusCode;
		private String _checkNumber;
		private String _notes;
		private String _responseMessage;
		private String _transactionCode;
		private TransactionType _transactionType;
		private TransactionType _transactionTypeOff;
		private Wave _wave;
		private WorkOrderHeader _workOrderHeader;

		#endregion

		#region Properties

		[DataMember]
		public virtual AdjustmentGroup AdjustmentGroup
		{
			get { return _adjustmentGroup; }
			set { _adjustmentGroup = value; }
		}

		[DataMember]
		public virtual AdjustmentType AdjustmentType
		{
			get { return _adjustmentType; }
			set { _adjustmentType = value; }
		}

		[DataMember]
		public virtual BillingCode BillingCode
		{
			get { return _billingCode; }
			set { _billingCode = value; }
		}

		[DataMember]
		public virtual CompanyOrganizationalUnit CompanyOrganizationalUnit
		{
			get { return _companyOrganizationalUnit; }
			set { _companyOrganizationalUnit = value; }
		}

		[DataMember]
		public virtual DateTime? EffectiveDate
		{
			get { return _effectiveDate; }
			set { _effectiveDate = value; }
		}

		[DataMember]
		public virtual DateTime? Occurred
		{
			get { return _occurred; }
			set { _occurred = value; }
		}

		[DataMember]
		public virtual DateTime? OccurredOff
		{
			get { return _occurredOff; }
			set { _occurredOff = value; }
		}

		[DataMember]
		public virtual DateTime? OccurredOn
		{
			get { return _occurredOn; }
			set { _occurredOn = value; }
		}

		[DataMember]
		public virtual DateTime? PaymentOccurred
		{
			get { return _paymentOccurred; }
			set { _paymentOccurred = value; }
		}

		[DataMember]
		public virtual Decimal? AdjustmentAmount
		{
			get { return _adjustmentAmount; }
			set { _adjustmentAmount = value; }
		}

		[DataMember]
		public virtual Decimal? CopayAmount
		{
			get { return _copayAmount; }
			set { _copayAmount = value; }
		}

		[DataMember]
		public virtual Decimal? PaymentAmount
		{
			get { return _paymentAmount; }
			set { _paymentAmount = value; }
		}

		[DataMember]
		public virtual Decimal? Quantity
		{
			get { return _quantity; }
			set { _quantity = value; }
		}

		[DataMember]
		public virtual Decimal? ServiceAmount
		{
			get { return _serviceAmount; }
			set { _serviceAmount = value; }
		}

		[DataMember]
		public virtual EraReasonCode EraReasonCode
		{
			get { return _eraReasonCode; }
			set { _eraReasonCode = value; }
		}

		[DataMember]
		public virtual LicensePlate LicensePlate
		{
			get { return _licensePlate; }
			set { _licensePlate = value; }
		}

		[DataMember]
		public virtual LicensePlate ParentLicensePlate
		{
			get { return _parentLicensePlate; }
			set { _parentLicensePlate = value; }
		}

		[DataMember]
		public virtual ICollection<EraClaimPosting> EraClaimPostings
		{
			get { return _eraClaimPostings; }
			set { _eraClaimPostings = value; }
		}

		[DataMember]
		public virtual Location Location
		{
			get { return _location; }
			set { _location = value; }
		}

		[DataMember]
		public virtual OrderHeader OrderHeader
		{
			get { return _orderHeader; }
			set { _orderHeader = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant OrganizationParticipant
		{
			get { return _organizationParticipant; }
			set { _organizationParticipant = value; }
		}

		[DataMember]
		public virtual ParticipantEncounter ParticipantEncounter
		{
			get { return _participantEncounter; }
			set { _participantEncounter = value; }
		}

		[DataMember]
		public virtual ParticipantImmunization ParticipantImmunization
		{
			get { return _participantImmunization; }
			set { _participantImmunization = value; }
		}

		[DataMember]
		public virtual PaymentCategory PaymentCategory
		{
			get { return _paymentCategory; }
			set { _paymentCategory = value; }
		}

		[DataMember]
		public virtual PaymentMethod PaymentMethod
		{
			get { return _paymentMethod; }
			set { _paymentMethod = value; }
		}

		[DataMember]
		public virtual ServiceDetail ServiceDetail
		{
			get { return _serviceDetail; }
			set { _serviceDetail = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
            get { return _statusCode != null ? _statusCode : StatusCode.GetOpenIntegrationStatusCode(); }
            set { _statusCode = value != null ? value : StatusCode.GetOpenIntegrationStatusCode(); }
        }

		[DataMember]
		public virtual String CheckNumber
		{
			get { return _checkNumber; }
			set { _checkNumber = value; }
		}

		[DataMember]
		public virtual String Notes
		{
			get { return _notes; }
			set { _notes = value; }
		}

		[DataMember]
		public virtual String ResponseMessage
		{
			get { return _responseMessage; }
			set { _responseMessage = value; }
		}

		[DataMember]
		public virtual String TransactionCode
		{
			get { return _transactionCode; }
			set { _transactionCode = value; }
		}

		[DataMember]
		public virtual TransactionType TransactionType
		{
			get { return _transactionType; }
			set { _transactionType = value; }
		}

		[DataMember]
		public virtual TransactionType TransactionTypeOff
		{
			get { return _transactionTypeOff; }
			set { _transactionTypeOff = value; }
		}

		[DataMember]
		public virtual Wave Wave
		{
			get { return _wave; }
			set { _wave = value; }
		}

		[DataMember]
		public virtual WorkOrderHeader WorkOrderHeader
		{
			get { return _workOrderHeader; }
			set { _workOrderHeader = value; }
		}


		#endregion
	}
}
