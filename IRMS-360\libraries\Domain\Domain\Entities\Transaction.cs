using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
    [DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
    public partial class Transaction : Entity
    {
        #region Constructor

        public Transaction()
        {
            //
        }

        #endregion

        #region Properities

        [DataMember]
        public virtual Int32 EncounterId
        {
            get;
            set;
        }

        [DataMember]
        public virtual Int32 ServiceDetailId
        {
            get;
            set;
        }
         [DataMember]
        public virtual Int32 ? LicensePlateId
        {
            get;
            set;
        }

        [DataMember]
        public virtual Int32 ParticipantImmunizationId
        {
            get;
            set;
        }

        #endregion
    }
}
