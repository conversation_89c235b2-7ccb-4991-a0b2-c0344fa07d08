using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class TransactionType : Entity
	{
		#region Fields

		private DateTime? _engineered;
		private FunctionalAreaCode _functionalAreaCode;
		private Int32? _metricEngineered;
		private Int32? _metricTarget;
		private ICollection<AdjustmentCodeTransactionType> _adjustmentCodeTransactionTypes = new HashSet<AdjustmentCodeTransactionType>();
		private ICollection<BillingCodeTask> _billingCodeTasks = new HashSet<BillingCodeTask>();
		private ICollection<CompanyLocationTransactionType> _companyLocationTransactionTypes = new HashSet<CompanyLocationTransactionType>();
		private ICollection<InterfaceHeader> _interfaceHeaders = new HashSet<InterfaceHeader>();
		private ICollection<ItemTransaction> _itemTransactions = new HashSet<ItemTransaction>();
		private ICollection<ParticipantInventoryItem> _participantInventoryItems = new HashSet<ParticipantInventoryItem>();
		private ICollection<Task> _tasks = new HashSet<Task>();
		private ICollection<Transaction> _transactions = new HashSet<Transaction>();
		private ICollection<WorkOrderItem> _workOrderItems = new HashSet<WorkOrderItem>();
		private String _active;
		private String _description;
		private String _transactionTypeCode;
		private UnitOfMeasure _metricEngineeredUom;
		private UnitOfMeasure _metricTargetUom;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime? Engineered
		{
			get { return _engineered; }
			set { _engineered = value; }
		}

		[DataMember]
		public virtual FunctionalAreaCode FunctionalAreaCode
		{
			get { return _functionalAreaCode; }
			set { _functionalAreaCode = value; }
		}

		[DataMember]
		public virtual Int32? MetricEngineered
		{
			get { return _metricEngineered; }
			set { _metricEngineered = value; }
		}

		[DataMember]
		public virtual Int32? MetricTarget
		{
			get { return _metricTarget; }
			set { _metricTarget = value; }
		}

		[DataMember]
		public virtual ICollection<AdjustmentCodeTransactionType> AdjustmentCodeTransactionTypes
		{
			get { return _adjustmentCodeTransactionTypes; }
			set { _adjustmentCodeTransactionTypes = value; }
		}

		[DataMember]
		public virtual ICollection<BillingCodeTask> BillingCodeTasks
		{
			get { return _billingCodeTasks; }
			set { _billingCodeTasks = value; }
		}

		[DataMember]
		public virtual ICollection<CompanyLocationTransactionType> CompanyLocationTransactionTypes
		{
			get { return _companyLocationTransactionTypes; }
			set { _companyLocationTransactionTypes = value; }
		}

		[DataMember]
		public virtual ICollection<InterfaceHeader> InterfaceHeaders
		{
			get { return _interfaceHeaders; }
			set { _interfaceHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<ItemTransaction> ItemTransactions
		{
			get { return _itemTransactions; }
			set { _itemTransactions = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantInventoryItem> ParticipantInventoryItems
		{
			get { return _participantInventoryItems; }
			set { _participantInventoryItems = value; }
		}

		[DataMember]
		public virtual ICollection<Task> Tasks
		{
			get { return _tasks; }
			set { _tasks = value; }
		}

		[DataMember]
		public virtual ICollection<Transaction> Transactions
		{
			get { return _transactions; }
			set { _transactions = value; }
		}

		[DataMember]
		public virtual ICollection<WorkOrderItem> WorkOrderItems
		{
			get { return _workOrderItems; }
			set { _workOrderItems = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String TransactionTypeCode
		{
			get { return _transactionTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("TransactionTypeCode must not be blank or null.");
				else _transactionTypeCode = value;
			}
		}

		[DataMember]
		public virtual UnitOfMeasure MetricEngineeredUom
		{
			get { return _metricEngineeredUom; }
			set { _metricEngineeredUom = value; }
		}

		[DataMember]
		public virtual UnitOfMeasure MetricTargetUom
		{
			get { return _metricTargetUom; }
			set { _metricTargetUom = value; }
		}


		#endregion
	}
}
