using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class TransactionType : Entity
	{
		#region Constructor

		public TransactionType()
		{
			//
		}

		#endregion

        #region "Properties"

        [DataMember]
        public virtual String FunctionalAreaCodeCode
        {
            get;
            set;
        }

        #endregion
    }
}
