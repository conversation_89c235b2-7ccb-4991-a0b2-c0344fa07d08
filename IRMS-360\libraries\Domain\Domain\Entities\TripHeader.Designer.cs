using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class TripHeader : Entity
	{
		#region Fields

		private DateTime? _deliveryDate;
		private ICollection<PreReceiptDetail> _preReceiptDetails = new HashSet<PreReceiptDetail>();
		private StatusCode _statusCode;
		private StatusCode _integrationStatusCode;
		private String _deliveryTime;
		private String _footageUsed;
		private String _loadExitLocation;
		private String _manifestCode;
		private String _orderNumber;
		private String _outboundTrailerCode;
		private String _scheduledBy;
		private String _scheduledWith;
		private String _serialNumber;
		private String _stopNumber;
		private String _tripCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime? DeliveryDate
		{
			get { return _deliveryDate; }
			set { _deliveryDate = value; }
		}

		[DataMember]
		public virtual ICollection<PreReceiptDetail> PreReceiptDetails
		{
			get { return _preReceiptDetails; }
			set { _preReceiptDetails = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual StatusCode IntegrationStatusCode
		{
            get { return _integrationStatusCode != null ? _integrationStatusCode : StatusCode.GetOpenIntegrationStatusCode(); }
            set { _integrationStatusCode = value != null ? value : StatusCode.GetOpenIntegrationStatusCode(); }
        }

		[DataMember]
		public virtual String DeliveryTime
		{
			get { return _deliveryTime; }
			set { _deliveryTime = value; }
		}

		[DataMember]
		public virtual String FootageUsed
		{
			get { return _footageUsed; }
			set { _footageUsed = value; }
		}

		[DataMember]
		public virtual String LoadExitLocation
		{
			get { return _loadExitLocation; }
			set { _loadExitLocation = value; }
		}

		[DataMember]
		public virtual String ManifestCode
		{
			get { return _manifestCode; }
			set { _manifestCode = value; }
		}

		[DataMember]
		public virtual String OrderNumber
		{
			get { return _orderNumber; }
			set { _orderNumber = value; }
		}

		[DataMember]
		public virtual String OutboundTrailerCode
		{
			get { return _outboundTrailerCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("OutboundTrailerCode must not be blank or null.");
				else _outboundTrailerCode = value;
			}
		}

		[DataMember]
		public virtual String ScheduledBy
		{
			get { return _scheduledBy; }
			set { _scheduledBy = value; }
		}

		[DataMember]
		public virtual String ScheduledWith
		{
			get { return _scheduledWith; }
			set { _scheduledWith = value; }
		}

		[DataMember]
		public virtual String SerialNumber
		{
			get { return _serialNumber; }
			set { _serialNumber = value; }
		}

		[DataMember]
		public virtual String StopNumber
		{
			get { return _stopNumber; }
			set { _stopNumber = value; }
		}

		[DataMember]
		public virtual String TripCode
		{
			get { return _tripCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("TripCode must not be blank or null.");
				else _tripCode = value;
			}
		}


		#endregion
	}
}
