using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class UdfInventoryValue : Entity
	{
		#region Fields

		private InventoryItem _inventoryItem;
		private String _userDefinedValue;
		private UdfMetadataValue _udfMetadataValue;

		#endregion

		#region Properties

		[DataMember]
		public virtual InventoryItem InventoryItem
		{
			get { return _inventoryItem; }
			set { _inventoryItem = value; }
		}

		[DataMember]
		public virtual String UserDefinedValue
		{
			get { return _userDefinedValue; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("UserDefinedValue must not be blank or null.");
				else _userDefinedValue = value;
			}
		}

		[DataMember]
		public virtual UdfMetadataValue UdfMetadataValue
		{
			get { return _udfMetadataValue; }
			set { _udfMetadataValue = value; }
		}


		#endregion
	}
}
