using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class UdfItemValue : Entity
	{
		#region Fields

		private Item _item;
		private String _promptAtReceiving;
		private String _userDefinedValue;
		private UdfMetadataValue _udfMetadataValue;

		#endregion

		#region Properties

		[DataMember]
		public virtual Item Item
		{
			get { return _item; }
			set { _item = value; }
		}

		[DataMember]
		public virtual String PromptAtReceiving
		{
			get { return _promptAtReceiving; }
			set { _promptAtReceiving = value; }
		}

		[DataMember]
		public virtual String UserDefinedValue
		{
			get { return _userDefinedValue; }
			set { _userDefinedValue = value; }
		}

		[DataMember]
		public virtual UdfMetadataValue UdfMetadataValue
		{
			get { return _udfMetadataValue; }
			set { _udfMetadataValue = value; }
		}


		#endregion
	}
}
