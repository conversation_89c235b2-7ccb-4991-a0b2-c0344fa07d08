using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class UdfLocationValue : Entity
	{
		#region Fields

		private Location _location;
		private String _userDefinedValue;
		private UdfMetadataValue _udfMetadataValue;

		#endregion

		#region Properties

		[DataMember]
		public virtual Location Location
		{
			get { return _location; }
			set { _location = value; }
		}

		[DataMember]
		public virtual String UserDefinedValue
		{
			get { return _userDefinedValue; }
			set { _userDefinedValue = value; }
		}

		[DataMember]
		public virtual UdfMetadataValue UdfMetadataValue
		{
			get { return _udfMetadataValue; }
			set { _udfMetadataValue = value; }
		}


		#endregion
	}
}
