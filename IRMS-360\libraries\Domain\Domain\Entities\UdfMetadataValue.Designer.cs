using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class UdfMetadataValue : Entity
	{
		#region Fields

		private Int32? _decimalPlaces;
		private ICollection<UdfInventoryValue> _udfInventoryValues = new HashSet<UdfInventoryValue>();
		private ICollection<UdfItemTransactionValue> _udfItemTransactionValues = new HashSet<UdfItemTransactionValue>();
		private ICollection<UdfItemValue> _udfItemValues = new HashSet<UdfItemValue>();
		private ICollection<UdfLocationValue> _udfLocationValues = new HashSet<UdfLocationValue>();
		private String _active;
		private String _dataType;
		private String _label;
		private String _tableName;

		#endregion

		#region Properties

		[DataMember]
		public virtual Int32? DecimalPlaces
		{
			get { return _decimalPlaces; }
			set { _decimalPlaces = value; }
		}

		[DataMember]
		public virtual ICollection<UdfInventoryValue> UdfInventoryValues
		{
			get { return _udfInventoryValues; }
			set { _udfInventoryValues = value; }
		}

		[DataMember]
		public virtual ICollection<UdfItemTransactionValue> UdfItemTransactionValues
		{
			get { return _udfItemTransactionValues; }
			set { _udfItemTransactionValues = value; }
		}

		[DataMember]
		public virtual ICollection<UdfItemValue> UdfItemValues
		{
			get { return _udfItemValues; }
			set { _udfItemValues = value; }
		}

		[DataMember]
		public virtual ICollection<UdfLocationValue> UdfLocationValues
		{
			get { return _udfLocationValues; }
			set { _udfLocationValues = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String DataType
		{
			get { return _dataType; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("DataType must not be blank or null.");
				else _dataType = value;
			}
		}

		[DataMember]
		public virtual String Label
		{
			get { return _label; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Label must not be blank or null.");
				else _label = value;
			}
		}

		[DataMember]
		public virtual String TableName
		{
			get { return _tableName; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("TableName must not be blank or null.");
				else _tableName = value;
			}
		}


		#endregion
	}
}
