using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class UnitOfMeasure : Entity
	{
		#region Fields

		private Decimal? _factor;
		private FunctionalAreaCode _functionalAreaCode;
		private ICollection<AgeRange> _ageRanges = new HashSet<AgeRange>();
		private ICollection<CartonDetail> _cartonDetails = new HashSet<CartonDetail>();
		private ICollection<CompanyArchive> _companyArchives = new HashSet<CompanyArchive>();
		private ICollection<Contract> _contracts = new HashSet<Contract>();
		private ICollection<InventoryItem> _inventoryItems = new HashSet<InventoryItem>();
		private ICollection<ItemTransaction> _itemTransactions = new HashSet<ItemTransaction>();
		private ICollection<ItemUomRelationship> _itemUomRelationships = new HashSet<ItemUomRelationship>();
		private ICollection<OrderDetail> _orderDetails = new HashSet<OrderDetail>();
		private ICollection<ParticipantImmunization> _participantImmunizations = new HashSet<ParticipantImmunization>();
		private ICollection<PurchaseOrderDetail> _purchaseOrderDetails = new HashSet<PurchaseOrderDetail>();
		private ICollection<ReceiptDetail> _receiptDetails = new HashSet<ReceiptDetail>();
		private ICollection<ReceiptHeader> _receiptHeaders = new HashSet<ReceiptHeader>();
		private ICollection<RequisitionDetail> _requisitionDetails = new HashSet<RequisitionDetail>();
		private ICollection<Service> _services = new HashSet<Service>();
		private ICollection<ServiceDetail> _serviceDetails = new HashSet<ServiceDetail>();
		private ICollection<UnitOfMeasure> _childUnitOfMeasures = new HashSet<UnitOfMeasure>();
		private ICollection<VendorItem> _vendorItems = new HashSet<VendorItem>();
		private ICollection<WorkOrderHeader> _workOrderHeaders = new HashSet<WorkOrderHeader>();
		private String _active;
		private String _defaultUom;
		private String _description;
		private String _uomCode;
		private UnitOfMeasure _parentUnitOfMeasure;

		#endregion

		#region Properties

		[DataMember]
		public virtual Decimal? Factor
		{
			get { return _factor; }
			set { _factor = value; }
		}

		[DataMember]
		public virtual FunctionalAreaCode FunctionalAreaCode
		{
			get { return _functionalAreaCode; }
			set { _functionalAreaCode = value; }
		}

		[DataMember]
		public virtual ICollection<AgeRange> AgeRanges
		{
			get { return _ageRanges; }
			set { _ageRanges = value; }
		}

		[DataMember]
		public virtual ICollection<CartonDetail> CartonDetails
		{
			get { return _cartonDetails; }
			set { _cartonDetails = value; }
		}

		[DataMember]
		public virtual ICollection<CompanyArchive> CompanyArchives
		{
			get { return _companyArchives; }
			set { _companyArchives = value; }
		}

		[DataMember]
		public virtual ICollection<Contract> Contracts
		{
			get { return _contracts; }
			set { _contracts = value; }
		}

		[DataMember]
		public virtual ICollection<InventoryItem> InventoryItems
		{
			get { return _inventoryItems; }
			set { _inventoryItems = value; }
		}

		[DataMember]
		public virtual ICollection<ItemTransaction> ItemTransactions
		{
			get { return _itemTransactions; }
			set { _itemTransactions = value; }
		}

		[DataMember]
		public virtual ICollection<ItemUomRelationship> ItemUomRelationships
		{
			get { return _itemUomRelationships; }
			set { _itemUomRelationships = value; }
		}

		[DataMember]
		public virtual ICollection<OrderDetail> OrderDetails
		{
			get { return _orderDetails; }
			set { _orderDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantImmunization> ParticipantImmunizations
		{
			get { return _participantImmunizations; }
			set { _participantImmunizations = value; }
		}

		[DataMember]
		public virtual ICollection<PurchaseOrderDetail> PurchaseOrderDetails
		{
			get { return _purchaseOrderDetails; }
			set { _purchaseOrderDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ReceiptDetail> ReceiptDetails
		{
			get { return _receiptDetails; }
			set { _receiptDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ReceiptHeader> ReceiptHeaders
		{
			get { return _receiptHeaders; }
			set { _receiptHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<RequisitionDetail> RequisitionDetails
		{
			get { return _requisitionDetails; }
			set { _requisitionDetails = value; }
		}

		[DataMember]
		public virtual ICollection<Service> Services
		{
			get { return _services; }
			set { _services = value; }
		}

		[DataMember]
		public virtual ICollection<ServiceDetail> ServiceDetails
		{
			get { return _serviceDetails; }
			set { _serviceDetails = value; }
		}

		[DataMember]
		public virtual ICollection<UnitOfMeasure> ChildUnitOfMeasures
		{
			get { return _childUnitOfMeasures; }
			set { _childUnitOfMeasures = value; }
		}

		[DataMember]
		public virtual ICollection<VendorItem> VendorItems
		{
			get { return _vendorItems; }
			set { _vendorItems = value; }
		}

		[DataMember]
		public virtual ICollection<WorkOrderHeader> WorkOrderHeaders
		{
			get { return _workOrderHeaders; }
			set { _workOrderHeaders = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String DefaultUom
		{
			get { return _defaultUom; }
			set { _defaultUom = value; }
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String UomCode
		{
			get { return _uomCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("UomCode must not be blank or null.");
				else _uomCode = value;
			}
		}

		[DataMember]
		public virtual UnitOfMeasure ParentUnitOfMeasure
		{
			get { return _parentUnitOfMeasure; }
			set { _parentUnitOfMeasure = value; }
		}


		#endregion
	}
}
