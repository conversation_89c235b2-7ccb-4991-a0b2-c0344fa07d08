using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class UpsHubCode : Entity
	{
		#region Fields

		private String _hubCode;
		private String _zipBegin;
		private String _zipEnd;

		#endregion

		#region Properties

		[DataMember]
		public virtual String HubCode
		{
			get { return _hubCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("HubCode must not be blank or null.");
				else _hubCode = value;
			}
		}

		[DataMember]
		public virtual String ZipBegin
		{
			get { return _zipBegin; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ZipBegin must not be blank or null.");
				else _zipBegin = value;
			}
		}

		[DataMember]
		public virtual String ZipEnd
		{
			get { return _zipEnd; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ZipEnd must not be blank or null.");
				else _zipEnd = value;
			}
		}


		#endregion
	}
}
