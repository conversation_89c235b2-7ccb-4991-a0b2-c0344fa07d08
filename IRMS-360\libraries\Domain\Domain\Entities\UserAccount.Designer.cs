using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class UserAccount : Entity
	{
		#region Fields

		private Byte[] _userPassword;
		private DateTime? _changePassword;
		private DateTime? _expired;
		private DateTime? _locked;
		private ICollection<ReportRequestHeader> _reportRequestHeaders = new HashSet<ReportRequestHeader>();
		private ICollection<UserAccountAgency> _userAccountAgencies = new HashSet<UserAccountAgency>();
		private ICollection<UserAccountAudit> _userAccountAudits = new HashSet<UserAccountAudit>();
		private ICollection<UserAccountCompany> _userAccountCompanies = new HashSet<UserAccountCompany>();
		private ICollection<UserAccountCustomer> _userAccountCustomers = new HashSet<UserAccountCustomer>();
		private ICollection<UserAccountFavorite> _userAccountFavorites = new HashSet<UserAccountFavorite>();
		private ICollection<UserAccountProvider> _userAccountProviders = new HashSet<UserAccountProvider>();
		private ICollection<UserAccountQuestion> _userAccountQuestions = new HashSet<UserAccountQuestion>();
		private ICollection<UserApplicationGroup> _userApplicationGroups = new HashSet<UserApplicationGroup>();
		private ICollection<UserApplicationGroupPrivilege> _userApplicationGroupPrivileges = new HashSet<UserApplicationGroupPrivilege>();
		private ICollection<UserPreference> _userPreferences = new HashSet<UserPreference>();
		private OrganizationParticipant _organizationParticipant;
		private String _active;
		private String _association;
		private String _customerCode;
		private String _userName;

		#endregion

		#region Properties

		[DataMember]
		public virtual Byte[] UserPassword
		{
			get { return _userPassword; }
			set { _userPassword = value; }
		}

		[DataMember]
		public virtual DateTime? ChangePassword
		{
			get { return _changePassword; }
			set { _changePassword = value; }
		}

		[DataMember]
		public virtual DateTime? Expired
		{
			get { return _expired; }
			set { _expired = value; }
		}

		[DataMember]
		public virtual DateTime? Locked
		{
			get { return _locked; }
			set { _locked = value; }
		}

		[DataMember]
		public virtual ICollection<ReportRequestHeader> ReportRequestHeaders
		{
			get { return _reportRequestHeaders; }
			set { _reportRequestHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<UserAccountAgency> UserAccountAgencies
		{
			get { return _userAccountAgencies; }
			set { _userAccountAgencies = value; }
		}

		[DataMember]
		public virtual ICollection<UserAccountAudit> UserAccountAudits
		{
			get { return _userAccountAudits; }
			set { _userAccountAudits = value; }
		}

		[DataMember]
		public virtual ICollection<UserAccountCompany> UserAccountCompanies
		{
			get { return _userAccountCompanies; }
			set { _userAccountCompanies = value; }
		}

		[DataMember]
		public virtual ICollection<UserAccountCustomer> UserAccountCustomers
		{
			get { return _userAccountCustomers; }
			set { _userAccountCustomers = value; }
		}

		[DataMember]
		public virtual ICollection<UserAccountFavorite> UserAccountFavorites
		{
			get { return _userAccountFavorites; }
			set { _userAccountFavorites = value; }
		}

		[DataMember]
		public virtual ICollection<UserAccountProvider> UserAccountProviders
		{
			get { return _userAccountProviders; }
			set { _userAccountProviders = value; }
		}

		[DataMember]
		public virtual ICollection<UserAccountQuestion> UserAccountQuestions
		{
			get { return _userAccountQuestions; }
			set { _userAccountQuestions = value; }
		}

		[DataMember]
		public virtual ICollection<UserApplicationGroup> UserApplicationGroups
		{
			get { return _userApplicationGroups; }
			set { _userApplicationGroups = value; }
		}

		[DataMember]
		public virtual ICollection<UserApplicationGroupPrivilege> UserApplicationGroupPrivileges
		{
			get { return _userApplicationGroupPrivileges; }
			set { _userApplicationGroupPrivileges = value; }
		}

		[DataMember]
		public virtual ICollection<UserPreference> UserPreferences
		{
			get { return _userPreferences; }
			set { _userPreferences = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant OrganizationParticipant
		{
			get { return _organizationParticipant; }
			set { _organizationParticipant = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Association
		{
			get { return _association; }
			set { _association = value; }
		}

		[DataMember]
		public virtual String CustomerCode
		{
			get { return _customerCode; }
			set { _customerCode = value; }
		}

		[DataMember]
		public virtual String UserName
		{
			get { return _userName; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("UserName must not be blank or null.");
				else _userName = value;
			}
		}


		#endregion
	}
}
