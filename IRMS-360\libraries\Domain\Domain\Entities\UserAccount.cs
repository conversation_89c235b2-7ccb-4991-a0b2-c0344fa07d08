using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class UserAccount : Entity
	{
		#region Properties

		[DataMember]
		public virtual String FirstName { get; set; }
		[DataMember]
		public virtual String FullName { get; set; }
		[DataMember]
		public virtual String LastName { get; set; }
		[DataMember]
		public virtual String MiddleName { get; set; }
		[DataMember]
		public virtual String RoleCode { get; set; }
		[DataMember]
		public virtual String RoleDescription { get; set; }

		#endregion

		#region Constructor

		public UserAccount()
		{
			//
		}

		#endregion
	}
}
