using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class UserAccountAgency : Entity
	{
		#region Properties

		[DataMember]
		public virtual String AgencyCode { get; set; }
		[DataMember]
		public virtual String AgencyName { get; set; }
		[DataMember]
		public virtual String OrganizationCode { get; set; }
		[DataMember]
		public virtual String OrganizationDescription { get; set; }

		#endregion

		#region Constructor

		public UserAccountAgency()
		{
			//
		}

		#endregion
	}
}
