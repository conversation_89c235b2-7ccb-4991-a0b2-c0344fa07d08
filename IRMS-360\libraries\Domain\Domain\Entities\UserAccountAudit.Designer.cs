using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class UserAccountAudit : Entity
	{
		#region Fields

		private String _answerProvided;
		private String _comments;
		private String _ipAddress;
		private String _passFail;
		private String _passwordProvided;
		private UserAccount _userAccount;
		private UserAccountQuestion _userAccountQuestion;

		#endregion

		#region Properties

		[DataMember]
		public virtual String AnswerProvided
		{
			get { return _answerProvided; }
			set { _answerProvided = value; }
		}

		[DataMember]
		public virtual String Comments
		{
			get { return _comments; }
			set { _comments = value; }
		}

		[DataMember]
		public virtual String IpAddress
		{
			get { return _ipAddress; }
			set { _ipAddress = value; }
		}

		[DataMember]
		public virtual String PassFail
		{
			get { return _passFail; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("PassFail must not be blank or null.");
				else _passFail = value;
			}
		}

		[DataMember]
		public virtual String PasswordProvided
		{
			get { return _passwordProvided; }
			set { _passwordProvided = value; }
		}

		[DataMember]
		public virtual UserAccount UserAccount
		{
			get { return _userAccount; }
			set { _userAccount = value; }
		}

		[DataMember]
		public virtual UserAccountQuestion UserAccountQuestion
		{
			get { return _userAccountQuestion; }
			set { _userAccountQuestion = value; }
		}


		#endregion
	}
}
