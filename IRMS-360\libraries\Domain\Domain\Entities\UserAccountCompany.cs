using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class UserAccountCompany : Entity
	{
		#region Properties

		[DataMember]
		public virtual String CompanyCode { get; set; }
		[DataMember]
		public virtual String CompanyLocationCode { get; set; }
		[DataMember]
		public virtual String CompanyLocationDescription { get; set; }
		[DataMember]
		public virtual String CompanyName { get; set; }

		#endregion

		#region Constructor

		public UserAccountCompany()
		{
			//
		}

		#endregion
	}
}
