using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class UserAccountFavorite : Entity
	{
		#region Fields

		private ApplicationModule _applicationModule;
		private Int32 _defaultDays;
		private Int32 _sortOrder;
		private Parameter _xAxisParameter;
		private Parameter _yAxisParameter;
		private Parameter _zAxisParameter;
		private String _active;
		private String _autoQuery;
		private String _bottomTitle;
		private String _leftTitle;
		private String _rightTitle;
		private String _threeDChart;
		private String _topTitle;
		private UserAccount _userAccount;

		#endregion

		#region Properties

		[DataMember]
		public virtual ApplicationModule ApplicationModule
		{
			get { return _applicationModule; }
			set { _applicationModule = value; }
		}

		[DataMember]
		public virtual Int32 DefaultDays
		{
			get { return _defaultDays; }
			set { _defaultDays = value; }
		}

		[DataMember]
		public virtual Int32 SortOrder
		{
			get { return _sortOrder; }
			set { _sortOrder = value; }
		}

		[DataMember]
		public virtual Parameter XAxisParameter
		{
			get { return _xAxisParameter; }
			set { _xAxisParameter = value; }
		}

		[DataMember]
		public virtual Parameter YAxisParameter
		{
			get { return _yAxisParameter; }
			set { _yAxisParameter = value; }
		}

		[DataMember]
		public virtual Parameter ZAxisParameter
		{
			get { return _zAxisParameter; }
			set { _zAxisParameter = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String AutoQuery
		{
			get { return _autoQuery; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("AutoQuery must not be blank or null.");
				else _autoQuery = value;
			}
		}

		[DataMember]
		public virtual String BottomTitle
		{
			get { return _bottomTitle; }
			set { _bottomTitle = value; }
		}

		[DataMember]
		public virtual String LeftTitle
		{
			get { return _leftTitle; }
			set { _leftTitle = value; }
		}

		[DataMember]
		public virtual String RightTitle
		{
			get { return _rightTitle; }
			set { _rightTitle = value; }
		}

		[DataMember]
		public virtual String ThreeDChart
		{
			get { return _threeDChart; }
			set { _threeDChart = value; }
		}

		[DataMember]
		public virtual String TopTitle
		{
			get { return _topTitle; }
			set { _topTitle = value; }
		}

		[DataMember]
		public virtual UserAccount UserAccount
		{
			get { return _userAccount; }
			set { _userAccount = value; }
		}


		#endregion
	}
}
