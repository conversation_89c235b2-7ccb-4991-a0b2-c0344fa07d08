using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class UserAccountQuestion : Entity
	{
		#region Fields

		private ICollection<UserAccountAudit> _userAccountAudits = new HashSet<UserAccountAudit>();
		private Question _question;
		private String _active;
		private String _answer;
		private UserAccount _userAccount;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<UserAccountAudit> UserAccountAudits
		{
			get { return _userAccountAudits; }
			set { _userAccountAudits = value; }
		}

		[DataMember]
		public virtual Question Question
		{
			get { return _question; }
			set { _question = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Answer
		{
			get { return _answer; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Answer must not be blank or null.");
				else _answer = value;
			}
		}

		[DataMember]
		public virtual UserAccount UserAccount
		{
			get { return _userAccount; }
			set { _userAccount = value; }
		}


		#endregion
	}
}
