using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class UserApplicationGroup : Entity
	{
		#region Fields

		private ApplicationGroup _applicationGroup;
		private ICollection<UserApplicationGroupPrivilege> _userApplicationGroupPrivileges = new HashSet<UserApplicationGroupPrivilege>();
		private String _active;
		private UserAccount _userAccount;

		#endregion

		#region Properties

		[DataMember]
		public virtual ApplicationGroup ApplicationGroup
		{
			get { return _applicationGroup; }
			set { _applicationGroup = value; }
		}

		[DataMember]
		public virtual ICollection<UserApplicationGroupPrivilege> UserApplicationGroupPrivileges
		{
			get { return _userApplicationGroupPrivileges; }
			set { _userApplicationGroupPrivileges = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual UserAccount UserAccount
		{
			get { return _userAccount; }
			set { _userAccount = value; }
		}


		#endregion
	}
}
