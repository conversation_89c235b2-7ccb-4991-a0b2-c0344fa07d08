using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class UserApplicationGroup : Entity
	{
		#region Properties

		[DataMember]
		public virtual String ApplicationGroupDescription { get; set; }

		#endregion

		#region Constructor

		public UserApplicationGroup()
		{
			//
		}

		#endregion
	}
}
