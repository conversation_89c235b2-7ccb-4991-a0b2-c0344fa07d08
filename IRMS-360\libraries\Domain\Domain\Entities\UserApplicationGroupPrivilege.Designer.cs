using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class UserApplicationGroupPrivilege : Entity
	{
		#region Fields

		private ApplicationPrivilege _applicationPrivilege;
		private String _active;
		private UserAccount _userAccount;
		private UserApplicationGroup _userApplicationGroup;

		#endregion

		#region Properties

		[DataMember]
		public virtual ApplicationPrivilege ApplicationPrivilege
		{
			get { return _applicationPrivilege; }
			set { _applicationPrivilege = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual UserAccount UserAccount
		{
			get { return _userAccount; }
			set { _userAccount = value; }
		}

		[DataMember]
		public virtual UserApplicationGroup UserApplicationGroup
		{
			get { return _userApplicationGroup; }
			set { _userApplicationGroup = value; }
		}


		#endregion
	}
}
