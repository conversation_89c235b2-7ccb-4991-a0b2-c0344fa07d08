using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class UserPreference : Entity
	{
		#region Fields

		private String _moduleName;
		private String _settings;
		private UserAccount _userAccount;

		#endregion

		#region Properties

		[DataMember]
		public virtual String ModuleName
		{
			get { return _moduleName; }
			set { _moduleName = value; }
		}

		[DataMember]
		public virtual String Settings
		{
			get { return _settings; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Settings must not be blank or null.");
				else _settings = value;
			}
		}

		[DataMember]
		public virtual UserAccount UserAccount
		{
			get { return _userAccount; }
			set { _userAccount = value; }
		}


		#endregion
	}
}
