using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class VaccinationRoute : Entity
	{
		#region Fields

		private ICollection<ParticipantImmunization> _participantImmunizations = new HashSet<ParticipantImmunization>();
		private String _active;
		private String _description;
		private String _vaccinationRouteCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<ParticipantImmunization> ParticipantImmunizations
		{
			get { return _participantImmunizations; }
			set { _participantImmunizations = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String VaccinationRouteCode
		{
			get { return _vaccinationRouteCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("VaccinationRouteCode must not be blank or null.");
				else _vaccinationRouteCode = value;
			}
		}


		#endregion
	}
}
