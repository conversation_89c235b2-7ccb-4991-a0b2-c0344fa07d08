using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Vendor : Entity
	{
		#region Fields

		private Agency _agency;
		private CompanyLocationType _companyLocationType;
		private Customer _customer;
		private ICollection<BillingRate> _billingRates = new HashSet<BillingRate>();
		private ICollection<Comment> _comments = new HashSet<Comment>();
		private ICollection<Contract> _contracts = new HashSet<Contract>();
		private ICollection<OrganizationParticipant> _organizationParticipants = new HashSet<OrganizationParticipant>();
		private ICollection<PreReceiptHeader> _preReceiptHeaders = new HashSet<PreReceiptHeader>();
		private ICollection<Printer> _printers = new HashSet<Printer>();
		private ICollection<PurchaseOrderHeader> _purchaseOrderHeaders = new HashSet<PurchaseOrderHeader>();
		private ICollection<ReturnHeader> _returnHeaders = new HashSet<ReturnHeader>();
		private ICollection<VendorCommunication> _vendorCommunications = new HashSet<VendorCommunication>();
		private ICollection<VendorItem> _vendorItems = new HashSet<VendorItem>();
		private ICollection<VendorLocation> _vendorLocations = new HashSet<VendorLocation>();
		private String _active;
		private String _name;
		private String _url;
		private String _vendorCode;
		private TimeZone _timeZone;

		#endregion

		#region Properties

		[DataMember]
		public virtual Agency Agency
		{
			get { return _agency; }
			set { _agency = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual Customer Customer
		{
			get { return _customer; }
			set { _customer = value; }
		}

		[DataMember]
		public virtual ICollection<BillingRate> BillingRates
		{
			get { return _billingRates; }
			set { _billingRates = value; }
		}

		[DataMember]
		public virtual ICollection<Comment> Comments
		{
			get { return _comments; }
			set { _comments = value; }
		}

		[DataMember]
		public virtual ICollection<Contract> Contracts
		{
			get { return _contracts; }
			set { _contracts = value; }
		}

		[DataMember]
		public virtual ICollection<OrganizationParticipant> OrganizationParticipants
		{
			get { return _organizationParticipants; }
			set { _organizationParticipants = value; }
		}

		[DataMember]
		public virtual ICollection<PreReceiptHeader> PreReceiptHeaders
		{
			get { return _preReceiptHeaders; }
			set { _preReceiptHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<Printer> Printers
		{
			get { return _printers; }
			set { _printers = value; }
		}

		[DataMember]
		public virtual ICollection<PurchaseOrderHeader> PurchaseOrderHeaders
		{
			get { return _purchaseOrderHeaders; }
			set { _purchaseOrderHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<ReturnHeader> ReturnHeaders
		{
			get { return _returnHeaders; }
			set { _returnHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<VendorCommunication> VendorCommunications
		{
			get { return _vendorCommunications; }
			set { _vendorCommunications = value; }
		}

		[DataMember]
		public virtual ICollection<VendorItem> VendorItems
		{
			get { return _vendorItems; }
			set { _vendorItems = value; }
		}

		[DataMember]
		public virtual ICollection<VendorLocation> VendorLocations
		{
			get { return _vendorLocations; }
			set { _vendorLocations = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Name
		{
			get { return _name; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Name must not be blank or null.");
				else _name = value;
			}
		}

		[DataMember]
		public virtual String Url
		{
			get { return _url; }
			set { _url = value; }
		}

		[DataMember]
		public virtual String VendorCode
		{
			get { return _vendorCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("VendorCode must not be blank or null.");
				else _vendorCode = value;
			}
		}

		[DataMember]
		public virtual TimeZone TimeZone
		{
			get { return _timeZone; }
			set { _timeZone = value; }
		}


		#endregion
	}
}
