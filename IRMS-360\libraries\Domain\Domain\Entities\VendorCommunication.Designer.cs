using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class VendorCommunication : Entity
	{
		#region Fields

		private CommunicationRole _communicationRole;
		private String _active;
		private String _communicationValue;
		private String _primaryCommunication;
		private Vendor _vendor;
		private VendorLocation _vendorLocation;

		#endregion

		#region Properties

		[DataMember]
		public virtual CommunicationRole CommunicationRole
		{
			get { return _communicationRole; }
			set { _communicationRole = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String CommunicationValue
		{
			get { return _communicationValue; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("CommunicationValue must not be blank or null.");
				else _communicationValue = value;
			}
		}

		[DataMember]
		public virtual String PrimaryCommunication
		{
			get { return _primaryCommunication; }
			set { _primaryCommunication = value; }
		}

		[DataMember]
		public virtual Vendor Vendor
		{
			get { return _vendor; }
			set { _vendor = value; }
		}

		[DataMember]
		public virtual VendorLocation VendorLocation
		{
			get { return _vendorLocation; }
			set { _vendorLocation = value; }
		}


		#endregion
	}
}
