using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class VendorItem : Entity
	{
		#region Fields

		private DateTime _effective;
		private DateTime? _expiration;
		private Decimal? _unitCost;
		private Decimal? _boxQuantity;
		private Decimal? _caseQuantity;
		private Decimal? _palletQuantity;
		private Item _item;
		private ICollection<ItemTransaction> _itemTransactions = new HashSet<ItemTransaction>();
		private ICollection<PurchaseOrderDetail> _purchaseOrderDetails = new HashSet<PurchaseOrderDetail>();
		private ICollection<ReceiptDetail> _receiptDetails = new HashSet<ReceiptDetail>();
		private ICollection<VendorItem> _childVendorItems = new HashSet<VendorItem>();
		private String _description;
		private String _vendorItemCode;
		private UnitOfMeasure _unitOfMeasure;
		private Vendor _vendor;
		private VendorItem _parentVendorItem;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime Effective
		{
			get { return _effective; }
			set { _effective = value; }
		}

		[DataMember]
		public virtual DateTime? Expiration
		{
			get { return _expiration; }
			set { _expiration = value; }
		}

		[DataMember]
		public virtual Decimal? UnitCost
		{
			get { return _unitCost; }
			set { _unitCost = value; }
		}

		[DataMember]
		public virtual Decimal? BoxQuantity
		{
			get { return _boxQuantity; }
			set { _boxQuantity = value; }
		}

		[DataMember]
		public virtual Decimal? CaseQuantity
		{
			get { return _caseQuantity; }
			set { _caseQuantity = value; }
		}

		[DataMember]
		public virtual Decimal? PalletQuantity
		{
			get { return _palletQuantity; }
			set { _palletQuantity = value; }
		}

		[DataMember]
		public virtual Item Item
		{
			get { return _item; }
			set { _item = value; }
		}

		[DataMember]
		public virtual ICollection<ItemTransaction> ItemTransactions
		{
			get { return _itemTransactions; }
			set { _itemTransactions = value; }
		}

		[DataMember]
		public virtual ICollection<PurchaseOrderDetail> PurchaseOrderDetails
		{
			get { return _purchaseOrderDetails; }
			set { _purchaseOrderDetails = value; }
		}

		[DataMember]
		public virtual ICollection<ReceiptDetail> ReceiptDetails
		{
			get { return _receiptDetails; }
			set { _receiptDetails = value; }
		}

		[DataMember]
		public virtual ICollection<VendorItem> ChildVendorItems
		{
			get { return _childVendorItems; }
			set { _childVendorItems = value; }
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set	{ _description = value; }
		}

		[DataMember]
		public virtual String VendorItemCode
		{
			get { return _vendorItemCode; }
			set	{ _vendorItemCode = value; }
		}

		[DataMember]
		public virtual UnitOfMeasure UnitOfMeasure
		{
			get { return _unitOfMeasure; }
			set { _unitOfMeasure = value; }
		}

		[DataMember]
		public virtual Vendor Vendor
		{
			get { return _vendor; }
			set { _vendor = value; }
		}

		[DataMember]
		public virtual VendorItem ParentVendorItem
		{
			get { return _parentVendorItem; }
			set { _parentVendorItem = value; }
		}


		#endregion
	}
}
