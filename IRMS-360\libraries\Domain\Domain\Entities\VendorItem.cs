using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class VendorItem : Entity
	{
		#region Properties.Reports

		public virtual String ItemCode { get; set; }
        public virtual String VendorName { get; set; }

		#endregion

		#region Constructor

		public VendorItem()
		{
			//
		}

		#endregion
	}
}
