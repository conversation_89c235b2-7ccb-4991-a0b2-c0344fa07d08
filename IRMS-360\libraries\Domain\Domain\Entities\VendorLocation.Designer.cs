using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class VendorLocation : Entity
	{
		#region Fields

		private ICollection<VendorCommunication> _vendorCommunications = new HashSet<VendorCommunication>();
		private ICollection<VendorLocationType> _vendorLocationTypes = new HashSet<VendorLocationType>();
		private String _active;
		private String _addressDirection;
		private String _addressLine1;
		private String _addressLine2;
		private String _addressLine3;
		private String _addressNumber;
		private String _addressSuffix;
		private String _addressZipCode;
		private String _city;
		private String _country;
		private String _stateCode;
		private TimeZone _timeZone;
		private Vendor _vendor;
		private ZipCode _zipCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<VendorCommunication> VendorCommunications
		{
			get { return _vendorCommunications; }
			set { _vendorCommunications = value; }
		}

		[DataMember]
		public virtual ICollection<VendorLocationType> VendorLocationTypes
		{
			get { return _vendorLocationTypes; }
			set { _vendorLocationTypes = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set { _active = value; }
		}

		[DataMember]
		public virtual String AddressDirection
		{
			get { return _addressDirection; }
			set { _addressDirection = value; }
		}

		[DataMember]
		public virtual String AddressLine1
		{
			get { return _addressLine1; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("AddressLine1 must not be blank or null.");
				else _addressLine1 = value;
			}
		}

		[DataMember]
		public virtual String AddressLine2
		{
			get { return _addressLine2; }
			set { _addressLine2 = value; }
		}

		[DataMember]
		public virtual String AddressLine3
		{
			get { return _addressLine3; }
			set { _addressLine3 = value; }
		}

		[DataMember]
		public virtual String AddressNumber
		{
			get { return _addressNumber; }
			set { _addressNumber = value; }
		}

		[DataMember]
		public virtual String AddressSuffix
		{
			get { return _addressSuffix; }
			set { _addressSuffix = value; }
		}

		[DataMember]
		public virtual String AddressZipCode
		{
			get { return _addressZipCode; }
			set { _addressZipCode = value; }
		}

		[DataMember]
		public virtual String City
		{
			get { return _city; }
			set { _city = value; }
		}

		[DataMember]
		public virtual String Country
		{
			get { return _country; }
			set { _country = value; }
		}

		[DataMember]
		public virtual String StateCode
		{
			get { return _stateCode; }
			set { _stateCode = value; }
		}

		[DataMember]
		public virtual TimeZone TimeZone
		{
			get { return _timeZone; }
			set { _timeZone = value; }
		}

		[DataMember]
		public virtual Vendor Vendor
		{
			get { return _vendor; }
			set { _vendor = value; }
		}

		[DataMember]
		public virtual ZipCode ZipCode
		{
			get { return _zipCode; }
			set { _zipCode = value; }
		}


		#endregion
	}
}
