using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class VendorLocationType : Entity
	{
		#region Fields

		private ICollection<ItemDocument> _itemDocuments = new HashSet<ItemDocument>();
		private ICollection<MaintenanceRequest> _maintenanceRequests = new HashSet<MaintenanceRequest>();
		private ICollection<OrganizationParticipant> _organizationParticipants = new HashSet<OrganizationParticipant>();
		private ICollection<Pool> _pools = new HashSet<Pool>();
		private LocationType _locationType;
		private String _active;
		private String _description;
		private String _vendorLocationCode;
		private VendorLocation _vendorLocation;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<ItemDocument> ItemDocuments
		{
			get { return _itemDocuments; }
			set { _itemDocuments = value; }
		}

		[DataMember]
		public virtual ICollection<MaintenanceRequest> MaintenanceRequests
		{
			get { return _maintenanceRequests; }
			set { _maintenanceRequests = value; }
		}

		[DataMember]
		public virtual ICollection<OrganizationParticipant> OrganizationParticipants
		{
			get { return _organizationParticipants; }
			set { _organizationParticipants = value; }
		}

		[DataMember]
		public virtual ICollection<Pool> Pools
		{
			get { return _pools; }
			set { _pools = value; }
		}

		[DataMember]
		public virtual LocationType LocationType
		{
			get { return _locationType; }
			set { _locationType = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set { _description = value; }
		}

		[DataMember]
		public virtual String VendorLocationCode
		{
			get { return _vendorLocationCode; }
			set { _vendorLocationCode = value; }
		}

		[DataMember]
		public virtual VendorLocation VendorLocation
		{
			get { return _vendorLocation; }
			set { _vendorLocation = value; }
		}


		#endregion
	}
}
