using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Wave : Entity
	{
		#region Fields

		private CompanyLocationType _companyLocationType;
		private Decimal? _totalWeight;
		private Int32? _totalLineCount;
		private Int32? _totalOrderCount;
		private ICollection<CartonHeader> _cartonHeaders = new HashSet<CartonHeader>();
		private ICollection<ItemFulfillment> _itemFulfillments = new HashSet<ItemFulfillment>();
		private ICollection<ItemTransaction> _itemTransactions = new HashSet<ItemTransaction>();
		private ICollection<Transaction> _transactions = new HashSet<Transaction>();
		private ICollection<WaveAssignment> _waveAssignments = new HashSet<WaveAssignment>();
		private StatusCode _integrationStatusCode;
		private StatusCode _statusCode;
		private String _hostBatch;
		private String _waveCode;
		private Task _task;
		private WaveType _waveType;

		#endregion

		#region Properties

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual Decimal? TotalWeight
		{
			get { return _totalWeight; }
			set { _totalWeight = value; }
		}

		[DataMember]
		public virtual Int32? TotalLineCount
		{
			get { return _totalLineCount; }
			set { _totalLineCount = value; }
		}

		[DataMember]
		public virtual Int32? TotalOrderCount
		{
			get { return _totalOrderCount; }
			set { _totalOrderCount = value; }
		}

		[DataMember]
		public virtual ICollection<CartonHeader> CartonHeaders
		{
			get { return _cartonHeaders; }
			set { _cartonHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<ItemFulfillment> ItemFulfillments
		{
			get { return _itemFulfillments; }
			set { _itemFulfillments = value; }
		}

		[DataMember]
		public virtual ICollection<ItemTransaction> ItemTransactions
		{
			get { return _itemTransactions; }
			set { _itemTransactions = value; }
		}

		[DataMember]
		public virtual ICollection<Transaction> Transactions
		{
			get { return _transactions; }
			set { _transactions = value; }
		}

		[DataMember]
		public virtual ICollection<WaveAssignment> WaveAssignments
		{
			get { return _waveAssignments; }
			set { _waveAssignments = value; }
		}

		[DataMember]
		public virtual StatusCode IntegrationStatusCode
		{
            get { return _integrationStatusCode != null ? _integrationStatusCode : StatusCode.GetOpenIntegrationStatusCode(); }
            set { _integrationStatusCode = value != null ? value : StatusCode.GetOpenIntegrationStatusCode(); }
        }

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual String HostBatch
		{
			get { return _hostBatch; }
			set { _hostBatch = value; }
		}

		[DataMember]
		public virtual String WaveCode
		{
			get { return _waveCode; }
			set { _waveCode = value; }
		}

		[DataMember]
		public virtual Task Task
		{
			get { return _task; }
			set { _task = value; }
		}

		[DataMember]
		public virtual WaveType WaveType
		{
			get { return _waveType; }
			set { _waveType = value; }
		}


		#endregion
	}
}
