using System;
using System.Runtime.Serialization;

using NHibernate.Criterion;

using Upp.Irms.Constants;
using Upp.Irms.Core;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class Wave : Entity
	{
		#region Properties

		[DataMember]
		public virtual Decimal? TotalCube { get; set; }
		[DataMember]
		public virtual Int32 Singles { get; set; }
		[DataMember]
		public virtual String HasEmergency { get; set; }
		[DataMember]
		public virtual String HasTask { get; set; }
		[DataMember]
		public virtual String StatusCodeDescription { get; set; }
        [DataMember]
        public virtual String WaveTypeCode { get; set; }

		#endregion

		#region Constructor

		public Wave()
		{
			//
		}

		#endregion

		#region Methods.Public

		public virtual void CalculateSingles()
		{
			DetachedCriteria count = DetachedCriteria.For<OrderDetail>()
				.CreateAlias("OrderHeader", "oh")
				.Add(Expression.EqProperty("_root.Id", "oh.Id"))
				.Add("ItemFulfillments.Wave", this)
				.SetProjection(Projections.Count("Id"));
			DetachedCriteria criteria = DetachedCriteria.For<OrderHeader>("_root")
				.Add(Restrictions.Eq(Projections.SubQuery(count), 1))
				.SetProjection(Projections.Count("Id"));
			this.Singles = Repositories.Get<OrderHeader>().Function<Int32>(criteria);
		}

		public virtual void CalculateTotalCube()
		{
			DetachedCriteria criteria = DetachedCriteria.For<ItemFulfillment>()
				.CreateAlias("Item", "i")
				.Add("Wave", this)
				.SetProjection(Projections.Sum("i.Cube"));
			this.TotalCube = Repositories.Get<ItemFulfillment>().Function<Decimal?>(criteria);
		}

		public virtual void ChangeStatus(StatusCode status)
		{
			_dateModified = DateTime.Now;
			_statusCode = status;
			_userModified = Registry.Find<UserAccount>().UserName;
			//
			Repositories.Get<Wave>().Update(this);
		}

		public virtual void CheckEmergency()
		{
			OrderType emergency = Entity.Retrieve<OrderType>(CoreOrderTypes.Emergency);
			DetachedCriteria criteria = DetachedCriteria.For<ItemFulfillment>()
				.Add("OrderDetail.OrderHeader.OrderType", emergency)
				.Add("Wave", this)
				.SetProjection(Projections.Count("Id"));
			this.HasEmergency = (Repositories.Get<ItemFulfillment>().Function<Int32>(criteria) > 0) ? "E" : " ";
		}

        public virtual void CheckWaveAssignment()
        {
            DetachedCriteria criteria = DetachedCriteria.For<WaveAssignment>()
                .Add(Expression.Eq("Wave", this))
                .SetMaxResults(1);
            WaveAssignment assignment = Repositories.Get<WaveAssignment>().Retrieve(criteria);
            //
            if (assignment != null && assignment.OrganizationParticipant != null)
            {
                if (assignment.OrganizationParticipant.ParticipantRole.Participant.Equals(Registry.Find<UserAccount>().OrganizationParticipant.ParticipantRole.Participant))
                    this.HasTask = "E";
                else this.HasTask = "R";
            }
            else this.HasTask = " ";
        }

		public virtual void Close()
		{
            StatusCode complete = Entity.Retrieve<StatusCode>(OrderStatuses.Complete);
			StatusCode shipped = Entity.Retrieve<StatusCode>(OrderStatuses.Shipped);
			DetachedCriteria criteria = DetachedCriteria.For<ItemFulfillment>()
				.Add(Expression.Eq("StatusCode", shipped))
				.Add(Expression.Eq("Wave", this))
				.SetProjection(Projections.Count("Id"));
			Int32 count = Repositories.Get<ItemFulfillment>().Function<Int32>(criteria);
			//
			criteria = DetachedCriteria.For<ItemFulfillment>()
				.Add(Expression.Eq("Wave", this))
				.SetProjection(Projections.Count("Id"));
			Int32 total = Repositories.Get<ItemFulfillment>().Function<Int32>(criteria);
			if (count == total)
			{
				_dateModified = DateTime.Now;
                _statusCode = complete;
				_userModified = Registry.Find<UserAccount>().UserName;
			}
		}

		public virtual void Open()
		{
			DetachedCriteria criteria = DetachedCriteria.For<ItemFulfillment>()
				.Add("Wave", this)
				.Add("StatusCode.Code", "!=", CodeValue.GetCode(OrderStatuses.Distributed))
				.SetProjection(Projections.Count("Id"));
			Int32 count = Repositories.Get<ItemFulfillment>().Function<Int32>(criteria);
			//
			if (count == 0)
			{
				_dateModified = DateTime.Now;
				_statusCode = Entity.Retrieve<StatusCode>(OrderStatuses.Open);
				_userModified = Registry.Find<UserAccount>().UserName;
				//
				Repositories.Get<Wave>().Update(this);
			}
		}

		#endregion
	}
}
