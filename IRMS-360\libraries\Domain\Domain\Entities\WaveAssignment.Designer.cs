using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class WaveAssignment : Entity
	{
		#region Fields

		private OrganizationParticipant _organizationParticipant;
		private Wave _wave;

		#endregion

		#region Properties

		[DataMember]
		public virtual OrganizationParticipant OrganizationParticipant
		{
			get { return _organizationParticipant; }
			set { _organizationParticipant = value; }
		}

		[DataMember]
		public virtual Wave Wave
		{
			get { return _wave; }
			set { _wave = value; }
		}


		#endregion
	}
}
