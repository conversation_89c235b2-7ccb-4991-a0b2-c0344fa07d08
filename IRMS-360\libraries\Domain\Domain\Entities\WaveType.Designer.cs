using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class WaveType : Entity
	{
		#region Fields

		private ICollection<Wave> _waves = new HashSet<Wave>();
		private String _active;
		private String _description;
		private String _waveTypeCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<Wave> Waves
		{
			get { return _waves; }
			set { _waves = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String WaveTypeCode
		{
			get { return _waveTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("WaveTypeCode must not be blank or null.");
				else _waveTypeCode = value;
			}
		}


		#endregion
	}
}
