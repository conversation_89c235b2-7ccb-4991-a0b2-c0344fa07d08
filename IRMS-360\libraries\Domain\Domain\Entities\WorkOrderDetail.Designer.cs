using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class WorkOrderDetail : Entity
	{
		#region Fields

		private DateTime? _maintenanceBegin;
		private DateTime? _maintenanceEnd;
		private Decimal? _kitQuantity;
		private Decimal? _laborCost;
		private Decimal? _maintenanceCost;
		private Decimal? _quantity;
		private Decimal? _repairCost;
		private Decimal? _unitCost;
		private Int32 _lineNumber;
		private InventoryItem _inventoryItem;
		private Item _item;
		private Item _kitItem;
		private ICollection<WorkOrderDocument> _workOrderDocuments = new HashSet<WorkOrderDocument>();
		private ICollection<WorkOrderItem> _workOrderItems = new HashSet<WorkOrderItem>();
		private ICollection<WorkOrderMeterReading> _workOrderMeterReadings = new HashSet<WorkOrderMeterReading>();
		private Location _locationIn;
		private Location _locationOut;
		private MaintenanceDetail _maintenanceDetail;
		private Pool _pool;
		private StatusCode _statusCode;
		private String _lotNumber;
		private String _notes;
		private String _workDescription;
		private WorkOrderHeader _workOrderHeader;

		#endregion

		#region Properties

		[DataMember]
		public virtual DateTime? MaintenanceBegin
		{
			get { return _maintenanceBegin; }
			set { _maintenanceBegin = value; }
		}

		[DataMember]
		public virtual DateTime? MaintenanceEnd
		{
			get { return _maintenanceEnd; }
			set { _maintenanceEnd = value; }
		}

		[DataMember]
		public virtual Decimal? KitQuantity
		{
			get { return _kitQuantity; }
			set { _kitQuantity = value; }
		}

		[DataMember]
		public virtual Decimal? LaborCost
		{
			get { return _laborCost; }
			set { _laborCost = value; }
		}

		[DataMember]
		public virtual Decimal? MaintenanceCost
		{
			get { return _maintenanceCost; }
			set { _maintenanceCost = value; }
		}

		[DataMember]
		public virtual Decimal? Quantity
		{
			get { return _quantity; }
			set { _quantity = value; }
		}

		[DataMember]
		public virtual Decimal? RepairCost
		{
			get { return _repairCost; }
			set { _repairCost = value; }
		}

		[DataMember]
		public virtual Decimal? UnitCost
		{
			get { return _unitCost; }
			set { _unitCost = value; }
		}

		[DataMember]
		public virtual Int32 LineNumber
		{
			get { return _lineNumber; }
			set { _lineNumber = value; }
		}

		[DataMember]
		public virtual InventoryItem InventoryItem
		{
			get { return _inventoryItem; }
			set { _inventoryItem = value; }
		}

		[DataMember]
		public virtual Item Item
		{
			get { return _item; }
			set { _item = value; }
		}

		[DataMember]
		public virtual Item KitItem
		{
			get { return _kitItem; }
			set { _kitItem = value; }
		}

		[DataMember]
		public virtual ICollection<WorkOrderDocument> WorkOrderDocuments
		{
			get { return _workOrderDocuments; }
			set { _workOrderDocuments = value; }
		}

		[DataMember]
		public virtual ICollection<WorkOrderItem> WorkOrderItems
		{
			get { return _workOrderItems; }
			set { _workOrderItems = value; }
		}

		[DataMember]
		public virtual ICollection<WorkOrderMeterReading> WorkOrderMeterReadings
		{
			get { return _workOrderMeterReadings; }
			set { _workOrderMeterReadings = value; }
		}

		[DataMember]
		public virtual Location LocationIn
		{
			get { return _locationIn; }
			set { _locationIn = value; }
		}

		[DataMember]
		public virtual Location LocationOut
		{
			get { return _locationOut; }
			set { _locationOut = value; }
		}

		[DataMember]
		public virtual MaintenanceDetail MaintenanceDetail
		{
			get { return _maintenanceDetail; }
			set { _maintenanceDetail = value; }
		}

		[DataMember]
		public virtual Pool Pool
		{
			get { return _pool; }
			set { _pool = value; }
		}

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual String LotNumber
		{
			get { return _lotNumber; }
			set { _lotNumber = value; }
		}

		[DataMember]
		public virtual String Notes
		{
			get { return _notes; }
			set { _notes = value; }
		}

		[DataMember]
		public virtual String WorkDescription
		{
			get { return _workDescription; }
			set { _workDescription = value; }
		}

		[DataMember]
		public virtual WorkOrderHeader WorkOrderHeader
		{
			get { return _workOrderHeader; }
			set { _workOrderHeader = value; }
		}


		#endregion
	}
}
