using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class WorkOrderDocument : Entity
	{
		#region Fields

		private Byte[] _document;
		private DocumentType _documentType;
		private String _alternateLocation;
		private String _fileName;
		private String _notes;
		private WorkOrderDetail _workOrderDetail;
		private WorkOrderHeader _workOrderHeader;

		#endregion

		#region Properties

		[DataMember]
		public virtual Byte[] Document
		{
			get { return _document; }
			set { _document = value; }
		}

		[DataMember]
		public virtual DocumentType DocumentType
		{
			get { return _documentType; }
			set { _documentType = value; }
		}

		[DataMember]
		public virtual String AlternateLocation
		{
			get { return _alternateLocation; }
			set { _alternateLocation = value; }
		}

		[DataMember]
		public virtual String FileName
		{
			get { return _fileName; }
			set { _fileName = value; }
		}

		[DataMember]
		public virtual String Notes
		{
			get { return _notes; }
			set { _notes = value; }
		}

		[DataMember]
		public virtual WorkOrderDetail WorkOrderDetail
		{
			get { return _workOrderDetail; }
			set { _workOrderDetail = value; }
		}

		[DataMember]
		public virtual WorkOrderHeader WorkOrderHeader
		{
			get { return _workOrderHeader; }
			set { _workOrderHeader = value; }
		}


		#endregion
	}
}
