using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class WorkOrderHeader : Entity
	{
		#region Fields

		private Agency _agency;
		private CompanyLocationType _companyLocationType;
		private CompanyOrganizationalUnit _companyOrganizationalUnit;
		private Customer _customer;
		private DateTime? _arrived;
		private DateTime? _departed;
		private DateTime? _maintenanceBegin;
		private DateTime? _maintenanceEnd;
		private DateTime? _requiredBy;
		private Decimal? _maintenanceCost;
		private InventoryItem _inventoryItem;
		private ICollection<ApprovalRequest> _approvalRequests = new HashSet<ApprovalRequest>();
		private ICollection<InvoiceDetail> _invoiceDetails = new HashSet<InvoiceDetail>();
		private ICollection<InvoiceHeader> _invoiceHeaders = new HashSet<InvoiceHeader>();
		private ICollection<Transaction> _transactions = new HashSet<Transaction>();
		private ICollection<WorkOrderComment> _workOrderComments = new HashSet<WorkOrderComment>();
		private ICollection<WorkOrderDetail> _workOrderDetails = new HashSet<WorkOrderDetail>();
		private ICollection<WorkOrderDocument> _workOrderDocuments = new HashSet<WorkOrderDocument>();
		private ICollection<WorkOrderHeader> _childWorkOrderHeaders = new HashSet<WorkOrderHeader>();
		private ICollection<WorkOrderService> _workOrderServices = new HashSet<WorkOrderService>();
		private ICollection<WorkOrderStatus> _workOrderStatuses = new HashSet<WorkOrderStatus>();
		private Location _location;
		private MaintenanceProgram _maintenanceProgram;
		private MaintenanceRequest _maintenanceRequest;
		private OrderDetail _orderDetail;
		private OrganizationParticipant _participantBy;
		private OrganizationParticipant _participantFor;
		private Pool _pool;
		private StatusCode _integrationStatusCode;
		private StatusCode _statusCode;
		private String _kitBuildType;
		private String _notes;
		private String _readingIn;
		private String _readingOut;
		private String _suffix;
		private String _workOrderCode;
		private UnitOfMeasure _unitOfMeasure;
		private WorkOrderHeader _parentWorkOrderHeader;
		private WorkOrderType _workOrderType;

		#endregion

		#region Properties

		[DataMember]
		public virtual Agency Agency
		{
			get { return _agency; }
			set { _agency = value; }
		}

		[DataMember]
		public virtual CompanyLocationType CompanyLocationType
		{
			get { return _companyLocationType; }
			set { _companyLocationType = value; }
		}

		[DataMember]
		public virtual CompanyOrganizationalUnit CompanyOrganizationalUnit
		{
			get { return _companyOrganizationalUnit; }
			set { _companyOrganizationalUnit = value; }
		}

		[DataMember]
		public virtual Customer Customer
		{
			get { return _customer; }
			set { _customer = value; }
		}

		[DataMember]
		public virtual DateTime? Arrived
		{
			get { return _arrived; }
			set { _arrived = value; }
		}

		[DataMember]
		public virtual DateTime? Departed
		{
			get { return _departed; }
			set { _departed = value; }
		}

		[DataMember]
		public virtual DateTime? MaintenanceBegin
		{
			get { return _maintenanceBegin; }
			set { _maintenanceBegin = value; }
		}

		[DataMember]
		public virtual DateTime? MaintenanceEnd
		{
			get { return _maintenanceEnd; }
			set { _maintenanceEnd = value; }
		}

		[DataMember]
		public virtual DateTime? RequiredBy
		{
			get { return _requiredBy; }
			set { _requiredBy = value; }
		}

		[DataMember]
		public virtual Decimal? MaintenanceCost
		{
			get { return _maintenanceCost; }
			set { _maintenanceCost = value; }
		}

		[DataMember]
		public virtual InventoryItem InventoryItem
		{
			get { return _inventoryItem; }
			set { _inventoryItem = value; }
		}

		[DataMember]
		public virtual ICollection<ApprovalRequest> ApprovalRequests
		{
			get { return _approvalRequests; }
			set { _approvalRequests = value; }
		}

		[DataMember]
		public virtual ICollection<InvoiceDetail> InvoiceDetails
		{
			get { return _invoiceDetails; }
			set { _invoiceDetails = value; }
		}

		[DataMember]
		public virtual ICollection<InvoiceHeader> InvoiceHeaders
		{
			get { return _invoiceHeaders; }
			set { _invoiceHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<Transaction> Transactions
		{
			get { return _transactions; }
			set { _transactions = value; }
		}

		[DataMember]
		public virtual ICollection<WorkOrderComment> WorkOrderComments
		{
			get { return _workOrderComments; }
			set { _workOrderComments = value; }
		}

		[DataMember]
		public virtual ICollection<WorkOrderDetail> WorkOrderDetails
		{
			get { return _workOrderDetails; }
			set { _workOrderDetails = value; }
		}

		[DataMember]
		public virtual ICollection<WorkOrderDocument> WorkOrderDocuments
		{
			get { return _workOrderDocuments; }
			set { _workOrderDocuments = value; }
		}

		[DataMember]
		public virtual ICollection<WorkOrderHeader> ChildWorkOrderHeaders
		{
			get { return _childWorkOrderHeaders; }
			set { _childWorkOrderHeaders = value; }
		}

		[DataMember]
		public virtual ICollection<WorkOrderService> WorkOrderServices
		{
			get { return _workOrderServices; }
			set { _workOrderServices = value; }
		}

		[DataMember]
		public virtual ICollection<WorkOrderStatus> WorkOrderStatuses
		{
			get { return _workOrderStatuses; }
			set { _workOrderStatuses = value; }
		}

		[DataMember]
		public virtual Location Location
		{
			get { return _location; }
			set { _location = value; }
		}

		[DataMember]
		public virtual MaintenanceProgram MaintenanceProgram
		{
			get { return _maintenanceProgram; }
			set { _maintenanceProgram = value; }
		}

		[DataMember]
		public virtual MaintenanceRequest MaintenanceRequest
		{
			get { return _maintenanceRequest; }
			set { _maintenanceRequest = value; }
		}

		[DataMember]
		public virtual OrderDetail OrderDetail
		{
			get { return _orderDetail; }
			set { _orderDetail = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant ParticipantBy
		{
			get { return _participantBy; }
			set { _participantBy = value; }
		}

		[DataMember]
		public virtual OrganizationParticipant ParticipantFor
		{
			get { return _participantFor; }
			set { _participantFor = value; }
		}

		[DataMember]
		public virtual Pool Pool
		{
			get { return _pool; }
			set { _pool = value; }
		}

		[DataMember]
		public virtual StatusCode IntegrationStatusCode
		{
            get { return _integrationStatusCode != null ? _integrationStatusCode : StatusCode.GetOpenIntegrationStatusCode(); }
            set { _integrationStatusCode = value != null ? value : StatusCode.GetOpenIntegrationStatusCode(); }
        }

		[DataMember]
		public virtual StatusCode StatusCode
		{
			get { return _statusCode; }
			set { _statusCode = value; }
		}

		[DataMember]
		public virtual String KitBuildType
		{
			get { return _kitBuildType; }
			set { _kitBuildType = value; }
		}

		[DataMember]
		public virtual String Notes
		{
			get { return _notes; }
			set { _notes = value; }
		}

		[DataMember]
		public virtual String ReadingIn
		{
			get { return _readingIn; }
			set { _readingIn = value; }
		}

		[DataMember]
		public virtual String ReadingOut
		{
			get { return _readingOut; }
			set { _readingOut = value; }
		}

		[DataMember]
		public virtual String Suffix
		{
			get { return _suffix; }
			set { _suffix = value; }
		}

		[DataMember]
		public virtual String WorkOrderCode
		{
			get { return _workOrderCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("WorkOrderCode must not be blank or null.");
				else _workOrderCode = value;
			}
		}

		[DataMember]
		public virtual UnitOfMeasure UnitOfMeasure
		{
			get { return _unitOfMeasure; }
			set { _unitOfMeasure = value; }
		}

		[DataMember]
		public virtual WorkOrderHeader ParentWorkOrderHeader
		{
			get { return _parentWorkOrderHeader; }
			set { _parentWorkOrderHeader = value; }
		}

		[DataMember]
		public virtual WorkOrderType WorkOrderType
		{
			get { return _workOrderType; }
			set { _workOrderType = value; }
		}


		#endregion
	}
}
