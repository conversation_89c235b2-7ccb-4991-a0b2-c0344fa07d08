using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class WorkOrderItem : Entity
	{
		#region Fields

		private Decimal _quantity;
		private InventoryItem _inventoryItem;
		private InventoryItem _oldInventoryItem;
		private String _itemDescription;
		private String _notes;
		private TransactionType _transactionType;
		private WorkOrderDetail _workOrderDetail;

		#endregion

		#region Properties

		[DataMember]
		public virtual Decimal Quantity
		{
			get { return _quantity; }
			set { _quantity = value; }
		}

		[DataMember]
		public virtual InventoryItem InventoryItem
		{
			get { return _inventoryItem; }
			set { _inventoryItem = value; }
		}

		[DataMember]
		public virtual InventoryItem OldInventoryItem
		{
			get { return _oldInventoryItem; }
			set { _oldInventoryItem = value; }
		}

		[DataMember]
		public virtual String ItemDescription
		{
			get { return _itemDescription; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ItemDescription must not be blank or null.");
				else _itemDescription = value;
			}
		}

		[DataMember]
		public virtual String Notes
		{
			get { return _notes; }
			set { _notes = value; }
		}

		[DataMember]
		public virtual TransactionType TransactionType
		{
			get { return _transactionType; }
			set { _transactionType = value; }
		}

		[DataMember]
		public virtual WorkOrderDetail WorkOrderDetail
		{
			get { return _workOrderDetail; }
			set { _workOrderDetail = value; }
		}


		#endregion
	}
}
