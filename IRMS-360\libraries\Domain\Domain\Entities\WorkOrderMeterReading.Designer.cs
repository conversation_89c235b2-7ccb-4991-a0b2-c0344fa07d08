using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class WorkOrderMeterReading : Entity
	{
		#region Fields

		private MaintenanceProgramReading _maintenanceProgramReading;
		private MeterReadingType _meterReadingType;
		private String _notes;
		private String _readingDescription;
		private String _readingIn;
		private String _readingOut;
		private UnitOfMeasure _readingInUom;
		private UnitOfMeasure _readingOutUom;
		private WorkOrderDetail _workOrderDetail;

		#endregion

		#region Properties

		[DataMember]
		public virtual MaintenanceProgramReading MaintenanceProgramReading
		{
			get { return _maintenanceProgramReading; }
			set { _maintenanceProgramReading = value; }
		}

		[DataMember]
		public virtual MeterReadingType MeterReadingType
		{
			get { return _meterReadingType; }
			set { _meterReadingType = value; }
		}

		[DataMember]
		public virtual String Notes
		{
			get { return _notes; }
			set { _notes = value; }
		}

		[DataMember]
		public virtual String ReadingDescription
		{
			get { return _readingDescription; }
			set { _readingDescription = value; }
		}

		[DataMember]
		public virtual String ReadingIn
		{
			get { return _readingIn; }
			set { _readingIn = value; }
		}

		[DataMember]
		public virtual String ReadingOut
		{
			get { return _readingOut; }
			set { _readingOut = value; }
		}

		[DataMember]
		public virtual UnitOfMeasure ReadingInUom
		{
			get { return _readingInUom; }
			set { _readingInUom = value; }
		}

		[DataMember]
		public virtual UnitOfMeasure ReadingOutUom
		{
			get { return _readingOutUom; }
			set { _readingOutUom = value; }
		}

		[DataMember]
		public virtual WorkOrderDetail WorkOrderDetail
		{
			get { return _workOrderDetail; }
			set { _workOrderDetail = value; }
		}


		#endregion
	}
}
