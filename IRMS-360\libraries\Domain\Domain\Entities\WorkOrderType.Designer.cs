using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class WorkOrderType : Entity
	{
		#region Fields

		private ICollection<WorkOrderHeader> _workOrderHeaders = new HashSet<WorkOrderHeader>();
		private String _active;
		private String _description;
		private String _workOrderTypeCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<WorkOrderHeader> WorkOrderHeaders
		{
			get { return _workOrderHeaders; }
			set { _workOrderHeaders = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String WorkOrderTypeCode
		{
			get { return _workOrderTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("WorkOrderTypeCode must not be blank or null.");
				else _workOrderTypeCode = value;
			}
		}


		#endregion
	}
}
