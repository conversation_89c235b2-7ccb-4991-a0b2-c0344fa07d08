using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ZipCode : Entity
	{
		#region Fields

		private CountyCode _countyCode;
		private ICollection<AgencyLocation> _agencyLocations = new HashSet<AgencyLocation>();
		private ICollection<Broker> _brokers = new HashSet<Broker>();
		private ICollection<CarrierLocation> _carrierLocations = new HashSet<CarrierLocation>();
		private ICollection<CompanyGlobalLocation> _companyGlobalLocations = new HashSet<CompanyGlobalLocation>();
		private ICollection<CompanyLocation> _companyLocations = new HashSet<CompanyLocation>();
		private ICollection<CustomerLocation> _customerLocations = new HashSet<CustomerLocation>();
		private ICollection<InsurancePayerLocation> _insurancePayerLocations = new HashSet<InsurancePayerLocation>();
		private ICollection<ManufacturerLocation> _manufacturerLocations = new HashSet<ManufacturerLocation>();
		private ICollection<OrderLocation> _orderLocations = new HashSet<OrderLocation>();
		private ICollection<ParentBillOfLading> _parentBillOfLadings = new HashSet<ParentBillOfLading>();
		private ICollection<ParticipantLocation> _participantLocations = new HashSet<ParticipantLocation>();
		private ICollection<ProviderLocation> _providerLocations = new HashSet<ProviderLocation>();
		private ICollection<PurchaseOrderLocation> _purchaseOrderLocations = new HashSet<PurchaseOrderLocation>();
		private ICollection<RequisitionLocation> _requisitionLocations = new HashSet<RequisitionLocation>();
		private ICollection<ShipmentLocation> _shipmentLocations = new HashSet<ShipmentLocation>();
		private ICollection<VendorLocation> _vendorLocations = new HashSet<VendorLocation>();
		private StateCode _stateCode;
		private String _city;
		private String _code;
		private String _country;
		private String _county;
		private String _latitude;
		private String _longitude;
		private String _state;

		#endregion

		#region Properties

		[DataMember]
		public virtual CountyCode CountyCode
		{
			get { return _countyCode; }
			set { _countyCode = value; }
		}

		[DataMember]
		public virtual ICollection<AgencyLocation> AgencyLocations
		{
			get { return _agencyLocations; }
			set { _agencyLocations = value; }
		}

		[DataMember]
		public virtual ICollection<Broker> Brokers
		{
			get { return _brokers; }
			set { _brokers = value; }
		}

		[DataMember]
		public virtual ICollection<CarrierLocation> CarrierLocations
		{
			get { return _carrierLocations; }
			set { _carrierLocations = value; }
		}

		[DataMember]
		public virtual ICollection<CompanyGlobalLocation> CompanyGlobalLocations
		{
			get { return _companyGlobalLocations; }
			set { _companyGlobalLocations = value; }
		}

		[DataMember]
		public virtual ICollection<CompanyLocation> CompanyLocations
		{
			get { return _companyLocations; }
			set { _companyLocations = value; }
		}

		[DataMember]
		public virtual ICollection<CustomerLocation> CustomerLocations
		{
			get { return _customerLocations; }
			set { _customerLocations = value; }
		}

		[DataMember]
		public virtual ICollection<InsurancePayerLocation> InsurancePayerLocations
		{
			get { return _insurancePayerLocations; }
			set { _insurancePayerLocations = value; }
		}

		[DataMember]
		public virtual ICollection<ManufacturerLocation> ManufacturerLocations
		{
			get { return _manufacturerLocations; }
			set { _manufacturerLocations = value; }
		}

		[DataMember]
		public virtual ICollection<OrderLocation> OrderLocations
		{
			get { return _orderLocations; }
			set { _orderLocations = value; }
		}

		[DataMember]
		public virtual ICollection<ParentBillOfLading> ParentBillOfLadings
		{
			get { return _parentBillOfLadings; }
			set { _parentBillOfLadings = value; }
		}

		[DataMember]
		public virtual ICollection<ParticipantLocation> ParticipantLocations
		{
			get { return _participantLocations; }
			set { _participantLocations = value; }
		}

		[DataMember]
		public virtual ICollection<ProviderLocation> ProviderLocations
		{
			get { return _providerLocations; }
			set { _providerLocations = value; }
		}

		[DataMember]
		public virtual ICollection<PurchaseOrderLocation> PurchaseOrderLocations
		{
			get { return _purchaseOrderLocations; }
			set { _purchaseOrderLocations = value; }
		}

		[DataMember]
		public virtual ICollection<RequisitionLocation> RequisitionLocations
		{
			get { return _requisitionLocations; }
			set { _requisitionLocations = value; }
		}

		[DataMember]
		public virtual ICollection<ShipmentLocation> ShipmentLocations
		{
			get { return _shipmentLocations; }
			set { _shipmentLocations = value; }
		}

		[DataMember]
		public virtual ICollection<VendorLocation> VendorLocations
		{
			get { return _vendorLocations; }
			set { _vendorLocations = value; }
		}

		[DataMember]
		public virtual StateCode StateCode
		{
			get { return _stateCode; }
			set { _stateCode = value; }
		}

		[DataMember]
		public virtual String City
		{
			get { return _city; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("City must not be blank or null.");
				else _city = value;
			}
		}

		[DataMember]
		public virtual String Code
		{
			get { return _code; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Code must not be blank or null.");
				else _code = value;
			}
		}

		[DataMember]
		public virtual String Country
		{
			get { return _country; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Country must not be blank or null.");
				else _country = value;
			}
		}

		[DataMember]
		public virtual String County
		{
			get { return _county; }
			set { _county = value; }
		}

		[DataMember]
		public virtual String Latitude
		{
			get { return _latitude; }
			set { _latitude = value; }
		}

		[DataMember]
		public virtual String Longitude
		{
			get { return _longitude; }
			set { _longitude = value; }
		}

		[DataMember]
		public virtual String State
		{
			get { return _state; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("State must not be blank or null.");
				else _state = value;
			}
		}


		#endregion
	}
}
