using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class Zone : Entity
	{
		#region Fields

		private FunctionalAreaCode _functionalAreaCode;
		private ICollection<CompanyLocationZone> _companyLocationZones = new HashSet<CompanyLocationZone>();
		private String _active;
		private String _description;
		private String _zoneCode;
		private ZoneType _zoneType;

		#endregion

		#region Properties

		[DataMember]
		public virtual FunctionalAreaCode FunctionalAreaCode
		{
			get { return _functionalAreaCode; }
			set { _functionalAreaCode = value; }
		}

		[DataMember]
		public virtual ICollection<CompanyLocationZone> CompanyLocationZones
		{
			get { return _companyLocationZones; }
			set { _companyLocationZones = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String ZoneCode
		{
			get { return _zoneCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ZoneCode must not be blank or null.");
				else _zoneCode = value;
			}
		}

		[DataMember]
		public virtual ZoneType ZoneType
		{
			get { return _zoneType; }
			set { _zoneType = value; }
		}


		#endregion
	}
}
