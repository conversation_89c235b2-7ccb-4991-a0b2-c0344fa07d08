using System;
using System.Runtime.Serialization;

namespace Upp.Irms.Domain
{
	[DataContract(IsReference = true, Namespace = "http://www.upp.com/irms/entities/")]
	public partial class ZoneCartonSize : Entity
	{
		#region Constructor

		public ZoneCartonSize()
		{
			//
		}

        #endregion

        #region Properties

        public virtual String CartonSizeCode { get; set; }

        public virtual String ZoneCode { get; set; }

        #endregion
    }
}
