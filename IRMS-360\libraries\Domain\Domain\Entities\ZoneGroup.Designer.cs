using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ZoneGroup : Entity
	{
		#region Fields

		private ICollection<CompanyLocationZone> _companyLocationZones = new HashSet<CompanyLocationZone>();
		private String _active;
		private String _description;
		private String _zoneGroupCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<CompanyLocationZone> CompanyLocationZones
		{
			get { return _companyLocationZones; }
			set { _companyLocationZones = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String ZoneGroupCode
		{
			get { return _zoneGroupCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ZoneGroupCode must not be blank or null.");
				else _zoneGroupCode = value;
			}
		}


		#endregion
	}
}
