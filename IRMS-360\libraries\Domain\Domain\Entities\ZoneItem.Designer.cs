using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ZoneItem : Entity
	{
		#region Fields

		private CompanyLocationZone _companyLocationZone;
		private Item _item;
		private String _active;

		#endregion

		#region Properties

		[DataMember]
		public virtual CompanyLocationZone CompanyLocationZone
		{
			get { return _companyLocationZone; }
			set { _companyLocationZone = value; }
		}

		[DataMember]
		public virtual Item Item
		{
			get { return _item; }
			set { _item = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}


		#endregion
	}
}
