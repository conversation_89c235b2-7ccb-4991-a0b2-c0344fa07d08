using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

using Iesi.Collections;
using Iesi.Collections.Generic;

namespace Upp.Irms.Domain
{
	[Serializable]
	partial class ZoneType : Entity
	{
		#region Fields

		private ICollection<Zone> _zones = new HashSet<Zone>();
		private String _active;
		private String _description;
		private String _zoneTypeCode;

		#endregion

		#region Properties

		[DataMember]
		public virtual ICollection<Zone> Zones
		{
			get { return _zones; }
			set { _zones = value; }
		}

		[DataMember]
		public virtual String Active
		{
			get { return _active; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Active must not be blank or null.");
				else _active = value;
			}
		}

		[DataMember]
		public virtual String Description
		{
			get { return _description; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("Description must not be blank or null.");
				else _description = value;
			}
		}

		[DataMember]
		public virtual String ZoneTypeCode
		{
			get { return _zoneTypeCode; }
			set
			{
				if (String.IsNullOrEmpty(value)) throw new NullReferenceException("ZoneTypeCode must not be blank or null.");
				else _zoneTypeCode = value;
			}
		}


		#endregion
	}
}
